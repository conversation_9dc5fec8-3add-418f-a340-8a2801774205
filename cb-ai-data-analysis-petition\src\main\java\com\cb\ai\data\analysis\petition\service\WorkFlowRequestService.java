package com.cb.ai.data.analysis.petition.service;



import com.cb.ai.data.analysis.petition.domain.dto.QaDto;
import com.cb.ai.data.analysis.petition.domain.vo.response.WorkFlowBaseParamVo;

import java.util.List;

public interface WorkFlowRequestService {
    String singleRequest(Object data, String workFlowId);

    String singleRequest(Object data,String promote, String workFlowId);

    String multipleRequest(List data, String workFlowId);

    WorkFlowBaseParamVo buildRequestParam(String workFlowId, Boolean stream);

    QaDto singleRequest(QaDto qaDto, String workFlowId, WorkFlowBaseParamVo param);
}
