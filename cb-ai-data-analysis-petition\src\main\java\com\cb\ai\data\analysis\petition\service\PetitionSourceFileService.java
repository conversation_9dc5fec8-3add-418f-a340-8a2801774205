package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionSourceFileEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile;
import com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.service.BaseService;

import java.util.List;

/***
 * <AUTHOR>
 * 信访源文件
 */
public interface PetitionSourceFileService extends BaseService<PetitionSourceFileEntity> {

    /***
     * 信访源文件分页查询，同时查询文件和源文件记录
     * @param file
     * @return
     */
    IPage<SourceFileVo> querySourceFileList(PetitionSourceFile file);


    /***
     * 信访源文件解析
     * @param ids
     * @param queryByStatus
     * @return
     */
    Result submitAnalyze(List<Long> ids, Boolean queryByStatus);
}
