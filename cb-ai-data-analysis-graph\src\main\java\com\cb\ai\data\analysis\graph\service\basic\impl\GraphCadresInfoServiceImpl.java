package com.cb.ai.data.analysis.graph.service.basic.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresInfo;
import com.cb.ai.data.analysis.graph.mapper.basic.GraphCadresInfoMapper;
import com.cb.ai.data.analysis.graph.repository.GraphCadresInfoRepository;
import com.cb.ai.data.analysis.graph.repository.esBo.GraphCadresInfoBo;
import com.cb.ai.data.analysis.graph.service.basic.GraphCadresInfoService;
import com.cb.ai.data.analysis.graph.utils.GraphUtil;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识图谱-干部信息(GraphCadresInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03 13:50:28
 */
@Service("graphCadresInfoService")
public class GraphCadresInfoServiceImpl extends ServiceImpl<GraphCadresInfoMapper, GraphCadresInfo> implements GraphCadresInfoService {

    @Autowired
    private GraphCadresInfoRepository repository;

    @Override
    @Transactional
    public boolean save(GraphCadresInfo graphCadresInfo) {
        if (ObjectUtil.isNotNull(graphCadresInfo) && StrUtil.isNotBlank(graphCadresInfo.getIdCard())) {
            QueryWrapper<GraphCadresInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id_card", graphCadresInfo.getIdCard());
            if (baseMapper.selectCount(queryWrapper) > 0) {
                throw new RuntimeException(String.format("身份证为【%s】的干部信息已存在！", graphCadresInfo.getIdCard()));
            }
        }
        if (baseMapper.insert(graphCadresInfo) > 0) {
            GraphUtil.handleOfficerInfo(List.of(graphCadresInfo));
            // ES 新增数据
            GraphCadresInfoBo bo = convert2Bo(graphCadresInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateById(GraphCadresInfo graphCadresInfo) {
        if (baseMapper.updateById(graphCadresInfo) > 0) {
            GraphUtil.handleOfficerInfo(List.of(graphCadresInfo));
            // ES 修改数据
            GraphCadresInfoBo bo = convert2Bo(graphCadresInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        // 从ES 中删除
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<GraphCadresInfo> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            GraphUtil.handleOfficerInfo(list);
            List<GraphCadresInfoBo> collect = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }

    private GraphCadresInfoBo convert2Bo(GraphCadresInfo info) {
        GraphCadresInfoBo bo = new GraphCadresInfoBo();
        BeanUtils.copyProperties(info, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }

}

