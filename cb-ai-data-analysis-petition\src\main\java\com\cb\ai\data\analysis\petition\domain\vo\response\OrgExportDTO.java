package com.cb.ai.data.analysis.petition.domain.vo.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class OrgExportDTO {

    @ExcelProperty("去向机构")
    @ColumnWidth(40)
    private String org;
    @ExcelProperty("数量")
    @ColumnWidth(15)
    private Integer count;

    @ExcelProperty("占比 (%)")
    @ColumnWidth(15)
    private Double percentage;
}