package com.cb.ai.data.analysis.graph.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BasicGraphLabelEnum {
    /**
     * 组织结构相关
     */
    ORG_NAME(BasicGraphCategoryEnum.ORG.getName(), "name", "社会机构/组织/企业/政府部门名称", Boolean.FALSE, Boolean.TRUE),
    ESTABLISHMENT_DATE(BasicGraphCategoryEnum.ORG.getName(), "成立日期", "成立日期", Boolean.TRUE, Boolean.FALSE),
    PROVINCE(BasicGraphCategoryEnum.ORG.getName(), "所属省份", "所属省份(按照标准行政区划来返回，并且一家公司/政府部门/机构组织的注册地址肯定只有一个省份，比如云南省，四川省等)", Boolean.TRUE, Boolean.FALSE),
    CITY(BasicGraphCategoryEnum.ORG.getName(), "所属城市", "所属城市(按照标准行政区划来返回，并且一家公司/政府部门/机构组织的注册地址肯定只有一个城市，比如昆明市，曲靖市等)", Boolean.TRUE, Boolean.FALSE),
    DISTRICT(BasicGraphCategoryEnum.ORG.getName(), "所属区县", "所属区县(按照标准行政区划来返回，并且一家公司/政府部门/机构组织的注册地址肯定只有一个省份，比如五华区，盘龙区等)", Boolean.TRUE, Boolean.FALSE),
    FORMER_NAME(BasicGraphCategoryEnum.ORG.getName(), "曾用名", "曾用名", Boolean.TRUE, Boolean.FALSE),

    /**
     * 人员相关
     */
    PERSON_ID(BasicGraphCategoryEnum.PERSON.getName(), "人员ID", "此属性请你自己生成，用于唯一确定一位人员，在抽取关系的过程中，用于标识两个人存在关系（可以是数字，可以是英文加数字，长度别超过5个字符即可。一定要可以唯一确定一位人员）", Boolean.FALSE, Boolean.TRUE),
    PERSON_NAME(BasicGraphCategoryEnum.PERSON.getName(), "name", "人员姓名", Boolean.FALSE, Boolean.TRUE),
    WORK_PLACE(BasicGraphCategoryEnum.PERSON.getName(), "工作单位", "人员工作单位，通常来说，这里出现的工作单位都应当在机构组织企业政府部门信息找到对应的信息", Boolean.FALSE, Boolean.TRUE),
    POSITION(BasicGraphCategoryEnum.PERSON.getName(), "身份证号", "人员身份证号", Boolean.FALSE, Boolean.TRUE),
    ID_CARD(BasicGraphCategoryEnum.PERSON.getName(), "现职务", "人员所在工作单位的职务", Boolean.FALSE, Boolean.FALSE),
    GENDER(BasicGraphCategoryEnum.PERSON.getName(), "性别", "性别", Boolean.TRUE, Boolean.FALSE),
    NATION(BasicGraphCategoryEnum.PERSON.getName(), "民族", "民族", Boolean.TRUE, Boolean.FALSE),
    NATIVE_PLACE(BasicGraphCategoryEnum.PERSON.getName(), "籍贯", "籍贯", Boolean.TRUE, Boolean.FALSE),
    BIRTH_DATE(BasicGraphCategoryEnum.PERSON.getName(), "出生年月", "出生年月", Boolean.TRUE, Boolean.FALSE),
    POLITICAL_STATUS(BasicGraphCategoryEnum.PERSON.getName(), "政治面貌", "群众/党员/团员/无党派人士/民主党派成员/中共预备党员 从这些选项中选择最接近的一个。", Boolean.TRUE, Boolean.FALSE),
    WORK_START_DATE(BasicGraphCategoryEnum.PERSON.getName(), "参加工作时间", "参加工作时间", Boolean.TRUE, Boolean.FALSE);

    private String category;

    private String attribute;

    private String desc;

    private Boolean editable;

    private Boolean index;
}
