package com.xong.boot.common.easyexcel.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.xong.boot.common.easyexcel.EasyExcelService;
import com.xong.boot.common.utils.StringUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
@Component
public class EasyExcelServiceImpl implements EasyExcelService {

    // 存储原始表头信息
    private static Map<Integer, String> originalHeaders = new LinkedHashMap<>();


    /**
     * 导入Excel文件
     *
     * @param inputStream
     * @param saveFunction
     * @param failMethod
     * @param <T>
     */
    @Override
    public <T> void importExcel(InputStream inputStream, Class<T> clazz, Function<List<T>, Boolean> saveFunction, Consumer<String> failMethod) {
        // 使用EasyExcel读取Excel文件并进行数据处理
        EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                    // 定义批量处理的大小,尽量和saveFunction批量写入的大小一致
                    private int batchSize = 1000;
                    // 存储读取的数据
                    private List<T> dataList = new ArrayList<>();

                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        // 保存原始表头
                        originalHeaders.putAll(headMap);
                    }

                    /**
                     * 每读取一行数据会调用一次该方法
                     * 将数据添加到dataList中，当达到batchSize时执行保存操作
                     */
                    @Override
                    public void invoke(T t, AnalysisContext analysisContext) {
                        dataList.add(t);
                        if (dataList.size() >= batchSize) {
                            saveFunction.apply(dataList); // 执行保存操作， dataList 中的数据类型已被转换为目标类型 clazz
                            dataList.clear(); // 清空列表以便下次存储
                        }
                    }

                    /**
                     * 所有数据分析完成后调用此方法
                     * 处理剩余未满batchSize的数据
                     */
                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        if (dataList.size() > 0) {
                            saveFunction.apply(dataList); // 保存剩余数据
                            dataList.clear(); // 清空列表
                        }
                    }

                    /**
                     * 判断是否继续读取下一行
                     * 默认调用父类方法
                     */
                    @Override
                    public boolean hasNext(AnalysisContext context) {
                        return super.hasNext(context);
                    }

                    /**
                     * 错误处理方法
                     * 如果读取过程中出现错误，将错误信息传递给failMethod
                     */
                    @Override
                    public void extra(CellExtra extra, AnalysisContext context) {
                        log.error("读取到错误信息：" + extra);
                    }

                    @Override
                    public void onException(Exception exception, AnalysisContext context) throws Exception {
                        log.error("解析异常: ", exception);
                        if (exception instanceof ExcelDataConvertException) {
                            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                            String errMessage = StringUtils.format("第<span style=\"color:red;\">{}</span>行，第<span style=\"color:red;\">{}</span>列数据转换失败，单元格内容：<span style=\"color:red;\">{}</span>",
                                    excelDataConvertException.getRowIndex() +1, // 默认第0行为表头，所以要+1
                                    excelDataConvertException.getColumnIndex(),
                                    excelDataConvertException.getCellData().getStringValue());
                            failMethod.accept(errMessage);
                            log.error(errMessage);
                        } else {
                            throw exception;
                        }
                    }
                })
                .sheet()
                .doRead(); // 开始读取Excel中的第一个sheet
    }

    @Override
    public void downloadExcelTemplate(Class clazz, String fileName, String sheetName, HttpServletResponse response) {
        // 设置响应内容类型为二进制流
        response.setContentType("application/octet-stream");
        // 设置文件下载的响应头，文件名进行URL编码
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        // 设置响应字符编码
        response.setCharacterEncoding("utf-8");
        try {
            // 使用EasyExcel写入空数据生成模板文件
            EasyExcel.write(response.getOutputStream())
                    .head(clazz) // 设置Excel表头
                    .registerWriteHandler(getStyleStrategy()) // 注册样式策略
                    .sheet(sheetName) // 设置sheet名称
                    .doWrite(new ArrayList<>()); // 写入空数据
        } catch (Exception e) {
            // 打印异常堆栈信息
            e.printStackTrace();
            // 抛出运行时异常
            throw new RuntimeException(e);
        }
    }

    @Override
    public <T> void exportExcel(Class clazz, String fileName, String sheetName, List<T> list, HttpServletResponse response) {
        // 设置响应内容类型为二进制流
        response.setContentType("application/octet-stream");
        // 设置文件下载的响应头，文件名进行URL编码
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        // 设置响应字符编码
        response.setCharacterEncoding("utf-8");
        try {
            // 使用EasyExcel写入数据到响应输出流
            EasyExcel.write(response.getOutputStream())
                    .head(clazz) // 配置表头
                    .sheet(sheetName) // 配置sheet名称
                    .registerWriteHandler(getStyleStrategy()) // 注册样式策略
                    .doWrite(list); // 写入数据
        } catch (Exception e) {
            // 抛出运行时异常
            throw new RuntimeException(e);
        }
    }


    private HorizontalCellStyleStrategy getStyleStrategy() {
        // 1. 定义表头样式
        WriteCellStyle headerStyle = new WriteCellStyle();
        // 设置背景色（蓝色）
        headerStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 设置字体（白色、加粗）
        WriteFont headerFont = new WriteFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerFont.setFontHeightInPoints((short) 10);
        headerStyle.setWriteFont(headerFont);
        // 设置居中
        headerStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 2. 定义内容样式（可选）
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        // 设置自动换行
        contentStyle.setWrapped(true);
        // 设置自动列宽

        // 3. 构建样式策略
        return new HorizontalCellStyleStrategy(headerStyle, contentStyle);
    }

}
