package com.cb.ai.data.analysis.query.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;
import com.cb.ai.data.analysis.query.domain.vo.ElasticRespVo;

import java.util.List;

public interface ElasticQueryService {
    /**
     * 分页搜索
     *
     * @param query 索引 为空查询所有
     * @return
     * @throws Exception
     */
    Page<ElasticRespVo.Resp> page(ElasticReqBo.Query query);

    /**
     * 搜索
     *
     * @param query 索引 为空查询所有
     * @return
     * @throws Exception
     */
    List<ElasticRespVo.Resp> list(ElasticReqBo.Query query);

    /**
     * 获取文档
     *
     * @param indexName
     * @param id        文档id
     * @return
     * @throws Exception
     */
    Object getDocument(String indexName, String id);
}
