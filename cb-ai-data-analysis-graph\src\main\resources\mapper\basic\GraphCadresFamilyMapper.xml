<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.graph.mapper.basic.GraphCadresFamilyMapper">
    <resultMap type="com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresFamily" id="GraphCadresFamilyMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="familyName" column="family_name" jdbcType="VARCHAR"/>
        <result property="familyRelation" column="family_relation" jdbcType="VARCHAR"/>
        <result property="familyIdCard" column="family_id_card" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

