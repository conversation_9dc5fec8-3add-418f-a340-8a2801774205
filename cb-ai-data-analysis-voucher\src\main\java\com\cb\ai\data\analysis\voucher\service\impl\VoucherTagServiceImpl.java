package com.cb.ai.data.analysis.voucher.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTag;
import com.cb.ai.data.analysis.voucher.mapper.VoucherTagMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherTagService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VoucherTagServiceImpl extends BaseServiceImpl<VoucherTagMapper, VoucherTag> implements VoucherTagService {

    @Override
    public Map<String, List<String>> getTagsByVoucherIds(List<String> voucherIds) {
        if(ObjectUtil.isEmpty(voucherIds)){
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<VoucherTag> queryWrapper = Wrappers.<VoucherTag>lambdaQuery()
                .in(VoucherTag::getVoucherId, voucherIds);
        return baseMapper.selectList(queryWrapper).stream().collect(
                Collectors.groupingBy(VoucherTag::getVoucherId,
                        Collectors.mapping(VoucherTag::getTag, Collectors.toList()))
        );
    }

    @Override
    public int batchAddTag(Set<String> infoIds, String[] tags) {
        tags = Arrays.stream(tags).map(String::trim).filter(StringUtils::isNotBlank).distinct().toArray(String[]::new);
        if(infoIds.isEmpty() || tags.length == 0){
            return 0;
        }
        // TODO 暂不考虑同一凭证重复标签的问题
        List<VoucherTag> list = new LinkedList<>();
        for (String infoId : infoIds) {
            for (String tag : tags) {
                VoucherTag voucherTag = new VoucherTag();
                voucherTag.setId(IdUtil.getSnowflakeNextIdStr());
                voucherTag.setVoucherId(infoId);
                voucherTag.setTag(tag);
                list.add(voucherTag);
            }
        }
        //入库
        saveBatch(list);
        return list.size();
    }

    @Override
    public int batchDelTag(Set<String> infoIds, String[] tags) {
        tags = Arrays.stream(tags).map(String::trim).filter(StringUtils::isNotBlank).distinct().toArray(String[]::new);
        if(infoIds.isEmpty() || tags.length == 0){
            return 0;
        }
        int rst = 0;
        for (int i = 0; i < tags.length; i++) {
            LambdaQueryWrapper<VoucherTag> queryWrapper = Wrappers.<VoucherTag>lambdaQuery()
                    .in(VoucherTag::getVoucherId, infoIds)
                    .eq(VoucherTag::getTag, tags[i]);
            rst += baseMapper.delete(queryWrapper);
        }
        return rst;
    }

    @Override
    public List<Map<String, String>> getAllTags() {
        List<String> values = baseMapper.getAllTags();
        if(values.isEmpty()){
            return Collections.emptyList();
        }
        return values.stream().map(tag -> Map.of("label", tag, "value", tag)).collect(Collectors.toList());
    }
}
