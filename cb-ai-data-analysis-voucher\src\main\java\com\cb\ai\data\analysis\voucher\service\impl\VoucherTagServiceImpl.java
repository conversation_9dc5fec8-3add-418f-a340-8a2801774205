package com.cb.ai.data.analysis.voucher.service.impl;

import cn.hutool.core.util.IdUtil;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTag;
import com.cb.ai.data.analysis.voucher.mapper.VoucherTagMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherTagService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

@Service
public class VoucherTagServiceImpl extends BaseServiceImpl<VoucherTagMapper, VoucherTag> implements VoucherTagService {

    @Override
    public int batchAddTag(Set<String> infoIds, String[] tags) {
        tags = Arrays.stream(tags).map(String::trim).filter(StringUtils::isNotBlank).distinct().toArray(String[]::new);
        if(infoIds.isEmpty() || tags.length == 0){
            return 0;
        }
        // TODO 暂不考虑同一凭证重复标签的问题
        List<VoucherTag> list = new LinkedList<>();
        for (String infoId : infoIds) {
            for (String tag : tags) {
                VoucherTag voucherTag = new VoucherTag();
                voucherTag.setId(IdUtil.getSnowflakeNextIdStr());
                voucherTag.setVoucherId(infoId);
                voucherTag.setTag(tag);
                list.add(voucherTag);
            }
        }
        //入库
        saveBatch(list);
        return list.size();
    }
}
