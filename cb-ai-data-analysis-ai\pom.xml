<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cb.ai</groupId>
        <artifactId>cb-ai-data-analysis</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>cb-ai-data-analysis-ai</artifactId>
    <description>数据分析-AI模块</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>*******</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    对象池    -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-common</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-framework</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-poi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.4.8-jre</version>
        </dependency>

        <dependency>
            <groupId>io.github.admin4j</groupId>
            <artifactId>http</artifactId>
            <version>0.9.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.admin4j.json</groupId>
                    <artifactId>admin4j-json-fastjson2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.admin4j.json</groupId>
            <artifactId>admin4j-json-jackson</artifactId>
            <version>0.10.0</version>
        </dependency>

        <!--   disruptor事件消费组件   -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>4.0.0</version>
        </dependency>

        <!--   pyhton代码   -->
<!--        <dependency>-->
<!--            <groupId>org.python</groupId>-->
<!--            <artifactId>jython-standalone</artifactId>-->
<!--            <version>2.7.3</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-file</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.2</version>
        </dependency>

        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>25.4</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/aspose-words-25.4.jar</systemPath>
        </dependency>

        <!--  json修复 -->
        <dependency>
            <groupId>io.github.haibiiin</groupId>
            <artifactId>json-repair</artifactId>
            <version>0.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.mikesamuel</groupId>
            <artifactId>json-sanitizer</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.20.1</version>
        </dependency>

<!--   阿里AI 百炼云，需要 API Key    -->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud.ai</groupId>-->
<!--            <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
