package com.cb.ai.data.analysis.petition.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionProblemHandleVo;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleResultService;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/***
 * <AUTHOR>
 * 问题批处理
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/problem")
public class PetitionProblemHandleController {

    @Autowired
    private PetitionProblemHandleService petitionProblemHandleService;

    @Autowired
    private PetitionProblemHandleResultService petitionProblemHandleResultService;

    @Value("${upload.problemFolderId}")
    private String  folderId;

    /***
     * 分页查询问题列表
     * @param petitionProblemHandle
     * @return
     */
    @GetMapping("/page")
    public Result page(PetitionProblemHandleVo petitionProblemHandle) {
        try{
            Page<PetitionProblemHandleEntity> knowledgeBasePage=petitionProblemHandleService.selectByPage(petitionProblemHandle);
            return Result.success(folderId,knowledgeBasePage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询问题处理列表失败！");
        }
    }
    /**
     * 根据ID删除问题批处理列表
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable String id){
        try{
            petitionProblemHandleService.delPetitionProblemHandle(id);
            return Result.success("删除批处理问题成功！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("问题列表删除失败！");
        }
    }

    @GetMapping("/analysis/result/{id}")
    public Result analysisResult(@PathVariable String id){
        try{
            LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PetitionProblemHandleResultEntity::getProblemId, id);
            queryWrapper.orderByAsc(PetitionProblemHandleResultEntity::getProblemSerial);
            List<PetitionProblemHandleResultEntity> list=petitionProblemHandleResultService.list(queryWrapper);
            return Result.successData(list);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取对话内容失败！");
        }
    }

    /**
     * 下载excel生成报告
     */
    @GetMapping("/export/word/{problemId}")
    public void downloadQaWord(@PathVariable String problemId, HttpServletResponse response) {
        try {
            petitionProblemHandleResultService.exportQaDetailWord(problemId, response);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 下载excel生成报告
     */
    @GetMapping("/export/excel/{problemId}")
    public void downloadQaExcel(@PathVariable String problemId, HttpServletResponse response) {
        petitionProblemHandleResultService.exportQaDetailExcel(problemId, response);
    }

    @PostMapping("/listByIds")
    public Result listByIds(@RequestBody List<String> ids){
        try{
            if(!CollectionUtils.isEmpty(ids)&&ids.size()>0){
                return Result.successData(petitionProblemHandleService.listByIds(ids));
            }else{
                return Result.successData(null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取问题信息失败！");
        }
    }
    @GetMapping("/download/problemBatchTemplate/{templateType}")
    public void downloadProblemBatchTemplate(@PathVariable String templateType,HttpServletResponse response){
        try{
            String templateName="problem_batch_template.xlsx";
            if(StringUtils.equals(templateType,"batch")){
                templateName="problem_batch_template.xlsx";
            }
            if(StringUtils.equals(templateType,"domain")){
                templateName="problem_domain_template.xlsx";
            }
            InputStream templateStream=this.getClass().getClassLoader().getResourceAsStream("templates/"+templateName);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition","attachment; filename="+templateName);

            try (InputStream inputStream = templateStream; OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
                if(inputStream!=null){//
                    inputStream.close();
                }
                if(templateStream!=null){
                    templateStream.close();
                }
                if(outputStream!=null){
                    outputStream.close();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

//    /**
//     * 问题批处理文件信息提交
//     * @return
//     */
//    @PostMapping("/file")
//    public Result saveProblemHandleFile(@RequestBody List<PetitionProblemHandleEntity> problemHandleFileList){
//        try{
//            petitionProblemHandleService.saveProblemHandleFile(problemHandleFileList);
//            return Result.success("文件上传成功！");
//        }catch (XServiceException e){
//            e.printStackTrace();
//            return Result.fail(e.getMessage());
//        }catch (Exception e){
//            e.printStackTrace();
//            return Result.fail("知识库删除失败！");
//        }
//    }
}
