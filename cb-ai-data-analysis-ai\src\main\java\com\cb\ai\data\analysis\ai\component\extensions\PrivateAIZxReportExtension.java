package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChain;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.func.TriConsumer;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.MdPromoteExtractorUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xong.boot.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI(中巡)生成报告扩展
 */
@ExtensionProvider(desc = "生成报告扩展", businessScenes = {
    @Route(tag = "巡视巡察报告", business = BusinessTypeEnum.PRIVATE_BUSINESS),
    @Route(tag = "生成报告(中巡)", business = BusinessTypeEnum.PRIVATE_BUSINESS),
    @Route(tag = "开展警示教育工作方案", business = BusinessTypeEnum.PRIVATE_BUSINESS),
    @Route(tag = "理论学习方案", business = BusinessTypeEnum.PRIVATE_BUSINESS),
    @Route(tag = "纪检监察建议", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PrivateAIZxReportExtension implements PrivateAIExtension<CommonAIRequestContext> {

    private final EmbeddingExtension embeddingExtension;

    @Override
    public Flux<?> invoke(CommonAIRequestContext requestContext) {
        FlowChain flowChain = FlowChain.newChain(requestContext.getRequestId());
        String[] tags = requestContext.getAnalyseTag()[0].split("/");
        String tag;
        if (tags.length > 1) {
            tag = tags[1];
            flowChain.addProcessNode("知识库向量文件查询", (flowContext, nodeContext) ->
                embeddingExtension.invoke(
                    requestContext,
                    collectData -> {
                        flowContext.set("embeddingContext", collectData.embeddingContext());
                        flowContext.set("embeddingList", collectData.dataList());
                        CommonLog.info("知识库向量结果条数为：{}", collectData.dataList().size());
                    }
                )
            );
        } else {
            tag = tags[0];
        }
        if ("生成大纲".equalsIgnoreCase(tag)) {
            buildNode(requestContext, flowChain, (context, flowContext, value) -> {
                String mdPath = "promote/report/" + tags[0] + "/生成大纲.md";
                context.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                context.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath,
                    Map.of("firstTitle", value, "references", flowContext.get("embeddingContext")))
                );
            });
        } else if ("重新生成".equalsIgnoreCase(tag)) {
            flowChain.addSerialNode(PrivateAIChat.class, node ->
                node.nodeName(tags[0] + "-重新生成内容")
                    .context(context -> {
                        String mdPath = "promote/report/" + tags[0] + "/重新生成.md";
                        CommonAIRequestContext aiContext = requestContext.copy();
                        JsonMap extendData = requestContext.getExtendData();
                        if (extendData == null) {
                            throw new CustomException("重新生成传参为空，需要content，los，title");
                        }
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.replaceSysPromoteTags(mdPath, Map.of("references", context.get("embeddingContext"))));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, extendData));
                        return aiContext;
                    })
                    .dataDispose(data -> {
                        Function<String, String> replaceFun = (str) -> {
                            if (StrUtil.isNotBlank(str)) {
                                return str.replaceAll("[*#\\- ]+", "");
                            }
                            return str;
                        };
                        data.setReasoning_content(replaceFun.apply(data.getReasoning_content()));
                        data.setContent(replaceFun.apply(data.getContent()));
                        return data;
                    })
            );
        } else if ("生成报告".equalsIgnoreCase(tag)) {
            buildNode(requestContext, flowChain, (context, flowContext, value) -> {
                String mdPath = "promote/report/" + tags[0] + "/生成报告.md";
                context.setSystemPromote(MdPromoteExtractorUtil.replaceSysPromoteTags(mdPath,
                    Map.of("userPromote", context.getPromote(), "references", flowContext.get("embeddingContext")))
                );
                context.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, Map.of("firstTitle", value)));
            });
            flowChain.addProcessNode("溯源文件", (flowContext, nodeContext) -> {
                List<JsonMap> embeddingList = flowContext.get("embeddingList");
                return Flux.fromIterable(embeddingList)
                    .map(item -> new PrivateAIBackData()
                        .setRole(RoleEnum.assistant.name())
                        .setSessionId(requestContext.getSessionId())
                        .setData(item)
                    );
            });
        } else {
            // 获取大纲一级标题
            String promote = MdPromoteExtractorUtil.getPromote("promote/report/" + tags[0] + "/一级标题.md");
            return Flux.just(JsonUtil.toMap(promote));

        }

        return flowChain.execute();
    }


    private void buildNode(CommonAIRequestContext requestContext, FlowChain flowChain, TriConsumer<CommonAIRequestContext, FlowContext, String> contextConsumer) {
        List<Map<String, String>> templateList = Assert.notEmpty(
            requestContext.getExtendData().getCollection("template", new TypeReference<List<Map<String, String>>>() {}),
            requestContext.getAnalyseTag()[0] + "的请求入参（template）为空！"
        );
        templateList.forEach(item -> {
            item.forEach((key, value) -> {
                flowChain.addParallelNode(PrivateAIChat.class, node ->
                    node.context(flowContext -> {
                        CommonAIRequestContext aiContext = requestContext.copy();
                        contextConsumer.accept(aiContext, flowContext, value);
                        return aiContext;
                    })
                    .maxTokens(token -> (int) (token * 0.9))
                    .temperature(temperature -> 0.1F)
                    .parentNodeId(key)
                , 5);
            });
        });
    }
}