package com.cb.ai.data.analysis.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFile;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeFileVo;

/***
 * <AUTHOR>
 * 知识库文件管理
 */
public interface KnowledgeFileService {

    /***
     * 文件上传  基于url
     * @param KnowledgeBaseFile
     * @return
     * @throws Exception
     */
    String saveupload(KnowledgeBaseFile KnowledgeBaseFile)throws Exception;

    /***
     *  文件列表分页查询
     * @param knowledgeFile
     * @return
     * @throws Exception
     */
    Page<KnowledgeFileVo> pageKnowledgeFile(KnowledgeFile knowledgeFile)throws Exception;

    /***
     * 根据ID删除知识库文件
     * @param knowledgeFileId
     * @return
     */
    String delKnowledgeFile(String knowledgeFileId)throws Exception;

    /***
     * 重新解析至向量库
     * @param knowledgeFileId
     * @return
     * @throws Exception
     */
    String reUploadAnalysis(String knowledgeFileId)throws Exception;

    /***
     * 根据文件ID获取文件信息
     * @param id
     * @return
     */
    KnowledgeFileVo getKnowledgeFileById(String id);


    /***
     * 插队解析
     * @param fileId
     * @return
     * @throws Exception
     */
    String fileUrgent(String fileId)throws Exception;

    /***
     * 获取文件管理的ID
     * @param aiFileId
     * @return
     */
    String getFileId(String aiFileId);


}
