package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile;
import com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileAnalysisService;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileService;
import com.xong.boot.common.api.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/file/analysis")
public class PetitionSourceFileAnalysisController {

    @Autowired
    private PetitionSourceFileAnalysisService petitionSourceFileAnalysisService;

    /**
     * 文件管理，上传ai解析
     * */
    @PostMapping("/ai")
    public Result aiAnalysis(@RequestBody String[] fileIds) {
        try {
            if (fileIds == null || fileIds.length == 0) {
                return Result.fail("上传文件ID不能为空");
            }
            return petitionSourceFileAnalysisService.aiAnalysis(fileIds);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("文件ai解析失败：" + e.getMessage());
        }
    }
}
