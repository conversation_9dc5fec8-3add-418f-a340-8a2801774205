package com.xong.boot.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.handlers.ArrayStringTypeHandler;
import com.xong.boot.common.json.deserializer.LocalDateDeserializer;
import com.xong.boot.common.json.serializer.AvatarSerializer;
import com.xong.boot.common.json.serializer.LocalDateSerializer;
import com.xong.boot.common.lang.UserAvatar;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseDomain {
    /**
     * 用户ID
     */
    @TableId
    @NotBlank(message = "用户ID不存在", groups = UpdateGroup.class)
    private String userId;
    /**
     * 用户编号
     */
    @TableField(condition = SqlCondition.LIKE)
    private String userCode;
    /**
     * 用户账号
     */
    @TableField(updateStrategy = FieldStrategy.NEVER, condition = SqlCondition.LIKE)
    @NotBlank(message = "用户账号不存在", groups = AddGroup.class)
    @Size(min = 2, max = 32, message = "账号长度必须大于等于2位小于32位", groups = AddGroup.class)
    private String username;
    /**
     * 用户密码
     */
    @JsonIgnore
    @TableField(whereStrategy = FieldStrategy.NEVER)
    private String password;
    /**
     * 盐值
     */
    @JsonIgnore
    @TableField(updateStrategy = FieldStrategy.NEVER, whereStrategy = FieldStrategy.NEVER)
    private String salt;
    /**
     * usb-key
     */
    @TableField(typeHandler = ArrayStringTypeHandler.class)
    private List<String> usbKey;
    /**
     * 用户头像
     */
    @JsonSerialize(using = AvatarSerializer.class)
    @TableField(whereStrategy = FieldStrategy.NEVER, typeHandler = Fastjson2TypeHandler.class)
    private UserAvatar avatar;
    /**
     * 真实姓名
     */
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY, condition = SqlCondition.LIKE)
    @NotBlank(message = "用户姓名不存在", groups = AddGroup.class)
    @Size(min = 2, max = 32, message = "用户姓名长度必须大于等于2位小于32位", groups = {AddGroup.class, UpdateGroup.class})
    private String realname;
    /**
     * 姓名拼音
     */
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY, condition = SqlCondition.LIKE)
    private String pinyin;
    /**
     * 手机号码
     */
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY, condition = SqlCondition.LIKE)
    private String phone;
    /**
     * 身份证号
     */
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY, condition = SqlCondition.LIKE)
    private String idCard;
    /**
     * 邮箱地址
     */
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY, condition = SqlCondition.LIKE)
    private String email;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 所属地区
     */
    @TableField(typeHandler = ArrayStringTypeHandler.class)
    private List<String> districtIds;
    /**
     * 出生日期
     */
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthday;
    /**
     * 性别(0未知 1男 2女)
     */
    private Integer gender;
    /**
     * 账号过期时间（秒）
     */
    private Long accountExpired;
    /**
     * 凭证过期时间（秒）
     */
    private Long credentialsExpired;
    /**
     * 排序
     */
    private Integer sortOn;
    /**
     * 状态（0正常 1禁用）
     */
    private Integer status;
    /**
     * 是否删除（0正常 1删除）
     */
    @JsonIgnore
    @TableLogic
    private Boolean delFlag;

    /**
     * 获取地区编码
     */
    public String getAreaCode() {
        if (districtIds == null || districtIds.size() == 0) {
            return null;
        }
        return districtIds.get(districtIds.size() - 1);
    }
}
