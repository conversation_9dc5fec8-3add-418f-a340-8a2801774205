package com.cb.ai.data.analysis.basdata.domain.entity.finance;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发票信息表(BasInvoiceInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:59:36
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class BasInvoiceInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //部门id
    @ExcelIgnore
    private String deptId;

    //区域编码
    @ExcelIgnore
    private String areaCode;

    //发票代码和号码
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "发票代码和号码")
    private String invoiceCodeNumber;

    //开票日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "开票日期")
    private LocalDateTime invoiceDate;

    //购买方名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "购买方名称")
    private String buyerName;

    //购买方纳税人识别号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "购买方纳税人识别号")
    private String buyerTaxNumber;

    //购买方地址
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "购买方地址")
    private String buyerAddress;

    //购买方电话
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "购买方电话")
    private String buyerPhone;

    //购买方开户行及账号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "购买方开户行及账号")
    private String buyerBankAccount;

    //销售方名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方名称")
    private String sellerName;

    //销售方纳税人识别号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方纳税人识别号")
    private String sellerTaxNumber;

    //销售方地址
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方地址")
    private String sellerAddress;

    //销售方电话
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方电话")
    private String sellerPhone;

    //销售方开户行及账号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方开户行及账号")
    private String sellerBankAccount;

    //价税合计
    @ExcelProperty(value = "价税合计")
    private BigDecimal priceTaxTotal;

    //金额合计
    @ExcelProperty(value = "金额合计")
    private BigDecimal priceTotal;

    //税额合计
    @ExcelProperty(value = "税额合计")
    private BigDecimal taxTotal;

    //开票人
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "开票人")
    private String drawer;

    //复核人
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "复核人")
    private String reviewer;

    //收款人
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "收款人")
    private String payee;

    //销售方
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "销售方")
    private String salesParty;

}

