package com.cb.ai.data.analysis.petition.utils;

import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import org.springframework.stereotype.Component;

@Component("petitionFileReaderFactory")
public class FileReaderFactory {

    public FileReaderHandler getReaderByContentType(String contentType) {
        if (contentType.equalsIgnoreCase(FileContentTypeEnum.DOC.getContentType())) {
            return new DocReaderHandler();
        } else if (contentType.equalsIgnoreCase(FileContentTypeEnum.XLS.getContentType()) || contentType.equalsIgnoreCase(FileContentTypeEnum.XLSX.getContentType())) {
            return new ExcelReaderHandler();
        } else if (contentType.equals(FileContentTypeEnum.DOCX.getContentType())) {
            return new DocxReaderHandler();
        } else if (contentType.equals(FileContentTypeEnum.PDF.getContentType())) {
            return new PDFReaderHandler();
        } else {
            throw new RuntimeException("无法解析的文件类型：" + contentType);
        }
    }

    public FileReaderHandler getReaderBySuffix(String suffix) {
        if (suffix.equalsIgnoreCase(FileContentTypeEnum.DOC.getSuffix())) {
            return new DocReaderHandler();
        } else if (suffix.equalsIgnoreCase(FileContentTypeEnum.XLS.getSuffix()) || suffix.equalsIgnoreCase(FileContentTypeEnum.XLSX.getSuffix())) {
            return new ExcelReaderHandler();
        } else if (suffix.equals(FileContentTypeEnum.DOCX.getSuffix())) {
            return new DocxReaderHandler();
        } else if (suffix.equals(FileContentTypeEnum.PDF.getSuffix())) {
            return new PDFReaderHandler();
        } else {
            throw new RuntimeException("无法解析的文件后缀类型：" + suffix);
        }
    }
}
