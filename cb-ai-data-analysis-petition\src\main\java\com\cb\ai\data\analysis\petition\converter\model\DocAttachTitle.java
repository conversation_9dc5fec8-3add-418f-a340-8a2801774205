package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文附件内容
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class DocAttachTitle extends BaseDoc {
//    /**
//     * 附件序号
//     */
//    private Integer serialNumber;
//    /**
//     * 标题列表
//     */
//    private List<String> texts = new ArrayList<>();
//
//    /**
//     * 标题换行数
//     */
//    public int size() {
//        return texts.size();
//    }
//}
