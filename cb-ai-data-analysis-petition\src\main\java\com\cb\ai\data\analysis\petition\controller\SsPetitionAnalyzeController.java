package com.cb.ai.data.analysis.petition.controller;


import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.service.SsPetitionAnalyzedService;
import com.cb.ai.data.analysis.petition.service.SsPetitionOriginService;
import com.xong.boot.common.api.Result;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;

/***
 * <AUTHOR>
 * 信访件解析结果
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/analyzed")
public class SsPetitionAnalyzeController {

    @Resource
    private SsPetitionAnalyzedService ssPetitionAnalyzedService;

    @Resource
    private SsPetitionOriginService ssPetitionOriginService;


    @GetMapping("/page")
    public Result page(PetitionQueryPageQueryVo queryPageVo) {
        return Result.successData(ssPetitionAnalyzedService.selectByPage(queryPageVo));
    }

    @GetMapping("/origin/{originId}")
    public Result getPetitionOrigin(@PathVariable Long originId) {
        return Result.successData(ssPetitionOriginService.getById(originId));
    }

    @GetMapping("/{id}")
    public Result getPetitionAnalyzed(@PathVariable Long id) {
        return Result.successData(ssPetitionAnalyzedService.getById(id));
    }


    @DeleteMapping("/{id}/{originId}")
    public Result delPetitionAnalyzed(@PathVariable Long id,@PathVariable Long originId) {
        try {
            ssPetitionAnalyzedService.removeById(id);
            ssPetitionOriginService.removeById(originId);
            return Result.success("删除成功！");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @PutMapping
    public Result editPetitionAnalyzed(@RequestBody SsPetitionAnalyzedEntity entity) {
        try {
            ssPetitionAnalyzedService.updateById(entity);
            return Result.success("修改成功");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping("/export")
    public void export(@RequestBody PetitionQueryPageQueryVo queryPageVo, HttpServletResponse response) {
        try {
            ssPetitionAnalyzedService.export(queryPageVo, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/export/statics")
    public void exportStatics(HttpServletResponse response) {
        try {
            ssPetitionAnalyzedService.exportStatics(response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @GetMapping("/origin/page")
    public Result originPage(PetitionQueryPageQueryVo queryPageVo) {
        return Result.successData(ssPetitionOriginService.selectByPage(queryPageVo));
    }

}
