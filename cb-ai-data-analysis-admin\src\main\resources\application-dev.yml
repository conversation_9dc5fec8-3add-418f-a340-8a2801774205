# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8888
  tomcat:
    connection-timeout: 30000ms

spring:
  codec:
    max-in-memory-size: 10MB
  datasource:
    dynamic:
      datasource:
        master:  # 业务系统库
#          url: *******************************************************************************************************************************************
#          username: root
#          password: Password@cbxxjs215
          url: ***************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
        clickhouse: # 动态表单库
          url: **************************************************************************
          username: default
          password:
          driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
          druid:
            filters: stat #去除wall不然无法创建表

  data:
    redis: # redis 配置
      host: localhost
      port: 6379
      password:
    neo4j:
      database: neo4j

  # 文件上传大小设置
  servlet:
    multipart:
      max-file-size: 2050MB
      max-request-size: 2100MB

  # elasticsearch 配置
  elasticsearch:
    uris:
    - http://***********:9210  # 测试环境 可新增多个节点
    username:
    password:
    connectionTimeout: 30
    readTimeout: 30

  # neo4j 配置
  neo4j:
    uri: neo4j://***********:7687
    authentication:
      username: neo4j
      password: 1q2w3e4r

xong:
  boot:
    security:
      licenseEnabled: false # 是否开启证书验证

cb:
  dbtable:
    db-name: db_data_analysis #动态表数据库名 必须与 多数据源中的 clickhouse: 的库名一致
  ai:
    # Authorization: 1
    # url:
      # knowledgeBase: http://***********:8686/cb-ai/knowledge/base
      # knowledgeVectorSearch: http://***********:8686/cb-ai/rag/embedding/search
      # chat: http://***********:8686/cb-ai/chat
      # knowledgeFile: http://***********:8686/cb-ai/knowledge/base/file
      # textEmbeddingVector: http://***********:8686/cb-ai/rag/embedding/textVector
    embeddingChat:
      topK: 30  # 私有化向量查询问答topK设置
    # 下面的配置是cb-ai-data-analysis-ai模块的配置
    private-ai-base:
      base-url: http://***********:8686/cb-ai # AI知识库平台地址
      llm:
        model: qwen25
        role: user
        temperature: 0.7
      header-map:
        Authorization: 1
      chat:
        stream-url: /chat/basic
        block-url: /chat/sync
      knowledge: /chat/knowledge
      ocr: /rag/ocr
      file-store: /knowledge/base/file/saveupload
      file-analysis: /tool/file/analysis/upload
      finance: http://***********:8055/api/v1/chat/stream  # 财务分析接口
      audio-analysis:
        base-url: http://***********:9992/api/v1  # 语音解析接口
        invoke-url: /upload
        #health-url: /health
        task-status-url: /status/{task_id}
        task-result-url: /task/{task_id}
        task-list-url: /tasks
        delete-task-url: /delete/{task_id}
      deep-think-url: http://10.10.10.68:8121/api/chat/stream # AI Python平台地址 深度思考地址
      embedding-url: /rag/embedding/search # 向量查询地址
    base-url: http://***********:8686 # AI知识库平台地址 暂时没有用
    py-base-url: http://10.10.10.68:8121 # AI Python平台地址 需要用这个地址去拿深度研究配置配置

logging:
  level:
    #    org: debug
    #    org.springframework: debug
    #    com.baomidou.dynamic: debug
    com.xong.boot: debug
    com.cb.ai.data.analysis: debug
#    org.activiti.engine.impl.persistence.entity: debug
minio:
  endpoint: http://***********:9100
  bucket: cb-ai-knowledge
  accessKey: minioadmin
  secretKey: minioadmin123
  expiry: 1 # redis 文件信息失效时间 天
  breakpointTime: 1 # 分片地址失效时间 天

upload:
  xfFolderId: 1946100942269550592  # 信访库文件上传目录
  problemFolderId: 1946101241042407424 #问答批处理文件上传目录
