package com.cb.ai.data.analysis.knowledge.service;

import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.NlsqlData;
import org.springframework.web.bind.annotation.RequestBody;

/***
 * <AUTHOR>
 * 知识库文件管理 因为要跟文件管理联动，故写在admin模块
 */
public interface KnowledgeService {

    /**
     * 保存至向量库
     * @param knowledgeBaseFile
     * @return
     * @throws Exception
     */
    String saveupload(@RequestBody KnowledgeBaseFile knowledgeBaseFile)throws Exception;


    /***
     * 文件上传合并，
     * 1、先上传文件库
     * 2、通过文件库获取下载地址
     * 3、同步至向量库解析
     * @param file
     * @param baseId
     * @return
     * @throws Exception
     */
   // SuperviseResourceFile upload(MultipartFile file,String baseId)throws Exception;


    /***
     * 获取文件跟节点信息
     * @return
     * @throws Exception
     */
    SuperviseResourceFolder getResourceFolderRootInfo()throws Exception;

    String saveNlsqlToFile(NlsqlData nlsqlData);


    /***
     * 根据ID删除知识库文件
     * @param knowledgeFileId
     */
    void delKnowledgeFile(String knowledgeFileId)throws Exception;

}
