package com.cb.ai.data.analysis.voucher.mapper;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherAlertRecordVo;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

public interface VoucherAlertRecordMapper extends BaseMapper<VoucherAlertRecord> {

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    Page<VoucherAlertRecordVo.RespItem> pageByEntity(Page<VoucherAlertRecordVo.RespItem> page, @Param(Constants.ENTITY) VoucherAlertRecordVo.PageReq req);

    /**
     * 详情
     * @param id
     * @return
     */
    VoucherAlertRecordVo.RespItem detail(@Param("id") String id);

    /**
     * 根据凭证ID查询(非任务分析)
     * @param voucherId
     * @return
     */
    VoucherAlertRecordVo.RespItem detailByVoucherId(@Param("voucherId") String voucherId);

}
