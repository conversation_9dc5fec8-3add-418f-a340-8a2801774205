package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsConversationRecordEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsConversationRecordVo;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.service.BaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/***
 * <AUTHOR>
 * 谈话解析
 */
public interface SsConversationRecordService extends BaseService<SsConversationRecordEntity> {

    /***
     * 谈话库分页查询
     * @param ssConversationRecord
     * @return
     */
    Page<SsConversationRecordEntity> selectByPage(SsConversationRecordVo ssConversationRecord);

    /***
     * ocr批量解析
     * @param files
     * @return
     */
    Result ocrBatchAnalysis(List<MultipartFile> files) throws Exception;


    /***
     * ocr解析
     * @param file
     * @return
     */
    Result ocrAnalysis(MultipartFile file) throws Exception;

    /***
     * 新增和更新
     * @return
     */
    int saveOrUpdate(SsConversationRecordVo vo);
}
