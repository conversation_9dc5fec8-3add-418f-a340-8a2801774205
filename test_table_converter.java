public class test_table_converter {
    public static void main(String[] args) {
        String simpleTable = "<table><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr></table>";
        System.out.println("Input: " + simpleTable);
        System.out.println("Output should be a markdown table");

        // Simple conversion logic demo
        String result = simpleTable.replaceAll("<table[^>]*>", "\n")
                                  .replaceAll("</table>", "\n")
                                  .replaceAll("<tr[^>]*>", "")
                                  .replaceAll("</tr>", "|\n")
                                  .replaceAll("<th[^>]*>", "|")
                                  .replaceAll("</th>", "")
                                  .replaceAll("<td[^>]*>", "|")
                                  .replaceAll("</td>", "");

        System.out.println("Simple conversion result:");
        System.out.println(result);
    }
}
