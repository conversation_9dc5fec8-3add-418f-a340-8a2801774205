package com.cb.ai.data.analysis.query.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;
import com.cb.ai.data.analysis.query.domain.vo.ElasticRespVo;
import com.cb.ai.data.analysis.query.service.ElasticQueryService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(Constants.API_ROOT_PATH + "/elastic")
@Slf4j
public class ElasticQueryController {
    private final ElasticQueryService queryService;

    public ElasticQueryController(ElasticQueryService queryService) {
        this.queryService = queryService;
    }

    /**
     * 分页查询数据
     *
     * @param query
     * @return
     * @throws Exception
     */

    @GetMapping("/page")
    public Result page(ElasticReqBo.Query query) throws Exception {
        Page<ElasticRespVo.Resp> page = queryService.page(query);
        return Result.successData(page);
    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     * @throws Exception
     */

    @GetMapping("/list")
    public Result search(ElasticReqBo.Query query) throws Exception {
        List<ElasticRespVo.Resp> list = queryService.list(query);
        return Result.successData(list);
    }

    @GetMapping("/get/{index}")
    public Result getDocument(@PathVariable("index") String index,
                              @RequestParam(value = "id",required = true) String id) throws Exception {
        if (StringUtils.isBlank(index)) {
            return Result.fail("索引不能为空");
        }
        try {
            Object document = queryService.getDocument(index, id);
            return Result.successData(document);
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }
}
