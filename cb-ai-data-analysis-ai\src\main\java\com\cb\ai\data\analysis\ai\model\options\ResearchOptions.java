package com.cb.ai.data.analysis.ai.model.options;

import lombok.Data;

/**
 * 深度分析配置
 * <AUTHOR>
 */
@Data
public class ResearchOptions {
    /**
     * 最大研究步数，推荐最大3，再大可能会爆tokens
     */
    private Integer maxPlanIterations;
    /**
     * 每轮研究生成的研究主题数量，灵活调整，3*3测试下来比较稳定，1*3大概跑10分钟
     */
    private Integer maxStepNum;
    /**
     * 初始情况不填，当发起用户反馈任务时，输入'accept'表示接受计划继续研究、输入'edit'并修改message.content可以修改计划、输入'reject'终止研究
     */
    private String interruptFeedback;
    /**
     * MCP Server配置
     */
    private String mcpSettings;
    /**
     * True表示自主探索模型（跳过用户交互）、False表示人机协作模式（用户需要反馈确认、修改或终止）
     */
    private Boolean autoAcceptedPlan;
    private Boolean debug;
}
