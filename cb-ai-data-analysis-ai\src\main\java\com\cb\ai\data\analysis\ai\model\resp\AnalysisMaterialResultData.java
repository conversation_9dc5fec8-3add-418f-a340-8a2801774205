package com.cb.ai.data.analysis.ai.model.resp;

import com.cb.ai.data.analysis.ai.model.AiResultData;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 材料分析返回
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AnalysisMaterialResultData extends AiResultData {
    /**
     * 文件ID
     */
    private String fileId;
}
