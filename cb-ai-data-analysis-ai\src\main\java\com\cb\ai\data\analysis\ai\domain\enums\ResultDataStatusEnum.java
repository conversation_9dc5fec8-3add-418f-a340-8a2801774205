package com.cb.ai.data.analysis.ai.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/10 09:30
 * @Copyright (c) 2025
 * @Description 返回的数据状态枚举类
 */
@Getter
public enum ResultDataStatusEnum {
    /**
     * 成功
     */
    SUCCESS("success"),
    /**
     * 失败
     */
    FAIR("streaming"),
    /**
     * 单节点开始
     */
    START("start"),
    /**
     * 主流程开始
     */
    FLOW_START("flowStart"),
    /**
     * 子流程开始
     */
    SUB_FLOW_START("subFlowStart"),
    /**
     * 输出中
     */
    STREAMING("streaming"),
    /**
     * 等待
     */
    WAIT("wait"),
    /**
     * 中止
     */
    STOP("stop"),
    /**
     * 单节点结束
     */
    END("end"),
    /**
     * 主流程结束
     */
    FLOW_END("flowEnd"),
    /**
     * 子流程结束
     */
    SUB_FLOW_END("subFlowEnd"),
    /**
     * 超时
     */
    TIMEOUT("timeout"),
    /**
     * 异常
     */
    ERROR("error"),
    /**
     * 未知
     */
    UNKNOWN("unknown"),

    ;

    private final String status;

    ResultDataStatusEnum(String status) {
        this.status = status;
    }

}
