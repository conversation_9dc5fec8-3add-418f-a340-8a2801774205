package com.cb.ai.data.analysis.file.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface SuperviseResourceFolderMapper extends BaseMapper<SuperviseResourceFolder> {
    /**
     * 复杂条件分页查询（XML方式）
     * @param page 分页对象
     * @param wrapper 条件构造器
     * @return 分页结果
     */
    Page<SuperviseResourceFolder> getFolderList(Page<SuperviseResourceFolder> page,
                                            @Param(Constants.WRAPPER) Wrapper<SuperviseResourceFolder> wrapper);
    List<SuperviseResourceFolder> getFolderList(@Param(Constants.WRAPPER) Wrapper<SuperviseResourceFolder> wrapper);

    /**
     * 自定义条件删除
     * @param wrapper
     * @return
     */
    Integer deleteByCustom(@Param(Constants.WRAPPER) Wrapper<SuperviseResourceFolder> wrapper);

    /**
     * 更新数据
     * @param superviseResourceFile
     * @return
     */

    /**
     * 从逻辑删除中恢复文件夹
     * @param ids
     * @return
     */
    Integer restoreFolders(@Param("ids") List<String> ids,@Param("parentId") String folderId);

}
