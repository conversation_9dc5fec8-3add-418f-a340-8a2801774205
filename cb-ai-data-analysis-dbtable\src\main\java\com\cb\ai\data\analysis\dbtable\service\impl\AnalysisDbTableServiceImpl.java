package com.cb.ai.data.analysis.dbtable.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTable;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumn;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumnRule;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableIndex;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableColumnMapper;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableColumnRuleMapper;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableIndexMapper;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableMapper;
import com.cb.ai.data.analysis.dbtable.model.clickhouse.DBColumn;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableService;
import com.cb.ai.data.analysis.dbtable.service.ClickHouseTableService;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 动态数据表 Service
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableServiceImpl extends BaseServiceImpl<AnalysisDbTableMapper, AnalysisDbTable> implements AnalysisDbTableService {
    private final AnalysisDbTableIndexMapper tableIndexMapper;
    private final AnalysisDbTableColumnMapper tableColumnMapper;
    private final AnalysisDbTableColumnRuleMapper tableColumnRuleMapper;
    private final ClickHouseTableService clickHouseTableService;

    public AnalysisDbTableServiceImpl(AnalysisDbTableIndexMapper tableIndexMapper, AnalysisDbTableColumnMapper tableColumnMapper, AnalysisDbTableColumnRuleMapper tableColumnRuleMapper, ClickHouseTableService clickHouseTableService) {
        this.tableIndexMapper = tableIndexMapper;
        this.tableColumnMapper = tableColumnMapper;
        this.tableColumnRuleMapper = tableColumnRuleMapper;
        this.clickHouseTableService = clickHouseTableService;
    }

    /**
     * 保存 表、字段、索引、校验规则
     * @param table   表
     * @param columns 字段
     * @param indexs  索引
     * @param rules   校验规则
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTable(AnalysisDbTable table, List<AnalysisDbTableColumn> columns, List<AnalysisDbTableIndex> indexs, List<AnalysisDbTableColumnRule> rules) {
        if (baseMapper.exists(Wrappers.<AnalysisDbTable>lambdaQuery().eq(AnalysisDbTable::getTableName, table.getTableName()))) {
            throw new XServerException(String.format("数据表已存在：%s", table.getTableName()));
        }
        if (StringUtils.isNotBlank(table.getParentTableName())) {
            if (table.getTableName().equals(table.getParentTableName()) || !baseMapper.exists(Wrappers.<AnalysisDbTable>lambdaQuery().eq(AnalysisDbTable::getTableName, table.getParentTableName()))) {
                throw new XServerException(String.format("父表不存在：%s", table.getParentTableName()));
            }
        }
        if (!SqlHelper.retBool(baseMapper.insert(table))) {
            throw new XServerException("表保存失败");
        }
        if (columns != null && columns.size() > 0) {
            columns.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableColumnMapper.insert(columns))) {
                throw new XServerException("表字段保存失败");
            }
        }
        if (indexs != null && indexs.size() > 0) {
            indexs.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableIndexMapper.insert(indexs))) {
                throw new XServerException("表索引保存失败");
            }
        }
        if (rules != null && rules.size() > 0) {
            rules.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableColumnRuleMapper.insert(rules))) {
                throw new XServerException("校验规则保存失败");
            }
        }
    }

    /**
     * 更新 表、字段、索引、校验规则
     * @param table   表
     * @param columns 字段
     * @param indexs  索引
     * @param rules   校验规则
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTable(AnalysisDbTable table, List<AnalysisDbTableColumn> columns, List<AnalysisDbTableIndex> indexs, List<AnalysisDbTableColumnRule> rules) {
        if (StringUtils.isNotBlank(table.getParentTableName())) {
            if (table.getTableName().equals(table.getParentTableName()) || !baseMapper.exists(Wrappers.<AnalysisDbTable>lambdaQuery().eq(AnalysisDbTable::getTableName, table.getParentTableName()))) {
                throw new XServerException(String.format("父表不存在：%s", table.getParentTableName()));
            }
        }
        if (!SqlHelper.retBool(baseMapper.updateById(table))) {
            throw new XServerException("数据表更新失败");
        }
        AnalysisDbTable dbTable = baseMapper.selectById(table.getId());
        tableColumnMapper.delete(Wrappers.<AnalysisDbTableColumn>lambdaQuery().eq(AnalysisDbTableColumn::getTableName, dbTable.getTableName()));
        if (columns != null && columns.size() > 0) {
            columns.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableColumnMapper.insertOrUpdate(columns))) {
                throw new XServerException("表字段保存失败");
            }
        }
        tableIndexMapper.delete(Wrappers.<AnalysisDbTableIndex>lambdaQuery().eq(AnalysisDbTableIndex::getTableName, dbTable.getTableName()));
        if (indexs != null && indexs.size() > 0) {
            indexs.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableIndexMapper.insertOrUpdate(indexs))) {
                throw new XServerException("表索引保存失败");
            }
        }
        tableColumnRuleMapper.delete(Wrappers.<AnalysisDbTableColumnRule>lambdaQuery().eq(AnalysisDbTableColumnRule::getTableName, dbTable.getTableName()));
        if (rules != null && rules.size() > 0) {
            rules.forEach(o -> o.setTableName(table.getTableName()));
            if (!SqlHelper.retBool(tableColumnRuleMapper.insertOrUpdate(rules))) {
                throw new XServerException("校验规则保存失败");
            }
        }
    }

    /**
     * 把表同步数据库
     * @param type    同步类型 1保留数据 2删除表，重新生成
     * @param tableId 表ID
     */
    @Override
    public void syncdbTable(Integer type, String tableId) {
        AnalysisDbTable dbTable = baseMapper.selectById(tableId);
        boolean existTableData = false;
        if ((type == null || type != 2) && clickHouseTableService.existSqlTable(dbTable.getTableName())) {
            existTableData = clickHouseTableService.existTableData(dbTable.getTableName());
            if (existTableData) {
                if (!clickHouseTableService.existSqlTable(dbTable.getTableName() + "_sync_copy") && !clickHouseTableService.renameTableName(dbTable.getTableName(), dbTable.getTableName() + "_sync_copy")) {
                    throw new XServerException("数据迁移失败");
                }
            }
        }
        List<AnalysisDbTableColumn> dbTableColumns = tableColumnMapper.selectList(Wrappers.<AnalysisDbTableColumn>lambdaQuery().eq(AnalysisDbTableColumn::getTableName, dbTable.getTableName()).orderByAsc(AnalysisDbTableColumn::getSortOn));
        if (dbTableColumns == null || dbTableColumns.size() == 0) {
            if (existTableData) {
                clickHouseTableService.removeTable(dbTable.getTableName() + "_sync_copy");
            }
            return;
        }
        clickHouseTableService.removeTable(dbTable.getTableName());
        List<DBColumn> dbColumns = new ArrayList<>();
        dbTableColumns.forEach(column -> {
            DBColumn dbColumn = new DBColumn();
            dbColumn.setName(column.getColumnName());
            dbColumn.setType(column.getColumnType());
            dbColumn.setPrecise(column.getColumnPrecise());
            dbColumn.setScale(column.getColumnScale());
            dbColumn.setDefVal(column.getColumnDefault());
            dbColumn.setComment(column.getColumnComment());
            dbColumn.setIsPrimary(column.getIsPrimary());
            dbColumns.add(dbColumn);
        });
        clickHouseTableService.createTable(dbTable.getTableName(), dbTable.getTableComment(), dbColumns);
        if (existTableData) {
            // 把数据还原回去
            List<String> columnNames1 = clickHouseTableService.listTableColumnName(dbTable.getTableName());
            List<String> columnNames2 = clickHouseTableService.listTableColumnName(dbTable.getTableName() + "_sync_copy");
            List<String> columnNames = new ArrayList<>();
            for (String name1 : columnNames1) {
                for (String name2 : columnNames2) {
                    if (name1.equals(name2)) {
                        columnNames.add(name1);
                    }
                }
            }
            clickHouseTableService.copyTableData(columnNames, dbTable.getTableName() + "_sync_copy", dbTable.getTableName());
            clickHouseTableService.removeTable(dbTable.getTableName() + "_sync_copy");
        }
    }

    @Override
    public Page<Map<String, Object>> pageTableData(Page<?> page, String tableName) {
        return clickHouseTableService.pageTableRow(page, tableName);
    }

    @Override
    public int deleteTableData(String tableName, List<String> ids) {
        return clickHouseTableService.deleteTableData(tableName, ids);
    }
}
