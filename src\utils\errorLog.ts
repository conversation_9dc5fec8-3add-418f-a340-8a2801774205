import { uuid } from '@/utils/index'
import { dayjs } from '@/core'

export interface ErrorItem {
  id?: string
  // 你想存的数据
  data: any
  // 错误信息标题
  title: string
  // 函数调用栈，一般在catch函数里面的err.stack,不知道的话可以写函数名+代码行号
  stack: string
  // 错误消息
  msg: string
  dateTime: string
}

class ErrorLog {
  static key: string = 'errorLog'

  push({ msg, stack, title, data }: ErrorItem) {
    try {
      localStorage.setItem(
        `${ErrorLog.key}${uuid()}`,
        JSON.stringify({
          msg,
          stack,
          title,
          data,
          dateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        })
      )
    } catch (error) {
      console.error('浏览器缓存数量已达上限，清空缓存')
      // this.downloadErrorLog()
      // this.clean()
    }
  }

  downloadErrorLog() {
    // 获取所有 localStorage 数据
    const allData = this.get('')
    // 转换为 JSON 字符串
    const dataStr = JSON.stringify(allData, null, 2)
    // 创建 Blob 对象
    const blob = new Blob([dataStr], { type: 'application/json' })
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'errorLog.json'
    // 触发点击事件
    document.body.appendChild(a)
    a.click()
    // 清理
    setTimeout(() => {
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }, 0)
  }

  get(index: string) {
    if (index) {
      return {
        id: index,
        ...JSON.parse(localStorage.getItem(index) || '{}')
      }
    } else {
      const keyReg = new RegExp(`^${ErrorLog.key}.+`, 'g')
      return Object.keys(localStorage)
        .filter((v) => keyReg.test(v))
        .map((v) => ({
          id: v,
          ...JSON.parse(localStorage.getItem(v) || '{}')
        }))
    }
  }

  del(key: string) {
    try {
      if (key) {
        localStorage.removeItem(key)
      } else {
        this.clean()
      }
      return true
    } catch (error) {
      throw error
    }
  }

  clean() {
    const keyReg = new RegExp(`^${ErrorLog.key}\d+`, 'g')
    const keys = Object.keys(localStorage).filter((v) => keyReg.test(v))
    keys.forEach((v) => {
      localStorage.removeItem(v)
    })
  }
}

export default new ErrorLog()
