package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.DocFormatConverter;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeChat;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeFileVo;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeChatService;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeFileService;
import com.cb.ai.data.analysis.petition.converter.QaExportRow;
import com.cb.ai.data.analysis.petition.converter.model.MergeCellSheetWriteHandler;
import com.cb.ai.data.analysis.petition.converter.model.QaSheetWriteHandler;
import com.cb.ai.data.analysis.petition.domain.FootnoteData;
import com.cb.ai.data.analysis.petition.domain.dto.QuestionDTO;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleResultService;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleService;
import com.cb.ai.data.analysis.petition.service.ProblemHandleService;
import com.cb.ai.data.analysis.petition.utils.MarkdownUtils;
import com.cb.ai.data.analysis.petition.utils.WordFootnoteUtils;
import com.cb.ai.data.analysis.utils.AIResponseProcessor;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.NumberToChinese;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/***
 * 问题批处理
 * <AUTHOR>
 */
@Service
public class ProblemHandleServiceImpl implements ProblemHandleService {

    @Resource(name = "problemFileThreadPoolExecutor")
    private ExecutorService problemFileThreadPoolExecutor;

    @Autowired
    private PetitionProblemHandleService petitionProblemHandleService;

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;


    @Autowired
    private PetitionProblemHandleResultService petitionProblemHandleResultService;


    @Autowired
    private KnowledgeChatService knowledgeChatService;

    @Autowired
    private KnowledgeFileService knowledgeFileService;


    @Autowired
    private AiConfigService aiConfigService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveProblemHandleFile(List<PetitionProblemHandleEntity> problemHandleFileList) throws Exception {
        try{
            if(CollectionUtils.isEmpty(problemHandleFileList)){
                throw new XServiceException("请求信息为空！");
            }
            for(PetitionProblemHandleEntity file:problemHandleFileList){
                file.setId(IdUtil.getSnowflakeNextIdStr());
                file.setHandleStatus(0);
            }
            for(PetitionProblemHandleEntity entity:problemHandleFileList){
                entity.setBaseId(String.join(",", entity.getBaseIds()));
            }
            petitionProblemHandleService.saveBatch(problemHandleFileList);
            AiConfig aiConfig=aiConfigService.getAiConfig();
            analysisProblemFile(problemHandleFileList,aiConfig);
            return 1;
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public void reAnalyze(String id) throws Exception {
        try{
            if(StringUtils.isBlank(id)){
                throw new XServiceException("请选择要解析的记录！");
            }
            PetitionProblemHandleEntity entity=petitionProblemHandleService.getById(id);
            if(ObjectUtils.isEmpty(entity)){
                throw new XServiceException("获取问题信息失败！");
            }
            entity.setId(id);
            entity.setHandleStatus(1);
            entity.setFinishedQaCount(0);
            petitionProblemHandleService.updateById(entity);
            AiConfig aiConfig=aiConfigService.getAiConfig();
            analysisProblemFile(entity,aiConfig);
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

    private void analysisProblemFile(PetitionProblemHandleEntity entity,AiConfig aiConfig){
        problemFileThreadPoolExecutor.submit(() -> {
            Integer fileReadStatus=entity.getFileReadStatus();
            try{
                Integer successCount=0;
                List<PetitionProblemHandleResultEntity> handleList=new ArrayList<>();
                //如果之前文件读取成功了，就不重复读取，直接获取数据就行
                if(!ObjectUtils.isEmpty(entity.getFileReadStatus())&&entity.getFileReadStatus().equals(1)){
                    UpdateWrapper<PetitionProblemHandleResultEntity> editWrapper = new UpdateWrapper<>();
                    editWrapper.eq("problem_id",entity.getId());
                    editWrapper.set("reasoning_content",null);
                    editWrapper.set("content",null);
                    petitionProblemHandleResultService.update(editWrapper);
                    LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(PetitionProblemHandleResultEntity::getProblemId, entity.getId());  // 精确匹配
                    handleList=petitionProblemHandleResultService.list(queryWrapper);
                }else{
                    List<QuestionDTO> questionList= EasyExcel.read(superviseResourceFileService.getInputStream(entity.getFilePath()))
                            .head(QuestionDTO.class)
                            .headRowNumber(2)
                            .sheet()
                            .doReadSync();
                    fileReadStatus=CollectionUtils.isEmpty(questionList)?0:1;
                    for(QuestionDTO question:questionList){
                        PetitionProblemHandleResultEntity resultEntity=getResultEntity(entity.getId(),null,null,question);
                        resultEntity.setStatus(0);
                        handleList.add(resultEntity);
                    }
                    petitionProblemHandleResultService.saveBatch(handleList);
                }
                for(PetitionProblemHandleResultEntity resultEntity:handleList){
                    try{
                        KnowledgeChat knowledgeChat=new KnowledgeChat();
                        BeanUtils.copyProperties(aiConfig,knowledgeChat);
                        knowledgeChat.setMaxTokens(aiConfig.getMaxTokens()*0.8);
                        knowledgeChat.setStream(Boolean.FALSE);
                        knowledgeChat.setPromote(resultEntity.getProblemName());
                        knowledgeChat.setBaseIds(Arrays.asList(entity.getBaseId().split(",")));
                        Result result=knowledgeChatService.chatKnowledge(knowledgeChat);
                        AIResponseProcessor aiResponseProcessor=new AIResponseProcessor();
                        successCount=successCount+1;
                        if(result.getCode() == ResultCode.SUCCESS.getCode()){
                            aiResponseProcessor.processChunk(result.getData());
                        }
                        PetitionProblemHandleResultEntity resultEntity2=new PetitionProblemHandleResultEntity();
                        resultEntity2.setId(resultEntity.getId());
                        resultEntity2.setStatus(result.getCode());
                        resultEntity2.setReasoningContent(aiResponseProcessor.getReasoningContent());
                        resultEntity2.setContent(aiResponseProcessor.getContent());
                        resultEntity2.setFileListJson(JSON.toJSONString(aiResponseProcessor.getFileList()));
                        petitionProblemHandleResultService.updateById(resultEntity2);
                        PetitionProblemHandleEntity entity2=new PetitionProblemHandleEntity();
                        entity2.setId(entity.getId());
                        entity2.setFinishedQaCount(successCount);
                        petitionProblemHandleService.updateById(entity2);
                    }catch (Exception e){
                        e.printStackTrace();
                        error(resultEntity,entity.getId());
                    }
                }
                updatePetitionProblemHandleStatus(entity.getId(),2,fileReadStatus,handleList.size(),successCount);
                // 如果之前文件读取成功了，就不重复读取，直接问答就行
//                if(!ObjectUtils.isEmpty(entity.getFileReadStatus())&&entity.getFileReadStatus().equals(1)){
//                    UpdateWrapper<PetitionProblemHandleResultEntity> editWrapper = new UpdateWrapper<>();
//                    editWrapper.eq("problem_id",entity.getId());
//                    editWrapper.set("reasoning_content",null);
//                    editWrapper.set("content",null);
//                    petitionProblemHandleResultService.update(editWrapper);
//                    LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new LambdaQueryWrapper<>();
//                    queryWrapper.eq(PetitionProblemHandleResultEntity::getProblemId, entity.getId());  // 精确匹配
//                    List<PetitionProblemHandleResultEntity> list=petitionProblemHandleResultService.list(queryWrapper);
//                    fileReadStatus=CollectionUtils.isEmpty(list)?0:1;
//                    for(PetitionProblemHandleResultEntity resultEntity:list){
//                        try{
//                            KnowledgeChat knowledgeChat=new KnowledgeChat();
//                            BeanUtils.copyProperties(aiConfig,knowledgeChat);
//                            knowledgeChat.setMaxTokens(aiConfig.getMaxTokens()*0.8);
//                            knowledgeChat.setStream(Boolean.FALSE);
//                            knowledgeChat.setPromote(resultEntity.getProblemName());
//                            knowledgeChat.setBaseIds(Arrays.asList(entity.getBaseId().split(",")));
//                            Result result=knowledgeChatService.chatKnowledge(knowledgeChat);
//                            AIResponseProcessor aiResponseProcessor=new AIResponseProcessor();
//                            successCount=successCount+1;
//                            if(result.getCode() == ResultCode.SUCCESS.getCode()){
//                                aiResponseProcessor.processChunk((String)result.getData());
//                            }
//                            PetitionProblemHandleResultEntity resultEntity2=new PetitionProblemHandleResultEntity();
//                            resultEntity2.setId(resultEntity.getId());
//                            resultEntity2.setStatus(result.getCode());
//                            resultEntity2.setReasoningContent(aiResponseProcessor.getReasoningContent());
//                            resultEntity2.setContent(aiResponseProcessor.getContent());
//                            resultEntity2.setFileListJson(JSON.toJSONString(aiResponseProcessor.getFileList()));
//                            petitionProblemHandleResultService.updateById(resultEntity2);
//                            PetitionProblemHandleEntity entity2=new PetitionProblemHandleEntity();
//                            entity2.setId(entity.getId());
//                            entity2.setFinishedQaCount(successCount);
//                            petitionProblemHandleService.updateById(entity2);
//                        }catch (Exception e){
//                            e.printStackTrace();
//                            error(resultEntity,entity.getId());
//                        }
//                    }
//                    updatePetitionProblemHandleStatus(entity.getId(),2,fileReadStatus,list.size(),successCount);
//                }else{
//                    List<QuestionDTO> questionList= EasyExcel.read(superviseResourceFileService.getInputStream(entity.getFilePath()))
//                            .head(QuestionDTO.class)
//                            .headRowNumber(2)
//                            .sheet()
//                            .doReadSync();
//                    fileReadStatus=CollectionUtils.isEmpty(questionList)?0:1;
//                    List<PetitionProblemHandleResultEntity> resultEntityList=new ArrayList<>();
//                    for(QuestionDTO question:questionList){
//                        PetitionProblemHandleResultEntity resultEntity=getResultEntity(entity.getId(),null,null,question);
//                        resultEntity.setStatus(0);
//                        resultEntityList.add(resultEntity);
//                    }
//                    petitionProblemHandleResultService.saveBatch(resultEntityList);
//                    for(PetitionProblemHandleResultEntity question:resultEntityList){
//                        try{
//                            KnowledgeChat knowledgeChat=new KnowledgeChat();
//                            BeanUtils.copyProperties(aiConfig,knowledgeChat);
//                            knowledgeChat.setMaxTokens(aiConfig.getMaxTokens()*0.8);
//                            knowledgeChat.setStream(Boolean.FALSE);
//                            knowledgeChat.setPromote(question.getProblemName());
//                            knowledgeChat.setBaseIds(Arrays.asList(entity.getBaseId().split(",")));
//                            Result result=knowledgeChatService.chatKnowledge(knowledgeChat);
//                            AIResponseProcessor aiResponseProcessor=new AIResponseProcessor();
//                            successCount=successCount+1;
//                            if(result.getCode() == ResultCode.SUCCESS.getCode()){
//                                String[] lines = result.getData().toString().split("\\r?\\n"); // 按行分割
//                                for (String line : lines) {
//                                    aiResponseProcessor.processChunk(line);
//                                }
//                            }
//                            question.setReasoningContent(aiResponseProcessor.getReasoningContent());
//                            question.setContent(aiResponseProcessor.getContent());
//                            question.setStatus(result.getCode());
//                            question.setFileListJson(JSON.toJSONString(aiResponseProcessor.getFileList()));
//                            petitionProblemHandleResultService.updateById(question);
//                            // 这里因为说想实时看到进度，所以循环更新，每次解析完成后成功数量+1，页面动态刷新
//                            PetitionProblemHandleEntity entity2=new PetitionProblemHandleEntity();
//                            entity2.setId(entity.getId());
//                            entity2.setFinishedQaCount(successCount);
//                            petitionProblemHandleService.updateById(entity2);
//                        }catch (Exception e){
//                            e.printStackTrace();
//                            error(question,entity.getId());
//                        }
//                    }
//                    updatePetitionProblemHandleStatus(entity.getId(),2,fileReadStatus,questionList.size(),successCount);
//                }
            }catch (Exception e){
                e.printStackTrace();
                updatePetitionProblemHandleStatus(entity.getId(),-1,fileReadStatus,0,0);
            }
        });
    }



    private void analysisProblemFile(List<PetitionProblemHandleEntity> problemHandleFileList,AiConfig aiConfig){
        problemFileThreadPoolExecutor.submit(() -> {
            for(PetitionProblemHandleEntity problemFile:problemHandleFileList){
                Integer fileReadStatus=0;
                try{
                    List<QuestionDTO> readQuestionList= EasyExcel.read(superviseResourceFileService.getInputStream(problemFile.getFilePath()))
                            .head(QuestionDTO.class)
                            .headRowNumber(2)
                            .sheet()
                            .doReadSync();
                    Integer successCount=0;
                    List<QuestionDTO> questionList = readQuestionList.stream().filter(q -> StringUtils.isNotBlank(q.getQuestion())).collect(Collectors.toList());
                    fileReadStatus=CollectionUtils.isEmpty(questionList)?0:1;
                    updatePetitionProblemHandleStatus(problemFile.getId(),1,fileReadStatus,questionList.size(),0);
                    List<PetitionProblemHandleResultEntity> resultEntityList=new ArrayList<>();
                    for(QuestionDTO question:questionList){
                        PetitionProblemHandleResultEntity resultEntity=getResultEntity(problemFile.getId(),null,null,question);
                        resultEntity.setStatus(0);
                        resultEntityList.add(resultEntity);
                    }
                    petitionProblemHandleResultService.saveBatch(resultEntityList);
                    for(PetitionProblemHandleResultEntity resultEntity:resultEntityList){
                        try{
                            KnowledgeChat knowledgeChat=new KnowledgeChat();
                            BeanUtils.copyProperties(aiConfig,knowledgeChat);
                            knowledgeChat.setMaxTokens(aiConfig.getMaxTokens()*0.8);
                            knowledgeChat.setStream(Boolean.FALSE);
                            knowledgeChat.setPromote(resultEntity.getProblemName());
                            knowledgeChat.setBaseIds(Arrays.asList(problemFile.getBaseIds()));
                            Result result=knowledgeChatService.chatKnowledge(knowledgeChat);
                            AIResponseProcessor aiResponseProcessor=new AIResponseProcessor();
                            successCount=successCount+1;
                            if(result.getCode() == ResultCode.SUCCESS.getCode()){
                                aiResponseProcessor.processChunk(result.getData());
                            }
                            resultEntity.setReasoningContent(aiResponseProcessor.getReasoningContent());
                            resultEntity.setContent(aiResponseProcessor.getContent());
                            resultEntity.setStatus(result.getCode());
                            resultEntity.setFileListJson(JSON.toJSONString(aiResponseProcessor.getFileList()));
                            petitionProblemHandleResultService.updateById(resultEntity);
                            PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
                            entity.setId(problemFile.getId());
                            entity.setFinishedQaCount(successCount);
                            petitionProblemHandleService.updateById(entity);
                        }catch (Exception e){
                            e.printStackTrace();
                            error(resultEntity,problemFile.getId());
                        }
                    }
                    updatePetitionProblemHandleStatus(problemFile.getId(),2,fileReadStatus,questionList.size(),successCount);
                }catch (Exception e){
                    e.printStackTrace();
                    updatePetitionProblemHandleStatus(problemFile.getId(),-1,fileReadStatus,0,0);
                }
            }
        });
    }

    private PetitionProblemHandleResultEntity getResultEntity(String problemId,String reasoningContent,String content,QuestionDTO question){
        PetitionProblemHandleResultEntity entity=new PetitionProblemHandleResultEntity();
        entity.setId(IdUtil.getSnowflakeNextIdStr());
        entity.setProblemId(problemId);
        entity.setProblemSerial(question.getId());
        entity.setProblemName(question.getQuestion());
        entity.setReasoningContent(reasoningContent);
        entity.setContent(content);
        return entity;
    }
    private void updatePetitionProblemHandleStatus(String id,Integer status,Integer fileReadStatus,Integer qaCount,Integer finishedQaCount){
        PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
        entity.setId(id);
        entity.setHandleStatus(status);
        entity.setFileReadStatus(fileReadStatus);
        if(!ObjectUtils.isEmpty(qaCount) && qaCount>0){
            entity.setQaCount(qaCount);
        }
        if(!ObjectUtils.isEmpty(finishedQaCount) && finishedQaCount>0){
            entity.setFinishedQaCount(finishedQaCount);
        }
        petitionProblemHandleService.updateById(entity);
    }



    @Override
    public void exportZip(List<String> ids, HttpServletResponse response) {
        try {
            response.setContentType("application/zip");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=export.zip");
            LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new QueryWrapper<PetitionProblemHandleResultEntity>().lambda();
            queryWrapper.in(PetitionProblemHandleResultEntity::getId,ids);
            List<PetitionProblemHandleResultEntity> qaList = petitionProblemHandleResultService.list(queryWrapper);
            if (qaList.isEmpty()) {
                throw new XServiceException("暂无问答数据！");
            }
            ByteArrayOutputStream excelOut = new ByteArrayOutputStream();
            try {
                List<QaExportRow> exportList = getExportExceData(qaList);
                EasyExcel.write(excelOut, QaExportRow.class)
                        .autoCloseStream(Boolean.TRUE)
                        .registerWriteHandler(new QaSheetWriteHandler())
                        .registerWriteHandler(new MergeCellSheetWriteHandler(0, 1)) // 大表头从第0行开始合并
                        .sheet("问题清单")
                        .doWrite(exportList);
            } catch (Exception e) {
                e.printStackTrace();
            }
            ByteArrayOutputStream wordOut = new ByteArrayOutputStream();

            // 2. 加载模板
           /* InputStream templateStream=this.getClass().getClassLoader().getResourceAsStream("templates/knowledge_batch_template.docx");
            List<Map<String, Object>> renderList = this.getExportWordData(qaList);
            // 4. 渲染内容
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(
                    new HashMap<String, Object>() {{
                        put("datas", renderList);
                    }}
            );
            // 排版
            DocFormatConverter.formatDocument(template.getXWPFDocument(), new DocConfig());*/
            String html=this.getExportWordHtml(qaList);
            Document doc = new Document();
            // 获取所有脚注信息
            Map<String,FootnoteData> footnotes = WordFootnoteUtils.extractFootnotes(html);
            String cleanHtml = WordFootnoteUtils.removeDataAttributes(html);
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.insertHtml(cleanHtml);
            WordFootnoteUtils.processSharedFootnotes(doc, footnotes);
            XWPFDocument template= convertToXWPFDocument(doc);
            DocConfig config=new DocConfig();
            config.setFontName("仿宋_GB2312");
            DocFormatConverter.formatMarkdown(template,config);

            template.write(wordOut);
            template.close();

            Set<Map<String,String>> fileSet = new HashSet<>();
            for(PetitionProblemHandleResultEntity resultEntity:qaList){
                JSONArray jsonArray=JSONArray.parseArray(resultEntity.getFileListJson());
                if(!ObjectUtils.isEmpty(jsonArray)){
                    for(int i=0;i<jsonArray.size();i++){
                        Map<String,String> map=new HashMap<>();
                        JSONObject jsonObject=jsonArray.getJSONObject(i);
                        map.put("fileId",jsonObject.getString("fileId"));
                        map.put("fileName",jsonObject.getString("fileName"));
                        fileSet.add(map);
                    }
                }
            }
            String dowFileName=getFileName(qaList,"问题清单");
            ZipOutputStream zos = null;
            try {
                zos = new ZipOutputStream(response.getOutputStream(), StandardCharsets.UTF_8);
                addToZip(zos, wordOut.toByteArray(), dowFileName+".docx");
                addToZip(zos, excelOut.toByteArray(), dowFileName+".xlsx");
                try{
                    Iterator<Map<String,String>> it = fileSet.iterator();
                    while(it.hasNext()) {
                        Map<String,String> fileMap = it.next();
                        String fileId=MapUtils.getString(fileMap,"fileId","");
                        String fileName=MapUtils.getString(fileMap,"fileName","");
                        if(StringUtils.isNotBlank(fileId)){
                            KnowledgeFileVo knowledgeFile=knowledgeFileService.getKnowledgeFileById(fileId);
                            if(!ObjectUtils.isEmpty(knowledgeFile)){
                                InputStream inputStream=superviseResourceFileService.getInputStream(knowledgeFile.getFileUrl());
                                if(!ObjectUtils.isEmpty(inputStream)){
                                    addToZip(zos, IOUtils.toByteArray(inputStream), "参考文献/"+fileName);
                                }
                            }
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }finally {
                zos.close();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private String getFileName(List<PetitionProblemHandleResultEntity> qaList,String defaultVal){
        try{
            String problemId=qaList.get(0).getProblemId();
            PetitionProblemHandleEntity entity=petitionProblemHandleService.getById(problemId);
            if(ObjectUtils.isEmpty(entity)){
                return defaultVal;
            }
            if(StringUtils.isBlank(entity.getFileName())){
                return defaultVal;
            }
            String fileName=entity.getFileName();
            int dotIndex = fileName.lastIndexOf('.');
            return (dotIndex < 0 ) ? fileName : fileName.substring(0, dotIndex);
        }catch (Exception e){
            return defaultVal;
        }
    }

    private static void addToZip(ZipOutputStream zos, byte[] data, String entryName)throws IOException {
        ZipEntry entry = new ZipEntry(entryName);
        zos.putNextEntry(entry);
        zos.write(data);
        zos.closeEntry();
    }

    public static XWPFDocument convertToXWPFDocument(Document asposeDoc) throws Exception {
        // 创建内存输出流
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 将Aspose文档保存为DOCX格式到内存流
            asposeDoc.save(outputStream, SaveFormat.DOCX);

            // 将内存流转换为输入流
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray())) {
                // 使用POI加载DOCX流，创建XWPFDocument
                return new XWPFDocument(inputStream);
            }
        }
    }


    private List<QaExportRow> getExportExceData(List<PetitionProblemHandleResultEntity> qaList){
        List<QaExportRow> exportList = new ArrayList<>();
        for (int i = 0; i < qaList.size(); i++) {
            StringBuffer sb=new StringBuffer();
            PetitionProblemHandleResultEntity detail = qaList.get(i);
            QaExportRow dto = new QaExportRow();
            dto.setIndex(i + 1);
            dto.setQuestion(detail.getProblemName());

            Map<String,String> fileMap=new HashMap<>();
            if(StringUtils.isNotEmpty(detail.getFileListJson())){
                JSONArray jsonArray= JSON.parseArray(detail.getFileListJson());
                if(!ObjectUtils.isEmpty(jsonArray)&&jsonArray.size()>0){
                    Map<String, String> subMap = new HashMap<>();
                    for(int j=0; j<jsonArray.size(); j++){
                        JSONObject obj = jsonArray.getJSONObject(j);
                        String fileName=obj.getString("fileName");
                        if(ObjectUtils.isEmpty(subMap.get(fileName))){
                            subMap.put(fileName,fileName);
                            sb.append(subMap.get(fileName)).append("\n");
                        }
                        String key="index"+(obj.containsKey("index")?obj.getString("index"):"");
                        fileMap.put(key,"“"+fileName+"”");
                    }
                }
            }

            String answer = StringUtils.isNotBlank(detail.getContent())?detail.getContent():"没有找到相关内容。";
            answer=replaceByPattern(answer,fileMap);
            if(StringUtils.isNotEmpty(answer)){
                answer = MarkdownUtils.convertHeaders(answer);
            }
            dto.setAnswer(contentFormat(answer));
            dto.setFileName(sb.toString());
            exportList.add(dto);
        }
        return exportList;
    }

    private List<Map<String, Object>> getExportWordData(List<PetitionProblemHandleResultEntity> qaList){
        String[] chineseNums = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十","十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"};
        List<Map<String, Object>> renderList = new ArrayList<>();
        for (int i = 0; i < qaList.size(); i++) {
            PetitionProblemHandleResultEntity item = qaList.get(i);
            Map<String, Object> map = new HashMap<>();
            map.put("question", Optional.ofNullable(item.getProblemName()).orElse(""));
            String answer = item.getContent();
            if(com.xong.boot.common.utils.StringUtils.isNotEmpty(answer)){
                answer = answer.replaceAll("(?<=\\d\\.)(\\s+)", "");
            }
            answer=MarkdownUtils.convertHeaders(formatAnswerWithLineBreaks(answer));
            StringBuffer sb=new StringBuffer();
            if(com.xong.boot.common.utils.StringUtils.isNotEmpty(item.getFileListJson())){
                JSONArray jsonArray= JSON.parseArray(item.getFileListJson());
                if(!ObjectUtils.isEmpty(jsonArray)&&jsonArray.size()>0){
                    sb.append("\n\n参考文献：\n");
                    Map<String, String> subMap = new HashMap<>();
                    for(int j=0; j<jsonArray.size(); j++){
                        JSONObject obj = jsonArray.getJSONObject(j);
                        String fileName=obj.getString("fileName");
                        if(ObjectUtils.isEmpty(subMap.get(fileName))){
                            subMap.put(fileName,fileName);
                            sb.append(subMap.get(fileName)).append("\n");
                        }
                    }
                }
            }
            map.put("answer", answer+sb);
            if (i > chineseNums.length - 1) {
                map.put("chineseIndex", NumberChineseFormatter.format(i + 1, false));
            } else {
                map.put("chineseIndex", chineseNums[i]);     // 中文编号：一、二、三……
            }
            renderList.add(map);
        }
        return renderList;
    }


    private String getExportWordHtml(List<PetitionProblemHandleResultEntity> qaList){
        StringBuffer sbHtml=new StringBuffer();
        Map<String,Integer> iMap=WordFootnoteUtils.getIndexMap();
        for (int i = 0; i < qaList.size(); i++) {
            PetitionProblemHandleResultEntity item = qaList.get(i);
            StringBuffer title=new StringBuffer();
            title.append("### ").append(NumberToChinese.numberToChinese(i + 1)).append("、").append(item.getProblemName()).append("\n");

            Map<String, String> subMap = new HashMap<>();
            Map<String,String> fileMap=new HashMap<>();
            if(StringUtils.isNotEmpty(item.getFileListJson())) {
                JSONArray jsonArray = JSON.parseArray(item.getFileListJson());
                if (!ObjectUtils.isEmpty(jsonArray) && jsonArray.size() > 0) {
                    for(int j=0; j<jsonArray.size(); j++){
                        JSONObject obj = jsonArray.getJSONObject(j);
                        String fileName=obj.getString("fileName");
                        subMap.put(obj.getString("index"),fileName);

                        String key="index"+(obj.containsKey("index")?obj.getString("index"):"");
                        fileMap.put(key,"“"+fileName+"”");
                    }
                }
            }


            String content= StringUtils.isNotBlank(item.getContent())?item.getContent():"没有找到相关内容。";
            content=replaceByPattern(content,fileMap);
            String htmlContent = MarkdownUtils.convert(title+content);
            String html= WordFootnoteUtils.convertFootnotes(htmlContent,subMap,iMap);
            sbHtml.append(html).append("\n\n");
        }
        return sbHtml.toString();
    }

    private void error(PetitionProblemHandleResultEntity resultEntity,String problemId){
        try {
            resultEntity.setStatus(500);
            resultEntity.setContent("问题解析失败！");
            petitionProblemHandleResultService.updateById(resultEntity);
            /*PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
            entity.setId(problemId);
            entity.setHandleStatus(-1);
            petitionProblemHandleService.updateById(entity);*/
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    private String formatAnswerWithLineBreaks(String answer) {
        if (answer == null) return "";

        // 给 1. 到 99. 前加换行符（前面不是数字、不是换行，避免干扰 11.、21. 这类）
        String formatted = answer.replaceAll("(?<!\\d)(?<!\\n)(\\b[1-9][0-9]?\\.)\\s*", "\n$1");

        // 可选：给 - 前也加换行
        formatted = formatted.replaceAll("(?<!\\n)-\\s+", "\n-");

        formatted=formatted.replaceAll("\\[\\^\\d+\\]", "");

        return formatted.trim();
    }

    private String contentFormat(String input) {
        if(org.apache.commons.lang3.StringUtils.isNotBlank(input)){
            return input.replaceAll("\\[\\^\\d+\\]", "");
        }else{
            return "";
        }
    }

    public static String replaceByPattern(String input, Map<String, String> replacementMap) {
        try{
            // 构建正则模式：匹配3个大写字母
            Pattern pattern = Pattern.compile("((index|索引)\\s+\\d+)");
            Matcher matcher = pattern.matcher(input);
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                String key = matcher.group(1).replace("索引","index");
                String k=key.replaceAll("\\s+", "");
                // 如果Map中存在该key则替换，否则保留原内容
                matcher.appendReplacement(sb,replacementMap.getOrDefault(k, "参考文本"));
            }
            matcher.appendTail(sb);
            return sb.toString();
        }catch (Exception e){
            return input;
        }
    }
}
