package com.cb.ai.data.analysis.petition.controller;

import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.service.ProblemHandleService;
import com.xong.boot.common.exception.XServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xong.boot.common.api.Result;

import java.util.List;

@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/problem")
public class ProblemHandleController {


    @Autowired
    private ProblemHandleService problemHandleService;


    /**
     * 问题批处理文件信息提交
     * @return
     */
    @PostMapping("/file")
    public Result saveProblemHandleFile(@RequestBody List<PetitionProblemHandleEntity> problemHandleFileList){
        try{
            problemHandleService.saveProblemHandleFile(problemHandleFileList);
            //String[] ids = problemHandleFileList.stream().map(PetitionProblemHandleEntity::getId).toArray(String[]::new);
            return Result.success("文件上传成功！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库删除失败！");
        }
    }

    @GetMapping("/reAnalyze/{id}")
    public Result reAnalyze(@PathVariable String id){
        try{
            problemHandleService.reAnalyze(id);
            return Result.success("已重新发起解析任务！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("重新解析失败！");
        }
    }


    @PostMapping("/export/zip")
    public void export(@RequestBody List<String> ids, HttpServletResponse response){
        try{
            problemHandleService.exportZip(ids,response);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
