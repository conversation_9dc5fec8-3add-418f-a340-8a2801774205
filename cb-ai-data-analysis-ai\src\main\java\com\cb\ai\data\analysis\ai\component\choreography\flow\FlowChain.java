package com.cb.ai.data.analysis.ai.component.choreography.flow;

import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.component.choreography.engine.INodeAttributeOperation;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeMapping;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import com.cb.ai.data.analysis.ai.utils.RefUtil;
import com.xong.boot.common.exception.CustomException;
import lombok.Getter;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:33
 * @Copyright (c) 2025
 * @Description 流程执行链：流程执行链是流程节点的集合，用于实现复杂的业务逻辑
 */
@Getter
public class FlowChain {
    /**
     * 流程节点集合
     */
    private final List<IFlowNode> nodes;

    /**
     * 流程执行链服务
     */
    private final FlowChainService flowChainService;

    /**
     * 是否为子流程
     */
    private final boolean isSubFlow;

    /**
     * 请求Id
     */
    private final String requestId;


    private FlowChain(String requestId, FlowChainService flowChainService) {
        this(requestId, flowChainService, false);
    }

    private FlowChain(String requestId, FlowChainService flowChainService, boolean isSubFlow) {
        this.nodes = new ArrayList<>(16);
        this.requestId = requestId;
        this.flowChainService = flowChainService;
        this.isSubFlow = isSubFlow;
    }

    /**
     * 新建默认流程执行链
     * @param flowContext 流程上下文
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:34
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> FlowChain newChain(String requestId) {
        return newChain(requestId, SpringUtil.getBean(FlowChainService.class), false);
    }

    /**
     * 新建默认子流程执行链
     * @param flowContext 流程上下文
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:34
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> FlowChain newSubChain(String requestId) {
        return newChain(requestId, SpringUtil.getBean(FlowChainService.class), true);
    }

    /**
     * 新建默认流程执行链
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:34
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> FlowChain newChain(String requestId, FlowChainService flowChainService, boolean isSubFlow) {
        return new FlowChain(requestId, flowChainService, isSubFlow);
    }

    /**
     * 执行上下文
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public Flux<?> execute() {
        return execute(null);
    }

    /**
     * 执行上下文
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public Flux<?> execute(Object requestContext) {
        FlowAttribute flowAttribute = new FlowAttribute();
        FlowContext flowContext = new FlowContext(this.nodes.size());
        flowAttribute.setFlowContext(flowContext);
        flowAttribute.setInFlowChain(true);
        return execute(requestContext, flowAttribute);
    }

    /**
     * 执行上下文
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public Flux<ResultData<?>> execute(Object requestContext, FlowAttribute flowAttribute) {
        return flowChainService.execute(this, requestContext, flowAttribute);
    }

    /**
     * 添加串行节点
     * @param clazz 节点Class
     * @param consumer 节点设置
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addSerialNode(Class<IFlowProcessNode<I, O>> clazz) {
        return addSerialNode(clazz, null);
    }

    /**
     * 添加串行节点
     * @param clazz 节点Class
     * @param consumer 节点设置
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addSerialNode(Class<N> clazz, Consumer<INodeAttributeOperation<I, O>> consumer) {
        return addSerialNode(clazz, null, consumer);
    }

    /**
     * 添加串行节点
     * @param clazz 节点Class
     * @param context 节点上下文
     * @param consumer 节点设置
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addSerialNode(Class<N> clazz, I context, Consumer<INodeAttributeOperation<I, O>> consumer) {
        try {
            IFlowProcessNode<I, O> flowNode = RefUtil.invokeConstructor(clazz);
            if (consumer != null && flowNode instanceof INodeAttributeOperation) {
                consumer.accept((INodeAttributeOperation<I, O>) flowNode);
            }
            return addSerialNode(flowNode, context);
        } catch (Throwable e) {
            throw new CustomException("实例化" + clazz.getSimpleName() + "失败， 原因：" + e.getMessage(), e);
        }
    }

    /**
     * 添加串行节点
     * @param nodeObj 节点对象
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addSerialNode(N nodeObj) {
        return addSerialNode(nodeObj, null);
    }

    /**
     * 添加串行节点
     * @param nodeObj 节点对象
     * @param context 节点上下文
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addSerialNode(N nodeObj, I context) {
        nodes.add((IFlowProcessNode<I, O>) nodeObj);
        return this;
    }

    /**
     * 增加执行链并行节点
     * @param clazz 增加并行节点Class
     * @param consumer 增加并行节点设置
     * @return ParallelFlowNode 并行流程执行节点
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz) {
        return addParallelNode(clazz, 10);
    }

    /**
     * 增加执行链并行节点
     * @param clazz 增加并行节点Class
     * @param parallelNum 并行数量
     * @return ParallelFlowNode 并行流程执行节点
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz, Integer parallelNum) {
        return addParallelNode(clazz, null, parallelNum);
    }

    /**
     * 增加执行链并行节点
     * @param clazz 增加并行节点Class
     * @param consumer 增加并行节点设置
     * @return ParallelFlowNode 并行流程执行节点
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz, Consumer<INodeAttributeOperation<I, O>> consumer) {
        return addParallelNode(clazz, consumer, 10);
    }

    /**
     * 增加执行链并行节点
     * @param clazz 增加并行节点Class
     * @param consumer 增加并行节点设置
     * @param parallelNum 并行数量
     * @return ParallelFlowNode 并行流程执行节点
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz, Consumer<INodeAttributeOperation<I, O>> consumer, Integer parallelNum) {
        return addParallelNode(clazz, null, consumer, parallelNum);
    }

    /**
     * 增加并行节点
     * @param clazz 并行节点Class
     * @param context 并行节点上下文
     * @param consumer 并行节点设置
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz, I context, Consumer<INodeAttributeOperation<I, O>> consumer) {
        return addParallelNode(clazz, context, consumer, 10);
    }

    /**
     * 增加并行节点
     * @param clazz 并行节点Class
     * @param context 并行节点上下文
     * @param consumer 并行节点设置
     * @param parallelNum 并行数量
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(Class<N> clazz, I context, Consumer<INodeAttributeOperation<I, O>> consumer, Integer parallelNum) {
        try {
            IFlowProcessNode<I, O> flowNode = RefUtil.invokeConstructor(clazz);
            if (consumer != null && flowNode instanceof INodeAttributeOperation) {
                consumer.accept((INodeAttributeOperation<I, O>) flowNode);
            }
            return addParallelNode(flowNode, context, parallelNum);
        } catch (Throwable e) {
            throw new CustomException("实例化" + clazz.getSimpleName() + "失败， 原因：" + e.getMessage(), e);
        }
    }

    /**
     * 增加并行节点
     * @param node 并行节点
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(N node) {
        return addParallelNode(node, 10);
    }

    /**
     * 增加并行节点
     * @param node 并行节点
     * @param parallelNum 并行数量
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(N node, Integer parallelNum) {
        return addParallelNode(node, null, parallelNum);
    }

    /**
     * 增加执行链节点
     * @param node 增加并行节点
     * @context 并行节点上下文
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(N node, I context) {
        return addParallelNode(node, context, 10);
    }

    /**
     * 增加执行链节点
     * @param node 增加并行节点
     * @context 并行节点上下文
     * @parallelNum 并行数量
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addParallelNode(N node, I context, Integer parallelNum) {
        ParallelFlowNode<I> parallelFlowNode = OptionalUtil.ofNullable(getLastParallelNode()).orElseGet(() -> {
            ParallelFlowNode<I> parallelNode = new ParallelFlowNode<I>();
            this.nodes.add(parallelNode);
            return parallelNode;
        });
        if (parallelFlowNode.getNodes().size() == Optional.ofNullable(parallelNum).orElse(10)) {
            ParallelFlowNode<I> newParallelFlowNode = new ParallelFlowNode<I>();
            newParallelFlowNode.addNode(node);
            this.nodes.add(newParallelFlowNode);
        } else {
            parallelFlowNode.addNode(node);
        }
        return this;
    }

    /**
     * 增加执行节点
     * @param node 增加并行节点
     * @context 并行节点上下文
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addProcessNode(String nodeName, Flux<?> flux) {
        nodes.add(new ProcessFlowNode(flux, nodeName));
        return this;
    }

    /**
     * 增加执行节点
     * @param node 增加并行节点
     * @context 并行节点上下文
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addProcessNode(String nodeName, BiFunction<FlowContext, NodeContext, Flux<?>> fluxFun) {
        nodes.add(new ProcessFlowNode(fluxFun, nodeName));
        return this;
    }

    /**
     * 增加迭代流程
     * @param node 增加并行节点
     * @context 并行节点上下文
     * @return FlowChain 执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends IFlowProcessNode<I, O>> FlowChain addIterateNode(BiConsumer<FlowContext, FlowChain> iterateFunc) {
        //nodes.add(new IterateFlowNode(iterateFunc));
        return this;
    }

    /**
     * @param clazz           节点的Class
     * @param subContextClass 子执行链的上下文Class
     * @param subNodes        子执行链的节点Class集合
     * @return FlowChain<T> 流程执行链
     * @description 增加子执行链的节点
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends SubFlowNode, F extends IFlowProcessNode<I, O>> FlowChain addSerialNodeWithSubNodes(Class<N> clazz, List<NodeMapping<I, O, F>> subNodes) {
        try {
            SubFlowNode bean = RefUtil.invokeConstructor(clazz);
            FlowChain subChain = newChain(IdUtil.fastSimpleUUID());
            for (NodeMapping<I, O, F> subNode : subNodes) {
                subChain.addSerialNode(subNode.nodeClass(), subNode.nodeConsumer());
            }
            bean.setSubChain(subChain);
            nodes.add((IFlowProcessNode<I, O>) bean);
            return this;
        } catch (Throwable e) {
            throw new CustomException("实例化" + clazz.getSimpleName() + "失败， 原因：" + e.getMessage(), e);
        }
    }

    /**
     * @description 增加并行子执行链的节点
     * @param clazz           节点的Class
     * @param subNodes        子执行链的节点Class集合
     * @return FlowChain<T> 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends SubFlowNode, F extends IFlowProcessNode<I, O>> FlowChain addParallelNodeWithSubNodes(Class<N> clazz, List<NodeMapping<I, O, F>> subNodes) {
        try {
            SubFlowNode bean = RefUtil.invokeConstructor(clazz);
            FlowChain subChain = newChain(IdUtil.fastSimpleUUID());
            for (NodeMapping<I, O, F> subNode : subNodes) {
                subChain.addParallelNode(subNode.nodeClass(), subNode.nodeConsumer());
            }
            bean.setSubChain(subChain);
            nodes.add((IFlowProcessNode<I, O>) bean);
            return this;
        } catch (Throwable e) {
            throw new CustomException("实例化" + clazz.getSimpleName() + "失败， 原因：" + e.getMessage(), e);
        }
    }

    /**
     * @description 增加子节点的子执行链
     * @param clazz    子节点的Class
     * @param subChain 子执行链
     * @return FlowChain 流程执行链
     * @createtime 2025/7/1 下午3:37
     * <AUTHOR>
     * @version 1.0
     */
    public <I, O, N extends SubFlowNode> FlowChain addNodeWithSubNodes(Class<N> clazz, FlowChain subChain) {
        try {
            SubFlowNode bean = RefUtil.invokeConstructor(clazz);
            bean.setSubChain(subChain);
            nodes.add((IFlowProcessNode<I, O>) bean);
            return this;
        } catch (Throwable e) {
            throw new CustomException("实例化" + clazz.getSimpleName() + "失败， 原因：" + e.getMessage(), e);
        }
    }

    private ParallelFlowNode getLastParallelNode() {
        if (nodes.size() > 0) {
            IFlowNode node = nodes.get(nodes.size() - 1);
            if (node instanceof ParallelFlowNode parallelNode) {
                return parallelNode;
            }
        }
        return null;
    }


}
