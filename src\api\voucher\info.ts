import { http } from '@/utils/http'

const prefix = '/api/voucher/info'

/**
 * 分页获取凭证信息列表
 * @param params 查询条件
 */
export function page(params?: Record<string, any>) {
  return http.get(`${prefix}/page`, { params })
}

/**
 * 获取凭证信息详情
 * @param id
 */
export function detail(id: string) {
  return http.get(`${prefix}/detail/${id}`)
}

/**
 * 获取所有标签
 */
export function getAllTags(){
  return http.get(`${prefix}/getAllTags`)
}


/**
 * 分页获取当前用户的凭证信息列表
 * @param params 查询条件
 */
export function pageBySelf(params?: Record<string, any>) {
  return http.get(`${prefix}/pageBySelf`, { params })
}

/**
 * 上传凭证文件
 * @param data 文件数据和标签
 */
export function upload(data: FormData) {
  return http.post(`${prefix}/upload`, data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 重新OCR解析凭证
 * @param id 凭证ID
 */
export function reOcr(id: string) {
  return http.post(`${prefix}/reOcr/${id}`)
}

/**
 * 批量添加标签
 * @param data 批量标签请求数据
 */
export function batchAddTag(data: {
  infoIds: string[]
  tags: string[]
}) {
  return http.post(`${prefix}/batchAddTag`, data)
}


/**
 * 批量添加标签
 * @param data 批量标签请求数据
 */
export function batchDelTag(data: {
  infoIds: string[]
  tags: string[]
}) {
  return http.post(`${prefix}/batchDelTag`, data)
}

/**
 * 单个凭证进行分析
 * @param id 凭证ID
 */
export function analysisById(id: string) {
  return http.post(`${prefix}/analysisById/${id}`)
}
