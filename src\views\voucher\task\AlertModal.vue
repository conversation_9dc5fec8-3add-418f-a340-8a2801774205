<template>
  <a-modal v-model:open="visible" :title="`任务告警记录 - ${taskName}`" width="80%" :footer="null" @cancel="onCancel">
    <div class="alert-modal-content">
      <x-table-search :model="searchFormData" :tableRef="tableRef" :col="3">
        <a-form-item label="凭证名称" name="voucherName">
          <a-input v-model:value="searchFormData.voucherName" :maxlength="100" allow-clear placeholder="请输入凭证名称" />
        </a-form-item>
        <a-form-item label="告警内容" name="content">
          <a-input v-model:value="searchFormData.content" :maxlength="200" allow-clear placeholder="请输入告警内容关键词" />
        </a-form-item>
      </x-table-search>

      <x-table ref="tableRef" :columns="columns" :loadData="loadData" :rowSelection="false" row-key="id"
        :pagination="{ pageSize: 10 }">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'content'">
            <div class="content-cell">
              <a-tooltip :title="text" placement="topLeft">
                <div class="content-text">{{ text }}</div>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'createTime'">
            {{ $date.formatDateTime(text) }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-button type="link" size="small" @click="onClickViewDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </x-table>
    </div>

    <!-- 详情弹窗 -->
    <a-modal v-model:open="detailVisible" title="告警记录详情" width="60%" :footer="null">
      <div v-if="detailData" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="凭证名称">
            {{ detailData.voucherName }}
          </a-descriptions-item>
          <a-descriptions-item label="任务名称">
            {{ detailData.taskName }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ detailData.createBy }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ $date.formatDateTime(detailData.createTime) }}
          </a-descriptions-item>
        </a-descriptions>

        <div class="alert-content-section">
          <h4>告警内容：</h4>
          <div class="alert-content">
            {{ detailData.content }}
          </div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts" name="TaskAlertModal">
import { computed, getCurrentInstance, ref, watch, type ComponentCustomProperties } from 'vue'
import { alertRecord } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean
  taskId?: string
  taskName?: string
}>()

const tableRef = ref()
const detailVisible = ref(false)
const detailData = ref(null)

const searchFormData = ref({
  voucherName: '',
  content: ''
})

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 重置搜索条件
      searchFormData.value = {
        voucherName: '',
        content: ''
      }
      // 刷新表格
      setTimeout(() => {
        tableRef.value?.refresh()
      }, 100)
    }
  }
)

const columns = computed(() => [
  {
    dataIndex: 'voucherName',
    title: '凭证名称',
    width: 200,
    ellipsis: true
  },
  {
    dataIndex: 'content',
    title: '告警内容',
    width: 300,
    ellipsis: true
  },
  {
    dataIndex: 'createBy',
    title: '创建人',
    width: 100,
    align: 'center'
  },
  {
    dataIndex: 'createTime',
    title: '创建时间',
    width: 150,
    align: 'center'
  },
  {
    key: 'actions',
    title: '操作',
    width: 100,
    align: 'center'
  }
])

function onCancel() {
  emits('update:visible', false)
}

/**
 * 加载数据
 */
async function loadData(params: Record<string, any>) {
  if (!props.taskId) return { data: { records: [], total: 0 } }

  const queryParams = {
    ...params,
    ...searchFormData.value
  }

  const res = await alertRecord.pageByTaskId(props.taskId, queryParams)
  return res
}

/**
 * 查看告警详情
 */
async function onClickViewDetail(record: any) {
  try {
    const { data } = await alertRecord.detail(record.id)
    detailData.value = data
    detailVisible.value = true
  } catch (error) {
    _this.$message.error('获取详情失败')
  }
}
</script>

<style scoped lang="less">
.alert-modal-content {
  .content-cell {
    .content-text {
      max-width: 280px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .detail-content {
    .alert-content-section {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #333;
      }

      .alert-content {
        background: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #ff4d4f;
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
      }
    }
  }
}
</style>
