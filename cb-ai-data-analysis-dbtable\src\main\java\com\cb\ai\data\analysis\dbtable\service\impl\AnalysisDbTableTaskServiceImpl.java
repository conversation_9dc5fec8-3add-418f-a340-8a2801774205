package com.cb.ai.data.analysis.dbtable.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableTaskService;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableTaskMapper;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 数据导入工作Service业务层处理
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableTaskServiceImpl extends BaseServiceImpl<AnalysisDbTableTaskMapper,AnalysisDbTableTask> implements AnalysisDbTableTaskService {

    /**
     * 查询数据导入工作列表
     * @param bigdataPoolJob 数据导入工作
     * @return 数据导入工作
     */
    @Override
    public Page<AnalysisDbTableTask> pageTableTaskList(AnalysisDbTableTask bigdataPoolJob) {
        return baseMapper.pageTableTaskList(QueryHelper.getPage(true),bigdataPoolJob);
    }


    /**
     * 批量删除数据导入工作
     * @param ids 需要删除的数据导入工作主键
     * @return 结果
     */
    @Override
    public int deleteTableTaskByIds(String[] ids) {
        if(baseMapper.existJobLog(ids)) {
            throw new IllegalArgumentException("工作组中存在日志详情，不能删除");
        }
        return baseMapper.deleteByIds(Arrays.asList(ids));
    }

}
