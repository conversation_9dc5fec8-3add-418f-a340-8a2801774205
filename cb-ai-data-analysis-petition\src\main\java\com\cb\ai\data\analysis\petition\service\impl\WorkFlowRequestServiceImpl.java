package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import com.cb.ai.data.analysis.petition.config.BiShengCommonConfigProperty;
import com.cb.ai.data.analysis.petition.context.WorkFlowContextHolder;
import com.cb.ai.data.analysis.petition.domain.dto.QaDto;
import com.cb.ai.data.analysis.petition.domain.vo.request.WorkFlowRequest;
import com.cb.ai.data.analysis.petition.domain.vo.request.WorkFlowResponse;
import com.cb.ai.data.analysis.petition.domain.vo.response.WorkFlowBaseParamVo;
import com.cb.ai.data.analysis.petition.redis.RedisCache;
import com.cb.ai.data.analysis.petition.service.OkHttpSseClient;
import com.cb.ai.data.analysis.petition.service.WorkFlowRequestService;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;


@Service
@Log4j2
public class WorkFlowRequestServiceImpl implements WorkFlowRequestService {

    public final static Map<Long, JSONObject> objectMap = new ConcurrentHashMap<>();

    @Resource
    private BiShengCommonConfigProperty biShengCommonConfigProperty;

    /*@Resource
    private WorkFlowProperties workFlowProperties;*/

    @Resource
    private WebClient.Builder webClientBuilder;
    @Resource
    private RedisCache redisCache;
    public WorkFlowBaseParamVo buildRequestParam(String workFlowId, Boolean stream) {
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setWorkflow_id(workFlowId);
        workFlowRequest.setStream(stream);

        String response = webClientBuilder.build()
                .post()
                .uri(biShengCommonConfigProperty.getBaseUrl() + "/api/v2/workflow/invoke")
                .bodyValue(workFlowRequest) // 直接传递 JSON body
                .retrieve()
                .bodyToMono(String.class)
                .block();  // 变为同步调用

        WorkFlowResponse workFlowResponse = JSON.parseObject(response, WorkFlowResponse.class);

        String sessionId = "";
        String inputNodeId = "";
        String messageId = "";
        String inputValueKey = "user_input";
        if (workFlowResponse.getStatusCode() != 200) {
            log.error("调用流程异常：", workFlowResponse.getStatusMessage());
        } else {
            WorkFlowResponse.DataContent dataContent = workFlowResponse.getData();
            sessionId = dataContent.getSessionId();
            if (dataContent == null) {
                log.error("dataContent is null");
            } else {
                List<WorkFlowResponse.Event> events = dataContent.getEvents();

                if (CollectionUtils.isEmpty(events)) {
                    log.error("events is null");
                } else {
                    Optional<WorkFlowResponse.Event> inputEventOptional = events.stream().filter(event -> "input".equals(event.getEvent())).findAny();

                    if (!inputEventOptional.isPresent()) {
                        log.error("event下未找到input节点");
                    } else {
                        WorkFlowResponse.Event inputEvent = inputEventOptional.get();

                        inputNodeId = inputEvent.getNodeId();
                        messageId = inputEvent.getMessageId();
                        WorkFlowResponse.InputSchema inputSchema = inputEvent.getInputSchema();
                        if (inputSchema != null) {
                            Optional<WorkFlowResponse.InputValue> inputValueOptional = inputSchema.getValue().stream().filter(
                                    event -> "text".equals(event.getType())).findAny();
                            if(inputValueOptional.isPresent()){
                                inputValueKey = inputValueOptional.get().getKey();
                            }
                        }
                    }
                }
            }
        }


        if (StringUtils.isNotEmpty(inputNodeId) && StringUtils.isNotEmpty(sessionId) && StringUtils.isNotEmpty(messageId)) {
            WorkFlowBaseParamVo workFlowBaseParamVo = new WorkFlowBaseParamVo();
            workFlowBaseParamVo.setSessionId(sessionId);
            workFlowBaseParamVo.setInputNodeId(inputNodeId);
            workFlowBaseParamVo.setMessageId(messageId);
            workFlowBaseParamVo.setInputValueKey(inputValueKey);
            return workFlowBaseParamVo;
        }
        return null;
    }

    @Override
    public QaDto singleRequest(QaDto qaDto, String workFlowId, WorkFlowBaseParamVo param) {
        Long startTime = System.currentTimeMillis();

        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setWorkflow_id(workFlowId);
        workFlowRequest.setSession_id(param.getSessionId());
        workFlowRequest.setStream(false);

//        WorkFlowResponse workFlowResponse = request(qaDto.getQuestion() +  "\n 需要进一步明确的事项：" + qaDto.getRemark(), param.getInputNodeId(), workFlowRequest);
        WorkFlowResponse workFlowResponse = request(qaDto.getQuestion() + "\n返回的内容一级分类按1、2、3，二级分类按(一)、(二)、(三)。 不要输出markdown相关的格式符号" ,
                param.getInputNodeId(), param.getInputValueKey(), workFlowRequest);

        if (workFlowResponse.getStatusCode() != 200) {
            err(workFlowResponse);
        } else {
            String result = extractFinalResult(workFlowResponse, startTime);
            qaDto.setAnswer(result);

            return qaDto;
        }
        return null;
    }

    @Override
    public String singleRequest(Object data, String workFlowId) {
        Long startTime = System.currentTimeMillis();

        WorkFlowBaseParamVo param = buildRequestParam(workFlowId, false);

        if (param != null) {
            WorkFlowRequest workFlowRequest = new WorkFlowRequest();
            workFlowRequest.setWorkflow_id(workFlowId);
            workFlowRequest.setSession_id(param.getSessionId());
            workFlowRequest.setMessage_id(param.getMessageId());
            workFlowRequest.setStream(false);

            WorkFlowResponse workFlowResponse = request(data, param.getInputNodeId(), param.getInputValueKey(), workFlowRequest);

            if (workFlowResponse.getStatusCode() != 200) {
                err(workFlowResponse);
            } else {
                closeConversation(workFlowId, workFlowRequest.getSession_id());
                String result = extractFinalResult(workFlowResponse, startTime);

                return result;
            }
        }
        return null;
    }

    @Override
    public String singleRequest(Object data, String promote, String workFlowId) {
        Long startTime = System.currentTimeMillis();

        WorkFlowBaseParamVo param = buildRequestParam(workFlowId, false);

        if (param != null) {
            WorkFlowRequest workFlowRequest = new WorkFlowRequest();
            workFlowRequest.setWorkflow_id(workFlowId);
            workFlowRequest.setSession_id(param.getSessionId());
            workFlowRequest.setMessage_id(param.getMessageId());
            workFlowRequest.setStream(false);

            WorkFlowResponse workFlowResponse;
            if (StringUtils.isNotEmpty(promote)) {
                workFlowResponse = request(data, param.getInputNodeId(), workFlowRequest, promote);
            } else {
                workFlowResponse = request(data, param.getInputNodeId(), param.getInputValueKey(), workFlowRequest);
            }

            if (workFlowResponse.getStatusCode() != 200) {
                err(workFlowResponse);
            } else {
                closeConversation(workFlowId, workFlowRequest.getSession_id());
                String result = extractFinalResult(workFlowResponse, startTime);

                return result;
            }
        }
        return null;
    }

    @Override
    public String multipleRequest(List data, String workFlowId) {
        Long startTime = System.currentTimeMillis();

        WorkFlowBaseParamVo param = buildRequestParam(workFlowId, false);

        if (param != null) {
            WorkFlowRequest workFlowRequest = new WorkFlowRequest();
            workFlowRequest.setWorkflow_id(workFlowId);
            workFlowRequest.setSession_id(param.getSessionId());
            workFlowRequest.setMessage_id(param.getMessageId());
            workFlowRequest.setStream(false);

            Integer totalLength = data.size();
            Integer maxLength = 10;

            Boolean end = false;
            for (int i = 0; i < totalLength; i += maxLength) {
                List subList;
                if (i + maxLength >= totalLength) {
                    subList = data.subList(i, totalLength);
                    end = true;
                } else {
                    subList = data.subList(i, i + maxLength);
                }

                WorkFlowResponse workFlowResponse = request(subList, param.getInputNodeId(), param.getInputValueKey(), workFlowRequest);

                if (workFlowResponse.getStatusCode() != 200) {
                    err(workFlowResponse);
                } else {
                    if (end) {
                        closeConversation(workFlowId, workFlowRequest.getSession_id());
                        return extractFinalResult(workFlowResponse, startTime);
                    } else {
                        List<WorkFlowResponse.Event> events = workFlowResponse.getData().getEvents();
                        Optional<WorkFlowResponse.Event> outputEvent = events.stream().filter(event -> "input".equals(event.getEvent())).findAny();
                        String messageId = outputEvent.get().getMessageId();
                        workFlowRequest.setMessage_id(messageId);
                    }
                }
            }
        }
        throw new RuntimeException("获取开启会话参数失败：" + param.toString());
    }

    private WorkFlowResponse request(Object data, String inputNodeId, WorkFlowRequest workFlowRequest, String promote) {
        JSONObject input = new JSONObject();
        JSONObject input_6df8b = new JSONObject();
        input_6df8b.put("data", data);
        input_6df8b.put("custom_promote", promote);
        input.put(inputNodeId, input_6df8b);
        workFlowRequest.setInput(input);
        Long businessId = WorkFlowContextHolder.getBusinessId();

        List<Long> delList = new ArrayList<>();
        for (Map.Entry<Long, JSONObject> entry : objectMap.entrySet()) {
            long time = entry.getValue().getLongValue("time");
            if (System.currentTimeMillis() - time > 60 * 60 * 1000) {
                delList.add(entry.getKey());
            }
        }
        if (delList.size() > 0) {
            for (Long id : delList) {
                objectMap.remove(id);
            }
        }
        JSONObject mJsonObject = new JSONObject();
        mJsonObject.put("id", businessId);
        mJsonObject.put("status", "process");
        mJsonObject.put("think", "");
        mJsonObject.put("time", System.currentTimeMillis());
//

        WorkFlowRequest workFlowRequest1 = new WorkFlowRequest();
        BeanUtils.copyProperties(workFlowRequest,workFlowRequest1);
        workFlowRequest1.setStream(true);
        OkHttpSseClient sseClient = new OkHttpSseClient();
        CompletableFuture<String> future = new CompletableFuture<>();
        // 创建监听器
        OkHttpSseClient.SseListener listener = new OkHttpSseClient.SseListener() {
            @Override
            public void onOpen() {
                System.out.println("成功连接到SSE服务器");
            }

            @Override
            public void onEvent(String event, String data, String id) {
                System.out.printf("事件[%s] ID[%s]: %s%n", event, id, data);
                JSONObject parse = JSONObject.parse(data);
                Object data1 = parse.get("data");
                if (null!=data1){
                    JSONObject parse1 = JSONObject.parse(data1.toString());
                    Object outputSchema = parse1.get("output_schema");
                    if (null!=outputSchema){
                        JSONObject parse2 = JSONObject.parse(outputSchema.toString());
                        Object reasoning_content = parse2.get("reasoning_content");
                        String str = reasoning_content==null?"":reasoning_content.toString();
                        Map<String,String> cacheObject = redisCache.getCacheObject(businessId.toString());
                        cacheObject=cacheObject==null?new HashMap<String,String>():cacheObject;
//                        HashMap<String, String> stringStringHashMap = new HashMap<>();
//                        stringStringHashMap.put("status","process");
//                        stringStringHashMap.put("think",cacheObject.get("think")==null?"":cacheObject.get("think").toString()+str);
//                        redisCache.setCacheObject(businessId.toString(),stringStringHashMap,60*60, TimeUnit.SECONDS);
                        mJsonObject.put("think", mJsonObject.getString("think") + reasoning_content);
                        objectMap.put(businessId, mJsonObject);
                    }


                    String eventStr = parse1.getString("event");
                    String status = parse1.getString("status");
                    if (StrUtil.isNotBlank(eventStr) && eventStr.equals("output_msg") && StrUtil.isNotBlank(status) && status.equals("end")) {
                        JSONObject outputSchemaResult = parse1.getJSONObject("output_schema");
                        if (ObjectUtil.isNotNull(outputSchemaResult)) {
                            String message = outputSchemaResult.getString("message");
                            if (StrUtil.isNotBlank(message)) {
                                String jsonString = "{\n" +
                                        "  \"status_code\": 200,\n" +
                                        "  \"status_message\": \"SUCCESS\",\n" +
                                        "  \"data\": {\n" +
                                        "    \"session_id\": \"14a366d9dfd44f95bb628dc6eae927a7_async_task_id\",\n" +
                                        "    \"events\": [\n" +
                                        "      {\n" +
                                        "        \"event\": \"output_msg\",\n" +
                                        "        \"message_id\": \"25027\",\n" +
                                        "        \"status\": \"end\",\n" +
                                        "        \"node_id\": \"output_3c334\",\n" +
                                        "        \"node_execution_id\": \"276c3d030e8a4f52938473ab12fe349e\",\n" +
                                        "        \"output_schema\": {\n" +
                                        "          \"message\": \"{\\\"机构组织企业政府部门信息\\\":[],\\\"人员信息\\\":[],\\\"人员关系信息\\\":[]}\",\n" +
                                        "          \"reasoning_content\": null,\n" +
                                        "          \"output_key\": \"\",\n" +
                                        "          \"files\": [],\n" +
                                        "          \"source_url\": null,\n" +
                                        "          \"extra\": null\n" +
                                        "        },\n" +
                                        "        \"input_schema\": null\n" +
                                        "      },\n" +
                                        "      {\n" +
                                        "        \"event\": \"close\",\n" +
                                        "        \"message_id\": null,\n" +
                                        "        \"status\": \"end\",\n" +
                                        "        \"node_id\": null,\n" +
                                        "        \"node_execution_id\": null,\n" +
                                        "        \"output_schema\": null,\n" +
                                        "        \"input_schema\": null\n" +
                                        "      }\n" +
                                        "    ]\n" +
                                        "  }\n" +
                                        "}";

                                // 将字符串解析为 JSONObject
                                JSONObject jsonObject = JSONObject.parseObject(jsonString);

                                // 获取嵌套的 JSON 字段
                                JSONObject outputSchema1 = jsonObject.getJSONObject("data")
                                        .getJSONArray("events")
                                        .getJSONObject(0)
                                        .getJSONObject("output_schema");
                                outputSchema1.put("message", message);
                                future.complete(jsonObject.toJSONString());
                            }
                        } else {

                        }
                    }
                }

            }

            @Override
            public void onClosed() {
                System.out.println("SSE连接已关闭");
                mJsonObject.put("status", "finish");
                objectMap.put(businessId, mJsonObject);
//                Map<String,String> cacheObject = redisCache.getCacheObject(businessId.toString());
//                if (null != cacheObject) {
//                    cacheObject.put("status","finish");
//                    redisCache.setCacheObject(businessId.toString(),cacheObject,60*60, TimeUnit.SECONDS);
//                } else {
//                    cacheObject = new HashMap<>();
//                    cacheObject.put("status", "finish");
//                    redisCache.setCacheObject(businessId.toString(), cacheObject, 60 * 60, TimeUnit.SECONDS);
//                }
                future.complete(null);
            }

            @Override
            public void onFailure(Throwable t) {
                System.err.println("连接错误: " + t.getMessage());
                // 可以在这里实现重连逻辑
//                Map<String,String> cacheObject = redisCache.getCacheObject(businessId.toString());
//                if (null != cacheObject) {
//                    cacheObject.put("status","finish");
//                    redisCache.setCacheObject(businessId.toString(),cacheObject,60*60, TimeUnit.SECONDS);
//                } else {
//                    cacheObject = new HashMap<>();
//                    cacheObject.put("status", "finish");
//                    cacheObject.put("think", "分析错误，请检查网络连接！");
//                    redisCache.setCacheObject(businessId.toString(), cacheObject, 60 * 60, TimeUnit.SECONDS);
//                }
                mJsonObject.put("status", "finish");
                objectMap.put(businessId, mJsonObject);
                future.complete(null);
            }
        };
        // 连接到SSE服务器
//        String jsonString = JSON.toJSONString(workFlowRequest1);

        sseClient.connectWithPost(biShengCommonConfigProperty.getBaseUrl() + "/api/v2/workflow/invoke", new HashMap<>(),
                JSON.toJSONString(workFlowRequest1),listener);

//        String response =webClientBuilder.build()
//                .post()
//                .uri(biShengCommonConfigProperty.getBaseUrl() + "/api/v2/workflow/invoke")
//                .bodyValue(workFlowRequest)
//                .retrieve()
//                .bodyToMono(String.class)
//                .timeout(Duration.ofHours(3)) //最长等待时间
//                .block();

        String join = future.join();

        String response = join;

        WorkFlowResponse workFlowResponse = JSON.parseObject(response, WorkFlowResponse.class);
        return workFlowResponse;
    }


    private WorkFlowResponse request(Object data, String inputNodeId, String inputValueKey, WorkFlowRequest workFlowRequest) {
        JSONObject input = new JSONObject();
        JSONObject input_6df8b = new JSONObject();
        input_6df8b.put(inputValueKey, data);
        input.put(inputNodeId, input_6df8b);
        workFlowRequest.setInput(input);

        String response = webClientBuilder.build()
                .post()
                .uri(biShengCommonConfigProperty.getBaseUrl() + "/api/v2/workflow/invoke")
                .bodyValue(workFlowRequest)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofHours(3)) //最长等待时间
                .block();

        WorkFlowResponse workFlowResponse = JSON.parseObject(response, WorkFlowResponse.class);
        return workFlowResponse;
    }

    private void err(WorkFlowResponse workFlowResponse) {
        log.error("解析异常:{}", workFlowResponse.getStatusMessage());
        throw new RuntimeException(workFlowResponse.getStatusMessage());
    }

    private String extractFinalResult(WorkFlowResponse workFlowResponse, Long startTime) {
        List<WorkFlowResponse.Event> events = workFlowResponse.getData().getEvents();
        Optional<WorkFlowResponse.Event> outputEvent = events.stream().filter(event -> "output_msg".equals(event.getEvent())).findAny();
        if (outputEvent.isPresent()) {
            WorkFlowResponse.Event event = outputEvent.get();
            WorkFlowResponse.OutputSchema outputSchema = event.getOutputSchema();
            String message = outputSchema.getMessage();
            if (StringUtils.isNotEmpty(message)) {
                message = message.replace("```json", "").replace("```", "");
                Long endTime = System.currentTimeMillis();
                log.info("耗时:{}ms", endTime - startTime);
                log.debug("======================  模型响应  ======================");
                log.debug(message);
                log.debug("=======================================================");
                return message;
            } else {
                throw new RuntimeException("outputSchema下message为空");
            }
        } else {
            throw new RuntimeException("未找到output_msg节点");
        }
    }


    /**
     * 结束对话
     */
    private void closeConversation(String workFlowId, String sessionId) {
        WorkFlowRequest workFlowRequest = new WorkFlowRequest();
        workFlowRequest.setWorkflow_id(workFlowId);
        workFlowRequest.setSession_id(sessionId);

        String response = webClientBuilder.build()
                .post()
                .uri(biShengCommonConfigProperty.getBaseUrl() + "/api/v2/workflow/stop")
                .bodyValue(workFlowRequest)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofHours(3)) //最长等待时间
                .block();

        JSONObject closeResponse = JSON.parseObject(response);

        if (!"200".equals(closeResponse.getString("status_code"))) {
            log.error("关闭对话失败：{}", closeResponse.getString("status_code"));
        }

    }
}
