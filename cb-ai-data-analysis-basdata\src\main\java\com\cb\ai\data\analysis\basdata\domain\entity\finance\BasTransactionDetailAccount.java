package com.cb.ai.data.analysis.basdata.domain.entity.finance;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易明细账表(BasTransactionDetailAccount)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-14 16:32:33
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class BasTransactionDetailAccount extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //交易明细表主键id
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //部门id
    @ExcelIgnore
    private String deptId;

    //区域编码
    @ExcelIgnore
    private String areaCode;

    //科目代码
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "科目代码")
    private String accountsCode;

    //科目名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "科目名称")
    private String accountsName;

    //日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "日期")
    private LocalDateTime transactionTime;

    //业务日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "业务日期")
    private LocalDateTime businessTime;

    //凭证字号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "凭证字号")
    private String voucherNumber;

    //摘要
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "摘要")
    private String abstractInfo;

    //业务编号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "业务编号")
    private String businessNumber;

    //结算方式
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "结算方式")
    private String paymentsWay;

    //结算号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "结算号")
    private String paymentsNumber;

    //对方科目
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方科目")
    private String oppositeAccounts;

    //关联凭证字号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "关联凭证字号")
    private String joinVoucherNumber;

    //借方金额
    @ExcelProperty(value = "借方金额")
    private BigDecimal debit;

    //贷方金额
    @ExcelProperty(value = "贷方金额")
    private BigDecimal creditMount;

    //方向
    @ExcelProperty(value = "方向")
    private String direction;

    //余额
    @ExcelProperty(value = "余额")
    private BigDecimal balance;

    //参考信息
    @ExcelProperty(value = "参考信息")
    private String referenceInfo;

}

