package com.cb.ai.data.analysis.basdata.service.finance.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasBankTransactionInfo;
import com.cb.ai.data.analysis.basdata.mapper.finance.BasBankTransactionInfoMapper;
import com.cb.ai.data.analysis.basdata.repository.finance.BasBankTransactionInfoRepository;
import com.cb.ai.data.analysis.basdata.repository.finance.esBo.BasBankTransactionInfoBo;
import com.cb.ai.data.analysis.basdata.service.finance.BasBankTransactionInfoService;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 银行交易信息表(BasBankTransactionInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:57:31
 */
@Service("basBankTransactionInfoService")
public class BasBankTransactionInfoServiceImpl extends ServiceImpl<BasBankTransactionInfoMapper, BasBankTransactionInfo> implements BasBankTransactionInfoService {

    @Autowired
    private BasBankTransactionInfoRepository repository;

    @Override
    public boolean save(BasBankTransactionInfo basBankTransactionInfo) {
        int insert = baseMapper.insert(basBankTransactionInfo);
        if (insert > 0) {
            BasBankTransactionInfoBo bo = convert2Bo(basBankTransactionInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateById(BasBankTransactionInfo basBankTransactionInfo) {
        int updated = baseMapper.updateById(basBankTransactionInfo);
        if (updated > 0) {
            BasBankTransactionInfoBo bo = convert2Bo(basBankTransactionInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<BasBankTransactionInfo> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            List<BasBankTransactionInfoBo> collect = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }

    private BasBankTransactionInfoBo convert2Bo(BasBankTransactionInfo item) {
        BasBankTransactionInfoBo bo = new BasBankTransactionInfoBo();
        BeanUtils.copyProperties(item, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }
}

