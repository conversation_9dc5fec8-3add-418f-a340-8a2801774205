package com.cb.ai.data.analysis.graph.service.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.graph.domain.vo.RelationGraphVo;
import com.cb.ai.data.analysis.graph.enums.Neo4jFulltextIndexEnum;
import com.cb.ai.data.analysis.graph.service.business.RelationGraphService;
import com.cb.ai.data.analysis.graph.utils.Neo4jUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class RelationGraphServiceImpl implements RelationGraphService, InitializingBean {

    @Autowired
    private Neo4jUtil neo4jUtil;

    @Override
    public RelationGraphVo.QueryResp query(String cypher, Map<String, Object> params) {
        return neo4jUtil.query(cypher, params);
    }

    @Override
    public long execute(String cypher, Map<String, Object> params) {
        return neo4jUtil.execute(cypher, params);
    }

    @Override
    public String createNode(String label, Map<String, Object> node, String extraLabel) {
        return neo4jUtil.createNode(label, node, extraLabel);
    }

    @Override
    public String createNode(String label, Map<String, Object> node, String extraLabel, String mergeCondition) {
        return neo4jUtil.createNode(label, node, extraLabel, mergeCondition);
    }

    @Override
    public String createNodeMatchOrCreate(String label, Map<String, Object> node, String extraLabel, String[] mergeCondition) {
        return neo4jUtil.createNodeMatchOrCreate(label, node, extraLabel, mergeCondition);
    }

    @Override
    public String createNodeMatchByPropScoreOrCreate(String label, Map<String, Object> node, String extraLabel, String[] matchPropAndScore, String passScore) {
        List<RelationGraphVo.ScoreNodeMatchItem> matchNodeList = getPropScoreMatchNodeList(label, node, matchPropAndScore, passScore);
        if (matchNodeList.size() > 0) {
            String nodeId = matchNodeList.get(0).getNodeId();
            String[] mergeCondition = new String[]{"{id: '" + nodeId + "' }"};
            return createNodeMatchOrCreate(label, node, extraLabel, mergeCondition);
        } else {
            return createNode(label, node, extraLabel);
        }
    }

    @Override
    public List<RelationGraphVo.ScoreNodeMatchItem> getPropScoreMatchNodeList(String label, Map<String, Object> node, String[] matchPropAndScore, String passScore) {
        return neo4jUtil.getPropScoreMatchNodeList(label, node, matchPropAndScore, passScore);
    }

    @Override
    public List<String> batchCreateNodes(String label, List<Map<String, Object>> nodes, String mergeCondition) {
        return neo4jUtil.batchCreateNodes(label, nodes, mergeCondition);
    }

    @Override
    public List<String> batchCreateNodesMatchOrCreate(String label, List<Map<String, Object>> nodes, String mergeCondition) {
        return neo4jUtil.batchCreateNodesMatchOrCreate(label, nodes, mergeCondition);
    }

    @Override
    public long deleteNodesByProp(String label, Map<String, Object> prop) {
        return neo4jUtil.deleteNodesByProp(label, prop);
    }

    @Override
    public void batchCreateRelationships(RelationGraphVo.BatchAddRelationshipReq req) {
        neo4jUtil.batchCreateRelationships(
                req.getStartNodeLabel(), req.getEndNodeLabel(),
                req.getMatchCondition(), req.getRelationType(),
                req.getNodeMatchList(), req.isAccurate()
        );
    }

    @Override
    public long batchDeleteRelationships(RelationGraphVo.BatchDeleteRelationshipReq req) {
        return neo4jUtil.batchDeleteRelationships(
                req.getStartNodeLabel(), req.getEndNodeLabel(),
                req.getMatchCondition(), req.getRelationType(),
                req.getNodeMatchList()
        );
    }

    @Override
    public RelationGraphVo.QueryResp createRelationByPropId(String startPropId, String endPropId, String relationLabel, Map<String, Object> relationProps) {
        return neo4jUtil.createRelationByPropId(startPropId, endPropId, relationLabel, relationProps);
    }

    @Override
    public void createOrUpdateRelation(
            String[] nodeLabels,
            String relationCondition,
            String relationPattern,
            String onCreateSet,
            String onMatchSet
    ) {
        neo4jUtil.createOrUpdateRelation(nodeLabels, relationCondition, relationPattern, onCreateSet, onMatchSet);
    }

    @Override
    public void createFullTextIndex(String indexName, String label, String[] props, boolean force) {
        neo4jUtil.createFullTextIndex(indexName, label, props, force);
    }

    @Override
    public void cleanAll() {
        String cypher = "MATCH (n) DETACH DELETE n";
        neo4jUtil.execute(cypher, null);
    }

    @Override
    public RelationGraphVo.QueryResp search(String name, int layers) {
        return neo4jUtil.searchByNameAndLayers(name, layers);
    }

    @Override
    public List<Map<String, Object>> nameMatch(String name) {
        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }
        String cypher = "MATCH (n:企业) " +
                "WHERE n.name =~ '.*" + name + ".*' " +
                "RETURN n " +
                "LIMIT 10";
        RelationGraphVo.QueryResp resp = neo4jUtil.query(cypher, null);
        List<Map<String, Object>> nodes = resp.getNodes();
        return nodes;
    }

    @Override
    public Map<String, List<String>> baseAttributes() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> saveNode(RelationGraphVo.AddNodeReq req) {
        List<String> labels = req.getLabels();
        Map<String, Object> node = req.getNode();
        if (null == labels && labels.isEmpty()) {
            throw new IllegalArgumentException("节点标签不能为空");
        }
        if (null == node || node.isEmpty()) {
            throw new IllegalArgumentException("节点信息不能为空");
        }
        String labelsStr = String.join(":", labels);
        String id = MapUtil.getStr(node, "id");
        RelationGraphVo.QueryResp resp = null;
        if (StringUtils.isBlank(id)) {
            //新增
            String cypher = String.format("CREATE (n:%s) " +
                    "SET n = $item, n.id = randomUUID() " +
                    "RETURN n ", labelsStr);
            resp = neo4jUtil.query(cypher, MapUtil.of("item", node));
        } else {

            // 第一步：获取节点的当前标签
            List<String> existLabels = neo4jUtil.getNodeLabels(id);
            // 第二步：获取需要删除的标签
            Collection<String> delLabels = CollUtil.subtract(existLabels, labels);
            if (!ObjectUtil.isEmpty(delLabels)) {
                String delLabelsStr = String.join(":", delLabels);
                String delLabelCypher = String.format("MATCH (n {id: $id}) " +
                        "REMOVE n:%s ", delLabelsStr);
                neo4jUtil.execute(delLabelCypher, MapUtil.of("id", id));
            }
            //修改
            String mergeCondition = "{id: $item.id}";
            String cypher = String.format("MERGE (n %s) " +
                    "ON CREATE SET n:%s, n = $item " +
                    "ON MATCH SET n:%s, n = $item " +
                    "RETURN n", mergeCondition, labelsStr, labelsStr);
            resp = neo4jUtil.query(cypher, MapUtil.of("item", node));
        }
        List<Map<String, Object>> nodes = resp.getNodes();
        if (CollectionUtil.isNotEmpty(nodes)) {
            return nodes.get(0);
        } else {
            throw new IllegalArgumentException("修改节点失败！");
        }
    }

    @Override
    public void delNodeById(String propId) {
        neo4jUtil.deleteNodeByPropId(null, propId);
    }

    @Override
    public RelationGraphVo.QueryResp addNodeAndRelation(RelationGraphVo.AddNodeAndRelationReq req) {
        Map<String, Object> startNode = req.getSource();
        Map<String, Object> endNode = req.getTarget();
        String relationType = req.getType();
        Map<String, Object> relationProps = req.getData();
        String startId = MapUtil.getStr(startNode, "id");
        if (StringUtils.isBlank(startId)) {
            RelationGraphVo.AddNodeReq addNodeReq = RelationGraphVo.AddNodeReq.of(startNode);
            Map<String, Object> startNode_rst = saveNode(addNodeReq);
            startId = MapUtil.getStr(startNode_rst, "id");
        }
        String endId = MapUtil.getStr(endNode, "id");
        if (StringUtils.isBlank(endId)) {
            RelationGraphVo.AddNodeReq addNodeReq = RelationGraphVo.AddNodeReq.of(endNode);
            Map<String, Object> endNode_rst = saveNode(addNodeReq);
            endId = MapUtil.getStr(endNode_rst, "id");
        }
        RelationGraphVo.QueryResp resp = neo4jUtil.createRelationByPropId(startId, endId, relationType, relationProps);
        return resp;
    }

    @Override
    public Map<String, Object> addRelationById(RelationGraphVo.AddRelationByPropIdReq req) {
        String startId = req.getSourceId();
        String endId = req.getTargetId();
        String relationType = req.getType();
        Map<String, Object> relationProps = req.getData();
        RelationGraphVo.QueryResp resp = neo4jUtil.createRelationByPropId(startId, endId, relationType, relationProps);
        return resp.getLinks().get(0);
    }

    @Override
    public Map<String, Object> changeRelation(RelationGraphVo.ChangeRelationReq req) {
        String sourceId = req.getSourceId();
        String targetId = req.getTargetId();
        String type = req.getType();
        Map<String, Object> data = ObjectUtil.isEmpty(req.getData()) ? Collections.emptyMap() : req.getData();
        String oldType = req.getOldType();

        if (StringUtils.isBlank(oldType)) {
            String matchCondition = String.format("(n {id:'%s'})-[r:`%s`]->(m {id:'%s'})", sourceId, type, targetId);
            //更新关系data
            String cypher = String.format("MATCH %s " +
                    "SET r = $props " +
                    "return n, r, m ", matchCondition);
            Map<String, Object> params = new LinkedHashMap<>();
            params.put("props", data);
            RelationGraphVo.QueryResp resp = neo4jUtil.query(cypher, params);
            return resp.getLinks().get(0);
        } else {
            String matchCondition = String.format("(n {id:'%s'})-[r:`%s`]->(m {id:'%s'})", sourceId, oldType, targetId);
            // 变更关系类型
            String cypher = String.format("MATCH %s " +
                    "WITH n, m, r " +
                    "CREATE (n)-[l:`%s`]->(m) " +
                    "SET l = $props " +
                    "DELETE r " +
                    "RETURN n, l, m ", matchCondition, type);
            Map<String, Object> params = new LinkedHashMap<>();
            params.put("props", data);
            RelationGraphVo.QueryResp resp = neo4jUtil.query(cypher, params);
            return resp.getLinks().get(0);
        }
    }

    @Override
    public void delRelation(RelationGraphVo.DelRelationReq req) {
        //检查一下，全空则抛错
        req.validate();
        String sourceId = req.getSourceId();
        String targetId = req.getTargetId();
        String type = req.getType();
        String cypher = "MATCH (n)-[r]->(m) " +
                "WHERE ($sourceId IS NULL OR n.id = $sourceId) AND " +
                "($targetId IS NULL OR m.id = $targetId) AND " +
                "($type IS NULL OR type(r) = $type) " +
                "DELETE r ";
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("sourceId", sourceId);
        params.put("targetId", targetId);
        params.put("type", type);
        neo4jUtil.execute(cypher, params);
    }

    @Override
    public void mergeNode(RelationGraphVo.MergeNodeReq req) {
        neo4jUtil.mergeNodeByPropId(req.getKeepId(), req.getMergeId());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            Neo4jFulltextIndexEnum[] values = Neo4jFulltextIndexEnum.values();
            for (Neo4jFulltextIndexEnum value : values) {
                createFullTextIndex(value.getIndexName(), value.getLabel(), value.getProps(), false);
            }
        } catch (Exception e) {
            log.error("create full text index error", e);
        }
    }
}
