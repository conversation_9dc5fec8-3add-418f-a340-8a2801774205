package com.cb.ai.data.analysis.ai.component.flows.chuangbo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.domain.common.MinioFileData;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/30 16:19
 * @Copyright (c) 2025
 * @Description 私有化Ocr文件解析
 */
public class PrivateOcrAnalysis extends BasePrivateAiPropertiesNode {

    private final SuperviseResourceFileService fileService;

    public PrivateOcrAnalysis() {
        this.fileService = SpringUtil.getBean(SuperviseResourceFileService.class);
    }

    @Override
    public String getNodeName() {
        return "私有化-ocr解析-接口";
    }

    @Override
    public String setRequestUrl() {
        return MergeUtil.mergePath(aiProp.getBaseUrl(), aiProp.getOcr());
    }

    @Override
    public Object setRequestBody() {
        CommonAIRequestContext webContext = getRequestContext();
        if (CollectionUtil.isNotEmpty(webContext.getMinioFileDataList())) {
            webContext.getMinioFileDataList().forEach(minioFileData -> {
                try(InputStream is = getMinioFileIns(minioFileData)) {
                    if (is != null) {

                    }
                } catch (Exception e) {

                }

            });
        }
        return null;
    }

    private InputStream getMinioFileIns(MinioFileData fileData) {
        String fileId = fileData.fileId();
        String fileUrl = fileData.fileUrl();
        if (StrUtil.isNotBlank(fileId)) {
            return fileService.getFileStream(fileId);
        } else if (StrUtil.isNotBlank(fileUrl)) {
            return fileService.getInputStream(fileUrl);
        } else {
            return null;
        }
    }
}
