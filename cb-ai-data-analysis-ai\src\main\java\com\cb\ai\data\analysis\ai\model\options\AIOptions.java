package com.cb.ai.data.analysis.ai.model.options;

import lombok.Data;

import java.io.Serializable;

/**
 * AI配置项
 * <AUTHOR>
 */
@Data
public class AIOptions implements Serializable {
    /**
     * 温度（控制随机性，范围 0~2）。低温（接近 0） ：模型更保守、更确定，倾向于选择概率最高的词。 高温（接近 1 或更高） ：模型更随机、更有创意，可能会选择不太常见的词
     */
    private Float temperature;
    /**
     * 采样策略（控制多样性，范围 0~1）。如果设置 top_p=0.9，模型会从概率加起来占前 90% 的词中进行随机采样
     */
    private Float topP;
    /**
     * 最大返回 token 数量（默认2048）。控制模型生成文本的最大长度
     */
    private Integer maxTokens;
    /**
     * 频率惩罚项（关注频率，范围 -2.0 ~ 2.0）。用于惩罚已经频繁出现过的 token，从而鼓励模型生成新的、多样化的词汇。值为 正数 → 惩罚重复词，减少重复；值为 负数 → 鼓励重复词，增加重复；值为 0 → 不做任何惩罚或鼓励
     */
    private Float frequencyPenalty;
    /**
     * 出现惩罚项（关注是否出现过，范围 -2.0 ~ 2.0）。用于惩罚已经频繁出现过的 token，越大越鼓励新内容。值为 正数 → 惩罚重复词，减少重复；值为 负数 → 鼓励重复词，增加重复；值为 0 → 不做任何惩罚或鼓励
     */
    private Float presencePenalty;
}
