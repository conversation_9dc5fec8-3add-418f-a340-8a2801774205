package com.cb.ai.data.analysis.graph.controller;

import com.cb.ai.data.analysis.graph.domain.vo.RelationGraphVo;
import com.cb.ai.data.analysis.graph.service.business.GraphFileRecordService;
import com.cb.ai.data.analysis.graph.service.business.RelationGraphService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 知识图谱-关系图(GraphRelation)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:12
 */
@Validated
@RestController
@RequestMapping(Constants.API_GRAPH_PATH)
public class RelationGraphController {

    @Autowired
    private RelationGraphService relationGraphService;
    @Autowired
    private GraphFileRecordService graphFileRecordService;

    @PostMapping({"/test", "/queryByCypher"})
    public Result queryByCypher(@RequestBody Map<String, Object> params) {
        String cypher = MapUtils.getString(params, "query");
        if (StringUtils.isBlank(cypher)) {
            cypher = "MATCH ()-[r]->() RETURN type(r) AS relationship_type, count(*) AS count ORDER BY count DESC";
        }
        try {
            RelationGraphVo.QueryResp resp = relationGraphService.query(cypher, null);
            return Result.successData(resp);
        } catch (Exception e) {
            String finalCypher = "MATCH (n)-[r]-(m)\n" +
                    "RETURN n, r, m\n" +
                    "LIMIT 50";
            return Result.successData(relationGraphService.query(finalCypher, null));
        }
    }

    /**
     * 全局搜索
     *
     * @param name   搜索名称
     * @param layers 层级
     * @param limit  限制最大数量 -1为不限制
     * @return
     */
    @GetMapping("/search")
    public Result search(String name, Integer layers, Integer limit) {
        if (null == layers) {
            layers = 0;
        }
        if (layers <= 0) {
            layers = 5;// 经测试，不限深度会很慢，预设为5层
        }
        if (StringUtils.isNotBlank(name)) {
            return Result.successData(relationGraphService.search(name, layers));
        } else {
            String limitStr = "";
            if (null != limit && limit > 0) {
                limitStr = String.format(" LIMIT %d ", limit);
            }
            String pathPattern = layers <= 0 ?
                    "(n)-[r*]-(m)" :  // 不限制深度
                    String.format("(n)-[r*0..%d]-(m)", layers);  // 限制深度
            String cypher = "MATCH (n) " +
                    "WITH n " + limitStr +
                    "OPTIONAL MATCH " + pathPattern + " " +
                    "RETURN n, collect(r) as relations, collect(m) as related_nodes";
//            String cypher = "MATCH (c:企业 {name: '云南嘉华食品有限公司'}) OPTIONAL MATCH path1 = (c)-[:法人]->(o) OPTIONAL MATCH path2 = (c)-[:股东]->(p) RETURN    c,    o,    p,    relationships(path1) AS 法人关系,    relationships(path2) AS 股东关系  LIMIT 100 ";
            return Result.successData(relationGraphService.query(cypher, null));
        }
    }

    @GetMapping("/cleanAll")
    public Result cleanAll() {
        relationGraphService.cleanAll();
        graphFileRecordService.truncate();
        return Result.success();
    }

    /**
     * 公司名称补全匹配
     *
     * @param name
     * @return
     */
    @GetMapping("/nameMatch")
    public Result nameMatch(String name) {
        List<Map<String, Object>> nodeList = relationGraphService.nameMatch(name);
        return Result.successData(nodeList);
    }


    @GetMapping("/category/attribute")
    public Result categoryAttribute() {
        Map<String, List<String>> attributeMap = relationGraphService.baseAttributes();
        return Result.successData(attributeMap);
    }

    /**
     * 保存或新增节点
     *
     * @param request
     * @param response
     * @param req
     * @return
     */
    @PostMapping("/saveNode")
    public Result saveNode(HttpServletRequest request, HttpServletResponse response,
                           @RequestBody @NotEmpty(message = "节点信息不能为空") Map<String, Object> req) {
        RelationGraphVo.AddNodeReq addNodeReq = RelationGraphVo.AddNodeReq.of(req);
        Map<String, Object> nodeInfo = relationGraphService.saveNode(addNodeReq);
        return Result.successData(nodeInfo);
    }

    /**
     * 根据id删除节点
     *
     * @param propId
     * @return
     */
    @GetMapping("/delNodeById/{id}")
    public Result delNodeById(@PathVariable("id") String propId) {
        relationGraphService.delNodeById(propId);
        return Result.success();
    }

    /**
     * 添加节点和关系
     *
     * @param req
     * @return
     */
    @PostMapping("/addNodeAndRelation")
    public Result addNodeAndRelation(@RequestBody @Valid RelationGraphVo.AddNodeAndRelationReq req) {
        RelationGraphVo.QueryResp resp = relationGraphService.addNodeAndRelation(req);
        return Result.successData(resp);
    }

    /**
     * 根据起止节点的id 添加关系
     *
     * @param req
     * @return
     */
    @PostMapping("/addRelationById")
    public Result addRelationById(@RequestBody @Valid RelationGraphVo.AddRelationByPropIdReq req) {
        Map<String, Object> rst = relationGraphService.addRelationById(req);
        return Result.successData(rst);
    }

    /**
     * 修改关系
     *
     * @param req
     * @return
     */
    @PostMapping("/changeRelation")
    public Result changeRelation(@RequestBody @Valid RelationGraphVo.ChangeRelationReq req) {
        Map<String, Object> rst = relationGraphService.changeRelation(req);
        return Result.successData(rst);
    }

    /**
     * 删除关系
     *
     * @param req
     * @return
     */
    @PostMapping("/delRelation")
    public Result delRelation(@RequestBody @Valid RelationGraphVo.DelRelationReq req) {
        relationGraphService.delRelation(req);
        return Result.success();
    }

    /**
     * 合并节点
     *
     * @param req
     * @return
     */
    @PostMapping("/mergeNode")
    public Result mergeNode(@RequestBody @Valid RelationGraphVo.MergeNodeReq req) {
        relationGraphService.mergeNode(req);
        return Result.success();
    }

}
