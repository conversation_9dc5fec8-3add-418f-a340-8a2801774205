package com.cb.ai.data.analysis.voucher.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 凭证分析任务
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoucherTask  extends BaseDomain {

    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    //任务名称
    @NotBlank(message = "任务名称不能为空")
    private String name;

    // 要分析的tag，多个tag用英文逗号分隔
    @NotBlank(message = "请选择要分析的标签")
    private String tags;

    // 任务开始时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime taskBeginTime;

    // 任务结束时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime taskEndTime;

    // 总数
    private Long totalNum;

    // 告警数
    private Long alertNum;

    // 已处理数
    private Long handleNum;

    // 状态 0-待处理 1-处理中 2-处理完成 3-处理失败
    private Integer status;

    // 错误信息
    private String errMsg;

}
