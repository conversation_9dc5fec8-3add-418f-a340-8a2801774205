package com.cb.ai.data.analysis.ai.component.flows.chuangbo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.common.MinioFileData;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.xong.boot.framework.utils.SecurityUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.codec.ServerSentEvent;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/22 14:42
 * @Copyright (c) 2025
 * @Description 深度思考接口
 */
public class PrivateDeepThinkChat extends BasePrivateAiPropertiesNode {
    /*
     * 深度思考接口请求参数
     */
    private final String requestStr = """
        {
            "messages": [
                {
                    "role": "user",
                    "content": "%s"
                }
            ],
            "thread_id": "%s",
            "session_id": "%s",
            "interrupt_feedback": "%s",
            "kb_settings": {
                "baseid_list": [%s],
                "fileid_list": [%s]
            }
        }
    """;

    @Override
    public String getNodeName() {
        return "私有化-深度思考-接口";
    }

    @Override
    public String setRequestUrl() {
        return aiProp.getDeepThinkUrl();
    }

    @Override
    public Map<String, Object> setRequestBody() {
        CommonAIRequestContext body = getRequestContext();
        Assert.notBlank(body.getPromote(), "深度思考接口请求参数promote不能为空");
        String userId = SecurityUtils.getUserId();
        return JsonUtil.toMap(requestStr.formatted(
            body.getPromote(),
            userId,
            body.getSessionId(),
            body.getExtendData().getStr("interruptFeedback", ""),
            ArrayUtil.isNotEmpty(body.getBaseIds()) ? String.join("\",\"", body.getBaseIds()).transform(s -> "\"" + s + "\"") : "",
            CollectionUtil.isNotEmpty(body.getMinioFileDataList()) ? String.join("\",\"", body.getMinioFileDataList().stream().map(MinioFileData::fileId).toList()).transform(s -> "\"" + s + "\"") : ""
        ));
    }

    @Override
    public PrivateAIBackData resultConvert(ServerSentEvent<String> ssEvent) {
        String rawData = ssEvent.data();
        DeepThinkBackData data = JsonUtil.toBean(rawData, DeepThinkBackData.class);
        PrivateAIBackData aiBackData = buildAiBackData(ssEvent, data).setSessionId(getRequestContext().getSessionId());
        if (ssEvent.event() != null) {
            aiBackData.setId(data.getId());
            switch (ssEvent.event()) {
                // 中断事件
                case "interrupt" -> aiBackData.setContent(data.getContent());
                // 工具调用事件
                case "tool_call_chunks" -> data.getToolCallChunks().forEach(tool -> aiBackData.setContent(tool.getArgs()));
                // 工具调用结果事件, 此次先不处理，如果前端重复，再处理
                case "tool_call_result" -> aiBackData.setContent(data.getContent());
                // 默认消息块事件
                default -> aiBackData.setReasoning_content(data.getReasoning_content()).setContent(data.getContent());
            }
        }
        return aiBackData;
    }

    private PrivateAIBackData buildAiBackData(ServerSentEvent<String> ssEvent, DeepThinkBackData data) {
        return new PrivateAIBackData()
            .setEvent(ssEvent.event())
            .setRawData(ssEvent.data())
            .setRole(data.getRole());
    }

    @Data
    @Accessors(chain = true)
    private static class DeepThinkBackData {
        // 消息id
        private String id;
        // 线程id
        @JsonAlias("thread_id")
        private String threadId;
        // 消息角色
        private String role;
        // 思考过程
        private String reasoning_content;
        // 消息内容
        private String content;
        // 智能体
        private String agent;
        // 消息中断原因
        @JsonAlias("finish_reason")
        private String finishReason;
        // 消息选项
        private List<JsonMap> options;
        // 工具调用id
        @JsonAlias("tool_call_id")
        private String toolCallId;
        // 消息工具调用
        @JsonAlias("tool_call_chunks")
        private List<ToolCallChunks> toolCallChunks;

    }

    @Data
    private static class ToolCallChunks {
        // 工具调用id
        private String id;
        // 工具调用name
        private String name;
        // 工具调用返回消息
        private String args;
        // 工具调用索引
        private Integer index;
        // 工具调用类型
        private String type;
    }
}
