package com.cb.ai.data.analysis.petition.utils;

import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.IOException;
import java.io.InputStream;

public class Docx<PERSON><PERSON>er<PERSON><PERSON><PERSON> extends FileReaderHandler<String> {
    @Override
    public String read(InputStream inputStream) throws IOException {
        XWPFDocument document = new XWPFDocument(inputStream);
        XWPFWordExtractor extractor = new XWPFWordExtractor(document);
        return extractor.getText();
    }
}
