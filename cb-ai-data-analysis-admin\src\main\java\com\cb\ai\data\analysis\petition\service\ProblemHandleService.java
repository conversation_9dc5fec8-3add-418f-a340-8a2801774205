package com.cb.ai.data.analysis.petition.service;

import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/***
 * 问题批处理
 * 因为需要调用其他模块，故而分析这个放在了admin
 * <AUTHOR>
 */
public interface ProblemHandleService {

    /***
     * 批处理文件上传
     * @param problemHandleFileList
     * @return
     * @throws Exception
     */
    int saveProblemHandleFile(List<PetitionProblemHandleEntity> problemHandleFileList)throws Exception;

    /***
     * 重新发起解析
     * @param id
     */
    void reAnalyze(String id)throws Exception;

    /***
     * 导出zip包，这里需要调用，其中参考文献需调用minio
     * @param ids
     * @param response
     */
    void exportZip(List<String> ids, HttpServletResponse response);
}
