package com.cb.ai.data.analysis.ai.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistoryDetailMapper;
import com.cb.ai.data.analysis.ai.service.IAiChatHistoryDetailService;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/22 22:05
 * @Copyright (c) 2025
 * @Description AI会话历史记录详情服务层实现类
 */
@Service("iAiChatHistoryDetailService")
public class AiChatHistoryDetailServiceImpl extends ServiceImpl<AiChatHistoryDetailMapper, AiChatHistoryDetail> implements IAiChatHistoryDetailService {

    @Override
    public JsonMap pageByTime(AiChatHistoryDetail aiChatHistoryDetail) {
        Assert.notNull(aiChatHistoryDetail.getCreateTime(), "分页查询历史会话详情：创建事件不能为空");
        String userId = OptionalUtil.ofBlankable(SecurityUtils.getUserId()).orElse(aiChatHistoryDetail.getUserId());

        // 先查询符合条件的记录（按创建时间降序）
        Page<AiChatHistoryDetail> results = page(QueryHelper.getPage(), XQueryWrapper.newInstance(new AiChatHistoryDetail()).lambda()
            .eq(StrUtil.isNotBlank(aiChatHistoryDetail.getSessionId()), AiChatHistoryDetail::getSessionId, aiChatHistoryDetail.getSessionId())
            .eq(StrUtil.isNotBlank(userId), AiChatHistoryDetail::getUserId, userId)
            .lt(AiChatHistoryDetail::getCreateTime, aiChatHistoryDetail.getCreateTime())
            .orderByDesc(AiChatHistoryDetail::getCreateTime)
        );

        // 对结果进行升序排序
        List<AiChatHistoryDetail> sortedList = results.getRecords().stream()
            .sorted(Comparator.comparing(AiChatHistoryDetail::getCreateTime))
            .toList();
        return JsonMap.of("result", sortedList).putOnce("hasMore", results.getPages() > 1);
    }

    @Override
    public List<AiChatHistoryDetail> getHistoryDetailById(String sessionId, String userId) {
        Assert.isFalse(StrUtil.isBlank(sessionId) && StrUtil.isBlank(userId), "查询历史会话详情：sessionId 和 userId不能同时为空");
        userId = OptionalUtil.ofBlankable(SecurityUtils.getUserId()).orElse(userId);
        return list(XQueryWrapper.newInstance(new AiChatHistoryDetail()).lambda()
            .eq(StrUtil.isNotBlank(sessionId), AiChatHistoryDetail::getSessionId, sessionId)
            .eq(StrUtil.isNotBlank(userId), AiChatHistoryDetail::getUserId, userId)
            .orderByAsc(AiChatHistoryDetail::getCreateTime)
        );
    }

    @Override
    public boolean saveHistoryDetail(AiChatHistoryDetail detail) {
        String userId = OptionalUtil.ofBlankable(SecurityUtils.getUserId()).orElse(detail.getUserId());
        if (StrUtil.isNotBlank(userId)) {
            detail.setUserId(userId);
        }
        detail.setCreateTime(LocalDateTime.now());
        return save(detail);
    }

    @Override
    public boolean removeById(String sessionId, String userId) {
        Assert.isFalse(StrUtil.isBlank(sessionId) && StrUtil.isBlank(userId), "删除历史会话详情：sessionId 和 userId不能同时为空");
        userId = OptionalUtil.ofBlankable(SecurityUtils.getUserId()).orElse(userId);
        return remove(XQueryWrapper.newInstance(new AiChatHistoryDetail()).lambda()
            .eq(StrUtil.isNotBlank(sessionId), AiChatHistoryDetail::getSessionId, sessionId)
            .eq(StrUtil.isNotBlank(userId), AiChatHistoryDetail::getUserId, userId));
    }

    @Override
    public boolean removeByIds(List<String> ids) {
        Assert.notEmpty(ids, "删除历史会话详情：id列表不能为空");
        return remove(XQueryWrapper.newInstance(new AiChatHistoryDetail()).lambda()
            .in(AiChatHistoryDetail::getId, ids));
    }
}

