<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.graph.mapper.GraphFileRecordMapper">

    <resultMap type="com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord" id="GraphFileRecordMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="errLog" column="err_log" jdbcType="VARCHAR"/>
        <result property="totalCount" column="total_count" jdbcType="INTEGER"/>
        <result property="processedCount" column="processed_count" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <update id="truncate">
        truncate table graph_file_record
    </update>

</mapper>

