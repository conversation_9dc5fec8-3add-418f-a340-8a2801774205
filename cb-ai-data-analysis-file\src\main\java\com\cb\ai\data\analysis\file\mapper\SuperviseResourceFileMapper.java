package com.cb.ai.data.analysis.file.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SuperviseResourceFileMapper  extends BaseMapper<SuperviseResourceFile> {
    /**
     * 复杂条件分页查询（XML方式）
     * @param page 分页对象
     * @param wrapper 条件构造器
     * @return 分页结果
     */
    Page<SuperviseResourceFile> getFileList(Page<SuperviseResourceFile> page,
                                            @Param(Constants.WRAPPER) Wrapper<SuperviseResourceFile> wrapper);
    List<SuperviseResourceFile> getFileList(@Param(Constants.WRAPPER) Wrapper<SuperviseResourceFile> wrapper);

    /**
     * 自定义条件删除
     * @param wrapper
     * @return
     */
    Integer deleteByCustom(@Param(Constants.WRAPPER) Wrapper<SuperviseResourceFile> wrapper);

    /**
     * 更新数据
     * @param superviseResourceFile
     * @return
     */
    Integer updateResourceFile(SuperviseResourceFile superviseResourceFile);

    /**
     * 从逻辑删除中恢复文件
     * @param ids
     * @return
     */
    Integer restoreFiles(@Param("ids") List<String> ids,@Param("parentId") String folderId);

}
