package com.cb.ai.data.analysis.graph.service.business.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.domain.vo.ExtractLabelVo;
import com.cb.ai.data.analysis.graph.domain.vo.RgFileRecordVo;
import com.cb.ai.data.analysis.graph.enums.BasicGraphCategoryEnum;
import com.cb.ai.data.analysis.graph.enums.BasicGraphLabelEnum;
import com.cb.ai.data.analysis.graph.enums.BasicGraphRelationEnum;
import com.cb.ai.data.analysis.graph.handler.FileProcessor;
import com.cb.ai.data.analysis.graph.handler.FileProcessorFactory;
import com.cb.ai.data.analysis.graph.mapper.GraphFileRecordMapper;
import com.cb.ai.data.analysis.graph.service.business.GraphFileRecordService;
import com.cb.ai.data.analysis.graph.utils.FileProcessCache;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关系图谱-上传文件记录(RgFileRecord)表服务实现类
 */
@Service("rgFileRecordService")
public class GraphFileRecordServiceImpl extends BaseServiceImpl<GraphFileRecordMapper, GraphFileRecord> implements GraphFileRecordService {
    @Autowired
    private GraphFileRecordMapper fileRecordMapper;
    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;
    @Resource
    private FileProcessCache fileProcessCache;

    @Override
    public Page<GraphFileRecord> page(RgFileRecordVo.RgFileRecordPageQueryReq req) {
        LambdaQueryWrapper<GraphFileRecord> wrapper = Wrappers.lambdaQuery(GraphFileRecord.class)
                .like(StringUtils.isNotBlank(req.getFileName()), GraphFileRecord::getFileName, req.getFileName())
                .eq(ObjectUtil.isNotNull(req.getStatus()), GraphFileRecord::getStatus, req.getStatus())
                .lt(ObjectUtil.isNotNull(req.getBeginTime()), GraphFileRecord::getCreateTime, req.getBeginTime())
                .gt(ObjectUtil.isNotNull(req.getEndTime()), GraphFileRecord::getCreateTime, req.getEndTime())
                .orderByDesc(GraphFileRecord::getCreateTime);
        Page<GraphFileRecord> page = fileRecordMapper.selectPage(QueryHelper.getPage(false), wrapper);

        page.getRecords().forEach(o->{
            if(Integer.valueOf(1).equals(o.getStatus())){
                String recordId = o.getId();
                FileProcessCache.Progress progress = fileProcessCache.getProgress(recordId);
                if (progress != null) {
                    o.setTotalCount(progress.getTotalCount());
                    o.setProcessedCount(progress.getProcessedCount());
                }
            }
        });

        return page;
    }

    @Override
    public void truncate() {
        fileRecordMapper.truncate();
    }

    @Override
    public void fileSyncNeo4j(GraphFileRecord graphFileRecord, Boolean precise, String promote) {
        if (null == graphFileRecord || StringUtils.isBlank(graphFileRecord.getFileId())) {
            throw new IllegalArgumentException("文件id不能为空");
        }
        SuperviseResourceFile file = superviseResourceFileService.getById(graphFileRecord.getFileId());
        if(null == file){
            throw new RuntimeException("文件不存在");
        }
        String filename = file.getFilename();
        InputStream fileStream = superviseResourceFileService.getFileStream(file.getId());
        //使用对应的处理器处理文件
        FileProcessor fileProcessor = FileProcessorFactory.getFileProcessor(filename, precise, promote);
        fileProcessor.process(fileStream, graphFileRecord, promote);
    }


    @Override
    public List<ExtractLabelVo> extractGraphLabels() {
        List<ExtractLabelVo> extractLabelVos = Arrays.stream(BasicGraphLabelEnum.values()).map(basicGraphLabelEnum -> {
            ExtractLabelVo extractLabelVo = new ExtractLabelVo();
            extractLabelVo.setCategory(basicGraphLabelEnum.getCategory());
            extractLabelVo.setEditable(basicGraphLabelEnum.getEditable());
            extractLabelVo.setAttribute(basicGraphLabelEnum.getAttribute());
            extractLabelVo.setDesc(basicGraphLabelEnum.getDesc());
            extractLabelVo.setIndex(basicGraphLabelEnum.getIndex());
            return extractLabelVo;
        }).collect(Collectors.toList());

        return extractLabelVos;
    }

    @Override
    public String promote(List<ExtractLabelVo> extractLabelVos) throws IOException {
        InputStream is = GraphFileRecordServiceImpl.class.getClassLoader()
                .getResourceAsStream("promote/relation.txt");

        BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
        StringBuilder content = new StringBuilder();
        String line;

        while ((line = reader.readLine()) != null) {
            content.append(line).append(System.lineSeparator());
        }

        String template = generatePromoteTemplate(extractLabelVos);
        String promote = content.toString().replace("#{template}", template);

        return promote;
    }

    /**
     * 根据预定义模板及用户的输入生成 提示词 中的 json模板
     * key：属性名称
     * value : 属性描述
     *
     * @param extractLabelVos
     * @return
     */
    private String generatePromoteTemplate(List<ExtractLabelVo> extractLabelVos) {
        JSONObject template = new JSONObject();

        extractLabelVos.stream().forEach(extractLabelVo -> {
            String category = extractLabelVo.getCategory();
            if (StringUtils.isNotEmpty(category)) {
                JSONArray categoryDetailList = template.getJSONArray(category);
                if (categoryDetailList == null) {
                    categoryDetailList = new JSONArray();
                    template.put(category, categoryDetailList);
                }

                if (StringUtils.isNotEmpty(extractLabelVo.getAttribute()) && StringUtils.isNotEmpty(extractLabelVo.getDesc())) {
                    JSONObject detail = new JSONObject();
                    detail.put(extractLabelVo.getAttribute(), extractLabelVo.getDesc());
                    categoryDetailList.add(detail);
                }
            }
        });

        JSONArray relationList = new JSONArray();
        template.put(BasicGraphCategoryEnum.RELATION.getName(), relationList);
        Arrays.stream(BasicGraphRelationEnum.values()).forEach(relation -> {
            JSONObject relationDetail = new JSONObject();
            relationDetail.put(relation.getName(), relation.getDesc());

            relationList.add(relationDetail);
        });

        return JSON.toJSONString(template, JSONWriter.Feature.PrettyFormat);
    }


    // 获取 List<T> 的泛型类型 T
    private Class<?> getGenericListType(Field field) {
        try {
            if (List.class.isAssignableFrom(field.getType())) {
                String typeName = field.getGenericType().getTypeName();
                if (typeName.contains("<") && typeName.contains(">")) {
                    String className = typeName.substring(typeName.indexOf("<") + 1, typeName.indexOf(">"));
                    return Class.forName(className);
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
}
