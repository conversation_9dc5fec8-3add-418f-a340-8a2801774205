package com.cb.ai.data.analysis.ai.service;

import com.cb.ai.data.analysis.ai.domain.request.context.ExtendCommonAIRequestContext;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:18
 * @Copyright (c) 2025
 * @Description 通用AI接口服务
 */
public interface ICommonService {
    /**
     * @description 通用AI接口服务
     * @param request 请求参数
     * @return SseEmitter 流式返回
     * @createtime 2025/7/4 12:18
     * <AUTHOR>
     * @version 1.0
     */
    Flux<?> invoke(ExtendCommonAIRequestContext<?> requestContext);

    /**
     * @description 通用AI同步接口服务
     * @param request 请求参数
     * @return SseEmitter 流式返回
     * @createtime 2025/7/4 12:18
     * <AUTHOR>
     * @version 1.0
     */
    Object syncInvoke(ExtendCommonAIRequestContext<?> requestContext);
}
