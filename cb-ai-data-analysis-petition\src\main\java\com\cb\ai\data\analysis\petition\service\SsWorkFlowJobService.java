package com.cb.ai.data.analysis.petition.service;


import com.cb.ai.data.analysis.petition.domain.entity.SsWorkFlowJobEntity;
import com.cb.ai.data.analysis.petition.enums.WorkFlowTypeEnum;
import com.xong.boot.common.service.BaseService;

public interface SsWorkFlowJobService extends BaseService<SsWorkFlowJobEntity> {

    SsWorkFlowJobEntity publish(WorkFlowTypeEnum workFlowTypeEnum);

    void success(SsWorkFlowJobEntity ssWorkFlowJobEntity, String workFlowMessage);

    void error(SsWorkFlowJobEntity ssWorkFlowJobEntity, String workFlowMessage, String errorMessage);

}
