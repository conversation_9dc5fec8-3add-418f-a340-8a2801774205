package com.cb.ai.data.analysis.voucher.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTag;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherInfoVo;
import com.cb.ai.data.analysis.voucher.service.VoucherInfoService;
import com.cb.ai.data.analysis.voucher.service.VoucherTagService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cb.ai.data.analysis.voucher.constant.VoucherConstant.FOLDER_NAME;

/**
 * 凭证信息控制器
 */
@Validated
@RestController
@RequestMapping(Constants.API_VOUCHER_ROOT_PATH + "/info")
public class VoucherInfoController extends BaseController<VoucherInfoService, VoucherInfo> {

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;
    @Autowired
    private VoucherTagService tagService;

    @GetMapping("/page")
    public Result page(VoucherInfoVo.PageReq entity){
        Date endDate = entity.getEndDate();
        if(null != endDate){
            entity.setEndDate(DateUtil.endOfDay(endDate));
        }
        Page<VoucherInfo> page = baseService.pageByEntity(QueryHelper.getPage(), entity);
        return Result.successData(page);
    }

    @GetMapping("/pageBySelf")
    public Result pageBySelf(VoucherInfoVo.PageReq entity){
        Date endDate = entity.getEndDate();
        if(null != endDate){
            entity.setEndDate(DateUtil.endOfDay(endDate));
        }
        String username = SecurityUtils.getUsername();
        entity.setCreateBy(username);
        Page<VoucherInfo> page = baseService.pageByEntity(QueryHelper.getPage(), entity);
        return Result.successData(page);
    }

    /**
     * 获取详情
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public Result detail(@PathVariable("id") String id){
        VoucherInfo detail = baseService.detail(id);
        return Result.successData(detail);
    }

    /**
     * 获取所有标签选项
     * @return
     */
    @GetMapping("/getAllTags")
    public Result getAllTags(){
        List<Map<String, String>> tagOptions = tagService.getAllTags();
        return Result.successData(tagOptions);
    }

    /**
     * 上传凭证并异步ocr解析内容
     * @param request
     * @param response
     * @param tags 上传凭证的标签
     * @return
     */
    @PostMapping("upload")
    public Result uploadVoucher(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = true) String tags) {
        String username = SecurityUtils.getUsername();
        Map<String,String> fileNameIdMap = new LinkedHashMap<>();
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            List<MultipartFile> multipartFileList = multipartRequest.getFiles("file");
            try {
                for (int i = 0; i < multipartFileList.size(); i++) {
                    MultipartFile multipartFile = multipartFileList.get(i);
                    if (null != multipartFile && !multipartFile.isEmpty()) {
                        String fileName = multipartFile.getOriginalFilename();
                        SuperviseResourceFolder folder = new SuperviseResourceFolder();
                        folder.setId("0");
                        InputStream inputStream = multipartFile.getInputStream();
                        SuperviseResourceFile superviseResourceFile = superviseResourceFileService
                                .uploadFileStreamByFolderName(inputStream, null, fileName, FOLDER_NAME, Long.valueOf(inputStream.available()), multipartFile.getContentType(), null, username);
                        fileNameIdMap.put(fileName, superviseResourceFile.getId());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("文件存储失败！" + e.getMessage());
            }
        }
        if (fileNameIdMap.isEmpty()) {
            throw new RuntimeException("未检测到文件，请选择文件！");
        }

        List<VoucherInfo> recordList = fileNameIdMap.entrySet().stream().map(entry -> {
            VoucherInfo voucherInfo = new VoucherInfo();
            voucherInfo.setId(IdUtil.getSnowflakeNextIdStr());
            voucherInfo.setName(entry.getKey());
            voucherInfo.setFileId(entry.getValue());
            voucherInfo.setStatus(0);
            voucherInfo.setCreateBy(username);
            voucherInfo.setCreateTime(LocalDateTime.now());
            return voucherInfo;
        }).collect(Collectors.toList());
        Set<String> ids = recordList.stream().map(VoucherInfo::getId).collect(Collectors.toSet());
        if(StringUtils.isNotBlank(tags)){
            List<VoucherTag> tagList = new LinkedList<>();
            String[] split = tags.trim().split(",");
            for (String tag : split) {
                String finalTag = tag.trim();
                if(StringUtils.isBlank(finalTag)){
                    continue;
                }
                List<VoucherTag> list = ids.stream().map(id -> {
                    VoucherTag voucherTag = new VoucherTag();
                    voucherTag.setId(IdUtil.getSnowflakeNextIdStr());
                    voucherTag.setVoucherId(id);
                    voucherTag.setTag(finalTag);
                    return voucherTag;
                }).toList();
                tagList.addAll(list);
            }
            if(!tagList.isEmpty()){
                tagService.saveBatch(tagList);
            }
        }
        //入库
        baseService.saveBatch(recordList);

        //异步解析
        baseService.asyncOcrByIds(ids, false);
        return Result.success();
    }

    /**
     * 凭证重新ocr解析
     * @param id 凭证id
     * @return
     */
    @PostMapping("reOcr/{id}")
    public Result reOcr(@PathVariable("id") String id){
        VoucherInfo voucherInfo = baseService.getById(id);
        if(null == voucherInfo){
            return Result.fail("凭证不存在！");
        }
        baseService.asyncOcrByIds(Collections.singleton(id), true);
        return Result.success();
    }

    /**
     * 批量添加标签
     * @param req
     * @return
     */
    @PostMapping("batchAddTag")
    public Result batchAddTag(@Valid @RequestBody VoucherInfoVo.BatchTagReq req){
        tagService.batchAddTag(req.getInfoIds(), req.getTags());
        return Result.success();
    }

    /**
     * 批量删除标签
     * @param req
     * @return
     */
    @PostMapping("batchDelTag")
    public Result batchDelTag(@Valid @RequestBody VoucherInfoVo.BatchTagReq req){
        tagService.batchDelTag(req.getInfoIds(), req.getTags());
        return Result.success();
    }

    /**
     * 单个凭证进行分析
     * @param id
     * @return
     */
    @PostMapping("analysisById/{id}")
    public Result analysisById(@PathVariable("id") String id){
        baseService.asyncAnalysisById(id);
        return Result.success("后台分析中...");
    }

}
