package com.cb.ai.data.analysis.graph.service.basic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresInfo;

import java.util.List;

/**
 * 知识图谱-干部信息(GraphCadresInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03 13:50:34
 */
public interface GraphCadresInfoService extends IService<GraphCadresInfo> {

    public boolean save(GraphCadresInfo graphCadresInfo);

    public boolean updateById(GraphCadresInfo graphCadresInfo);

    public boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<GraphCadresInfo> list);
}

