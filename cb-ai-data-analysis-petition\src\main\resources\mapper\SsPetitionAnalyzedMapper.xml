<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsPetitionAnalyzedMapper">

    <select id="selectByDateRange" resultType="com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity">
        SELECT * FROM
        ss_petition_analyzed analyzed
        <where>
            1 = 1
            <if test="startDate != null and endDate != null">
                AND register_date BETWEEN #{startDate}
                AND #{endDate}
            </if>
            <if test="types != null and types.size() >0 ">
                AND SUBSTRING_INDEX(petition_domain, '/', 1)
                IN
                <foreach collection="types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="city != null and city != ''">
                AND petition_city = #{city}
            </if>
            <if test="district  != null and district  != ''">
                AND petition_district = #{district}
            </if>
        </where>
        LIMIT #{size}
    </select>


    <select id="getPurposeStatistics" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.PurposeStatisticExportDTO">
        SELECT petition_purpose_category AS firstCategory,
               petition_purpose          AS secondCategory,
               COUNT(*)                  AS count
        FROM ss_petition_analyzed
        GROUP BY petition_purpose_category, petition_purpose
        ORDER BY COUNT(*) DESC
    </select>

    <select id="getDomainStatistics" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.PurposeStatisticExportDTO">
        SELECT petition_domain_category AS firstCategory,
               petition_domain          AS secondCategory,
               COUNT(*)                 AS count
        FROM ss_petition_analyzed
        GROUP BY petition_domain_category, petition_domain
        ORDER BY COUNT(*) DESC
    </select>

    <select id="getOrgStatistics" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.OrgExportDTO">
        SELECT petition_belong_org AS org, COUNT(*) AS count
        FROM ss_petition_analyzed
        GROUP BY petition_belong_org
        ORDER BY COUNT(*) DESC
    </select>

    <select id="getAnalyzeGroup" resultType="com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo$RepeatTaskAnalyzeGroup" >
        select
        petition_purpose_category as petitionPurposeCategory,
        petition_purpose as petitionPurpose,
        petition_domain_category as petitionDomainCategory,
        petition_domain as petitionDomain,
        petition_province as petitionProvince,
        petition_city as petitionCity,
        count(1) as total
        from ss_petition_analyzed
        where brief is not null and brief != ''
        <if test="beginDate != null">
            and register_date &gt;= #{beginDate}
        </if>
        <if test="endDate != null">
            and register_date &lt;= #{endDate}
        </if>
        group by petition_purpose_category, petition_purpose, petition_domain_category, petition_domain, petition_province, petition_city
        having total &gt; 1
    </select>

    <select id="streamPetitionByAnalyzeGroup" resultType="com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity">
        select *
        from ss_petition_analyzed
        where brief is not null and brief != ''
        <if test="g.petitionPurposeCategory != null">
            and petition_purpose_category = #{g.petitionPurposeCategory}
        </if>
        <if test="g.petitionPurpose != null">
            and petition_purpose = #{g.petitionPurpose}
        </if>
        <if test="g.petitionDomainCategory != null">
            and petition_domain_category = #{g.petitionDomainCategory}
        </if>
        <if test="g.petitionDomain != null">
            and petition_domain = #{g.petitionDomain}
        </if>
        <if test="g.petitionProvince != null">
            and petition_province = #{g.petitionProvince}
        </if>
        <if test="g.petitionCity != null">
            and petition_city = #{g.petitionCity}
        </if>
        <if test="g.beginDate != null">
            and register_date &gt;= #{g.beginDate}
        </if>
        <if test="g.endDate != null">
            and register_date &lt;= #{g.endDate}
        </if>

    </select>

</mapper>
