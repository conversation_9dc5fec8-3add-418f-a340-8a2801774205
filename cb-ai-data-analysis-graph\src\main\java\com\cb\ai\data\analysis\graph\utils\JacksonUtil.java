package com.cb.ai.data.analysis.graph.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * jackson 反序列化工具，用于修复及解析结构缺失/错误的json字符串
 *
 * <AUTHOR>
 */
@Slf4j
public class JacksonUtil {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        // 忽略未知字段
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略空Bean转JSON失败
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 容忍单引号、非标准JSON字段名
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        // 容忍尾随逗号
        mapper.configure(JsonParser.Feature.ALLOW_TRAILING_COMMA, true);
        // 容忍不闭合的数组或对象（部分修复）
        mapper.configure(JsonParser.Feature.ALLOW_MISSING_VALUES, true);
        // 容忍控制字符
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        // 容忍注释（如果你的输入可能含注释）
        mapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
    }

    /**
     * 安全解析 JSON 中的数组字段，允许部分元素损坏
     */
    /**
     * 安全解析 JSON 中指定字段的数组，内部元素解析失败时忽略
     *
     * @param jsonStr  原始 JSON 字符串
     * @param arrayKey 目标数组字段名称
     * @return 仅包含合法元素的 ArrayNode
     */
    public static ArrayNode extractSafeJsonArray(String jsonStr, String arrayKey) {
        ArrayNode result = mapper.createArrayNode();

        try {
            // 提取目标数组内容（字符串形式）
            Pattern pattern = Pattern.compile("\"" + arrayKey + "\"\\s*:\\s*\\[(.*?)\\]", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(jsonStr);
            if (!matcher.find()) {
                return result; // 没找到数组
            }

            String arrayContent = matcher.group(1);

            // 通过 } 分段，并尝试补齐 } 开头结尾
            String[] possibleObjects = arrayContent.split("(?<=\\})\\s*,");
            for (String objStr : possibleObjects) {
                objStr = objStr.trim();
                if (!objStr.startsWith("{")) objStr = "{" + objStr;
                if (!objStr.endsWith("}")) objStr = objStr + "}";

                try {
                    JsonNode node = mapper.readTree(objStr);
                    result.add(node);
                } catch (Exception e) {
                    log.warn("跳过非法 JSON 对象元素: {}", objStr);
                }
            }
        } catch (Exception e) {
            log.error("提取数组 [{}] 失败: {}", arrayKey, e.getMessage());
        }

        return result;
    }

    public static String repairJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return jsonStr;
        }

        String result = jsonStr;

        try {
            // 1. 清除 BOM、控制符、换行符
            result = result
                    .replace("\uFEFF", "")
                    .replaceAll("[\\u0000-\\u001F]", "")
                    .replaceAll("[\r\n\t]", " ");

            // 2. 去掉 JSON 数组或对象中多余的尾逗号
            result = result.replaceAll(",(\\s*[}\\]])", "$1");

            // 3. 替换数组元素之间漏掉的逗号：} {
            result = result.replaceAll("}(\\s*)\\{", "},$1{");

            // 4. 替换数组结束后紧接对象的错误结构
            result = result.replaceAll("](\\s*)\\{", "],$1{");

            // 5. 替换连续两个逗号为一个（可能是空字段）
            result = result.replaceAll(",\\s*,", ",");

            // 6. 补齐缺失的右括号
            int l1 = countChar(result, '{'), r1 = countChar(result, '}');
            int l2 = countChar(result, '['), r2 = countChar(result, ']');
            while (r1 < l1) result += "}";
            while (r2 < l2) result += "]";

            // 7. 修复 "key" "value" 形式，推断缺冒号（保守替换）
            result = result.replaceAll("\"(\\w+)\"\\s+\"([^\"]*)\"", "\"$1\":\"$2\"");

            // 兼容单双引号不闭合
            if (countChar(result, '"') % 2 != 0) {
                result += "\"";
            }

            // 智能补逗号：在对象或数组后若下一个字符是开始标志，则插入逗号
            result = result.replaceAll("([}\\]])\\s*([\\[{])", "$1,$2");

            result = result.trim();
        } catch (Exception e) {
            System.err.println("repairJson failed: " + e.getMessage());
            throw e;
        }

        return result;
    }

    private static int countChar(String str, char ch) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == ch) count++;
        }
        return count;
    }


    public static JsonNode parseToJsonNode(String jsonStr) throws Exception {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        String content = jsonStr;
        try {
            return mapper.readTree(content);
        } catch (Exception firstEx) {
            content = repairJson(content);
            try {
                return mapper.readTree(content);
            } catch (Exception secondEx) {
                log.error("repairJson and parse to JsonNode failed: {}", secondEx.getMessage());
                throw secondEx;
            }
        }
    }

    /**
     * 安全解析 JSON 字符串为 Java 对象，异常时返回 null
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz) throws Exception {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        String content = jsonStr;
        try {
            return mapper.readValue(content, clazz);
        } catch (Exception firstEx) {
            content = repairJson(content);
            try {
                return mapper.readValue(content, clazz);
            } catch (Exception secondEx) {
                log.error("repairJson and format json failed: {}", secondEx.getMessage());
                throw secondEx;
            }
        }
    }

    /**
     * JSON转为通用 JsonNode，可用于手动修复/遍历
     */
    public static JsonNode parseJsonNode(String jsonStr) {
        try {
            return mapper.readTree(jsonStr);
        } catch (IOException e) {
            log.warn("解析为JsonNode失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 对象转 JSON 字符串
     */
    public static String toJsonString(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("序列化失败：{}", e.getMessage());
            return null;
        }
    }
}