package com.cb.ai.data.analysis.voucher.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 凭证分析预警记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoucherAlertRecord extends BaseDomain {

    @TableId
    private String id;

    // 任务id，单文件分析时无值，任务分析时设置任务id
    private String taskId;

    // 凭证id
    private String voucherId;

    // 预警级别
    private String level;

    // 预警内容
    private String content;

}
