package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAudioAnalysis;
import com.cb.ai.data.analysis.ai.domain.common.FileMultipartFile;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化音频分析扩展
 */
@ExtensionProvider(desc = "私有化音频分析扩展", businessScenes = {
    @Route(tag = "音频分析", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PrivateAudioAnalysisExtension implements PrivateAIExtension<CommonAIRequestContext> {

    private final SuperviseResourceFileService fileService;

    @Override
    public Flux<ResultData<PrivateAIBackData>> invoke(CommonAIRequestContext requestContext) {
        // 1. 校验文件
        Assert.isFalse(requestContext == null || CollectionUtil.isEmpty(requestContext.getMinioFileDataList()), "参数（minioFileDataList）中没有待解析的音频文件");
        // 检查文件类型
        return Flux.fromIterable(requestContext.getMinioFileDataList())
            .flatMap(minioFileData -> {
                String filename = minioFileData.fileName();
                // 允许的音频后缀
                Assert.isFalse(filename == null || !filename.matches("(?i).+\\.(wav|mp3|m4a|flac|ogg|amr)$"), "音频文件格式错误");
                Assert.notBlank(minioFileData.fileId(), "未传入音频文件的minio库的Id");
                InputStream fileStream = fileService.getFileStream(minioFileData.fileId());
                CommonAIRequestContext newContext = requestContext.copy();
                newContext.setFileData(MultiFileData.of(filename, new FileMultipartFile(filename, fileStream)));
                return new PrivateAudioAnalysis().processData(newContext);
            });
    }

}
