package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsReportEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsWorkFlowJobEntity;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsReportPageQueryQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.WorkFlowReportResponseDto;
import com.cb.ai.data.analysis.petition.enums.SsReportStatusEnum;
import com.cb.ai.data.analysis.petition.enums.WorkFlowJobStatusEnum;
import com.cb.ai.data.analysis.petition.enums.WorkFlowTypeEnum;
import com.cb.ai.data.analysis.petition.mapper.SsReportMapper;
import com.cb.ai.data.analysis.petition.service.*;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.properties.FileProperties;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * 信访报告
 */
@Slf4j
@Service
public class SsReportServiceImpl extends BaseServiceImpl<SsReportMapper, SsReportEntity> implements SsReportService {

    @Autowired
    private SsReportMapper ssReportMapper;

    @Autowired
    private SsWorkFlowJobService ssWorkFlowJobService;

    @Autowired
    private SsPetitionAnalyzedService ssPetitionAnalyzedService;


    @Autowired
    private ChatService chatService;

    @Autowired
    private FileProperties fileProperties;

    @Override
    public Page<SsReportEntity> selectByPage(SsReportPageQueryQueryVo pageVo) {
        Page<SsReportEntity> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<SsReportEntity> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(pageVo.getFileName())){
            queryWrapper.like("file_name",pageVo.getFileName());
        }
        queryWrapper.orderByDesc("create_time");
        return ssReportMapper.selectPage(page, queryWrapper);
    }

    @Override
    public void download(String reportId, HttpServletResponse response) {
        SsReportEntity ssReportEntity = ssReportMapper.selectById(reportId);
        if (ssReportEntity == null || ssReportEntity.getFileUrl() == null) {
            throw new RuntimeException("文件不存在或路径错误");
        }
        File file = new File(ssReportEntity.getFileUrl());
        if (!file.exists()) {
            throw new RuntimeException("文件未找到：" + ssReportEntity.getFileUrl());
        }
        try (FileInputStream inputStream = new FileInputStream(file);
             BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
             ServletOutputStream outputStream = response.getOutputStream()) {

            // 设置响应头，支持文件下载
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            String encodedFileName = URLEncoder.encode(ssReportEntity.getFileName(), "UTF-8").replaceAll("\\+", "%20"); // 处理空格编码兼容性
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
            response.setContentLengthLong(file.length());
            // 读取并写入输出流
            byte[] buffer = new byte[1024 * 8];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Result submitGenerate(List<MultipartFile> files, String startDate, String endDate, String filename, List<String> types, String previousReportData, String city, String district, Integer relateRepetitiveSize) throws IOException {
        try {
            List<byte[]> fileWithBytes = new ArrayList<>();
            for (MultipartFile file : files) {
                fileWithBytes.add(file.getBytes());
            }

            if (org.springframework.util.StringUtils.hasText(previousReportData)) {
                template(previousReportData, filename, fileWithBytes, city, district, null);
                return Result.success("报告生成成功，请到报告列表查看。");
            } else {
                // 1. 查询数据库数据
                List<SsPetitionAnalyzedEntity> ssPetitionVos = ssPetitionAnalyzedService.selectByDateRange(startDate, endDate, types, city, district, relateRepetitiveSize);
                if (CollectionUtils.isEmpty(ssPetitionVos)) {
                    return Result.fail("所选时间范围内不存在工单!");
                }

                //异步提交生成任务的操作
                new Thread(() -> {
                    SsWorkFlowJobEntity ssWorkFlowJobEntity = ssWorkFlowJobService.publish(WorkFlowTypeEnum.GENERATE_REPORT);
                    String id = IdUtil.getSnowflakeNextIdStr();
                    SsReportEntity ssReportEntity = new SsReportEntity();
                    ssReportEntity.setId(id);
                    ssReportEntity.setFileName(filename);
                    ssReportEntity.setStatus(SsReportStatusEnum.GENERATING.getStatus());
                    ssReportEntity.setCreateTime(LocalDateTime.now());

                    ssReportMapper.insert(ssReportEntity);
                    generate(ssPetitionVos, filename, ssWorkFlowJobEntity, fileWithBytes, city, district, id);
                }).start();
                return Result.success("提交生成任务成功！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("提交生成报告任务异常：{}", e.getMessage());
            return Result.success("提交生成任务成功！");
        }
    }

    private void template(String message, String filename, List<byte[]> fileWithBytes, String city, String district, String reportId) throws IOException {
        WorkFlowReportResponseDto responseDto = JSON.parseObject(message, WorkFlowReportResponseDto.class);

        String title;
        if (org.springframework.util.StringUtils.hasText(district)) {
            title = district;
        } else if (org.springframework.util.StringUtils.hasText(city)) {
            title = city;
        } else {
            title = "云南省";
        }


        // 2. 准备数据映射
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("summary", responseDto.getSummary());
        dataMap.put("domainStatics", responseDto.getDomainStatics());
        dataMap.put("regionStatics", responseDto.getRegionStatics());
        dataMap.put("majorProblem", responseDto.getMajorProblem() != null ? responseDto.getMajorProblem() : "无数据");
        dataMap.put("suggestion", responseDto.getSuggestion() != null ? responseDto.getSuggestion() : "无数据");
        dataMap.put("region", title);

        // 3. 处理图片
        if (fileWithBytes != null && fileWithBytes.size() > 0) {
            dataMap.put("domainImage", Pictures.ofBytes(fileWithBytes.get(0), PictureType.JPEG).size(300, 200)
                    .fitSize().create());
            dataMap.put("regionImage", Pictures.ofBytes(fileWithBytes.get(1), PictureType.JPEG).size(300, 200)
                    .fitSize().create());
        } else {
            dataMap.put("domainImage", "无图片");
            dataMap.put("regionImage", "无图片");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");


        String profile=fileProperties.getRootDir();
        // 4. 生成报告
        String outputPath = Paths.get(profile + File.separator + sdf.format(new Date())) + File.separator;

//        String outputPath = profile + sdf.format(new Date()) + File.separator;

        File outputDir = new File(outputPath);
        if (!outputDir.exists()) outputDir.mkdirs();
        InputStream is = SsReportServiceImpl.class.getClassLoader()
                .getResourceAsStream("templates/report_template.docx");

        XWPFTemplate template = XWPFTemplate.compile(is).render(dataMap);

        String fileFullPath = outputPath + filename + ".docx";

        template.writeAndClose(Files.newOutputStream(new File(fileFullPath).toPath()));
        SsReportEntity ssReportEntity = new SsReportEntity();
        ssReportEntity.setFileUrl(fileFullPath);
        ssReportEntity.setStatus(SsReportStatusEnum.GENERATED.getStatus());
        ssReportEntity.setFinishTime(LocalDateTime.now());
        if (reportId == null) {
            ssReportEntity.setId(IdUtil.getSnowflakeNextIdStr());
            ssReportEntity.setCreateTime(LocalDateTime.now());
            ssReportMapper.insert(ssReportEntity);
        } else {
            ssReportEntity.setId(reportId);
            ssReportMapper.updateById(ssReportEntity);
        }

    }


    public void generate(List<SsPetitionAnalyzedEntity> ssPetitionAnalyzedVos,
                         String filename,
                         SsWorkFlowJobEntity ssWorkFlowJobEntity,
                         List<byte[]> fileWithBytes,
                         String city, String district, String reportId) {

        String message = "";
        try {
            String prompt=this.getAnalysisPromote();
            Object data=ssPetitionAnalyzedVos.stream().map(SsPetitionAnalyzedEntity::getBrief).collect(Collectors.toList());
            String respMsg=chatService.chat(prompt,data.toString());
            message=replaceCharacter(respMsg);
            //message = workFlowRequestService.singleRequest(ssPetitionAnalyzedVos.stream().map(SsPetitionAnalyzedEntity::getBrief).collect(Collectors.toList()), workFlowProperties.getGenerateReportId());
            ssWorkFlowJobEntity.setFinishTime(new Date());

            if (!org.springframework.util.StringUtils.hasText(message)) {
                throw new RuntimeException("AI大模型未返回数据");
            }
            ssWorkFlowJobEntity.setWorkFlowResponse(message);
            ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_SUCCESS.getStatus());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用AI流程接口出现异常：{}", e.getMessage());
            ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_FAILED.getStatus());
            ssWorkFlowJobEntity.setErrorMessage(e.getMessage());
            fail(reportId);
        }
        ssWorkFlowJobService.updateById(ssWorkFlowJobEntity);

        try {
            template(message, filename, fileWithBytes, city, district, reportId);
        } catch (Exception e) {
            fail(reportId);
            e.printStackTrace();
        }

    }
    private void fail(String reportId) {
        if (reportId != null) {
            SsReportEntity ssReportEntity = ssReportMapper.selectById(reportId);
            ssReportEntity.setFinishTime(LocalDateTime.now());
            ssReportEntity.setStatus(SsReportStatusEnum.GENERATE_FAILED.getStatus());
            ssReportMapper.updateById(ssReportEntity);
        }
    }

    private String getAnalysisPromote() throws IOException {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("promote/report.md");
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append(System.lineSeparator());
        }
        return content.toString();
    }

    private String replaceCharacter(String message) {
        try {
            JSONObject jsonObject=JSONObject.parseObject(message,JSONObject.class);
            JSONObject choicesObject=jsonObject.getJSONArray("choices").getJSONObject(0);
            JSONObject messageObject=choicesObject.getJSONObject("message");
            String content=messageObject.getString("content");
            Integer firstCharacterIndex = content.indexOf("{");
            Integer lastCharacterIndex = content.lastIndexOf("}");
            content = content.substring(firstCharacterIndex, lastCharacterIndex + 1);
            return content;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
