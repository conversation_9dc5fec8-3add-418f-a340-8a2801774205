package com.cb.ai.data.analysis.ai.component.flows.chuangbo;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.xong.boot.common.exception.CustomException;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/16 10:31
 * @Copyright (c) 2025
 * @Description 私有化音频上传加分析
 */
public class PrivateAudioAnalysis extends BasePrivateAiPropertiesNode {

    @Override
    public String getNodeName() {
        return "私有化-音频分析-接口";
    }

    @Override
    public void setRequestHeader(HttpHeaders headers) {
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    }

    @Override
    public String setRequestUrl() {
        return MergeUtil.mergePath(aiProp.getAudioAnalysis().getBaseUrl(), aiProp.getAudioAnalysis().getInvokeUrl());
    }

    @Override
    public Flux<PrivateAIBackData> process(CommonAIRequestContext context) {
        // 检查文件
        Assert.isFalse(context == null || context.getFileData() == null, "参数（fileData）中没有待解析的音频文件");
        return Flux.fromIterable(context.getFileData().entrySet())
            .flatMap(entry -> {
                List<MultipartFile> fileList = entry.getValue();
                // 检查文件类型和数量
                Assert.notEmpty(fileList, "音频解析文件为空");
                Assert.isFalse(fileList.size() > 1, "音频解析限制一个文件名对应一个文件");

                MultipartFile audioFile = fileList.get(0);
                Assert.isTrue(audioFile.getOriginalFilename() != null &&
                    audioFile.getOriginalFilename().matches("(?i).+\\.(wav|mp3|m4a|flac|ogg|amr)$"),
    "音频文件格式错误"
                );

                String audioUrl = setRequestUrl();
                WebClient webClient = getWebClient();
                MultiValueMap<String, Object> fileBody = CommonUtil.convertMultiDataToResourceData("file", audioFile);
                fileBody.set("query", context.getPromote());
                // 1. 提交任务
                TaskInfo info = webClient.method(HttpMethod.POST)
                    .uri(audioUrl)
                    .headers(this::setRequestHeader)
                    .body(BodyInserters.fromMultipartData(fileBody))
                    .retrieve()
                    .bodyToMono(TaskInfo.class)
                    .onErrorResume(e -> {
                        throw new CustomException(getNodeName() + "(" + audioUrl + ")调用失败，原因：" + e.getMessage(), e);
                    })
                    .block();

                Assert.notNull(info, getNodeName() + "(" + audioUrl + ")调用失败，原因：返回的任务信息为空");
                String taskId = info.getTaskId();

                // 2. 轮询任务状态
                return Flux.interval(Duration.ofMillis(2500))  // 每2.5秒轮询一次
                    .flatMap(tick -> {
                        String statusUrl = MergeUtil.mergePath(
                            aiProp.getAudioAnalysis().getBaseUrl(),
                            aiProp.getAudioAnalysis().getTaskStatusUrl().replace("{task_id}", taskId)
                        );
                        return webClient.get()
                            .uri(statusUrl)
                            .headers(this::setRequestHeader)
                            .retrieve()
                            .bodyToMono(TaskInfo.class)
                            .onErrorResume(e -> Mono.error(new CustomException(getNodeName() + "获取任务状态失败，原因：" + e.getMessage(), e)));
                    })
                    .takeUntil(taskInfo -> "complete".equalsIgnoreCase(taskInfo.getStatus()) || "failed".equalsIgnoreCase(taskInfo.getStatus()))
                    .concatMap(taskInfo -> {
                        if ("complete".equalsIgnoreCase(taskInfo.getStatus())) {
                            if (taskInfo.getResult() != null) {
                                return Mono.just(buildBackData(context, taskInfo));
                            } else {
                                // 获取最终结果
                                String resultUrl = MergeUtil.mergePath(
                                    aiProp.getAudioAnalysis().getBaseUrl(),
                                    aiProp.getAudioAnalysis().getTaskResultUrl().replace("{task_id}", taskId)
                                );
                                return webClient.get()
                                    .uri(resultUrl)
                                    .headers(this::setRequestHeader)
                                    .retrieve()
                                    .bodyToMono(TaskInfo.class)
                                    .map(taskResult -> buildBackData(context, taskInfo))
                                    .onErrorResume(e -> Mono.error(new CustomException(getNodeName() + "获取任务结果失败，原因：" + e.getMessage(), e)));
                            }
                        } else if ("failed".equalsIgnoreCase(taskInfo.getStatus())) {
                            return Mono.error(new CustomException(getNodeName() + "任务处理失败，原因: " + taskInfo.getErrorMessage()));
                        } else {
                            return Mono.just(buildBackData(context, taskInfo));
                        }
                    }).doFinally(signalType -> returnWebClient(webClient));
            });
    }

    private PrivateAIBackData buildBackData(CommonAIRequestContext context, TaskInfo taskInfo) {
        return new PrivateAIBackData(
            context.getSessionId(),
            RoleEnum.assistant,
            null,
            null,
            ObjectUtil.isEmpty(taskInfo.getResult()) ? JsonMap.of(TaskInfo::getProgress, taskInfo::getProgress) : new JsonMap(taskInfo.getResult()),
            JsonUtil.toStr(taskInfo)
        );
    }

    @Getter
    @Setter
    private static class TaskInfo {
        /**
         * 任务id
         */
        @JsonAlias("task_id")
        private String taskId;
        /**
         * 任务状态
         */
        private String status;
        /**
         * 任务创建时间
         */
        @JsonAlias("created_at")
        private String createdAt;
        /**
         * 任务更新时间
         */
        @JsonAlias("created_at")
        private String updatedAt;
        /**
         * 任务进度
         */
        private int progress;
        /**
         * 任务结果
         */
        private Map<String, Object> result;
        /**
         * 任务错误信息
         */
        @JsonAlias("error_message")
        private String errorMessage;

    }

}
