<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsRepeatResultGroupMapper">

    <resultMap type="com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup" id="SsRepeatResultGroupMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="petitionPurposeCategory" column="petition_purpose_category" jdbcType="VARCHAR"/>
        <result property="petitionPurpose" column="petition_purpose" jdbcType="VARCHAR"/>
        <result property="petitionDomainCategory" column="petition_domain_category" jdbcType="VARCHAR"/>
        <result property="petitionDomain" column="petition_domain" jdbcType="VARCHAR"/>
        <result property="petitionProvince" column="petition_province" jdbcType="VARCHAR"/>
        <result property="petitionCity" column="petition_city" jdbcType="VARCHAR"/>
        <result property="brief" column="brief" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.task_id, t.petition_purpose_category, t.petition_purpose, t.petition_domain_category, t.petition_domain, t.petition_province, t.petition_city,
            t.brief, t.remark, t.create_by, t.create_time, t.update_by, t.update_time
            , ( select count(*) from ss_repeat_result_item ri where ri.group_id = t.id) as item_count
    </sql>

    <select id="pageByWrapper" resultMap="SsRepeatResultGroupMap">
        select
        <include refid="Base_Column_List"/>
        from ss_repeat_result_group t
        ${ew.customSqlSegment}
        order by item_count desc
    </select>

</mapper>