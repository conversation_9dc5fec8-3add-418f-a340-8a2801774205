package com.cb.ai.data.json.tool.domain;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 报表数据
 *
 * <AUTHOR>
 * 2025/7/31
 */
@Data
@NoArgsConstructor
public class ReportContextData {
    /**
     * 是否为纵向报表（无用字段）
     */
    private Boolean isVertical;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 报表列配置信息
     */
    private LinkedList<ReportConfig> columns;
    /**
     * 列头信息
     */
    private Map<String, String> headerMap;

    /**
     * 报表数据内容
     */
    private List<LinkedHashMap<Integer, Object>> data;

    public ReportContextData(Boolean isVertical, LinkedList<ReportConfig> columns) {
        this.isVertical = isVertical;
        this.columns = columns;
    }

    public ReportContextData(Boolean isVertical, String fileName, LinkedList<ReportConfig> columns) {
        this.isVertical = isVertical;
        this.fileName = fileName;
        this.columns = columns;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReportConfig {
        // 下标
        private Integer index;
        // 标题
        private String title;
        // 数据字段
        private String dataIndex;
        // 插槽 （该字段对于抽取无效）
        private JSONObject scopedSlots;
    }
}
