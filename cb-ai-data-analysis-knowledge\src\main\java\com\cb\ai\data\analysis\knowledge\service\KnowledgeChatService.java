package com.cb.ai.data.analysis.knowledge.service;

import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeChat;
import com.xong.boot.common.api.Result;

/***
 * <AUTHOR>
 * 聊天
 */
public interface KnowledgeChatService {

    /***
     * 知识库问答
     */
    String knowledgeChat(KnowledgeChat knowledgeChat)throws Exception;

    /**
     * 基础问答
     * @param knowledgeChat
     * @return
     * @throws Exception
     */
    String memoryChat(KnowledgeChat knowledgeChat)throws Exception;


    /***
     * 知识库问答
     */
    Result chatKnowledge(KnowledgeChat knowledgeChat);

}
