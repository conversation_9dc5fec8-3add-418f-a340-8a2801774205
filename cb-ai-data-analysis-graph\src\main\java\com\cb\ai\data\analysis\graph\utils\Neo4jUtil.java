package com.cb.ai.data.analysis.graph.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.graph.domain.vo.RelationGraphVo;
import com.xong.boot.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.*;
import org.neo4j.driver.Record;
import org.neo4j.driver.internal.value.ListValue;
import org.neo4j.driver.internal.value.NodeValue;
import org.neo4j.driver.internal.value.PathValue;
import org.neo4j.driver.internal.value.RelationshipValue;
import org.neo4j.driver.types.Node;
import org.neo4j.driver.types.Path;
import org.neo4j.driver.types.Relationship;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

@Slf4j
@Component
public class Neo4jUtil {

    private final Driver driver;
    private Neo4jClient neo4jClient;

    @Autowired
    public Neo4jUtil(Driver driver) {
        this.driver = driver;
    }

    /**
     * 执行 Cypher 查询并返回结果
     */
    public RelationGraphVo.QueryResp query(String cypher, Map<String, Object> params) {
        long start = System.currentTimeMillis();
        log.info("查询开始");
        log.info("cypher语句：{}", cypher);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Result result = tx.run(cypher, params);
                long end = System.currentTimeMillis();
                log.info("查询耗时：{}ms", end - start);
                RelationGraphVo.QueryResp resp = buildSearchResp(result);
                tx.commit();
                long end1 = System.currentTimeMillis();
                log.info("返回结果构造耗时：{}ms", end1 - end);
                return resp;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 执行写操作（创建、更新、删除）
     */
    public long execute(String cypher, Map<String, Object> params) {
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Result result = tx.run(cypher, params);
                long count = result.consume().counters().nodesCreated() +
                        result.consume().counters().nodesDeleted() +
                        result.consume().counters().relationshipsCreated() +
                        result.consume().counters().relationshipsDeleted();
                tx.commit();
                return count;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 获取节点的当前标签
     *
     * @param id
     * @return
     */
    public List<String> getNodeLabels(String id) {
        Session session = driver.session();
        String cypher = "MATCH (n {id: $id}) RETURN labels(n) as labels";
        Map<String, Object> params = MapUtil.of("id", id);
        try {
            Transaction tx = session.beginTransaction();
            try {
                Result result = tx.run(cypher, params);
                Value value = result.single().get(0);
                if (value instanceof ListValue) {
                    return value.asList(Value::asString);
                } else {
                    return Collections.emptyList();
                }
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 纯纯的创建节点，不作任何合并
     *
     * @param label
     * @param node
     * @return prop的id
     */
    public String createNode(String label, Map<String, Object> node, String extraLabel) {
        String extraLabelSetStr = "";
        if (StringUtils.isNotBlank(extraLabel)) {
            extraLabelSetStr = "n:" + extraLabel + ",";
        }
        String cypher = String.format("CREATE (n:`%s`) " +
                "SET n = $item," + extraLabelSetStr + " n.id = randomUUID() " +
                "RETURN n.id", label);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("item", node);
                Result result = tx.run(cypher, params);
                String nodeId = result.single().get(0).asString();
                tx.commit();
                return nodeId;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 创建节点,支持合并条件
     *
     * @param label          节点标签
     * @param node           节点信息
     * @param mergeCondition 节点合并条件，默认为{name: $item.name}
     * @return prop的id
     */
    public String createNode(String label, Map<String, Object> node, String extraLabel, String mergeCondition) {
        if (StringUtils.isBlank(mergeCondition)) {
            mergeCondition = "{name: $item.name}";
        }
        if (StringUtils.isBlank(extraLabel)) {
            extraLabel = label;
        }
        String cypher = String.format(
                "MERGE (n:%s %s) " +
                        "ON CREATE SET n:%s, n = $item, n.id = randomUUID() " +
                        "ON MATCH SET n:%s, n += $item " +
                        "RETURN n.id", label, mergeCondition, extraLabel, extraLabel);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("item", node);
                Result result = tx.run(cypher, params);
                String nodeId = result.single().get(0).asString();
                tx.commit();
                return nodeId;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 创建或合并接单
     *
     * @param label          节点标签
     * @param node           节点信息
     * @param mergeCondition 多级匹配（多种匹配方式、任一匹配到就合并）合并条件，默认为{name: $item.name}
     * @return prop的id
     */
    public String createNodeMatchOrCreate(String label, Map<String, Object> node, String extraLabel, String[] mergeCondition) {
        if (null == mergeCondition || mergeCondition.length == 0) {
            mergeCondition = new String[]{"{name: $item.name}"};
        }
        if (StringUtils.isBlank(extraLabel)) {
            extraLabel = label;
        }
        String matchStr = "";
        for (int i = 0; i < mergeCondition.length; i++) {
            matchStr += "OPTIONAL MATCH (n" + (i + 1) + ":" + label + " " + mergeCondition[i] + ") ";
        }
        String nodeStr = IntStream.rangeClosed(1, mergeCondition.length)
                .mapToObj(i -> "n" + i)
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
        String withStr = "WITH coalesce(" + nodeStr + ") AS node, $item AS item ";
        String cypher = String.format("%s " +
                "%s " +
                "CALL { " +
                "  WITH node, item " +
                "  WITH node, item WHERE node IS NULL " +
                "  CREATE (newNode:%s) " +
                "  SET newNode:%s, newNode = item, newNode.id = randomUUID() " +
                "  RETURN newNode " +
                "  UNION " +
                "  WITH node, item " +
                "  WITH node, item WHERE node IS NOT NULL " +
                "  SET node:%s, node += item " +
                "  RETURN node AS newNode " +
                "} " +
                "RETURN newNode.id AS nodeId", matchStr, withStr, label, extraLabel, extraLabel);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("item", node);
                Result result = tx.run(cypher, params);
                String nodeId = result.single().get(0).asString();
                tx.commit();
                return nodeId;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 根据数据的属性匹配热度查询满足匹配度条件的节点
     * 匹配多项属性的节点score为累加
     *
     * @param label
     * @param node              节点数据
     * @param matchPropAndScore 用于匹配的属性值及score的字符串数组,每项代表使用的属性名和对应的热度分值, 由英文冒号拼接
     *                          示例：{"身份证号:1.0","手机号:0.8","name:0.4","工作单位:0.4"}
     * @param passScore         及格线，超过多少分的记录才返回 示例: 0.6
     * @return
     */
    public List<RelationGraphVo.ScoreNodeMatchItem> getPropScoreMatchNodeList(String label, Map<String, Object> node, String[] matchPropAndScore, String passScore) {
        if (StringUtils.isBlank(passScore) || ObjectUtil.isEmpty(matchPropAndScore)) {
            throw new IllegalArgumentException("请指定匹配的属性值(含分值)和及格分数");
        }
        String matchPattern = StringUtils.isNotBlank(label) ? String.format("n:`%s` ", label) : "n";
        // 组装属性匹配度累加的计算字符串
        String scoreStr = String.join(" + ", Arrays.stream(matchPropAndScore).map(s -> {
            String[] split = s.split(":");
            String prop = split[0];
            String score = split[1];
            return String.format("CASE WHEN n.`%s` = $item.`%s` THEN %s ELSE 0 END ", prop, prop, score);
        }).collect(Collectors.toList()));

        String cypher = String.format("MATCH (%s) " +
                "WITH n,  " +
                "( %s ) as score  " +
                "WHERE score >= %s  " +
                "WITH n, score  " +
                "ORDER BY score DESC  " +
                "LIMIT 3  " +
                "return id(n) as id ,n.id as nodeId, n.name as nodeName, score", matchPattern, scoreStr, passScore);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("item", node);
                Result result = tx.run(cypher, params);
                RelationGraphVo.QueryResp queryResp = buildSearchResp(result);
                tx.commit();
                List<Map<String, Object>> extras = queryResp.getExtras();
                if (ObjectUtil.isEmpty(extras)) {
                    return Collections.emptyList();
                }
                return extras.stream().map(row -> {
                    Long id = MapUtil.getLong(row, "id");
                    String nodeId = MapUtil.getStr(row, "nodeId");
                    String nodeName = MapUtil.getStr(row, "nodeName");
                    Double score = MapUtil.getDouble(row, "score");
                    return new RelationGraphVo.ScoreNodeMatchItem(id, nodeId, nodeName, score);
                }).collect(Collectors.toList());
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 批量创建节点
     *
     * @param label          节点标签
     * @param nodes          节点信息集合
     * @param mergeCondition 节点的优先合并条件，比如先根据id来判断是否同一个人则{id: item.id},没有匹配到则会根据name属性匹配
     *                       不设置默认使用name属性匹配 {name: item.name}
     * @return prop的id集合
     */
    public List<String> batchCreateNodes(String label, List<Map<String, Object>> nodes, String mergeCondition) {
        if (null == nodes || nodes.isEmpty()) {
            return Collections.emptyList();
        }
        String cypher = "";
        if (StringUtils.isBlank(mergeCondition)) {
            cypher = String.format(
                    "UNWIND $props AS item " +
                            "MERGE (n:%s {name: item.name}) " +
                            "ON CREATE SET n = item, n.id = randomUUID() " +
                            "ON MATCH SET n += CASE WHEN n.type IS NOT NULL THEN item {.id, .*, type: n.type} ELSE item END, n.id = coalesce(n.id, randomUUID()) " +
                            "RETURN n.id", label);
        } else {
//            cypher = String.format(
//                    "UNWIND $props AS item " +
//                            "MERGE (n:%s %s) " +
//                            "ON CREATE SET n = item, n.id = randomUUID()  " +
//                            "ON MATCH SET n += CASE WHEN n.type IS NOT NULL THEN item {.id, .*, type: n.type} ELSE item END, n.id = coalesce(n.id, randomUUID())  " +
//                            "WITH item, n " +
//                            "WHERE n.id IS NULL " +
//                            "MERGE (m:%s {name: item.name}) " +
//                            "ON CREATE SET m = item, m.id = randomUUID()  " +
//                            "ON MATCH SET m += CASE WHEN m.type IS NOT NULL THEN item {.id, .*, type: m.type} ELSE item END, m.id = coalesce(m.id, randomUUID())  " +
//                            "RETURN COALESCE(id(n), id(m)) as nodeId",label,mergeCondition,label);
            cypher = String.format(
                    "UNWIND $props AS item " +
                            "OPTIONAL MATCH (n1:%s %s) " +
                            "WITH item, n1 " +
                            "OPTIONAL MATCH (n2:%s {name: item.name}) " +
                            "WHERE n1 IS NULL " +
                            "WITH item, COALESCE(n1, n2) AS n " +
                            "FOREACH (x IN CASE WHEN n IS NOT NULL THEN [1] ELSE [] END | " +
                            "    SET n += item, " +
//                            "        n.type = CASE WHEN n.type IS NOT NULL THEN n.type ELSE item.type END, " +
                            "        n.id = coalesce(n.id, randomUUID()) " +
                            ") " +
                            "WITH item, n " +
                            "WHERE n IS NULL " +
                            "CREATE (m:%s) " +
                            "SET m = item, " +
                            "    m.id = randomUUID() " +
                            "RETURN COALESCE(n.id, m.id) AS nodeId", label, mergeCondition, label, label);
        }

        Session session = driver.session();
        try {
            final int BATCH_SIZE = 1000;
            int totalSize = nodes.size();
            List<String> nodeIds = new ArrayList<>();
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                List<Map<String, Object>> batchList = nodes.subList(i, endIndex);
                Transaction tx = session.beginTransaction();
                try {
                    Map<String, Object> params = new HashMap<>();
                    params.put("props", batchList);
                    Result result = tx.run(cypher, params);
                    while (result.hasNext()) {
                        nodeIds.add(result.next().get(0).asString());
                    }
                    tx.commit();
                    log.info("批量创建【{}}】节点，已处理{}条数据,共{}条数据", label, endIndex, totalSize);
                } finally {
                    tx.close();
                }
            }
            return nodeIds;
        } finally {
            session.close();
        }
    }

    /**
     * 批量创建节点，如果节点存在则更新，不存在则创建
     *
     * @param label
     * @param nodes
     * @param mergeCondition 合并的条件，不满足则创建 比如: { name: item.name , `身份证号`: item.`身份证号`}
     * @return
     */
    public List<String> batchCreateNodesMatchOrCreate(String label, List<Map<String, Object>> nodes, String mergeCondition) {
        if (null == nodes || nodes.isEmpty()) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(mergeCondition)) {
            throw new RuntimeException("合并匹配条件不能为空");
        }
        String cypher = String.format(
                "UNWIND $props AS item " +
                        "MERGE (n:%s %s) " +
                        "ON CREATE SET n = item, n.id = randomUUID() " +
                        "ON MATCH SET n += CASE WHEN n.type IS NOT NULL THEN item {.id, .*, type: n.type} ELSE item END, n.id = coalesce(n.id, randomUUID()) " +
                        "RETURN n.id", label, mergeCondition);

        Session session = driver.session();
        try {
            final int BATCH_SIZE = 1000;
            int totalSize = nodes.size();
            List<String> nodeIds = new ArrayList<>();
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                List<Map<String, Object>> batchList = nodes.subList(i, endIndex);
                Transaction tx = session.beginTransaction();
                try {
                    Map<String, Object> params = new HashMap<>();
                    params.put("props", batchList);
                    Result result = tx.run(cypher, params);
                    while (result.hasNext()) {
                        nodeIds.add(result.next().get(0).asString());
                    }
                    tx.commit();
                    log.info("批量创建【{}】节点，已处理{}}条数据,共{}条数据", label, endIndex, totalSize);
                } finally {
                    tx.close();
                }
            }
            return nodeIds;
        } finally {
            session.close();
        }
    }

    /**
     * 删除指定属性值的节点
     *
     * @param label 节点标签
     * @param prop  过滤条件 name-value
     * @return 删除的节点数量
     */
    public long deleteNodesByProp(String label, Map<String, Object> prop) {
        if (prop.isEmpty()) {
            throw new IllegalArgumentException("过滤条件不能为空！");
        }
        String patternStr = prop.entrySet().stream()
                .map(entry -> String.format("n.`%s` = $prop.`%s`", entry.getKey(), entry.getKey()))
                .collect(Collectors.joining(" AND "));
        String cypher = String.format(
                "MATCH (n:%s) " +
                        "WHERE %s " +
                        "DETACH DELETE n " +
                        "RETURN count(n)",
                label, patternStr
        );

        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("prop", prop);
                Result result = tx.run(cypher, params);
                long deletedCount = result.single().get(0).asLong();
                tx.commit();
                return deletedCount;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    public long deleteNodeByPropId(String label, String propId) {
        String labelMatch = StringUtils.isBlank(label) ? "(n)" : String.format("(n:%s)", label);
        String cypher = String.format(
                "MATCH %s " +
                        "WHERE n.id = $id " +
                        "DETACH DELETE n " +
                        "RETURN count(n)", labelMatch);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("id", propId);
                Result result = tx.run(cypher, params);
                long deletedCount = result.single().get(0).asLong();
                tx.commit();
                return deletedCount;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 批量创建关系
     *
     * @param startNodeLabel 起始节点Label
     * @param endNodeLabel   截止节点Label
     * @param matchCondition 匹配条件 示例: n.name = item.startValue AND m.name = item.endValue
     * @param relationType
     * @param nodeMatchList
     * @param accurate       是否确切的关系(还是疑似的关系) 默认为疑似
     */
    public void batchCreateRelationships(
            String startNodeLabel,
            String endNodeLabel,
            String matchCondition,
            String relationType,
            List<Map<String, Object>> nodeMatchList,
            boolean accurate) {
        if (null == nodeMatchList || nodeMatchList.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(matchCondition)) {
            throw new IllegalArgumentException("匹配条件不能为空！");
        }
        nodeMatchList.forEach(item -> {
            Object props = item.get("props");
            Map<String, Object> propsMap;
            if (null == props) {
                propsMap = new LinkedHashMap<>();
            } else {
                propsMap = (Map<String, Object>) props;
            }
            propsMap.put("state", accurate ? "accurate" : "possible");
            item.putIfAbsent("props", propsMap);
        });
        // 没有指定节点类型时做兼容处理
        String startLabelPattern = StringUtils.isBlank(startNodeLabel) ? "" : ":" + startNodeLabel;
        String endLabelPattern = StringUtils.isBlank(endNodeLabel) ? "" : ":" + endNodeLabel;
        // (n:xxx), (m:xxx)
        String matchPattern = String.format("(n%s), (m%s)", startLabelPattern, endLabelPattern);
        String cypher = String.format(
                "UNWIND $rels AS item " +
                        "MATCH %s " +
                        "WHERE %s " +
                        "MERGE (n)-[r:`%s`]->(m) " +
                        "ON CREATE SET r = item.props " +
                        "ON MATCH SET r += item.props " +
                        "RETURN count(r)",
                matchPattern, matchCondition, relationType
        );

        Session session = driver.session();
        try {
            final int BATCH_SIZE = 1000;
            int totalSize = nodeMatchList.size();
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                List<Map<String, Object>> batchList = nodeMatchList.subList(i, endIndex);
                Transaction tx = session.beginTransaction();
                try {
                    Map<String, Object> params = new HashMap<>();
                    params.put("rels", batchList);
                    tx.run(cypher, params);
                    tx.commit();
                } catch (Exception e) {
                    tx.rollback();
                    throw e;
                } finally {
                    tx.close();
                }
                log.info("批量创建【{}】关系，已创建{}条数据，共{}条数据", relationType, endIndex, totalSize);
            }
        } finally {
            session.close();
        }
    }

    /**
     * 批量删除关系
     *
     * @param startNodeLabel 起始节点标签
     * @param endNodeLabel   结束节点标签
     * @param matchCondition 匹配条件 示例: n.name = item.startValue AND m.name = item.endValue
     * @param relationType   关系类型
     * @param relationships  关系列表，每个关系包含起始节点值和结束节点值
     * @return 删除的关系数量
     */
    public long batchDeleteRelationships(
            String startNodeLabel,
            String endNodeLabel,
            String matchCondition,
            String relationType,
            List<Map<String, Object>> relationships) {
        if (StringUtils.isBlank(matchCondition)) {
            throw new IllegalArgumentException("匹配条件不能为空！");
        }
        // 没有指定节点类型时做兼容处理
        String startLabelPattern = StringUtils.isNotBlank(startNodeLabel) ? "" : ":" + startNodeLabel;
        String endLabelPattern = StringUtils.isNotBlank(endNodeLabel) ? "" : ":" + endNodeLabel;
        String relationTypePattern = StringUtils.isNotBlank(relationType) ? "" : ":`" + relationType + "`";
        // (n:xxx)-[r:xxx]->(m:xxx)
        String matchPattern = String.format("(n%s)-[r%s]->(m%s)", startLabelPattern, relationTypePattern, endLabelPattern);
        String cypher = String.format(
                "UNWIND $rels AS item " +
                        "MATCH %s " +
                        "WHERE %s " +
                        "DELETE r " +
                        "RETURN count(r)",
                matchPattern, matchCondition
        );

        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("rels", relationships);
                Result result = tx.run(cypher, params);
                long deletedCount = result.single().get(0).asLong();
                tx.commit();
                return deletedCount;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 根据起止节点的属性id建立关系 （单个关系进行创建）
     *
     * @param startPropId   开始节点的属性id
     * @param endPropId     结束节点的属性id
     * @param relationLabel 关系label
     * @param relationProps 关系数据
     */
    public RelationGraphVo.QueryResp createRelationByPropId(String startPropId, String endPropId, String relationLabel, Map<String, Object> relationProps) {
        if (StringUtils.isBlank(startPropId) || StringUtils.isBlank(endPropId)) {
            log.error("startPropId(:{}) or endPropId(:{}) is null !", startPropId, endPropId);
            return null;
        }
        if (StringUtils.isBlank(relationLabel)) {
            throw new IllegalArgumentException("关系标签不能为空！");
        }
        if (null == relationProps) {
            relationProps = Collections.emptyMap();
        }
        String cypher = String.format("MATCH (n) WHERE n.id = '%s' " +
                "MATCH (m) WHERE m.id = '%s' " +
                "MERGE (n)-[r:%s]->(m) " +
                "SET r += $props " +
                "return n, r, m ", startPropId, endPropId, relationLabel);

        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("props", relationProps);
                Result result = tx.run(cypher, params);
                RelationGraphVo.QueryResp resp = buildSearchResp(result);
                tx.commit();
                return resp;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 根据属性id查询节点信息
     *
     * @param propId
     * @return
     */
    public Map<String, Object> queryNodeByPropId(String propId) {
        String cypher = String.format("MATCH (n) WHERE n.id = '%s' return n ", propId);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Result result = tx.run(cypher);
                RelationGraphVo.QueryResp resp = buildSearchResp(result);
                tx.commit();
                List<Map<String, Object>> nodes = resp.getNodes();
                if (!nodes.isEmpty()) {
                    return nodes.get(0);
                }
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
        return null;
    }

    /**
     * 合并节点
     *
     * @param keepId  要保持的节点id
     * @param mergeId 被合并的节点id
     */
    public void mergeNodeByPropId(String keepId, String mergeId) {
        Map<String, Object> keepNode = queryNodeByPropId(keepId);
        Map<String, Object> mergeNode = queryNodeByPropId(mergeId);

        List<String> keepLabels = (List<String>) keepNode.get("labels");
        List<String> mergeLabels = (List<String>) mergeNode.get("labels");
        keepNode.remove("labels");
        mergeNode.remove("labels");
        //被合并的节点移除id和name属性
        mergeNode.remove("id");
        mergeNode.remove("name");
        Set<String> labelsSet = new LinkedHashSet<String>();
        labelsSet.addAll(keepLabels);
        labelsSet.addAll(mergeLabels);
        List<String> labels = new ArrayList<>(labelsSet);

        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                // 1. 合并 label 与属性到 keep节点
                String cypher = String.format(
                        "MATCH (n), (m) WHERE n.id = $keepId AND m.id = $mergeId " +
                                "SET n:%s, n += $mergeNode", String.join(":", labels));
                Map<String, Object> params = new HashMap<>();
                params.put("keepId", keepId);
                params.put("mergeId", mergeId);
                params.put("mergeNode", mergeNode);
                tx.run(cypher, params);

                // 2. 查询所有出关系
                String outRelQuery = "MATCH (m)-[r]->(other) WHERE m.id = $mergeId RETURN id(r) as rid, type(r) as type, properties(r) as props, id(other) as otherId";
                List<Map<String, Object>> outRels = new ArrayList<>();
                Result outResult = tx.run(outRelQuery, Collections.singletonMap("mergeId", mergeId));
                while (outResult.hasNext()) {
                    Record rec = outResult.next();
                    Map<String, Object> relInfo = new HashMap<>();
                    relInfo.put("type", rec.get("type").asString());
                    relInfo.put("props", rec.get("props").asMap());
                    relInfo.put("otherId", rec.get("otherId").asLong());
                    outRels.add(relInfo);
                }
                // 3. 查询所有入关系
                String inRelQuery = "MATCH (other)-[r]->(m) WHERE m.id = $mergeId RETURN id(r) as rid, type(r) as type, properties(r) as props, id(other) as otherId";
                List<Map<String, Object>> inRels = new ArrayList<>();
                Result inResult = tx.run(inRelQuery, Collections.singletonMap("mergeId", mergeId));
                while (inResult.hasNext()) {
                    Record rec = inResult.next();
                    Map<String, Object> relInfo = new HashMap<>();
                    relInfo.put("type", rec.get("type").asString());
                    relInfo.put("props", rec.get("props").asMap());
                    relInfo.put("otherId", rec.get("otherId").asLong());
                    inRels.add(relInfo);
                }
                // 4. 创建新的出关系 (keep)->(other)
                for (Map<String, Object> rel : outRels) {
                    String relType = (String) rel.get("type");
                    Map<String, Object> relProps = (Map<String, Object>) rel.get("props");
                    Long otherId = (Long) rel.get("otherId");
                    String createCypher = String.format("MATCH (n), (other) WHERE n.id = $keepId AND id(other) = $otherId CREATE (n)-[r:%s]->(other) SET r = $props", relType);
                    Map<String, Object> createParams = new HashMap<>();
                    createParams.put("keepId", keepId);
                    createParams.put("otherId", otherId);
                    createParams.put("props", relProps);
                    tx.run(createCypher, createParams);
                }
                // 5. 创建新的入关系 (other)->(keep)
                for (Map<String, Object> rel : inRels) {
                    String relType = (String) rel.get("type");
                    Map<String, Object> relProps = (Map<String, Object>) rel.get("props");
                    Long otherId = (Long) rel.get("otherId");
                    String createCypher = String.format("MATCH (n), (other) WHERE n.id = $keepId AND id(other) = $otherId CREATE (other)-[r:%s]->(n) SET r = $props", relType);
                    Map<String, Object> createParams = new HashMap<>();
                    createParams.put("keepId", keepId);
                    createParams.put("otherId", otherId);
                    createParams.put("props", relProps);
                    tx.run(createCypher, createParams);
                }
                // 6. 删除合并节点和其所有关系
                String delNodeCypher = "MATCH (m) WHERE m.id = $mergeId DETACH DELETE m";
                tx.run(delNodeCypher, Collections.singletonMap("mergeId", mergeId));
                tx.commit();
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 创建全文索引
     *
     * @param indexName
     * @param label
     * @param props
     * @param force     是否删除已有的索引
     */
    public void createFullTextIndex(String indexName, String label, String[] props, boolean force) {
        Session session = driver.session();
        // 1. 检查索引是否存在
        String checkIndex = "SHOW INDEXES YIELD name, type WHERE name = '" + indexName + "' AND type = 'FULLTEXT' RETURN count(*) AS cnt";
        Result result = session.run(checkIndex);
        long count = result.single().get("cnt").asLong();
        // 2. 如果存在
        if (count > 0) {
            // 需要强制删除
            if (force) {
                String dropIndex = "DROP INDEX `" + indexName + "`";
                session.run(dropIndex);
                log.info("已删除原有全文索引：{}", indexName);
            } else {
                // 已有则不动
                return;
            }

        }
        // 3. 创建新的全文索引
        String createIndex = "CREATE FULLTEXT INDEX `" + indexName + "` FOR (n:`" + label + "`) " +
                "ON EACH [" + String.join(",", Arrays.stream(props).map(prop -> "n.`" + prop + "`").collect(Collectors.toList())) + "]";
        log.info("创建全文索引：{} \n 语句:{}", indexName, createIndex);
        session.run(createIndex);
        log.info("已创建新的全文索引：{}", indexName);
    }

    public RelationGraphVo.QueryResp searchByNameAndLayers(String name, int layers) {
        String pathPattern = layers <= 0 ?
                "(n)-[*]-(m)" :  // 不限制深度
                String.format("(n)-[*0..%d]-(m)", layers);  // 限制深度
        String cypher = String.format("MATCH (n) " +
                "WHERE n.name =~ '.*%s.*' " +
                "WITH n MATCH path=%s " +
                "RETURN n, m, relationships(path) AS r", name, pathPattern);
        Session session = driver.session();
        try {
            Transaction tx = session.beginTransaction();
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("name", name);
                Result result = tx.run(cypher, params);
                RelationGraphVo.QueryResp resp = buildSearchResp(result);
                tx.commit();
                return resp;
            } finally {
                tx.close();
            }
        } finally {
            session.close();
        }
    }

    /**
     * 根据查询构建返回数据
     *
     * @param result
     * @return
     */
    private RelationGraphVo.QueryResp buildSearchResp(Result result) {
        Map<Long, Node> nodeMap = new LinkedHashMap<>();// 节点信息Map
        Map<Long, Relationship> relMap = new LinkedHashMap<>(); // 关系信息Map
        List<Map<String, Object>> extraList = new LinkedList<>();// 普通属性数据
        while (result.hasNext()) {
            Record record = result.next();
            //处理普通属性（如统计数量、属性名等）
            Map<String, Object> extraRow = new LinkedHashMap<>();
            boolean hasExtra = false;
            for (String key : record.keys()) {
                Value value = record.get(key);
                if (value instanceof NodeValue || value instanceof RelationshipValue || value instanceof PathValue) {
                    // 收集节点和关系
                    handleRespValue(value, nodeMap, relMap);
                } else if (value instanceof ListValue) {
                    List<Object> colData = new ArrayList<>();
                    value.asList(o -> {
                        String typeName = o.type().name();
                        if ("RELATIONSHIP".equals(typeName) || "NODE".equals(typeName) || "PATH".equals(typeName)
                                || ("LIST OF ANY?".equals(typeName) || typeName.contains("LIST")) && !value.isEmpty()) {
                            handleRespValue(o, nodeMap, relMap);
                        } else {
                            colData.add(o.asString());
                        }
                        handleRespValue(o, nodeMap, relMap);
                        return o;
                    });
                    if (!colData.isEmpty()) {
                        extraRow.put(key, colData);
                    }
                } else {
                    hasExtra = true;
                    extraRow.put(key, value.asObject());
                }
            }
            if (hasExtra) {
                extraList.add(extraRow);
            }
        }
        Map<Long, String> originIdIdMap = new LinkedHashMap<>();// 节点的 neo4jId-属性idMap
        nodeMap.values().forEach(item -> {
            long id = item.id();
            Map<String, Object> props = item.asMap();
            originIdIdMap.put(id, props.get("id").toString());
        });

        List<Map<String, Object>> nodeList = nodeMap.values().stream().map(item -> {
            Map<String, Object> row = new LinkedHashMap<>();
            Map<String, Object> props = item.asMap();
            row.putAll(props);
            Iterable<String> labels = item.labels();
            Iterator<String> iterator = labels.iterator();
            if (iterator.hasNext()) {
                List<String> labelList = StreamSupport.stream(labels.spliterator(), false).collect(Collectors.toList());
                row.put("labels", labelList);
            }
            return row;
        }).collect(Collectors.toList());
        List<Map<String, Object>> relList = relMap.values().stream().map(item -> {
            long startNodeId = item.startNodeId();
            long endNodeId = item.endNodeId();
            String type = item.type();
            Map<String, Object> prop = item.asMap();
            Map<String, Object> row = new HashMap<>();
            row.put("source", originIdIdMap.get(startNodeId));
            row.put("target", originIdIdMap.get(endNodeId));
            row.put("type", type);
            row.put("data", prop);
            return row;
        }).collect(Collectors.toList());

        return new RelationGraphVo.QueryResp(nodeList, relList, extraList, null);
    }


    private void handleRespValue(Value value, Map<Long, Node> nodeMap, Map<Long, Relationship> relMap) {
        String typeName = value.type().name();
        if ("RELATIONSHIP".equals(typeName)) {
            handleRelationship(value, relMap);
        } else if ("NODE".equals(typeName)) {
            handleNode(value, nodeMap);
        } else if ("PATH".equals(typeName)) {
            handlePath(value, nodeMap, relMap);
        } else if (("LIST OF ANY?".equals(typeName) || typeName.contains("LIST")) && !value.isEmpty()) {
            value.asList(o -> {
                handleRespValue(o, nodeMap, relMap);
                return o;
            });
        }
    }

    /**
     * 处理返回的 Node
     *
     * @param value
     * @param nodeMap
     */
    private void handleNode(Value value, Map<Long, Node> nodeMap) {
        Node node = value.asNode();
        nodeMap.putIfAbsent(node.id(), node);
    }

    /**
     * 处理返回的 Relationship
     *
     * @param value
     * @param relMap
     */
    private void handleRelationship(Value value, Map<Long, Relationship> relMap) {
        Relationship relationship = value.asRelationship();
        relMap.putIfAbsent(relationship.id(), relationship);
    }

    /**
     * 处理返回的 Path
     *
     * @param value
     * @param nodeMap
     * @param relMap
     */
    private void handlePath(Value value, Map<Long, Node> nodeMap, Map<Long, Relationship> relMap) {
        Path path = value.asPath();
        path.nodes().forEach(node -> {
            nodeMap.putIfAbsent(node.id(), node);
        });
        path.relationships().forEach(relationship -> {
            relMap.putIfAbsent(relationship.id(), relationship);
        });
    }

    public void createOrUpdateRelation(
            String[] nodeLabels,
            String relationCondition,
            String relationPattern,
            String onCreateSet,
            String onMatchSet
    ) {
        if (nodeLabels == null || nodeLabels.length != 2) {
            log.error("nodeLabels length must be 2 to create relation!");
            return;
        }

        if (StringUtils.isEmpty(relationCondition) || !relationCondition.contains("=")) {
            log.error("relationCondition cannot be null and must contain '='");
            return;
        }

        if (StringUtils.isEmpty(relationPattern)) {
            log.error("relationPattern cannot be null");
            return;
        }

        StringBuilder cypher = new StringBuilder();
        cypher.append(String.format("MATCH (%s), (%s) ", nodeLabels[0], nodeLabels[1]));
        cypher.append("WHERE ").append(relationCondition).append(" ");
        cypher.append("MERGE ").append(relationPattern).append(" ");

        if (StringUtils.isNotEmpty(onCreateSet)) {
            cypher.append("ON CREATE SET ").append(onCreateSet).append(" ");
        }

        if (StringUtils.isNotEmpty(onMatchSet)) {
            cypher.append("ON MATCH SET ").append(onMatchSet).append(" ");
        }

        try (Session session = driver.session();
             Transaction tx = session.beginTransaction()) {

            tx.run(cypher.toString());
            tx.commit();

        } catch (Exception e) {
            log.error("Failed to create or update relation", e);
        }
    }
}
