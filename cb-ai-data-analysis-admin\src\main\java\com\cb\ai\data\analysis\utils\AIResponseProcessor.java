package com.cb.ai.data.analysis.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.resp.AIResponseDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class AIResponseProcessor {

    private  String reasoningContent;
    private  String content;
    private List<AIResponseDTO.SourceFile> fileList = new ArrayList<>();

    public void processChunk(Object object) throws Exception{
        JSONObject jsonObject=JSONObject.from(object);
        if(!ObjectUtils.isEmpty(jsonObject)&&jsonObject.containsKey("responseResult")){
            JSONObject responseResultObject=jsonObject.getJSONObject("responseResult");
            if(!ObjectUtils.isEmpty(responseResultObject)&&responseResultObject.containsKey("choices")){
                JSONArray choicesArray=responseResultObject.getJSONArray("choices");
                if(!ObjectUtils.isEmpty(choicesArray)&&choicesArray.size()>0){
                    JSONObject choicesObject=choicesArray.getJSONObject(0);
                    if(!ObjectUtils.isEmpty(choicesObject)&&choicesObject.containsKey("message")){
                        JSONObject messageObject=choicesObject.getJSONObject("message");
                        String reasoningContent=messageObject.getString("reasoning_content");
                        String content=messageObject.getString("content");
                        setContent(content);
                        setReasoningContent(reasoningContent);
                    }
                }
            }
        }
        if(!ObjectUtils.isEmpty(jsonObject)&&jsonObject.containsKey("referenceFile")){
            JSONArray choicesArray=jsonObject.getJSONArray("referenceFile");
            if (!ObjectUtils.isEmpty(choicesArray)&&choicesArray.size()>0) {
                for(int i=0;i<choicesArray.size();i++){
                    JSONObject json=choicesArray.getJSONObject(i);
                    AIResponseDTO.SourceFile file = new AIResponseDTO.SourceFile();
                    file.setFileId(json.getString("fileId"));
                    file.setFileName(json.getString("fileName"));
                    file.setIndex(json.getString("index"));
                    file.setFileUrl(json.getString("fileUrl"));
                    fileList.add(file);
                }
                setFileList(fileList);
            }
        }
    }

    private String contentFormat(String input) {
        if(StringUtils.isNotBlank(input)){
            return input.replaceAll("\\[\\^\\d+\\]", "");
        }else{
            return "";
        }
    }
}
