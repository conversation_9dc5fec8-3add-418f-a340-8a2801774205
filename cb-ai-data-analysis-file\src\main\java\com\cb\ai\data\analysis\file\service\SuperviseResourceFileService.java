package com.cb.ai.data.analysis.file.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.model.FileUploadInfo;
import com.cb.ai.data.analysis.file.model.UploadUrlsVO;
import com.xong.boot.common.service.BaseService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface SuperviseResourceFileService  extends BaseService<SuperviseResourceFile> {
//    SuperviseResourceFile uploadFile(MultipartFile file, String folderId, String fileTags) throws IOException;


    /**
     *
     * @param inputStream 输入流
     * @param md5  计算的md5的值，md5为空我就自己算
     * @param fileName 文件原始名称，带后缀的
     * @param folderName 文件夹名称，会创建在根目录下
     * @param fileSize 文件大小
     * @param contentType 文件类型
     * @param fileTags 文件标签，可以不传
     * @param userName 用户名
     * @return
     * @throws Exception
     */
    SuperviseResourceFile uploadFileStreamByFolderName(InputStream inputStream, String md5, String fileName, String folderName, Long fileSize,
                                                       String contentType, String fileTags,
                                                       String userName
    ) throws Exception;
    /**
     *
     * @param inputStream 输入流
     * @param md5  计算的md5的值，md5为空我就自己算
     * @param fileName 件原始名称，带后缀的
     * @param folderId 文件夹id
     * @param fileSize 文件大小
     * @param contentType 文件类型
     * @param fileTags 文件标签，可以不传
     * @param userName 用户名
     * @return
     * @throws Exception
     */
    SuperviseResourceFile uploadFileStreamByFolderId(InputStream inputStream, String md5, String fileName, String folderId, Long fileSize,
                                                     String contentType, String fileTags,
                                                     String userName
    ) throws Exception;
    /**
     * 根据文件id获取下载链接
     * @param fileId
     * @return
     */
    String getDownloadUrl(String fileId);
    /**
     * 获取文件输入流
     * @param fileId
     * @return
     */
    InputStream getFileStream(String fileId);

    /**
     * 文件上传
     * @param file
     * @param superviseResourceFolder
     * @param fileTags
     * @return
     * @throws IOException
     */
    SuperviseResourceFile uploadFile(MultipartFile file, SuperviseResourceFolder superviseResourceFolder,String md5, String fileTags) throws Exception;

    /**
     * 获取自己文件夹下面的文件名文件
     * @param fileName
     * @param folderId
     * @param userName
     * @return
     */
    SuperviseResourceFile getFileByFileNameAndFolderId(String fileName, String folderId, String userName);

    void downloadDirect(
            String fileId,
            HttpServletResponse response) throws IOException;

    /**
     * 获取已删除的文件列表-分页
     * @param page
     * @param superviseResourceFile
     * @return
     */
    Page<SuperviseResourceFile> pageDeletedFiles(Page<SuperviseResourceFile> page, SuperviseResourceFile superviseResourceFile);

    /**
     * 获取文件详情
     * @param id
     * @return
     */
    SuperviseResourceFile getFileDetail(String id);

    /**
     * 还原文件到文件夹
     * @param ids
     * @param folderId
     * @return
     */
    Integer restoreFile(List<String> ids,String folderId);

    /**
     * 更新文件的原路径
     * @param idList
     * @return
     */
    Boolean updateFilesOriginalPath(List<String> idList);

    /**
     * 获取文件列表
     * @param superviseResourceFile
     * @return
     */
    List<SuperviseResourceFile> getFileList(SuperviseResourceFile  superviseResourceFile);

    /**
     * 获取文件列表
     * @param queryWrapper
     * @return
     */
    List<SuperviseResourceFile> getFileList(LambdaQueryWrapper<SuperviseResourceFile> queryWrapper);

    /**
     *
     * @param ids
     * @return
     */
    List<SuperviseResourceFile> getFileListByIds(List<String> ids);

    /**
     * 永久删除
     * @param ids
     * @return
     */
    boolean permanentlyDelete(List<String> ids);



//    FileUploadInfo checkFileByMd5(String md5,String folderId);

    FileUploadInfo checkFileByMd5(String md5, String fileName, String userName, String folderId);

    UploadUrlsVO initMultipartUpload(FileUploadInfo fileUploadInfo);

    SuperviseResourceFile mergeMultipartUpload(String md5,String folderId,String fileName,String userName);

    byte[] downloadMultipartFile(Long id, HttpServletRequest request, HttpServletResponse response) throws Exception;

    String getDownloadUrlByFilePath(String filePath);


    InputStream getInputStream(String filePath);

}
