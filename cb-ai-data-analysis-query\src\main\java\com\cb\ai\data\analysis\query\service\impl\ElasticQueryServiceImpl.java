package com.cb.ai.data.analysis.query.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.ElasticsearchException;
import co.elastic.clients.elasticsearch.core.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;
import com.cb.ai.data.analysis.query.domain.vo.DbTableVo;
import com.cb.ai.data.analysis.query.domain.vo.ElasticRespVo;
import com.cb.ai.data.analysis.query.enums.DataTypeEnum;
import com.cb.ai.data.analysis.query.service.DbTableService;
import com.cb.ai.data.analysis.query.service.ElasticQueryService;
import com.cb.ai.data.analysis.query.utils.ElasticQueryUtils;
import com.xong.boot.common.utils.ServletUtils;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.ResourceNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ElasticQueryServiceImpl implements ElasticQueryService {
    private final ElasticsearchClient restClient;

    @Autowired
    private SysUserService userService;
    @Autowired
    private DbTableService tableService;

    public ElasticQueryServiceImpl(ElasticsearchClient restClient) {
        this.restClient = restClient;
    }

    @Override
    public Page<ElasticRespVo.Resp> page(ElasticReqBo.Query query) {
        List<SysUser> userList = userService.selectAll();
        Map<String, String> userMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getUsername, e -> e.getRealname()));
        Integer pageNum = ServletUtils.getParameterToInt(QueryHelper.PARAM_NAME_PAGE_CURRENT);
        Integer pageSize = ServletUtils.getParameterToInt(QueryHelper.PARAM_NAME_PAGE_SIZE);
        // 确保分页参数有效
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0 || pageSize > ElasticQueryUtils.MAX_PAGE_SIZE) {
            pageSize = ElasticQueryUtils.DEFAULT_PAGE_SIZE;
        }
        // 构建查询并执行分页搜索
        Integer finalPageNum = pageNum;
        Integer finalPageSize = pageSize;
        try {
            long start = System.currentTimeMillis();
            // 方式一：SearchRequest.Builder 构建查询
            /*SearchResponse<Object> search = restClient.search(builder -> {
                // 分页查询
                ElasticQueryUtils.buildPageSearchBuilder(builder, query, finalPageNum, finalPageSize);
                // 高亮
                ElasticQueryUtils.highlight(builder);
                return builder;
            }, Object.class);*/

            //方法二：使用 SearchRequest.of(...) 显式构建查询
            SearchRequest searchRequest = SearchRequest.of(builder -> {
                // 分页查询
                ElasticQueryUtils.buildPageSearchBuilder(builder, query, finalPageNum, finalPageSize);
                // 高亮
                ElasticQueryUtils.highlight(builder,query);
                return builder;
            });
            log.info("Elasticsearch Query DSL: {}", searchRequest.toString()); // 打印 DSL 查询语句
            SearchResponse<Object> search = restClient.search(searchRequest, Object.class); //执行搜索
            long end = System.currentTimeMillis();
            log.info("Elasticsearch Query Time: {}ms", end - start);

            // 提取结果并封装为 Map，包含 source 和 highlight
            List<ElasticRespVo.Resp> records = search.hits().hits().stream()
                    .map(hit -> {
                        ElasticRespVo.Resp resp = new ElasticRespVo.Resp();
                        String index = hit.index();
                        resp.setIndexName(index);
                        String type = DataTypeEnum.ofType(index);
                        resp.setDataType(type);
                        Map<String,Object> source = (Map<String,Object>)hit.source();
                        Object createBy = source.get("createBy");
                        source.put("createUserName", userMap.get(createBy));
                        resp.setSource(source);
                        // 处理高亮字段
                        if (hit.highlight() != null && !hit.highlight().isEmpty()) {
                            Map<String, List<String>> highlights = new HashMap<>(hit.highlight());
                            resp.setHighlight(highlights);
                        }
                        // 设置表描述和字段描述
                        // 优化表信息获取逻辑，增加索引名有效性校验和异常处理
                        if (index != null && index.contains(Constant.SLICING)) {
                            try {
                                String[] split = index.split(Constant.SLICING, 2);
                                if (split.length == 2) {
                                    String tableName = split[1];
                                    DbTableVo dbTableVo = tableService.selectTableColumnList(type, tableName);
                                    if (dbTableVo != null) {
                                        resp.setTableComment(dbTableVo.getTableComment());
                                        resp.setColumns(dbTableVo.getColumns());
                                    } else {
                                        log.warn("未找到表信息，索引：{}，表名：{}", index, tableName);
                                    }
                                }
                            } catch (Exception e) {
                                log.error("解析索引表信息异常，索引：{}", index, e);
                            }
                        }
                        return resp;
                    })
                    .toList();
            Page<ElasticRespVo.Resp> page = new Page<>();
            page.setCurrent(pageNum);
            page.setSize(pageSize);
            page.setTotal(search.hits().total().value());
            page.setRecords(records);
            return page;
        } catch (IOException | ElasticsearchException e) {
            log.error("Elasticsearch 查询失败", e);
            throw new RuntimeException("Elasticsearch 查询异常", e);
        }
    }


    @Override
    public List<ElasticRespVo.Resp> list(ElasticReqBo.Query query) {
        try {
            return restClient.search(builder -> {
                ElasticQueryUtils.buildSearchBuilder(builder, query);
                return builder;
            }, Object.class).hits().hits().stream().map(hit -> {
                ElasticRespVo.Resp resp = new ElasticRespVo.Resp();
                resp.setIndexName(hit.index());
                resp.setDataType(DataTypeEnum.ofType(hit.index()));
                Object source = hit.source();
                if (source != null) {
                    resp.setSource(source);
                }
                return resp;
            }).toList();
        } catch (IOException | ElasticsearchException e) {
            log.error("Elasticsearch 列表查询失败", e);
            throw new RuntimeException("Elasticsearch 列表查询异常", e);
        }
    }

    @Override
    public Object getDocument(String index, String id) {
        Assert.hasLength(index, "Elasticsearch exception indexName null");
        Assert.hasLength(id, "Elasticsearch exception id null");
        try {
            GetResponse<Object> response = restClient.get(g -> g.index(index).id(id), Object.class);
            if (response.found()) {
                Object source = response.source();
                if (source == null) {
                    log.warn("Document found but source is null, index: {}, id: {}", index, id);
                    return null;
                }
                return source;
            } else {
                log.error("Document not found, index: {}, id: {}", index, id);
                throw new ResourceNotFoundException("文档未找到，索引：" + index + "，ID：" + id);
            }
        } catch (IOException | ElasticsearchException e) {
            log.error("Elasticsearch get document error, index: {}, id: {}", index, id, e);
            throw new RuntimeException("Elasticsearch 获取文档错误", e);
        }
    }

}
