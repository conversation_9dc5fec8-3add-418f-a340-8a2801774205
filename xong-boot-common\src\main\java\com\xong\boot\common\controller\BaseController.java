package com.xong.boot.common.controller;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * controller 顶类
 * <AUTHOR>
 */
public abstract class BaseController<S extends IService<T>, T> {
    protected S baseService;

    @Autowired(required = false)
    public void setBaseService(S baseService) {
        this.baseService = baseService;
    }

    protected S getBaseService() {
        return baseService;
    }


}
