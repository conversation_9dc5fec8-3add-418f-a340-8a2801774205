<template>
  <x-page-wrapper :hiddenTitle="true" class="batch-index">
    <a-card :bordered="false">
      <x-table-search :model="searchFormData" :tableRef="tableRef">
        <a-form-item label="文件名称" name="fileName">
          <a-input
            v-model:value="searchFormData.fileName"
            :maxlength="32"
            allow-clear
            placeholder="请输入文件名称"
          />
        </a-form-item>
        <a-form-item label="处理状态" name="handleStatus">
          <a-select
            v-model:value="searchFormData.handleStatus"
            :options="handleOption">
          </a-select>
        </a-form-item>
      </x-table-search>
      <x-table
        :sticky="false"
        :hideSerial="true"
        ref="tableRef"
        :columns="columns"
        :loadData="loadData"
        title="问题批处理列表"
        row-key="id">
        <template #toolbar="{ selectedRowKeys }">
          <a-button @click="openProblemMarket" type="primary" style="margin-right:10px;">
            提问矩阵管理
          </a-button>
          <a-button @click="downloadTemplate('batch')"
                    type="primary" style="margin-right:10px;">
            <template #icon>
              <x-icon type="DownloadOutlined" />
            </template>
            模板下载
          </a-button>
          <a-button @click="openImportFile"
                    type="primary" style="background-color: #102BAF;">
            <template #icon>
              <x-icon type="CloudUploadOutlined" />
            </template>
            导入问题清单
          </a-button>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'id'">
            {{ text }}
          </template>
          <template v-else-if="column.dataIndex === 'fileName'">
            {{ text }}
          </template>
          <template v-else-if="column.dataIndex === 'qaCount'">
            {{ text }}
          </template>
          <template v-else-if="column.dataIndex === 'handleProgress'">
            <a-progress :percent="computeProgress(record)"/>
          </template>
          <template v-else-if="column.dataIndex === 'handleStatus'">
            <a-tag :color="statusTagType(record.handleStatus)">{{statusText(record.handleStatus)}}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'createTime'">
            {{ $date.formatDateTime(text) }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-popconfirm @confirm="onClickDelete(record.id)" title="是否删除批处理问题清单？">
              <a>删除</a>
            </a-popconfirm>
            <a-popconfirm @confirm="onReAnalyze(record.id)" title="确定重新发起解析？">
              <a style="padding-left:5px;" v-if="[2,-1].includes(record.handleStatus)">重新解析</a>
            </a-popconfirm>
            <a style="padding-left:5px;" @click="openViewAnalysisChat(record.id,record.fileName)">查看解析结果</a>
          </template>
        </template>
      </x-table>
    </a-card>
    <FileImport v-if="folderId" v-model:visible="dialog.fileImportVisible" :folderId="folderId" @closeFileImport="closeFileImport" />
    <ViewAnalysisChat v-model:visible="dialog.viewAnalysisVisible" :problemId="dialog.problemId" :fileName="dialog.fileName" />
    <ProblemMarket v-model:visible="dialog.ProblemMarketVisible" @success="onSuccess" @downloadTemplate="downloadTemplate('domain')"/>
  </x-page-wrapper>
</template>
<script setup lang="ts" name="batchIndex">
import {type ComponentCustomProperties, getCurrentInstance, ref, computed} from 'vue'
import {problem} from "@/api/petition";
import FileImport from '@/views/petition/batch/FileImport.vue'
import ViewAnalysisChat from '@/views/petition/batch/ViewAnalysisChat.vue'
import ProblemMarket from '@/views/petition/batch/ProblemMarket.vue'
const _this = getCurrentInstance()?.proxy as ComponentCustomProperties


const dialog = ref({
  fileImportVisible:false,
  viewAnalysisVisible:false,
  ProblemMarketVisible:false,
  problemId:'',
  fileName:''
})
const folderId=ref()
const tableRef = ref()
const timer=ref();
const searchFormData=ref({
  fileName:'',
  handleStatus:undefined
})

const handleOption = ref([
  {
    value:0,
    label:"等待解析"
  },{
    value:1,
    label:"解析中"
  },{
    value:2,
    label:"解析完成"
  },{
    value:-1,
    label:"解析失败"
  }
])

const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'id',
      title: 'ID',
      width: 100,
      hidden:true
    },
    {
      dataIndex: 'fileName',
      title: '文件名',
      width: 100
    },
    {
      dataIndex: 'qaCount',
      title: '问题数量',
      width: 100
    },
    {
      dataIndex: 'handleProgress',
      title: '处理进度',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'handleStatus',
      title: '状态',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createTime',
      title: '上传时间',
      width: 100,
      align: 'center'
    }
  ] as TableColumn[]
  //if (_this.$auth.hasPermission()) {
    columnItems.push({
      key: 'actions',
      title: '操作',
      width: 150,
      align: 'center'
    })
  // }
  return columnItems
})

function openProblemMarket(){
  dialog.value.ProblemMarketVisible=true;
}
function openViewAnalysisChat(id,fileName){
  dialog.value.viewAnalysisVisible=true;
  dialog.value.problemId=id
  let removeExtensionFileName=removeExtension(fileName)
  dialog.value.fileName=removeExtensionFileName
}

function removeExtension(filename) {
  if(filename && filename.length>0){
    return filename?.replace(/\.[^/.]+$/, "");
  }else {
    return filename
  }
}
function onSuccess(isRefresh = false) {
  tableRef.value.refresh(isRefresh)
}
async function loadData(parameter: Record<string, any> | undefined) {
  let reqData={
    pageNo:parameter?.pageCurrent,
    pageSize:parameter?.pageSize,
    searchKey:searchFormData.value.fileName,
    handleStatus:searchFormData.value.handleStatus
  }
  let result=await problem.page(reqData);
  folderId.value=result.message
  const stillLoading = result?.data?.records?.some(item =>item.handleStatus === 1)
  await startPolling(stillLoading)
  return result;
}
async function onClickDelete(key){
  try {
    _this.$message.loading({ key, content: '删除中...', duration: 0 })
    const { message } = await problem.del(key)
    _this.$message.success({ key, content: message })
    onSuccess()
  } catch (error: any) {
    _this.$message.error({ key, content: error.message })
  }
}

async function onReAnalyze(key){
  try {
    _this.$message.loading({ key, content: '解析中...', duration: 0 })
    const { message } = await problem.reAnalyze(key)
    _this.$message.success({ key, content: message })
    onSuccess()
  } catch (error: any) {
    _this.$message.error({ key, content: error.message })
  }
}

async function downloadTemplate(templateType){
  const closeLoading = _this.$message.loading({content: '下载中...', duration: 0 })
  try {
    const {data} = await problem.downloadProblemBatchTemplate(templateType);
    const blob = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    let downloadName="AI滤网模板.xlsx"
    if(templateType === 'domain'){
      downloadName="提问矩阵模板.xlsx";
    }
    link.download = downloadName;
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    }, 100);
  } catch (error) {
    _this.$message.error('下载失败');
  }finally {
    closeLoading()
  }
}

function openImportFile(){
  dialog.value.fileImportVisible=true
}
function closeFileImport(){
  dialog.value.fileImportVisible=false
  onSuccess(true)
}

async function startPolling(stillLoading) {
  if(stillLoading){
    if(!timer.value){
      timer.value = setInterval(async () => {
        await listByIds()
      }, 3000)
    }
  } else {
    stopPolling()
  }
}
async function listByIds(){
  const resultArray = tableRef.value.localDataSource.filter(item => item.handleStatus === 1).map(item => item.id)
  if(resultArray.length>0){
    const {code,data} = await problem.listByIds(resultArray)
    if(code=== 200){
      tableRef.value.localDataSource.map(table => {
        data.map(d => {
          if(d.id === table.id && d.finishedQaCount && d.finishedQaCount>0){
            table.finishedQaCount=d.finishedQaCount;
          }
          if(d.id === table.id && d.handleStatus !== 1){
            table.handleStatus=d.handleStatus;
          }
        })
      })
    }
  }else{
    stopPolling()
  }
}
function stopPolling() {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

function computeProgress(row){
  if (!row.qaCount || row.qaCount === 0) return 0;
  const progress = Math.round((row.finishedQaCount / row.qaCount) * 100);
  return progress>0 ? progress : 0;
}
function statusTagType(status){
  switch (status) {
    case 0:
      return 'pink';
    case 1:
      return '#2db7f5';
    case 2:
      return '#87d068';
    case -1:
      return '#f50';
    default:
      return '';
  }
}
function statusText(status){
  switch (status) {
    case 0:
      return '等待解析';
    case 1:
      return '解析中';
    case 2:
      return '解析完成';
    case -1:
      return '解析失败';
    default:
      return '未知状态';
  }
}
</script>

