package com.cb.ai.data.analysis.basdata.service.finance.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasInvoiceInfo;
import com.cb.ai.data.analysis.basdata.mapper.finance.BasInvoiceInfoMapper;
import com.cb.ai.data.analysis.basdata.repository.finance.BasInvoiceInfoRepository;
import com.cb.ai.data.analysis.basdata.repository.finance.esBo.BasInvoiceInfoBo;
import com.cb.ai.data.analysis.basdata.service.finance.BasInvoiceInfoService;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票信息表(BasInvoiceInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:59:36
 */
@Service("basInvoiceInfoService")
public class BasInvoiceInfoServiceImpl extends ServiceImpl<BasInvoiceInfoMapper, BasInvoiceInfo> implements BasInvoiceInfoService {

    @Autowired
    private BasInvoiceInfoRepository repository;

    @Override
    public boolean save(BasInvoiceInfo info) {
        int insert = baseMapper.insert(info);
        if (insert > 0) {
            BasInvoiceInfoBo bo = convert2To(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateById(BasInvoiceInfo info) {
        int update = baseMapper.updateById(info);
        if (update > 0) {
            BasInvoiceInfoBo bo = convert2To(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<BasInvoiceInfo> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            List<BasInvoiceInfoBo> collect = list.stream().
                    map(item -> convert2To(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }

    private BasInvoiceInfoBo convert2To(BasInvoiceInfo invoiceInfo) {
        BasInvoiceInfoBo invoiceInfoBo = new BasInvoiceInfoBo();
        BeanUtils.copyProperties(invoiceInfo, invoiceInfoBo);
        invoiceInfoBo.setDeptId(SecurityUtils.getDeptId());
        invoiceInfoBo.setDistrictId(SecurityUtils.getDistrictId());
        return invoiceInfoBo;
    }
}

