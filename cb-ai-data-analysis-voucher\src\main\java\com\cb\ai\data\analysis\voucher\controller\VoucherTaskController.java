package com.cb.ai.data.analysis.voucher.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTask;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherTaskVo;
import com.cb.ai.data.analysis.voucher.service.VoucherTaskService;
import com.cb.ai.data.analysis.voucher.utils.task.TaskHelper;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分析任务
 */
@Validated
@RestController
@RequestMapping(Constants.API_VOUCHER_ROOT_PATH + "/task")
public class VoucherTaskController extends BaseController<VoucherTaskService, VoucherTask> {

    @GetMapping("/page")
    public Result page(VoucherTaskVo.PageReq entity){
        Wrapper<VoucherTask> wrapper = buildPageWrapper(entity);
        Page<VoucherTask> page = baseService.page(QueryHelper.getPage(), wrapper);
        extShowNum(page.getRecords());
        return Result.successData(page);
    }

    @GetMapping("/pageBySelf")
    public Result pageBySelf(VoucherTaskVo.PageReq entity){
        String username = SecurityUtils.getUsername();
        entity.setCreateBy(username);
        Wrapper<VoucherTask> wrapper = buildPageWrapper(entity);
        Page<VoucherTask> page = baseService.page(QueryHelper.getPage(), wrapper);
        extShowNum(page.getRecords());
        return Result.successData(page);
    }

    private Wrapper<VoucherTask> buildPageWrapper(VoucherTaskVo.PageReq req) {
        LambdaQueryWrapper<VoucherTask> wrapper = Wrappers.<VoucherTask>lambdaQuery()
                .eq(StringUtils.isNotBlank(req.getId()), VoucherTask::getId, req.getId())
                .like(StringUtils.isNotBlank(req.getName()), VoucherTask::getName, req.getName())
                .eq(ObjectUtil.isNotNull(req.getStatus()), VoucherTask::getStatus, req.getStatus())
                .eq(StringUtils.isNotBlank(req.getCreateBy()),  VoucherTask::getCreateBy, req.getCreateBy())
                .orderByDesc(VoucherTask::getCreateTime);
        return wrapper;
    }

    /**
     * 处理中的数量
     * @param list
     */
    private void extShowNum(List<VoucherTask> list){
        list.forEach(task->{
            if(Integer.valueOf(1).equals(task.getStatus())){
                TaskHelper.TaskCounter counter = TaskHelper.getCounter(task.getId());
                if(null != counter){
                    task.setHandleNum(counter.getHandleNum());
                    task.setAlertNum(counter.getAlertNum());
                }
            }
        });
    }

    @PostMapping()
    @XLog(title = "新增分析任务", execType = ExecType.INSERT)
    public Result add(@Validated @RequestBody VoucherTask entity) {
        baseService.add(entity);
        return Result.success("新增任务成功。后台分析中...");
    }

    @PostMapping("/runTask/{id}")
    public Result runTask(@PathVariable("id") String id){
        baseService.asyncRunTask(id);
        return Result.success("后台分析中...");
    }

    @DeleteMapping("/{id}")
    @XLog(title = "删除分析任务", execType = ExecType.DELETE)
    public Result delete(@PathVariable("id") String id){
        baseService.deleteById(id);
        return Result.success("删除成功！");
    }

}
