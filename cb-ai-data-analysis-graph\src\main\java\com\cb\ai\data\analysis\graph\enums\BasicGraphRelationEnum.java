package com.cb.ai.data.analysis.graph.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BasicGraphRelationEnum {

    SOURCE_PERSON_ID("关系源人员ID", "确定的一段两人关系中，其中一人的id，请同上方生成的人物信息中的id保持一致。（可以依据此id在上方的人物信息中找到唯一的一位人员）"),
    TARGET_PERSON_ID("关系目标人员ID", "确定的一段两人关系中，另外一人的id，请同上方生成的人物信息中的id保持一致。（可以依据此id在上方的人物信息中找到唯一的一位人员）"),
    RELATION_DESC("关系描述", "主体人员同关联人员具体关系（父子，母子，夫妻，朋友，同学，情人，上级，下级等,尽量以两字描述，只允许一个关系的存在。)，如果无法定义具体的关系，但可以确定存在关系，则填充空字符串即可");

    private String name;

    private String desc;
}
