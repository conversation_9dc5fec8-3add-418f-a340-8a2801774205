package com.xong.boot.common.easyexcel;

import com.alibaba.excel.metadata.CellExtra;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

public interface EasyExcelService {


    /**
     *  导入Excel
     * @param inputStream
     * @param clazz
     * @param saveFunction
     * @param failMethod
     * @param <T>
     */
    public <T> void importExcel(InputStream inputStream,Class<T> clazz, Function<List<T>, Boolean> saveFunction, Consumer<String> failMethod);

    /**
     * 下载Excel模板
     *
     * @param clazz
     * @param fileName
     * @param sheetName
     * @param response
     */
    public void downloadExcelTemplate(Class clazz, String fileName, String sheetName, HttpServletResponse response);

    /**+
     * 导出数据
     * @param clazz
     * @param fileName
     * @param sheetName
     * @param list
     * @param response
     * @param <T>
     */
    public <T> void exportExcel(Class clazz, String fileName, String sheetName, List<T> list, HttpServletResponse response);

}
