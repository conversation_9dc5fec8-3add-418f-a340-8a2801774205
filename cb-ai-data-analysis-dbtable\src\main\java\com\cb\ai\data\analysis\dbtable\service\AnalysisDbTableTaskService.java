package com.cb.ai.data.analysis.dbtable.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.xong.boot.common.service.BaseService;

public interface AnalysisDbTableTaskService extends BaseService<AnalysisDbTableTask> {

    /**
     * 查询数据导入工作列表
     * @param bigdataPoolJob 数据导入工作
     * @return 数据导入工作集合
     */
    Page<AnalysisDbTableTask> pageTableTaskList(AnalysisDbTableTask bigdataPoolJob);

    /**
     * 批量删除数据导入工作
     * @param ids 需要删除的数据导入工作主键集合
     * @return 结果
     */
    int deleteTableTaskByIds(String[] ids);

}
