package com.cb.ai.data.analysis.petition.domain.vo.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class DomainStatisticExportDTO {

    @ExcelProperty("所属领域（一级分类）")
    @ColumnWidth(25)
    private String firstCategory;

    @ExcelProperty("所属领域（二级分类）")
    @ColumnWidth(25)
    private String secondCategory;

    @ExcelProperty("数量")
    @ColumnWidth(15)
    private Integer count;

    @ExcelProperty("占比 (%)")
    @ColumnWidth(15)
    private Double percentage;
}