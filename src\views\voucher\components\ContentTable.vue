<template>
  <div class="content-table">
    <a-table v-if="tableData.length > 0" :columns="columns" :data-source="tableData" :pagination="false" size="small"
      bordered>
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === '严重程度'">
          <a-tag :color="getSeverityColor(text)">
            {{ text }}
          </a-tag>
        </template>
      </template>
    </a-table>
    <div v-else class="no-data">
      <a-empty description="暂无告警数据" />
    </div>
  </div>
</template>

<script setup lang="ts" name="ContentTable">
import { computed } from 'vue'

const props = defineProps<{
  content: string
}>()

const tableData = computed(() => {
  if (!props.content) return []

  try {
    const data = JSON.parse(props.content)
    return Array.isArray(data) ? data : []
  } catch (error) {
    console.error('解析告警内容失败:', error)
    return []
  }
})

const columns = computed(() => {
  if (tableData.value.length === 0) return []

  // 根据第一条数据的键来生成列配置
  const firstItem = tableData.value[0]
  return Object.keys(firstItem).map(key => ({
    title: key,
    dataIndex: key,
    key: key,
    width: getColumnWidth(key),
    ellipsis: true
  }))
})

function getColumnWidth(key: string) {
  const widthMap: Record<string, number> = {
    '序号': 80,
    '违规类型': 120,
    '适用规则': 150,
    '违规内容': 200,
    '严重程度': 100,
    '问题描述': 250
  }
  return widthMap[key] || 120
}

function getSeverityColor(severity: string) {
  const colorMap: Record<string, string> = {
    '轻微': 'blue',
    '一般': 'orange',
    '严重': 'red',
    '特别严重': 'purple'
  }
  return colorMap[severity] || 'default'
}
</script>

<style scoped lang="less">
.content-table {
  .no-data {
    text-align: center;
    padding: 20px;
  }

  :deep(.ant-table) {
    .ant-table-tbody>tr>td {
      padding: 8px;
      word-break: break-word;
    }

    .ant-table-thead>tr>th {
      padding: 8px;
      font-weight: 600;
      background: #fafafa;
    }
  }
}
</style>
