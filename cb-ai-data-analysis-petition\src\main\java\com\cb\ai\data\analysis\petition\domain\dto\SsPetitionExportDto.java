package com.cb.ai.data.analysis.petition.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SsPetitionExportDto {
    @ExcelProperty("文件名")
    private String fileName;

    @ExcelProperty("信访件编号")
    private String petitionNumber;

    @ExcelProperty("姓名")
    private String petitionUsername;

    @ExcelProperty("登记部门")
    private String registerDeptName;

    @ExcelProperty("信访形式")
    private String petitionType;

    @ExcelProperty("信访目的")
    private String petitionPurpose;

    @ExcelProperty("问题属地名称")
    private String petitionRegion;

    @ExcelProperty("登记日期")
    private Date registerDate;

    @ExcelProperty("一级内容分类")
    private String categoryFirst;

    @ExcelProperty("二级内容分类")
    private String categorySecond;

    @ExcelProperty("三级内容分类")
    private String categoryThird;

    @ExcelProperty("初重件")
    private String caseType;

    @ExcelProperty("概况信息（投诉内容）")
    private String overview;

    @ExcelProperty("是否流程性办结")
    private String isFinished;

    @ExcelProperty("国家局信访件编号")
    private String petitionCountryCode;

    @ExcelProperty("手机号码")
    private String phoneNumber;

    @ExcelProperty("身份证")
    private String certificationCode;

    @ExcelProperty("信访人住址")
    private String petitionUserResidenceAddress;

    @ExcelProperty("登记机构名称")
    private String registerOrgName;

    @ExcelProperty("登记机构类别")
    private String registerOrgType;

    @ExcelProperty("信访日期")
    private Date petitionRegisterDate;

    @ExcelProperty("要求办结时间")
    private Date petitionDeadlineDate;

    @ExcelProperty("是否扬言")
    private String isThreaten;

    @ExcelProperty("是否涉法涉诉")
    private String isIllegal;

    @ExcelProperty("办结时间")
    private Date finishDate;

    @ExcelProperty("扬言词汇")
    private String threatenWords;

    @ExcelProperty("本单位去向机构")
    private String selfDeptSendOrgName;

    @ExcelProperty("本单位办理方式名称")
    private String selfDeptHandleType;

    @ExcelProperty("办理意见")
    private String handleSuggestion;

    @ExcelProperty("出具处理意见单位")
    private String handleSuggestionResponsibleOrg;

    @ExcelProperty("处理意见内容")
    private String handleSuggestionDetail;


}

