package com.cb.ai.data.analysis.graph.handler;

import cn.hutool.core.map.MapUtil;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.service.business.RelationGraphService;
import com.cb.ai.data.analysis.graph.utils.ExcelReaderUtil;
import com.xong.boot.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: excel文件对应的处理器
 * @Author: ARPHS
 * @Date: 2025-05-01 10:58
 * @Version: 1.0
 **/
@Slf4j
public class ExcelProcessor implements FileProcessor {

    private final RelationGraphService relationGraphService;

    {
        relationGraphService = SpringUtils.getBean(RelationGraphService.class);
    }

    public static void main(String[] args) {
        String dir = "C:\\Users\\<USER>\\Desktop\\";
        String fileName = "fake_kin_data.xlsx";
//        String fileName = "长丰中学.docx"
        String filePath = dir + fileName;
        FileProcessor fileProcessor = FileProcessorFactory.getFileProcessor(filePath, null, null);
        fileProcessor.process(filePath, null, null);
    }

    @Override
    public void process(InputStream is, GraphFileRecord graphFileRecord, String promote) {
        ByteArrayInputStream copy1 = null;
        ByteArrayInputStream copy2 = null;
        try{
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            copy1 = new ByteArrayInputStream(baos.toByteArray());
            copy2 = new ByteArrayInputStream(baos.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Collection<String> headers = ExcelReaderUtil.readHeader(copy1, 0, 1).values();
        if (headers.containsAll(Arrays.asList("公司名称", "法定代表人", "股东人员", "统一社会信用代码"))) {
            processEnterpriseExcel(copy2);
        } else if (headers.containsAll(Arrays.asList("工作单位", "姓名", "身份证号", "政治面貌", "入党时间", "参加工作时间", "现职务"))) {
            processPersonExcel(copy2);
        } else if (headers.containsAll(Arrays.asList("工作单位", "姓名", "亲属姓名", "亲属关系"))) {
            processKinExcel(copy2);
        } else if (headers.contains("干部近亲属关系信息统计表")) {
            processKinV1Excel(copy2);
        } else {
            throw new RuntimeException("暂不支持的数据表头!");
        }
    }

    private void processEnterpriseExcel(InputStream is) {
        List<Map<String, String>> mapList = ExcelReaderUtil.readToMap(is, 0, 1);
        handleEnterpriseExcel(mapList);
    }

    private void processPersonExcel(InputStream is) {
        List<Map<String, String>> mapList = ExcelReaderUtil.readToMap(is, 0, 1);
        handlePersonExcel(mapList);
    }

    private void processKinExcel(InputStream is) {
        List<Map<String, String>> mapList = ExcelReaderUtil.readToMap(is, 0, 1);
        handleKinExcel(mapList);
    }

    private void processKinV1Excel(InputStream is) {
        List<Map<String, String>> mapList = ExcelReaderUtil.readToMap(is, 0, 3)
                .stream().filter(map -> {
                    String name = MapUtil.getStr(map, "姓名");
                    return StringUtils.isNotBlank(name);
                }).collect(Collectors.toList());
        handleKinV1Excel(mapList);
    }

}
