package com.cb.ai.data.analysis.ai.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.ai.domain.dto.QueryColumnsDto;
import com.cb.ai.data.analysis.ai.domain.dto.QueryDatasDto;
import com.cb.ai.data.analysis.ai.service.ISqlParserService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/sql/parser")
public class SqlParserController {

    @Autowired
    private ISqlParserService sqlParserService;

    // 解析sql
    @PostMapping("/getQueryColumns")
    public Result getQueryColumns(@Validated @RequestBody QueryColumnsDto dto) {
        try {
            String sql = dto.getSql();
            sql = sql.replaceAll(";", "");
            // 去掉limit限制
            sql = sql.replaceAll("(?i)\\s*limit\\s+\\d+\\s*$", "");
            Map<String, Object> map = sqlParserService.selectData(sql);
            if (ObjectUtil.isNull(map)) {
                return Result.fail("sql解析无对应数据！");
            }
            for (String key : map.keySet()) {
                map.put(key, key + "字段");
            }
            return Result.successData(map);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("sql解析异常！");
        }
    }

    // 查询sql
    @PostMapping("/selectDataPage")
    public Result selectDataPage(@Validated @RequestBody QueryDatasDto dto) {
        String sql = dto.getSql();
        sql = sql.replaceAll(";", "");
        // 去掉limit限制
        sql = sql.replaceAll("(?i)\\s*limit\\s+\\d+\\s*$", "");
        return Result.successData(sqlParserService.selectDataPage(new Page<>(dto.getPageCurrent(), dto.getPageSize()), sql));
    }

    @GetMapping("/selectTableInfoList")
    public Result selectTableInfoList() {
        return Result.successData(sqlParserService.selectTableInfoList());
    }

}
