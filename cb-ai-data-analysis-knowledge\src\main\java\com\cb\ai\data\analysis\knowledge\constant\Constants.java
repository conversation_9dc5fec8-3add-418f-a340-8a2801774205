package com.cb.ai.data.analysis.knowledge.constant;


import java.util.Arrays;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库常量
 */
public class Constants {

    /**
     * 知识库请求前缀
     */
    public static final String API_KNOWLEDGE_ROOT_PATH = com.xong.boot.common.constant.Constants.API_ROOT_PATH + "/knowledge";

    /***
     * 管理员用户ID
     */
    public static final List<String> ADMIN_USER_IDS = Arrays.asList("1932612248178454527", "11111");


    public static final class PERMISSION_MARKE {

        public static final String ALLOW="1";
        
        public static final String NO_ALLOW="0";
    }
}
