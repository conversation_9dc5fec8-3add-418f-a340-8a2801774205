<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.ai.mapper.SqlParserMapper">

    <select id="selectData" resultType="hashmap">
        select
            *
        from
            (
                ${sql}
            )
        limit 1
    </select>

    <select id="selectDataPage" resultType="hashmap">
        ${sql}
    </select>

    <select id="selectTableInfoList" resultType="hashmap">
        SELECT
            name,
            if(comment = '' OR comment IS NULL, name, comment) AS comment
        FROM
            system.tables
        where
            database = 'db_data_analysis'
    </select>

</mapper>

