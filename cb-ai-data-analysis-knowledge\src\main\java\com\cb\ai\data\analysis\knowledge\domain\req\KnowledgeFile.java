package com.cb.ai.data.analysis.knowledge.domain.req;

import com.xong.boot.common.domain.BasePage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件实体
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class KnowledgeFile extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;
    /***
     * 文件名称
     */
    private String fileName;
    /***
     * 文件地址
     */
    private String fileUrl;

    /***
     * 知识库ID
     */
    private String baseId;

    /***
     * 文件ID
     */
    private String fileId;

    /***
     * 解析状态
     */
    private String processStatus;

    /***
     * 解析结果
     */
    private String processRemark;

    /***
     * 文件ID列表
     */

    private List<String> fileIds;

    public KnowledgeFile(String fileName, String fileUrl, String baseId, String fileId) {
        this.fileName = fileName;
        this.fileUrl = fileUrl;
        this.baseId = baseId;
        this.fileId = fileId;
    }
}
