package com.cb.ai.data.analysis.ai.component.choreography.extension;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:04
 * @Copyright (c) 2025
 * @Description 扩展点类型
 */
@Getter
public enum ExtensionType {
    PRIVATE_BASE(0, "private_base"),

    COMMON(99, "通用")
    ;

    /**
     * 扩展点类型 (自定义的)
     */
    private final int code;
    /**
     * 扩展点类型说明 (自定义的)
     */
    private final String name;

    ExtensionType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /*
     * 根据code获取扩展类型
     */
    public static ExtensionType findByCode(int code) throws IllegalArgumentException {
        for (ExtensionType item : ExtensionType.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }
}
