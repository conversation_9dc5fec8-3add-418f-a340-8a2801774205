package com.cb.ai.data.analysis.knowledge.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeFileEntity;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgePurviewEntity;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBase;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeCountResult;
import com.cb.ai.data.analysis.knowledge.mapper.KnowledgeBaseMapper;
import com.cb.ai.data.analysis.knowledge.mapper.KnowledgeFileMapper;
import com.cb.ai.data.analysis.knowledge.mapper.KnowledgePurviewMapper;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeBaseService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.AiRespConvertUtils;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/***
 * <AUTHOR>
 * 知识库管理
 */
@Service
public class KnowledgeBaseServiceImpl extends BaseServiceImpl<KnowledgeBaseMapper, KnowledgeBaseEntity>  implements KnowledgeBaseService {


    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/knowledge/base'}")
    private String baseUlr;


    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization;


    @Autowired
    private KnowledgeFileMapper knowledgeFileMapper;

    @Autowired
    private KnowledgePurviewMapper knowledgePurviewMapper;


    @Override
    public Page<KnowledgeBaseEntity> pageKnowledgeBase(KnowledgeBase knowledgeBase) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBase)){
                throw new XServiceException("请求对象为空！");
            }
            Page<KnowledgeBaseEntity> page = new Page<>(knowledgeBase.getPageCurrent(), knowledgeBase.getPageSize());
            LambdaQueryWrapper<KnowledgeBaseEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(KnowledgeBaseEntity::getIsDel,0);
            Page<KnowledgeBaseEntity> basePage=baseMapper.pageKnowledgeBase(page,lambdaQueryWrapper,SecurityUtils.getUserId(),SecurityUtils.getDeptId());
            setAiBaseData(basePage.getRecords(),knowledgeBase.getIsShowFileStatus());
            return basePage;
//            if(ObjectUtils.isEmpty(knowledgeBase.getPageCurrent()) && knowledgeBase.getPageCurrent()<=0){
//                knowledgeBase.setPage(0);
//            }else{
//                knowledgeBase.setPage(knowledgeBase.getPageCurrent());
//            }
//            if(ObjectUtils.isEmpty(knowledgeBase.getPageSize()) &&  knowledgeBase.getPageSize()<=0){
//                knowledgeBase.setSize(10);
//            }else{
//                knowledgeBase.setSize(knowledgeBase.getPageSize());
//            }
//            Map<String, String> headers = new HashMap<>();
//            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
//            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
//            HttpResponse httpResponse=HttpUtils.sendPost(baseUlr+"/page",JSON.toJSONString(knowledgeBase),headers);
//            if(ObjectUtils.isEmpty(httpResponse)){
//                throw new XServiceException("请求响应为空！");
//            }
//            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
//                throw new XServiceException(httpResponse.body());
//            }
//            Page<KnowledgeBaseEntity> page=JSON.parseObject(httpResponse.body(),new TypeReference<Page<KnowledgeBaseEntity>>() {});
//            if(ObjectUtils.isEmpty(page)){
//                return null;
//            }
//            if(!CollectionUtils.isEmpty(page.getRecords())){
//                for(KnowledgeBaseEntity knowledgeBaseEntity:page.getRecords()){
//                    KnowledgeBaseEntity kbe=baseMapper.selectById(knowledgeBaseEntity.getId());
//                    if(!ObjectUtils.isEmpty(kbe)){
//                        knowledgeBaseEntity.setTag(kbe.getTag());
//                        knowledgeBaseEntity.setCreateBy(kbe.getCreateBy());
//                        knowledgeBaseEntity.setUpdateTime(kbe.getUpdateTime());
//                        knowledgeBaseEntity.setUpdateBy(kbe.getUpdateBy());
//                        knowledgeBaseEntity.setRemark(kbe.getRemark());
//                        knowledgeBaseEntity.setCreateUserId(kbe.getCreateUserId());
//                    }
//                }
//            }
//            return page;
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addKnowledgeBase(KnowledgeBase knowledgeBase) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBase)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse=HttpUtils.sendPut(baseUlr,JSON.toJSONString(knowledgeBase),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            Object body=AiRespConvertUtils.getSuccessData(httpResponse.body());
            if(ObjectUtils.isEmpty(body)){
                throw new XServiceException("知识库信息添加失败！");
            }
            knowledgeBase.setBaseType(0);
            KnowledgeBaseEntity knowledgeBaseEntity=getKnowledgeBaseEntity(knowledgeBase);
            knowledgeBaseEntity.setId(body.toString());
            knowledgeBaseEntity.setCreateUserId(SecurityUtils.getUserId());
            knowledgeBaseEntity.setCreateTime(LocalDateTime.now());
            if(baseMapper.insert(knowledgeBaseEntity)<=0){
                throw new XServiceException("添加知识库信息失败！");
            }
            /**
             * 同步新增权限信息
             */
            if(!ObjectUtils.isEmpty(knowledgeBase.getPurviewMark()) && knowledgeBase.getPurviewMark().equals(2)){
                if(CollectionUtils.isEmpty(knowledgeBase.getDeptIds())){
                    throw new XServiceException("请选择可见部门！");
                }
                List<KnowledgePurviewEntity> purviewEntityList=new ArrayList<>();
                for(String deptId:knowledgeBase.getDeptIds()){
                    KnowledgePurviewEntity purviewEntity=new KnowledgePurviewEntity();
                    purviewEntity.setId(IdUtil.getSnowflakeNextIdStr());
                    purviewEntity.setPurviewType("dept");
                    purviewEntity.setBaseId(knowledgeBaseEntity.getId());
                    purviewEntity.setDeptId(deptId);
                    purviewEntityList.add(purviewEntity);
                }
                if(CollectionUtils.isEmpty(knowledgePurviewMapper.insert(purviewEntityList))){
                    throw new XServiceException("设置知识库权限信息失败！");
                }
            }
            return ResultCode.SUCCESS.getMessage();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    private KnowledgeBaseEntity getKnowledgeBaseEntity(KnowledgeBase knowledgeBase){
        KnowledgeBaseEntity knowledgeBaseEntity=new KnowledgeBaseEntity();
        if(!ObjectUtils.isEmpty(knowledgeBase.getId())){
            knowledgeBaseEntity.setId(knowledgeBase.getId());
        }
        knowledgeBaseEntity.setBaseType(knowledgeBase.getBaseType());
        knowledgeBaseEntity.setParentId(knowledgeBase.getParentId());
        knowledgeBaseEntity.setTag(knowledgeBase.getTag());
        knowledgeBaseEntity.setName(knowledgeBase.getName());
        knowledgeBaseEntity.setIsDel(0);
        knowledgeBaseEntity.setRemark(knowledgeBase.getRemark());
        knowledgeBaseEntity.setPurviewMark(knowledgeBase.getPurviewMark());
        return knowledgeBaseEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editKnowledgeBase(KnowledgeBase knowledgeBase) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBase)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            knowledgeBase.setCreateTime(null);
            HttpResponse httpResponse=HttpUtils.sendPost(baseUlr,JSON.toJSONString(knowledgeBase),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            knowledgeBase.setBaseType(0);
            KnowledgeBaseEntity knowledgeBaseEntity=getKnowledgeBaseEntity(knowledgeBase);
            knowledgeBaseEntity.setCreateTime(null);
            knowledgeBaseEntity.setCreateBy(null);
            if(baseMapper.updateById(knowledgeBaseEntity)<=0){
                throw new XServiceException("修改知识库信息失败！");
            }
            /**
             * 同步新增权限信息
             */
            if(!ObjectUtils.isEmpty(knowledgeBase.getPurviewMark()) && knowledgeBase.getPurviewMark().equals(2)){
                if(CollectionUtils.isEmpty(knowledgeBase.getDeptIds())){
                    throw new XServiceException("请选择可见部门！");
                }
                /***
                 * 先删除之前的配置
                 */
                LambdaQueryWrapper<KnowledgePurviewEntity> lambdaQueryWrapper=new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(KnowledgePurviewEntity::getBaseId,knowledgeBase.getId());
                knowledgePurviewMapper.delete(lambdaQueryWrapper);

                /***
                 * 重新新增权限信息
                 */
                List<KnowledgePurviewEntity> purviewEntityList=new ArrayList<>();
                for(String deptId:knowledgeBase.getDeptIds()){
                    KnowledgePurviewEntity purviewEntity=new KnowledgePurviewEntity();
                    purviewEntity.setId(IdUtil.getSnowflakeNextIdStr());
                    purviewEntity.setPurviewType("dept");
                    purviewEntity.setBaseId(knowledgeBaseEntity.getId());
                    purviewEntity.setDeptId(deptId);
                    purviewEntityList.add(purviewEntity);
                }
                if(CollectionUtils.isEmpty(knowledgePurviewMapper.insert(purviewEntityList))){
                    throw new XServiceException("设置知识库权限信息失败！");
                }
            }
            return ResultCode.SUCCESS.getMessage();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String delKnowledgeBase(String knowledgeBaseId) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBaseId)){
                throw new XServiceException("请选择要删除的数据项！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);

            Map<String,Object> params=new HashMap<>();
            params.put("id",knowledgeBaseId);
            HttpResponse httpResponse=HttpUtils.sendDelete(baseUlr,params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            if(baseMapper.deleteById(knowledgeBaseId)<=0){
                throw new XServiceException("删除知识库信息失败！");
            }
            /***
             * 同步删除权限信
             */
            LambdaQueryWrapper<KnowledgePurviewEntity> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(KnowledgePurviewEntity::getBaseId,knowledgeBaseId);
            knowledgePurviewMapper.delete(lambdaQueryWrapper);
            return ResultCode.SUCCESS.getMessage();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public KnowledgeBaseEntity getKnowledgeBase(String knowledgeBaseId) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBaseId)){
                throw new XServiceException("请选择要删除的数据项！");
            }
//            不在去查询ai侧知识库。已业务侧为基准
//            Map<String, String> headers = new HashMap<>();
//            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
//
//            Map<String,Object> params=new HashMap<>();
//            params.put("id",knowledgeBaseId);
//            HttpResponse httpResponse=HttpUtils.sendGet(baseUlr,params,headers);
//            if(ObjectUtils.isEmpty(httpResponse)){
//                throw new XServiceException("请求响应为空！");
//            }
//            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
//                throw new XServiceException(httpResponse.body());
//            }
//            KnowledgeBase knowledgeBase=JSON.parseObject(httpResponse.body(),KnowledgeBase.class);
//            if(ObjectUtils.isEmpty(knowledgeBase)){
//                throw new XServiceException("获取知识库详情失败！");
//            }
//            KnowledgeBaseEntity knowledgeBaseEntity=getKnowledgeBaseEntity(knowledgeBase);
//            KnowledgeBaseEntity kbe=baseMapper.selectById(knowledgeBaseId);
//            if(!ObjectUtils.isEmpty(kbe)){
//                knowledgeBaseEntity.setPurviewMark(kbe.getPurviewMark());
//                knowledgeBaseEntity.setTag(kbe.getTag());
//                knowledgeBaseEntity.setUpdateBy(kbe.getUpdateBy());
//                knowledgeBaseEntity.setCreateBy(kbe.getCreateBy());
//                knowledgeBaseEntity.setUpdateTime(kbe.getUpdateTime());
//                knowledgeBaseEntity.setCreateUserId(kbe.getCreateUserId());
//                knowledgeBaseEntity.setRemark(kbe.getRemark());
//            }

            KnowledgeBaseEntity knowledgeBaseEntity=baseMapper.selectById(knowledgeBaseId);
            /***
             * 获取权限列表
             */
            LambdaQueryWrapper<KnowledgePurviewEntity> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(KnowledgePurviewEntity::getDeptId);
            lambdaQueryWrapper.eq(KnowledgePurviewEntity::getBaseId,knowledgeBaseId);
            knowledgeBaseEntity.setDeptIds(knowledgePurviewMapper.selectObjs(lambdaQueryWrapper));
            return knowledgeBaseEntity;
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public List<KnowledgeBaseEntity> listKnowledgeBase(String parentId,String searchKey) throws Exception {
        try{
            return baseMapper.getKnowledgeBaseList(searchKey,parentId,SecurityUtils.getUserId(),SecurityUtils.getDeptId());
            /*Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);

            Map<String,Object> params=new HashMap<>();
            params.put("parentId",parentId);
            params.put("searchKey",searchKey);
            HttpResponse httpResponse=HttpUtils.sendGet(baseUlr+"/list",params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            List<KnowledgeBaseEntity> knowledgeBaseEntityList=JSON.parseArray(httpResponse.body(),KnowledgeBaseEntity.class);
            if(!CollectionUtils.isEmpty(knowledgeBaseEntityList)){
                for(KnowledgeBaseEntity knowledgeBaseEntity:knowledgeBaseEntityList){
                    KnowledgeBaseEntity kbe=baseMapper.selectById(knowledgeBaseEntity.getId());
                    if(!ObjectUtils.isEmpty(kbe)){
                        knowledgeBaseEntity.setTag(kbe.getTag());
                        knowledgeBaseEntity.setCreateBy(kbe.getCreateBy());
                        knowledgeBaseEntity.setUpdateTime(kbe.getUpdateTime());
                        knowledgeBaseEntity.setUpdateBy(kbe.getUpdateBy());
                        knowledgeBaseEntity.setRemark(kbe.getRemark());
                        knowledgeBaseEntity.setCreateUserId(kbe.getCreateUserId());
                    }
                }
            }
            return knowledgeBaseEntityList;*/
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String reparse(String knowledgeBaseId) throws Exception {
        try{
            if(StringUtils.isBlank(knowledgeBaseId)){
                throw new XServiceException("知识库ID不能为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            Map<String,String> params=new HashMap<>();
            params.put("id",knowledgeBaseId);
            HttpResponse httpResponse=HttpUtils.sendPost(baseUlr+"/reparse",JSON.toJSONString(params),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            this.editUpdateTime(knowledgeBaseId);
            Result result= AiRespConvertUtils.getResult(httpResponse.body());
            return result.getMessage();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    private void setAiBaseData(List<KnowledgeBaseEntity> list,Boolean isShowFileStatus){
        try{
            if(!ObjectUtils.isEmpty(isShowFileStatus) && isShowFileStatus && !CollectionUtils.isEmpty(list)){
                for(KnowledgeBaseEntity entity:list){
                    Map<String, String> headers = new HashMap<>();
                    headers.put(Header.AUTHORIZATION.getValue(), Authorization);

                    Map<String,Object> params=new HashMap<>();
                    params.put("id",entity.getId());
                    HttpResponse httpResponse=HttpUtils.sendGet(baseUlr,params,headers);
                    if(ObjectUtils.isEmpty(httpResponse)){
                        throw new XServiceException("请求响应为空！");
                    }
                    if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                        throw new XServiceException(httpResponse.body());
                    }
                    Object data=AiRespConvertUtils.getSuccessData(httpResponse.body());
                    if(!ObjectUtils.isEmpty(data)){
                        JSONObject jsonObject=JSONObject.from(data);
                        if(!ObjectUtils.isEmpty(jsonObject)){
                            if(jsonObject.containsKey("fileCount") && !ObjectUtils.isEmpty(jsonObject.getInteger("fileCount"))){
                                entity.setFileCount(jsonObject.getInteger("fileCount"));
                            }
                            if(jsonObject.containsKey("countResult") && !ObjectUtils.isEmpty(jsonObject.get("countResult"))){
                                entity.setCountResult(jsonObject.getList("countResult",KnowledgeCountResult.class));
                            }
                        }
                    }
                }
            }
        }catch (XServiceException e){
            e.printStackTrace();
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    void editUpdateTime(String baseId){
        try{
            LambdaUpdateWrapper<KnowledgeFileEntity> editWrapper = new LambdaUpdateWrapper<>();
            editWrapper.eq(KnowledgeFileEntity::getBaseId,baseId);
            editWrapper.set(KnowledgeFileEntity::getUpdateTime, LocalDateTime.now());
            knowledgeFileMapper.update(editWrapper);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
