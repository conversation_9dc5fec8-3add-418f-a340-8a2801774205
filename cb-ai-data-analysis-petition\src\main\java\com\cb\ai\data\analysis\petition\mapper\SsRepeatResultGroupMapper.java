package com.cb.ai.data.analysis.petition.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SsRepeatResultGroupMapper extends BaseMapper<SsRepeatResultGroup> {

    public Page<SsRepeatResultGroup> pageByWrapper(Page<SsRepeatResultGroup> page, @Param(Constants.WRAPPER) Wrapper<SsRepeatResultGroup> wrapper);

}
