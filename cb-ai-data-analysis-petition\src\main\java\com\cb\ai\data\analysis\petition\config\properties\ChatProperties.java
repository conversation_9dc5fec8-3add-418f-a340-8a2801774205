//package com.cb.ai.data.analysis.petition.config.properties;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
//@Data
//@Component
//@ConfigurationProperties(prefix = "cbxxjs.chat")
//public class ChatProperties {
//
//    private String baseUrl;
//
//    private String model;
//
//    private String temperature;
//
//    private String role;
//
//    private String uri;
//
//}
