package com.cb.ai.data.analysis.knowledge.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBase;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeBaseService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库管理
 */
@Validated
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/base")
public class KnowledgeBaseController extends Base<PERSON>ontroller<KnowledgeBaseService, KnowledgeBaseEntity> {

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    /***
     * 知识库清单分页查询
     * @param knowledgeBase
     * @return
     */
    @GetMapping("/page")
    public Result page(KnowledgeBase knowledgeBase) {
        try{
            Page<KnowledgeBaseEntity> knowledgeBasePage=knowledgeBaseService.pageKnowledgeBase(knowledgeBase);
            return Result.successData(knowledgeBasePage);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询知识库信息失败！");
        }
    }

    /***
     * 知识库清单新增
     * @param knowledgeBase
     * @return
     */
    @PostMapping
    public Result add(@RequestBody KnowledgeBase knowledgeBase){
        try{
            String respMsg=knowledgeBaseService.addKnowledgeBase(knowledgeBase);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库新增失败！");
        }
    }

    /***
     * 知识库清单修改
     * @param knowledgeBase
     * @return
     */
    @PutMapping
    public Result edit(@RequestBody KnowledgeBase knowledgeBase){
        try{
            String respMsg=knowledgeBaseService.editKnowledgeBase(knowledgeBase);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库信息修改失败！");
        }
    }

    /**
     * 根据ID删除知识库清单
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable String id){
        try{
            String respMsg=knowledgeBaseService.delKnowledgeBase(id);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库删除失败！");
        }
    }

    /***
     * 根据ID查询知识库清单
     * @param id
     * @return
     */
    @GetMapping(value = "/{id}")
    public Result getKnowledgeBase(@PathVariable("id") String id){
        try{
            KnowledgeBaseEntity knowledgeBaseEntity=knowledgeBaseService.getKnowledgeBase(id);
            return Result.successData(knowledgeBaseEntity);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库信息获取失败！");
        }
    }

    /***
     * 知识库清单列表查询
     * @param parentId
     * @return
     */
    @GetMapping("/list")
    public Result list(@RequestParam(value="parentId",required=false) String parentId,@RequestParam(value="searchKey",required=false) String searchKey) {
        try{
            List<KnowledgeBaseEntity> knowledgeBasePage=knowledgeBaseService.listKnowledgeBase(parentId,searchKey);
            return Result.successData(knowledgeBasePage);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询知识库信息失败！");
        }
    }

    /***
     * 知识库整库重解
     * @param id
     * @return
     */
    @GetMapping(value = "/reparse/{id}")
    public Result reparse(@PathVariable("id") String id){
        try{
            String respMsg=knowledgeBaseService.reparse(id);
            return Result.success("提交重新解析成功",respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("提交重新解析失败！");
        }
    }
}
