package com.cb.ai.data.analysis.dbtable.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 数据导入任务对象 analysis_db_table_task
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTableTask extends SimpleBaseDomain {

    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 数据表ID
     */
    @NotBlank(message = "数据表ID不存在", groups = AddGroup.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String tableId;
    /**
     * 数据表名称
     */
    @NotBlank(message = "表名不存在", groups = AddGroup.class)
    private String tableName;
    /**
     * 表注释
     */
    private String tableComment;
    /**
     * 数据源文件ID
     */
    private String sourceFileId;
    /**
     * 数据源文件名
     */
    private String filename;
    /**
     * 工作薄位置
     */
    private Integer sheetNo;
    /**
     * EXCEL表头开始位置
     */
    private Integer headStart;
    /**
     * EXCEL表头结束位置
     */
    private Integer headEnd;
    /**
     * EXCEL数据开始位置
     */
    private Integer startRow;
    /**
     * EXCEL数据结束位置
     */
    private Integer endRow;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 总数
     */
    @TableField(exist = false)
    private Integer total;
    /**
     * 成功数
     */
    @TableField(exist = false)
    private Integer successCount;
    /**
     * 错误数
     */
    @TableField(exist = false)
    private Integer errorCount;
    /**
     * 待确认数据
     */
    @TableField(exist = false)
    private Integer waitConfirmCount;
    /**
     * 必填字段未填写备注数
     */
    @TableField(exist = false)
    private Integer notRemarkCount;
}
