package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 副标题管道
 * <AUTHOR>
 */
public class SubtitlePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        if (pos < 2) {
            return false;
        }
        String paragraphText = paragraph.getText().trim();
        if (paragraphText.length() > 3 && ((ReUtil.contains("[〔〕]", paragraphText) && paragraphText.length() < 15) ||
                ReUtil.contains("[）)]$", paragraphText) ||
                ReUtil.contains("[(（]$", paragraphText) ||
                paragraph.getAlignment() == ParagraphAlignment.CENTER)) {
            XWPFParagraph preParagraph = document.getParagraphArray(pos - 1);
            String preParagraphText = preParagraph.getText().trim();
            if (preParagraphText.length() > 3 && !ReUtil.contains("[〔〕]", preParagraphText) &&
                    !ReUtil.contains("[）)]$", preParagraphText) &&
                    preParagraph.getAlignment() == ParagraphAlignment.CENTER) {
                boolean noRed = true;
                for (XWPFRun run : preParagraph.getRuns()) {
                    String color = run.getColor();
                    if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                        noRed = false;
                        break;
                    }
                }
                return noRed;
            }
        }
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化段落
        FormatTools.formatParagraph(paragraph, config).setCTJcCenter();
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
