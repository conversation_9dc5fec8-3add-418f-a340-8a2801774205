package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;


/**
 * 源文件
 * */
@Data
@TableName("ss_petition_source_file")
public class PetitionSourceFileEntity extends BaseDomain implements Serializable {

    private String id;

    private String fileId;

    private Integer analysisStatus;

    private Integer dataCount;

}
