package com.cb.ai.data.analysis.dbtable.converts;

import cn.hutool.core.date.DateUtil;
import com.cb.ai.data.analysis.dbtable.model.ExcelConvert;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 日期时间清洗
 * <AUTHOR>
 */
@Component
public class DataTimeConvert extends Convert<Date> {
    public DataTimeConvert() {
        super("DATA_TIME", "日期时间");
    }

    @Override
    protected Date transform(Object value, ExcelConvert excelConvert, Map<Integer, String> source) {
        return DateUtil.parse((String) value);
    }
}
