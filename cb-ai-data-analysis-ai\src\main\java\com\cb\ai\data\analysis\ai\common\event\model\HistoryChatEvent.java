package com.cb.ai.data.analysis.ai.common.event.model;

import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:21
 * @Copyright (c) 2025
 * @Description Disruptor流程历史会话事件
 */
@Data
public class HistoryChatEvent {
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 会话角色
     */
    private RoleEnum roleEnum;
    /**
     * 会话事件
     */
    private String event;
    /**
     * 节点上下文列表
     */
    private List<NodeContext> nodeContextList;

    public void addNodeContext(NodeContext context) {
        if (nodeContextList == null) {
            nodeContextList = new ArrayList<>();
        }
        nodeContextList.add(context);
    }
}