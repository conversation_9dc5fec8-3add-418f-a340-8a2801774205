package com.cb.ai.data.analysis.ai.domain.request.context;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:19
 * @Copyright (c) 2025
 * @Description 通用AI请求上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AIRequestContext extends RequestContext {
    /** 用户输入提示词 **/
    private String promote;

    /** 系统提示词 **/
    private String systemPromote;

    /** 是否深度思考 **/
    private boolean deepThink;

    /** 是否为流式输出 **/
    private boolean stream = true;

    /** 是否为展示思考过程 **/
    private boolean showThink = true;

    /** 知识库是否溯源 **/
    private boolean source = true;

    /** 模型名称 **/
    private String model;

    /** 采样温度 **/
    private Float temperature;

    /** 采样策略（控制多样性，范围 0~1） **/
    private Float topP;

    /** 向量相似度检索的结果数量 **/
    private Integer topK;

    /** 生成的最大token数 **/
    private Integer maxTokens;

    /** 停止生成的标识 **/
    private String stop;

    /** 出现惩罚项（关注是否出现过，范围 -2.0 ~ 2.0） **/
    private Float presencePenalty;

    /** 控制输出文本的重复率，范围 0~1 **/
    private Float frequencyPenalty;

    /** 如果为 true，则跳过“断崖法”兜底，将返回符合标准语义（0.80以上的数据）； 否则走断崖法 + TopK 兜底 **/
    private boolean usePresetScoreThreshold;
}
