package com.cb.ai.data.analysis.dbtable.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.converts.Convert;
import com.cb.ai.data.analysis.dbtable.domain.*;
import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import com.cb.ai.data.analysis.dbtable.enums.ConvertErrorType;
import com.cb.ai.data.analysis.dbtable.exception.RuleCheckException;
import com.cb.ai.data.analysis.dbtable.exception.RuleCheckRegExpException;
import com.cb.ai.data.analysis.dbtable.exception.RuleCheckRequiredException;
import com.cb.ai.data.analysis.dbtable.exception.RuleCheckRequiredNotRemarkException;
import com.cb.ai.data.analysis.dbtable.mapper.*;
import com.cb.ai.data.analysis.dbtable.model.*;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableDataService;
import com.cb.ai.data.analysis.dbtable.utils.ExcelUtils;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.google.common.base.CaseFormat;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 数据Service业务层处理
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableDataServiceImpl implements AnalysisDbTableDataService {
    private final ClickHouseTableMapper clickHouseTableMapper;
    private final SuperviseResourceFileService superviseResourceFileService;
    private final AnalysisDbTableMapper dynamicTableMapper;
    private final AnalysisDbTableColumnMapper dynamicTableColumnMapper;
    private final AnalysisDbTableColumnRuleMapper dynamicTableColumnRuleMapper;
    private final AnalysisDbTableTaskMapper dynamicTableTaskMapper;
    private final AnalysisDbTableTaskItemMapper dynamicTableTaskItemMapper;
    private final List<Convert<?>> converts;

    private final Executor datapoolExecutor;

    public AnalysisDbTableDataServiceImpl(ClickHouseTableMapper clickHouseTableMapper,
                                          SuperviseResourceFileService superviseResourceFileService,
                                          AnalysisDbTableMapper dynamicTableMapper,
                                          AnalysisDbTableColumnMapper dynamicTableColumnMapper,
                                          AnalysisDbTableColumnRuleMapper dynamicTableColumnRuleMapper,
                                          AnalysisDbTableTaskMapper dynamicTableTaskMapper,
                                          AnalysisDbTableTaskItemMapper dynamicTableTaskItemMapper,
                                          List<Convert<?>> converts,
                                          @Qualifier("taskExecutor") Executor datapoolExecutor) {
        this.clickHouseTableMapper = clickHouseTableMapper;
        this.superviseResourceFileService = superviseResourceFileService;
        this.dynamicTableMapper = dynamicTableMapper;
        this.dynamicTableColumnMapper = dynamicTableColumnMapper;
        this.dynamicTableColumnRuleMapper = dynamicTableColumnRuleMapper;
        this.dynamicTableTaskMapper = dynamicTableTaskMapper;
        this.dynamicTableTaskItemMapper = dynamicTableTaskItemMapper;
        this.converts = converts;
        this.datapoolExecutor = datapoolExecutor;
    }

    /**
     * 读取Excel工作薄名称集
     * @param file 文件
     */
    @Override
    public List<ExcelSheetName> readExcelSheetNames(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            return ExcelUtils.getSheetNames(inputStream, file.getOriginalFilename());
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 读取Excel头
     * @param file      文件
     * @param sheetNo   工作薄位置
     * @param headStart 表头开始位置
     * @param headEnd   表头结束位置
     */
    @Override
    public List<ExcelHeadColumn> readExcelHead(MultipartFile file, Integer sheetNo, Integer headStart, Integer headEnd) {
        try {
            List<Map<Integer, String>> headList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), new ReadListener<Map<Integer, Object>>() {
                        @Override
                        public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                            int rowIndex = context.readRowHolder().getRowIndex();
                            if (headStart > rowIndex + 1) {
                                return;
                            }
                            Map<Integer, String> rowMap = new HashMap<>();
                            for (Map.Entry<Integer, ReadCellData<?>> entry : headMap.entrySet()) {
                                ReadCellData<?> readCellData = entry.getValue();
                                if (readCellData.getType() == CellDataTypeEnum.EMPTY) {
                                    rowMap.put(entry.getKey(), CellDataTypeEnum.EMPTY.name());
                                } else {
                                    rowMap.put(entry.getKey(), readCellData.getStringValue());
                                }
                            }
                            headList.add(rowMap);
                        }

                        @Override
                        public void invoke(Map<Integer, Object> data, AnalysisContext context) {
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }

                        @Override
                        public boolean hasNext(AnalysisContext context) {
                            Integer rowIndex = context.readRowHolder().getRowIndex();
                            return headEnd > rowIndex;
                        }
                    }).sheet((sheetNo == null || sheetNo < 0 ? 0 : sheetNo))
                    .headRowNumber(headEnd)
                    .doReadSync();
            return ExcelUtils.parseExcelHead(headList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取ETL转换器
     */
    @Override
    public List<Convert<?>> getConvertOptions() {
        return converts;
    }

    /**
     * 启动导入工作
     * @param file          文件
     * @param tableId       表ID
     * @param sheetNo       工作薄
     * @param headStart     表头开始位置
     * @param headEnd       表头结束位置
     * @param startRow      开始行
     * @param endRow        结束行
     * @param excelConverts excel转换
     */
    @Override
    public AnalysisDbTableTask startJob(MultipartFile file,
                                     String tableId,
                                     Integer sheetNo,
                                     Integer headStart,
                                     Integer headEnd,
                                     Integer startRow,
                                     Integer endRow,
                                     List<ExcelConvert> excelConverts) throws IOException {
        // 保存数据文件
        // TODO 保存文件,先存到根目录下，将来要选择文件夹
//        AnalysisDbTableSourceFile dynamicTableSourceFile = dynamicTableSourceFileService.insertSourceFile(file, tableId);
        SuperviseResourceFolder folder = new SuperviseResourceFolder();
        folder.setId("0");
        SuperviseResourceFile superviseResourceFile = null;
        try {
            superviseResourceFile = superviseResourceFileService.uploadFile(file, folder, null, null);
        } catch (Exception e) {
            throw new RuntimeException("保存数据文件失败！",e);
        }
        // 启动工作任务
        AnalysisDbTable table = dynamicTableMapper.selectById(tableId);
        LambdaQueryWrapper<AnalysisDbTableColumn> columnQueryWrapper = new QueryWrapper<AnalysisDbTableColumn>().lambda()
                .eq(AnalysisDbTableColumn::getTableName, table.getTableName());
        List<AnalysisDbTableColumn> tableColumns = dynamicTableColumnMapper.selectList(columnQueryWrapper);
        // 保存数据工作
        AnalysisDbTableTask dynamicTableTask = new AnalysisDbTableTask();
        dynamicTableTask.setId(IdUtil.getSnowflakeNextIdStr());
        dynamicTableTask.setTableId(tableId);
        dynamicTableTask.setSourceFileId(superviseResourceFile.getId());
        dynamicTableTask.setSheetNo(sheetNo);
        dynamicTableTask.setHeadStart(headStart);
        dynamicTableTask.setHeadEnd(headEnd);
        dynamicTableTask.setStartRow(startRow);
        dynamicTableTask.setEndRow(endRow);
        dynamicTableTask.setStartTime(new Date());
        dynamicTableTask.setCreateBy(SecurityUtils.getUsername());
        dynamicTableTask.setCreateTime(LocalDateTime.now());
        dynamicTableTaskMapper.insert(dynamicTableTask);
        AtomicInteger total = new AtomicInteger(0);
        EasyExcel.read(file.getInputStream(), new ReadListener<Map<Integer, String>>() {
            private final List<AnalysisDbTableTaskItem> dynamicTableTaskItems = new ArrayList<>();
            private final Map<String, List<ColumnData>> columnRows = new HashMap<>();

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                AnalysisDbTableTaskItem dynamicTableTaskItem = null;
                List<ColumnData> columnDatas = new ArrayList<>();
                try {
                    boolean hasRemark = false;
                    for (ExcelConvert excelConvert : excelConverts) {
                        Convert<?> convert = getExcelConvert(excelConvert); // 获取转换器
                        ColumnData columnData;
                        Object value = null;
                        if (convert == null) {
                            columnData = ColumnData.newInstance(excelConvert);
                            value = data.get(excelConvert.getExcelColumnNo());
                        } else {
                            columnData = ColumnData.newInstance(excelConvert, convert);
                            try {
                                value = convert.execute(columnData, data); // 转换数据
                            } catch (Exception e) {
                                columnData.setConvertErrorType(ConvertErrorType.ETL_ERROR);
                                columnData.setErrorMessage(e.getMessage());
                            }
                        }
                        // 是否存在备注
                        if (!hasRemark && columnData.getColumnName().equals("remark")) {
                            if ((value instanceof CharSequence && StrUtil.isNotBlank((CharSequence) value)) || ObjectUtil.isNotEmpty(value)) {
                                hasRemark = true;
                            }
                        }
                        columnData.setColumnValue(value);
                        columnDatas.add(columnData);
                    }
                    if (columnDatas.size() > 0) {
                        dynamicTableTaskItem = new AnalysisDbTableTaskItem();
                        dynamicTableTaskItem.setStatus(0);
                        for (ColumnData columnData : columnDatas) { // 校验规则
                            // 判断字段规则
                            if (columnData.getConvertErrorType() == null) { // ETL没有错误进行规则校验
                                try {
                                    checkRule(tableColumns, columnData, hasRemark);
                                } catch (RuleCheckRequiredNotRemarkException e) {
                                    columnData.setConvertErrorType(ConvertErrorType.REQUIRED_NOT_REMARK_ERROR);
                                    columnData.setErrorMessage(e.getMessage());
                                    dynamicTableTaskItem.setStatus(3);
                                    dynamicTableTaskItem.setErrorMessage(e.getMessage());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    columnData.setConvertErrorType(ConvertErrorType.RULE_ERROR);
                                    columnData.setErrorMessage(e.getMessage());
                                    dynamicTableTaskItem.setStatus(1);
                                    dynamicTableTaskItem.setErrorMessage(e.getMessage());
                                }
                            } else {
                                dynamicTableTaskItem.setStatus(1);
                                dynamicTableTaskItem.setErrorMessage(columnData.getErrorMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    dynamicTableTaskItem = new AnalysisDbTableTaskItem();
                    dynamicTableTaskItem.setStatus(1);
                    dynamicTableTaskItem.setErrorMessage(e.getMessage());
                }
                if (dynamicTableTaskItem != null) {
                    dynamicTableTaskItem.setId(IdUtil.getSnowflakeNextIdStr());
                    dynamicTableTaskItem.setJobId(dynamicTableTask.getId());
                    dynamicTableTaskItem.setSourceData(data);
                    dynamicTableTaskItem.setTargetData(columnDatas);
                    dynamicTableTaskItem.setCreateBy(SecurityUtils.getUsername());
                    dynamicTableTaskItem.setCreateTime(LocalDateTime.now());
                    // 每500条插入一次
                    if (dynamicTableTaskItems.size() >= 500) {
                        saveDataExecutor(table, dynamicTableTaskItems, columnRows);
                        dynamicTableTaskItems.clear();
                        columnRows.clear();
                    }
                    dynamicTableTaskItems.add(dynamicTableTaskItem); // 添加日志
                    if (dynamicTableTaskItem.getStatus() == 0) {
                        dynamicTableTaskItem.setConfirmBy(SecurityUtils.getUsername());
                        dynamicTableTaskItem.setConfirmTime(new Date());
                        columnRows.put(dynamicTableTaskItem.getId(), getColumnData(tableColumns, columnDatas));
                    }
                    total.incrementAndGet();
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                if (dynamicTableTaskItems.size() > 0) {
                    saveDataExecutor(table, dynamicTableTaskItems, columnRows, dynamicTableTask);
                } else {
                    dynamicTableTask.setEndTime(new Date());
                    dynamicTableTaskMapper.updateById(dynamicTableTask);
                }
            }

            @Override
            public boolean hasNext(AnalysisContext context) {
                if (endRow == null || endRow < startRow) {
                    return true;
                }
                Integer rowIndex = context.readRowHolder().getRowIndex();
                if (endRow > rowIndex + 1) {
                    return true;
                }
                this.doAfterAllAnalysed(null);
                return false;
            }
        }).sheet(sheetNo).headRowNumber(startRow - 1).doRead();
        dynamicTableTask.setTotal(total.get());
        return dynamicTableTask;
    }

    /**
     * 分页查询表数据
     * @param tableName    表名
     */
    @Override
    public Page<Map<String, Object>> pageTableData(String tableName) {
        return clickHouseTableMapper.selectTableRows(QueryHelper.getPage(true), tableName);
    }

    /**
     * 查询指定id
     * @param tableName
     * @param id
     * @return
     */
    @Override
    public Map<String, Object> getTableDataById(String tableName, String id) {
        return clickHouseTableMapper.selectTableDataById(tableName, id);
    }

    @Override
    public String addTableData(String tableName, Map<String, Object> record) {
        LambdaQueryWrapper<AnalysisDbTableColumn> columnWrapper = Wrappers.lambdaQuery(AnalysisDbTableColumn.class)
                .eq(AnalysisDbTableColumn::getTableName, tableName)
                .orderByAsc(AnalysisDbTableColumn::getSortOn);
        List<AnalysisDbTableColumn> columnList = dynamicTableColumnMapper.selectList(columnWrapper);
        Map<String,Object> data = formCamel2Underscore(columnList, record, true);
        String id = IdUtil.getSnowflakeNextIdStr();
        data.put("id", id);
        clickHouseTableMapper.addTableData(tableName, data);
        return id;
    }

    @Override
    public int editTableDataById(String tableName, String id, Map<String, Object> record) {
        LambdaQueryWrapper<AnalysisDbTableColumn> columnWrapper = Wrappers.lambdaQuery(AnalysisDbTableColumn.class)
                .eq(AnalysisDbTableColumn::getTableName, tableName)
                .orderByAsc(AnalysisDbTableColumn::getSortOn);
        List<AnalysisDbTableColumn> columnList = dynamicTableColumnMapper.selectList(columnWrapper);
        Map<String,Object> data = formCamel2Underscore(columnList, record, false);
        return clickHouseTableMapper.updateTableData(tableName, id, data);
    }

    /**
     * 将key为驼峰的map数据转为下划线风格key的map数据
     * @param columnList
     * @param record
     * @return
     */
    private Map<String,Object> formCamel2Underscore(List<AnalysisDbTableColumn> columnList, Map<String, Object> record, boolean isInsert){
        Map<String,Object> row = new LinkedHashMap<>();
        Map<String, Object> digestData = new HashMap<>();
        for (AnalysisDbTableColumn column : columnList) {
            String columnName = column.getColumnName();
            if(!column.getIsDb()){
                continue;
            }
            if (Arrays.asList("id", "digest_id", "create_by", "create_time", "update_by", "update_time").contains(columnName)) { // 跳过不允许插入的字段
                continue;
            }
            String columnCamelName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, columnName);
            Object value = record.containsKey(columnCamelName) ? record.get(columnCamelName) : record.get(columnName);
            value = convertValue(column, value);
            if(value == null){
                if(column.getNotNull() || column.getFormRequired()){
                    throw new RuntimeException("字段" + columnName + "不能为空");
                }
            }
            row.put(columnName, value);
            // 摘要字段
            if(Boolean.TRUE.equals(column.getIsDigest())){
                digestData.put(columnName, value);
            }

        }
        //摘要字段
        if (!digestData.isEmpty()) {
            row.put("digest_id", SecureUtil.signParamsMd5(digestData));
        }
        if(isInsert){
            row.put("create_by", SecurityUtils.getUsername());
            row.put("create_time", DateUtil.formatDateTime(new Date()));
        } else {
            row.put("update_by", SecurityUtils.getUsername());
            row.put("update_time", DateUtil.formatDateTime(new Date()));
        }
        return row;
    }


    private Object convertValue(AnalysisDbTableColumn column, Object value){
        Object rst = null;
        String defaultValue = column.getColumnDefault();
        if(ObjectUtil.isNull(value)){
            if(StringUtils.isNotBlank(defaultValue)){
                value = defaultValue;
            } else {
                return null;
            }
        }
        if (value instanceof Date && column.getColumnType() == CHColumnType.DateTime64) {
            rst = DateUtil.format((Date) value, DatePattern.NORM_DATETIME_PATTERN);
        } else if (value instanceof Date && column.getColumnType() == CHColumnType.Date) {
            rst = DateUtil.format((Date) value, DatePattern.NORM_DATE_PATTERN);
        } else if (value instanceof LocalDate) {
            rst = LocalDateTimeUtil.format((LocalDate) value, DatePattern.NORM_DATE_PATTERN);
        } else if (value instanceof LocalDateTime) {
            rst = LocalDateTimeUtil.format((LocalDateTime) value, DatePattern.NORM_DATETIME_PATTERN);
        } else {
            CHColumnType columnType = column.getColumnType();
            switch (columnType){
                case String:
                    rst = cn.hutool.core.convert.Convert.toStr(value);
                    break;
                case Bool:
                    rst = cn.hutool.core.convert.Convert.toBool(value);
                    break;
                case Int8:
                case Int16:
                case Int32:
                    rst = cn.hutool.core.convert.Convert.toInt(value);
                    break;
                case Int64:
                    rst = cn.hutool.core.convert.Convert.toLong(value);
                    break;
                case Float32:
                    rst = cn.hutool.core.convert.Convert.toFloat(value);
                    break;
                case Float64:
                    rst = cn.hutool.core.convert.Convert.toDouble(value);
                    break;
                case Decimal:
                    rst = cn.hutool.core.convert.Convert.toBigDecimal(value);
                    if(column.getColumnPrecise() != null && column.getColumnScale() != null){
                        rst = new BigDecimal(value.toString()).setScale(column.getColumnPrecise(), RoundingMode.HALF_UP);
                    }
            }

        }
        return rst;
    }

    /**
     * 删除表数据
     * @param tableName 表ID
     * @param ids     数据ID集
     */
    @Override
    public void deleteTableData(String tableName, List<String> ids) {
        clickHouseTableMapper.deleteTableData(tableName, ids);
    }

    /**
     * 获取数据清洗器
     * @param excelConvert excel转换
     */
    private Convert<?> getExcelConvert(ExcelConvert excelConvert) {
        if (excelConvert != null && StrUtil.isNotBlank(excelConvert.getConvertKey())) { // 是否需要进行数据转换
            for (Convert<?> convert : converts) {
                if (convert.compare(excelConvert.getConvertKey())) { // 判断转换规则
                    return convert;
                }
            }
        }
        return null;
    }

    /**
     * 字段校验
     * @param tableColumns 数据表字段集
     * @param columnData   字段名称与值
     * @param hasRemark    有备注信息
     */
    private void checkRule(List<AnalysisDbTableColumn> tableColumns, ColumnData columnData, boolean hasRemark) throws RuleCheckException {
        Assert.notEmpty(tableColumns, "数据表字段不能为空");
        LambdaQueryWrapper<AnalysisDbTableColumnRule> columnRuleQueryWrapper = new LambdaQueryWrapper<AnalysisDbTableColumnRule>()
                .eq(AnalysisDbTableColumnRule::getTableName, tableColumns.get(0).getTableName());
        Map<String, List<AnalysisDbTableColumnRule>> columnRuleMap = dynamicTableColumnRuleMapper.selectList(columnRuleQueryWrapper)
                .stream().collect(Collectors.groupingBy(o -> o.getColumnName()));
        for (AnalysisDbTableColumn tableColumn : tableColumns) {
            if (!columnData.getColumnName().equals(tableColumn.getColumnName())) { // 判断字段名称是否相等
                continue;
            }
            Object value = columnData.getColumnValue();
            if(Boolean.TRUE.equals(tableColumn.getNotNull()) && ObjectUtil.isEmpty(value)) {
                throw new RuleCheckRequiredException(tableColumn.getColumnName() + "字段为必填字段，不能为空");
            }
            List<AnalysisDbTableColumnRule> columnRules = columnRuleMap.get(tableColumn.getColumnName());
            if (ObjectUtil.isEmpty(columnRules)) { // 没有规则
                break;
            }
            //排序
            columnRules = columnRules.stream().sorted(Comparator.comparingInt(AnalysisDbTableColumnRule::getSortOn)).collect(Collectors.toList());
            for(AnalysisDbTableColumnRule columnRule : columnRules){
                if (ObjectUtil.isEmpty(value)) {
                    break;
                }
                if (value instanceof CharSequence) { // 正则规则
                    if (!ReUtil.isMatch(columnRule.getRulePattern(), (CharSequence) value)) {
                        throw new RuleCheckRegExpException(columnRule.getRuleMessage());
                    }
                }
            }
        }
    }

    /**
     * 获取数据库插入字段信息
     * @param tableColumns 数据库字段
     * @param columnDatas  转换后字段信息
     */
    private List<ColumnData> getColumnData(List<AnalysisDbTableColumn> tableColumns, List<ColumnData> columnDatas) {
        List<ColumnData> listData = new ArrayList<>();
        listData.add(ColumnData.newInstance("id", "ID", CHColumnType.String, IdUtil.getSnowflakeNextIdStr()));
        Map<String, Object> params = new HashMap<>();
        for (AnalysisDbTableColumn tableColumn : tableColumns) {
            if (Arrays.asList("id", "digest_id", "create_by", "create_time", "update_by", "update_time").contains(tableColumn.getColumnName())) { // 跳过不允许插入的字段
                continue;
            }
            ColumnData columnData = new ColumnData();
            columnData.setColumnName(tableColumn.getColumnName());
            for (ColumnData item : columnDatas) {
                if (item.getColumnName().equals(columnData.getColumnName())) { // 判断字段名称是否相等
                    Object val = item.getColumnValue();
                    if (val instanceof Date && tableColumn.getColumnType() == CHColumnType.DateTime64) {
                        val = DateUtil.format((Date) val, DatePattern.NORM_DATETIME_PATTERN);
                    } else if (val instanceof Date && tableColumn.getColumnType() == CHColumnType.Date) {
                        val = DateUtil.format((Date) val, DatePattern.NORM_DATE_PATTERN);
                    } else if (val instanceof LocalDate) {
                        val = LocalDateTimeUtil.format((LocalDate) val, DatePattern.NORM_DATE_PATTERN);
                    } else if (val instanceof LocalDateTime) {
                        val = LocalDateTimeUtil.format((LocalDateTime) val, DatePattern.NORM_DATETIME_PATTERN);
                    }
                    columnData.setColumnValue(val); // 设置字段值
                    listData.add(columnData);
                    break;
                }
            } // 没有值就不进行填充
            if (Boolean.TRUE.equals(tableColumn.getIsDigest())) { // 判断是否需要加入到摘要加密中
                params.put(columnData.getColumnName(), columnData.getColumnValue());
            }
        }
        if (params.size() > 0) {
            listData.add(ColumnData.newInstance("digest_id", "摘要ID", CHColumnType.String, SecureUtil.signParamsMd5(params)));
        }
        listData.add(ColumnData.newInstance("create_by", "创建者", CHColumnType.String, SecurityUtils.getUsername()));
        listData.add(ColumnData.newInstance("create_time", "创建时间", CHColumnType.DateTime64, DateUtil.formatDateTime(new Date())));
        return listData;
    }

    /**
     * 异步保存数据任务
     * @param table                 表信息
     * @param dynamicTableTaskItems 日志信息
     * @param columnRows            数据
     */
    private void saveDataExecutor(AnalysisDbTable table, List<AnalysisDbTableTaskItem> dynamicTableTaskItems, Map<String, List<ColumnData>> columnRows) {
        saveDataExecutor(table, dynamicTableTaskItems, columnRows, null);
    }

    /**
     * 异步保存数据任务
     * @param table                 表信息
     * @param dynamicTableTaskItems 日志信息
     * @param columnRows            数据
     * @param dynamicTableTask      结束更新结束时间
     */
    private void saveDataExecutor(AnalysisDbTable table, List<AnalysisDbTableTaskItem> dynamicTableTaskItems, Map<String, List<ColumnData>> columnRows, AnalysisDbTableTask dynamicTableTask) {
        List<AnalysisDbTableTaskItem> taskItems = new ArrayList<>(dynamicTableTaskItems);
        Map<String, List<ColumnData>> rowDataMap = new HashMap<>(columnRows);
        datapoolExecutor.execute(() -> {
            if (rowDataMap.size() > 0) { // 不需要确认或者已确认才把数据保存到数据库中
                for (AnalysisDbTableTaskItem taskItem : taskItems) {
                    List<ColumnData> rowData = rowDataMap.get(taskItem.getId());
                    if (rowData == null) {
                        continue;
                    }
                    try {
                        for (ColumnData item : rowData) {
                            if ("id".equals(item.getColumnName())) {
                                taskItem.setDataId((String) item.getColumnValue());
                                break;
                            }
                        }
                        if (StrUtil.isBlank(taskItem.getDataId())) {
                            throw new IllegalArgumentException("数据ID不存在");
                        }
                        clickHouseTableMapper.insertTableData(table.getTableName(), rowData);
                    } catch (Exception e) {
                        taskItem.setStatus(1);
                        taskItem.setErrorMessage(e.getMessage());
                    }
                }
            }
            dynamicTableTaskItemMapper.insertBatch(taskItems);
            taskItems.clear();
            rowDataMap.clear();
            if (dynamicTableTask != null) {
                dynamicTableTask.setEndTime(new Date());
                dynamicTableTaskMapper.updateById(dynamicTableTask);
            }
        });
    }
}
