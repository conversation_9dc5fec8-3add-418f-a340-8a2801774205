@import './base.css';
@import './fonts.css';

* {
  font-family: '方正粗金陵';
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* 黑夜模式 */
.x-layout-night {
  filter: invert(100%) hue-rotate(180deg);

  img {
    filter: invert(100%) hue-rotate(-180deg);
  }
}

/* 色弱模式 */
.x-layout-color-weak {
  filter: invert(80%);
}

/* 灰色模式 */
.x-layout-color-gray {
  filter: saturate(0%);
}

/* 滚动条美化 */
.beauty-scroll::-webkit-scrollbar {
  width: 4px;
}

.beauty-scroll::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background: #7f7f7f;
}

/* ----------- modal全屏 ----------- */
.full-modal.ant-modal {
  max-width: 100%;
  top: 0;
  padding-bottom: 0;
  margin: 0;
}

.full-modal.ant-modal .ant-modal-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.full-modal.ant-modal .ant-modal-body {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
}

/* ----------- ant 问题修改 ----------- */
.ant-descriptions-item-label {
  white-space: nowrap;
}


.ant-tooltip-inner {
  max-height: 40vh;
  max-width: 40vw;
  overflow: auto;
}

.scrollBeauty {
  /* 隐藏轨道但保留滑块 */
  overflow: auto;

  &::-webkit-scrollbar {
    width: 4px; /* 滚动条宽度 */
    height: 4px; /* 水平滚动条高度 */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* 完全透明轨道 */
  }

  &:hover::-webkit-scrollbar-thumb {
    display: block;
  }

  &::-webkit-scrollbar-thumb {
    display: none;
    background: rgba(0, 0, 0, 0.1); /* 半透明滑块 */
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5); /* 悬停时更明显 */
  }

  /* 隐藏上下按钮 */

  &::-webkit-scrollbar-button {
    display: none;
  }
}
