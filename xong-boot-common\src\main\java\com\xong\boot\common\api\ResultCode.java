package com.xong.boot.common.api;

/**
 * 错误代码
 * <AUTHOR>
 */
public enum ResultCode {
    SUCCESS(200, "成功"),
    FAIL(10000, "请求失败"),
    DATA_ACCESS_ERROR(12000, "数据操作错误"),
    FILE_ERROR(13000, "文件操作异常"),
    UNAUTHORIZED_FAIL(14000, "未经授权"),
    ACCESS_DENIED_FAIL(14001, "拒绝访问"),
    RE_LOGIN(14002, "请重新登录");

    private final int code;
    private final String message;

    ResultCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }
}
