package com.cb.ai.data.analysis.petition.service.impl;


import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionPurposeDateQueryConditionVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.RegisterDateQueryCondition;
import com.cb.ai.data.analysis.petition.domain.vo.request.SelfDeptSendOrgNameQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsStaticsQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo;
import com.cb.ai.data.analysis.petition.enums.StaticsSelectOptionTypeEnum;
import com.cb.ai.data.analysis.petition.mapper.SsMapStaticsMapper;
import com.cb.ai.data.analysis.petition.service.SsMapStaticsService;
import com.xong.boot.common.api.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


/***
 * <AUTHOR>
 * 信访报告图表相关
 */
@Service
public class SsMapStaticsServiceImpl implements SsMapStaticsService {

    @Autowired
    private SsMapStaticsMapper ssMapStaticsMapper;

    @Override
    public List<MapStaticsVo> countByRegion(SsStaticsQueryVo ssStaticsQueryVo) {
        List<MapStaticsVo> list = ssMapStaticsMapper.countByRegion(ssStaticsQueryVo);
        return list;
    }

    @Override
//    @Cacheable(value = "domainCache", key = "#root.methodName + '_' + #ssStaticsQueryVo.region + '_' + #ssStaticsQueryVo.startDate + '_' + #ssStaticsQueryVo.endDate + '_' + #ssStaticsQueryVo.types+ '_' + #ssStaticsQueryVo.fileName")
    public List<MapStaticsVo> countByDomain(SsStaticsQueryVo ssStaticsQueryVo) {
        List<MapStaticsVo> list = ssMapStaticsMapper.countByDomain(ssStaticsQueryVo);
        return list;
    }

    @Override
    public Result selectOptions(Integer type) {
        List<String> list;
        if (type == StaticsSelectOptionTypeEnum.DOMAIN_SELECT_OPTION.getType()) {
            list = ssMapStaticsMapper.domainSelectOptions(type);
        } else {
            list = ssMapStaticsMapper.regionSelectOptions(type);
        }
        return Result.successData(list);
    }

    @Override
    public Result countBySelfDeptSendOrgName(SelfDeptSendOrgNameQueryVo selfDeptSendOrgNameQueryVo) {
        List<MapStaticsVo> responsibleDeptStaticsVos = ssMapStaticsMapper.countBySelfDeptSendOrgName(selfDeptSendOrgNameQueryVo);
        return Result.successData(responsibleDeptStaticsVos);
    }

    @Override
    public Result registerDate(RegisterDateQueryCondition registerDateQueryCondition) {
        List<MapStaticsVo> hotline = ssMapStaticsMapper.countByRegisterDate(registerDateQueryCondition);
        return Result.successData(hotline);
    }

    @Override
    public Result petitionPurpose(PetitionPurposeDateQueryConditionVo petitionPurposeDateQueryConditionVo) {
        List<MapStaticsVo> petitionPurpose = ssMapStaticsMapper.countByPetitionPurpose(petitionPurposeDateQueryConditionVo);
        return Result.successData(petitionPurpose);
    }

    @Override
    public Result analyzedStatus() {
        List<MapStaticsVo> hotline = ssMapStaticsMapper.countByAnalyzedStatus();
        return Result.successData(hotline);
    }


}
