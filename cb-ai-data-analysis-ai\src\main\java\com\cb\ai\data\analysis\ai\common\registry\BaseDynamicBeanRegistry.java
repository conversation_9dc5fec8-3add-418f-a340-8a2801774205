package com.cb.ai.data.analysis.ai.common.registry;

import cn.hutool.core.collection.CollectionUtil;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/6/6 15:07
 * @Copyright (c) 2025
 * @Description Bean注册工厂
 */
abstract class BaseDynamicBeanRegistry implements BeanDefinitionRegistryPostProcessor, EnvironmentAware {

    Environment environment;
    
    protected static final Set<String> PACKAGE_SET;

    protected static final String BASE_PACKAGE_NAME;
    
    static {
        PACKAGE_SET = new HashSet<>(32);
        BASE_PACKAGE_NAME = BaseDynamicBeanRegistry.class.getPackageName().replace(".common.registry", "");
        String pattern = ResourcePatternResolver.CLASSPATH_URL_PREFIX + BASE_PACKAGE_NAME.replace(".", "/") + "/**/*.class";
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            Resource[] resources = resolver.getResources(pattern);
            for (Resource resource : resources) {
                MetadataReaderFactory readerFactory = new CachingMetadataReaderFactory();
                MetadataReader metadataReader = readerFactory.getMetadataReader(resource);
                ClassMetadata classMetadata = metadataReader.getClassMetadata();
                String className = classMetadata.getClassName();
                String packageName = className.substring(0, className.lastIndexOf("."));
                PACKAGE_SET.add(packageName);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @description 动态注册实现
     * @param beanDefinitionRegistry
     * @param scanner
     * @createtime 2025/7/1 下午6:22
     * <AUTHOR>
     * @version 1.0
     */
    abstract void dynamicRegistry(BeanDefinitionRegistry beanDefinitionRegistry, ClassPathScanningCandidateComponentProvider scanner);

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry beanDefinitionRegistry) throws BeansException {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        dynamicRegistry(beanDefinitionRegistry, scanner);
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) throws BeansException {}

    String packagePath(String relativePackagePath) {
        List<String> findList = PACKAGE_SET.stream()
            .filter(packageName -> packageName.contains(relativePackagePath))
            .sorted((s1, s2) -> s2.length() - s1.length())
            .toList();
        if (CollectionUtil.isNotEmpty(findList)) {
            return findList.get(0);
        } else {
            return MergeUtil.mergePath(BASE_PACKAGE_NAME, relativePackagePath, '.');
        }
    }

    //void registerBean(BeanDefinition, BeanDefinitionRegistry registry) {
    //    BeanDefinition eventBean = BeanDefinitionBuilder
    //            .genericBeanDefinition(eventBeanCLass)
    //            .addConstructorArgValue(aiWork)
    //            .addConstructorArgValue(aiWork.getIdKey())
    //            .addConstructorArgValue(baseAiWork.getInvokeUrl())
    //            .setInitMethodName("initEvents")
    //            .setScope(BeanDefinition.SCOPE_SINGLETON)
    //            .setPrimary(aiWork.isDefault())
    //            .setLazyInit(true)
    //            .getBeanDefinition();
    //    beanDefinitionRegistry.registerBeanDefinition(aiWork.getIdKey(), eventBean);
    //}

}
