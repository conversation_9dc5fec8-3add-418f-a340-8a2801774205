package com.cb.ai.data.analysis.petition.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.cb.ai.data.analysis.petition.mapper.SsRepeatResultGroupMapper;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultGroupService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SsRepeatResultGroupServiceImpl extends BaseServiceImpl<SsRepeatResultGroupMapper, SsRepeatResultGroup> implements SsRepeatResultGroupService {

    @Override
    public Page<SsRepeatResultGroup> pageByWrapper(Page<SsRepeatResultGroup> page, Wrapper<SsRepeatResultGroup> wrapper) {
        return baseMapper.pageByWrapper(page, wrapper);
    }
}
