package com.cb.ai.data.analysis.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AI 会话历史记录(AiChatHistory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-07 14:23:50
 */
@Service("aiChatHistoryService")
public interface AiChatHistoryService extends IService<AiChatHistory> {

    /*
     * 初始化AI会话历史记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public boolean initAiChatHistory(String sessionId);

    /*
     * 获取对应会话的历史会话记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public AiChatHistory getChatHistoryBySessionId(String sessionId);

    /*
     * 获取对应用户的历史会话记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public AiChatHistory getChatHistoryByUserId(String userId);

    /*
     * 删除对应会话历史记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public boolean removeBySessionId(String sessionId);

    /*
     * 删除对应用户的历史会话记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public boolean removeByUserId(String userId);

    /*
     * 删除对应会话Id的历史会话记录
     * @param sessionId 会话ID
     *
     * @return
     */
    public boolean removeByIds(List<String> ids);

}

