<script lang="ts" setup>
import { computed, ref, useTemplateRef } from 'vue'
import MinioUploadModal from '@/views/file/components/MinioUploadModal.vue'
import { useImStore } from '@/stores'
import generator from '@/views/ai/modal/addSummary/generator.vue'
import knowledgeSelect from './knowledgeSelect.vue'
import mcpSelect from './mcpSelect.vue'
import { useRoute } from 'vue-router'
import swiper from '../swiper.vue'
import errorLog from '@/utils/errorLog'

const route = useRoute()
const MinioUploadModalRef = useTemplateRef('MinioUploadModalRef')
const imStore = useImStore()
const generatorRef = useTemplateRef('generatorRef')
const activeId = defineModel('activeId', {
  default: 0
})
const visible = defineModel('visible', {
  default: false
})

const menus = computed(() => {
  const arr = [
    {
      label: '知识库',
      value: '知识库',
      id: imStore.knowledgeId
    }
  ]
  if (!imStore.deepThink) {
    arr.push(
      {
        label: '生成报告',
        value: '生成报告',
        id: imStore.reportId
      }
      // {
      //   label: '大数据分析',
      //   value: '大数据分析',
      //   id: imStore.mcpId
      // }
    )
  }
  return arr
})

const reportListTpl = ref([])

function selectReport(item) {
  // visible.value = false
  // MinioUploadModalRef.value?.openModal()
  // return
  if (item.fn) {
    item.fn()
  } else {
    const knowledgeList = imStore.quotes.filter((v) => v.parentId === imStore.knowledgeId)
    imStore.promote = item.promote
    imStore.quotes = [
      {
        parentId: imStore.reportId,
        id: item.id,
        label: item.label,
        params: {
          analyseTag: [item.value]
        }
      },
      ...knowledgeList
    ]
    visible.value = false
  }
}

const contentHeight = computed(() => {
  return route.name === 'AiWelcome' ? '30vh' : '70vh'
})

async function getTmpl() {
  try {
    // const res = await listTpl()
    reportListTpl.value = [
      {
        label: '巡视巡察生成报告',
        value: '巡视巡察报告/生成报告',
        analyseTagData: {
          getOutline: ['巡视巡察报告'],
          generateOutline: ['巡视巡察报告/生成大纲'],
          createContent: ['巡视巡察报告/生成报告'],
          reCreateContent: ['巡视巡察报告/重新生成']
        },
        class: 'special',
        promote: '请根据知识库内容提取问题清单，并根据提供的模板生成巡视巡察报告'
      },
      {
        label: '开展警示教育工作方案',
        value: '开展警示教育工作方案/生成报告',
        analyseTagData: {
          getOutline: ['开展警示教育工作方案'],
          generateOutline: ['开展警示教育工作方案/生成大纲'],
          createContent: ['开展警示教育工作方案/生成报告'],
          reCreateContent: ['开展警示教育工作方案/重新生成']
        },
        class: 'special',
        promote: '请根据知识库内容，生成开展警示教育工作方案'
      },
      {
        label: '理论学习方案',
        value: '理论学习方案/生成报告',
        analyseTagData: {
          getOutline: ['理论学习方案'],
          generateOutline: ['理论学习方案/生成大纲'],
          createContent: ['理论学习方案/生成报告'],
          reCreateContent: ['理论学习方案/重新生成']
        },
        class: 'special',
        promote: '请根据知识库内容，生成理论学习方案'
      },
      {
        label: '纪检监察建议',
        value: '纪检监察建议/生成报告',
        analyseTagData: {
          getOutline: ['纪检监察建议'],
          generateOutline: ['纪检监察建议/生成大纲'],
          createContent: ['纪检监察建议/生成报告'],
          reCreateContent: ['纪检监察建议/重新生成']
        },
        class: 'special',
        promote: '请根据知识库内容，生成纪检监察建议'
      }
      // ...(res.data || []).map((v, i) => ({
      //   id: `${i}`,
      //   label: v,
      //   value: `生成报告(纪委)/${v}`,
      //   promote: '请根据知识库内容提取问题清单，并根据提供的模板生成报告'
      // })),
      // {
      //   label: '上传模板',
      //   value: '上传模板',
      //   class: 'special',
      //   fn() {
      //     visible.value = false
      //     MinioUploadModalRef.value?.openModal()
      //   }
      // }
    ]
    reportListTpl.value.forEach((v, i) => {
      v.fn = () => {
        visible.value = false
        const baseIds = []
        for (const key in imStore.quotes) {
          const data = imStore.quotes[key]
          // 拿知识库已选的下标
          if (data.parentId === imStore.knowledgeId) {
            baseIds.push(data.id)
          }
        }
        generatorRef.value.open({
          promote: v.promote,
          baseIds,
          analyseTagData: v.analyseTagData
        })
      }
    })
  } catch (err) {
    errorLog.push({
      error: err,
      stack: err.stack,
      title: '生成报告获取模板失败'
    })
  }
}

function isSelect(id) {
  // 获取已选知识库ID
  return imStore.quotes.some((item) => item.parentId === imStore.reportId && item.id === id)
}

function uploadSuccess(files) {
  if (files && files.length > 0) {
    imStore.quotes = [
      {
        parentId: imStore.reportId,
        label: files.at().name,
        params: {
          analyseTag: [`生成报告(纪委)${files.at().filePath}`]
        }
      }
    ]
    imStore.promote = '请根据提供的模板生成报告'
    MinioUploadModalRef.value?.closeModal()
  }
}

getTmpl()
</script>

<template>
  <div class="inputMenu">
    <swiper v-model:value="menus">
      <template v-slot:default="{ data }">
        <div :class="['item', data.id === activeId && 'active']" @click="activeId = data.id">
          <div class="itemLabel">
            {{ data.label }}
          </div>
        </div>
      </template>
    </swiper>

    <!--      生成报告-->
    <div v-if="activeId === imStore.reportId" class="flowLayout scrollBeauty">
      <div
        v-for="item in reportListTpl"
        :key="item.value"
        :class="['data', isSelect(item.id) && 'active', item.class]"
        @click="selectReport(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <!--    知识库-->
    <knowledgeSelect v-else-if="activeId === imStore.knowledgeId"></knowledgeSelect>
    <!--    mcp-->
    <mcpSelect v-else-if="activeId === imStore.mcpId" />

    <div v-else>暂无其他分类数据</div>
  </div>
  <generator ref="generatorRef" />
  <MinioUploadModal ref="MinioUploadModalRef" :multiple="false" @success="uploadSuccess" />
</template>

<style lang="less">
.inputMenu {
  width: 100%;
  user-select: none;
  display: flex;
  flex-direction: column;
  --content-height: v-bind(contentHeight);

  .item {
    text-align: center;
    cursor: pointer;
    font-weight: 600;

    .itemLabel {
      width: 95%;
      margin: 0 auto;
      padding-block: 5px;
      border-radius: 5px;

      &:hover {
        background-color: var(--primary-color);
        color: var(--layout-dark-color);
      }
    }
  }

  .active .itemLabel {
    background-color: var(--primary-color);
    color: var(--layout-dark-color);
  }

  .flowLayout {
    padding-top: 5px;
    margin-top: 5px;
    border-top: 2px dashed var(--transparent-black);
    flex-grow: 1;
    width: 100%;
    height: var(--content-height);
    align-items: stretch;
    grid-auto-rows: max-content;
    overflow: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;

    .data {
      width: 100%;
      height: 100%;
      border-radius: 15px;
      border: 1px solid rgba(228, 228, 228, 0.44);
      padding: 5px 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1rem;

      &:hover {
        background-color: var(--primary-color);
        color: var(--layout-dark-color);
      }
    }

    .special {
      background-color: var(--transparent-black);
    }

    .active {
      background-color: var(--primary-color);
      color: var(--layout-dark-color);
    }
  }
}
</style>
