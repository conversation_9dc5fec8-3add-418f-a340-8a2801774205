<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.basdata.mapper.finance.BasContractInfoMapper">

    <resultMap type="com.cb.ai.data.analysis.basdata.domain.entity.finance.BasContractInfo" id="BasContractInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
        <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
        <result property="contractType" column="contract_type" jdbcType="VARCHAR"/>
        <result property="signingDate" column="signing_date" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="partA" column="part_a" jdbcType="VARCHAR"/>
        <result property="partB" column="part_b" jdbcType="VARCHAR"/>
        <result property="telName" column="tel_name" jdbcType="VARCHAR"/>
        <result property="telPhone" column="tel_phone" jdbcType="VARCHAR"/>
        <result property="contractObject" column="contract_object" jdbcType="VARCHAR"/>
        <result property="contractAmount" column="contract_amount" jdbcType="NUMERIC"/>
        <result property="payItem" column="pay_item" jdbcType="VARCHAR"/>
    </resultMap>

    

</mapper>

