package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySession;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistorySessionMapper;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistorySessionMessageMapper;
import com.cb.ai.data.analysis.ai.service.AiChatHistorySessionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * AI历史会话 Service
 * <AUTHOR>
 */
@Service
public class AiChatHistorySessionServiceImpl extends ServiceImpl<AiChatHistorySessionMapper, AiChatHistorySession> implements AiChatHistorySessionService {
    private final AiChatHistorySessionMessageMapper aiChatHistorySessionMessageMapper;

    public AiChatHistorySessionServiceImpl(AiChatHistorySessionMessageMapper aiChatHistorySessionMessageMapper) {
        this.aiChatHistorySessionMessageMapper = aiChatHistorySessionMessageMapper;
    }

    /**
     * 删除历史会话
     * @param sessionIds 会话ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBySessionId(List<String> sessionIds) {
        // 删除删除历史会话消息
        aiChatHistorySessionMessageMapper.delete(Wrappers.<AiChatHistorySessionMessage>lambdaQuery().in(AiChatHistorySessionMessage::getSessionId, sessionIds));
        // 删除删除历史会话
        baseMapper.deleteByIds(sessionIds);
    }
}
