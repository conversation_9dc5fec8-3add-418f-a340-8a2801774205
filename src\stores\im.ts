import { defineStore } from 'pinia'
import { createVNode, reactive, ref } from 'vue'
// import { dayjs } from '@/core'
import { chatHistory, config, messages } from '@/api/ai/index'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { uuid } from '@/utils'
import { dayjs } from '@/core'
import { fetchStream } from '@/utils/http'
import { useUserStore } from '@/stores/user'
import { NumberZh } from 'number-zh'
import errorLog from '@/utils/errorLog'

export interface Message {
  id: string
  avatar?: string
  name: string
  createTime: string | number
  role: number //0系统,1用户，2AI
  loading: boolean
  loadingText: string
  content: MsgContent
  check?: boolean //消息是否被选中
}

export interface MsgContent {
  unexpand: boolean //思考默认隐藏
  files?: string[] //[]消息文件
  thinking?: boolean //是否显示思考过程
  thinkStatus?: boolean //思考状态已结束、思考中
  thinkStatusText?: string
  thinkText?: string //思考文本
  content: string
  quote?: string[] //引用信息
  error?: string[] //错误信息
  dbTableInfo?: string //数据表信息（包含sql语句以及中文映射）
  progress?: number //0-100
  deepThink?: boolean //是否深度思考
}

export interface Conversation {
  sessionId: string
  title: string
  loading: boolean
  abortController?: AbortController
  createTime: string | number
  updateTime: string | number
  more: boolean //是否还有更多消息
  list?: Message[]
}

interface MultipartThinkItem {
  id: string
  thinking: boolean
  thinkStatus: boolean
  thinkStatusText: string
  thinkText: string
  unexpand: boolean
}

interface Quotes {
  id: any
  label: string
  params: any
}

export const useImStore = defineStore('im', () => {
  const promote = ref('')
  const deepThink = ref(false)
  const conversation = ref<Conversation[]>([])
  const quotes = ref<Quotes[]>([])
  const loading = reactive({
    session: false,
    sendMsg: false
  })
  const usePresetScoreThreshold = ref(false)
  const reportId = ref(0)
  const knowledgeId = ref(1)
  const knowledgeFileId = ref(4)
  const mcpId = ref(2)
  const fileId = ref(3)

  const sessionPrms = ref<any>(undefined)
  const deepThinkInfo = reactive<Record<string, any>>({
    visible: false,
    planContent: '', // 用于展示
    planData: '', // 用于占存上一次的深度研究计划
    orginalPromote: '', // 用于占存上一次的原始输入
    formData: {
      operate: null,
      promote: ''
    }
  })

  const numberZh = new NumberZh()

  async function getSession({ force = false } = { force: false }) {
    try {
      if (sessionPrms.value) {
        return sessionPrms.value
      } else if (force || conversation.value.length === 0) {
        loading.session = true
        sessionPrms.value = chatHistory
          .userChatHistory()
          .then((res) => {
            if (res.code === 200) {
              conversation.value = (res.data || []).map((v: Conversation) => ({
                ...v,
                loading: false,
                more: true,
                list: []
              }))
            } else {
              errorLog.push({
                msg: res.message,
                stack: 'getSession',
                title: '获取会话失败',
                data: res
              })
              message.error(res.message)
            }
            return res
          })
          .finally(() => {
            sessionPrms.value = undefined
            loading.session = false
          })
        return sessionPrms.value
      }
    } catch (err) {
      errorLog.push({ msg: err.message, stack: err.stack, title: '获取会话失败' })
    } finally {
    }
  }

  function resetSession() {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '警告',
        icon: createVNode(ExclamationCircleOutlined),
        content: '是否删除全部会话数据',
        onOk() {
          loading.session = true
          return chatHistory
            .del(conversation.value.map((v) => v.sessionId))
            .then((res) => {
              if (res.code === 200) {
                resolve(res)
                conversation.value = []
              } else {
                errorLog.push({
                  msg: res.message,
                  stack: 'resetSession',
                  title: '重置会话失败',
                  data: res
                })
                reject(res)
                message.error(res.message)
              }
              return res
            })
            .finally(() => {
              loading.session = false
            })
        }
      })
    })
  }

  function delSession(ids: string[], index: number) {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '警告',
        icon: createVNode(ExclamationCircleOutlined),
        content: '是否删除该数据',
        onOk() {
          loading.session = true
          return chatHistory
            .del(ids)
            .then((res) => {
              if (res.code === 200) {
                resolve(res)
                conversation.value.splice(index, 1)
              } else {
                errorLog.push({
                  msg: res.message,
                  stack: 'delSession',
                  title: '删除会话失败',
                  data: ids
                })
                reject(res)
                message.error(res.message)
              }
              return res
            })
            .finally(() => {
              loading.session = false
            })
        }
      })
    })
  }

  async function addSession(data: Conversation) {
    try {
      loading.session = true
      const res = await chatHistory.add({ title: data.title })
      if (res.code === 200) {
        conversation.value.unshift({
          ...res.data,
          more: false,
          loading: (data.list || []).every((v) => v.loading),
          list: data.list || []
        })
      } else {
        errorLog.push({ msg: res.message, stack: 'addSession', title: '创建会话失败', data: res })
        message.error(res.message)
      }
      return res
    } catch (err) {
      errorLog.push({ msg: err.message, stack: err.stack, title: '创建会话失败', data: data })
    } finally {
      loading.session = false
    }
  }

  async function saveMsg(data) {
    try {
      const res = await messages.addMsg(data)
      if (res.code === 200) {
      } else {
        throw new Error(res.message)
      }
    } catch (err) {
      errorLog.push({ msg: err.message, stack: err.stack, title: '保存消息失败', data: data })
    }
  }

  function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 100)
    const minuteStr = minutes < 10 ? '0' + minutes : minutes.toString()
    const secStr = secs < 10 ? '0' + secs : secs.toString()
    const msStr = ms < 10 ? '0' + ms : ms.toString()
    return minuteStr + ':' + secStr + '.' + msStr
  }

  function mergeObjectsToArray(arr) {
    // 浅合并
    return arr.reduce((merged, obj) => {
      for (const key in obj) {
        if (merged[key] instanceof Array) {
          merged[key] = [...new Set(merged[key].concat(obj[key]))]
        } else if (merged[key] instanceof Object) {
          merged[key] = {
            ...merged[key],
            ...obj[key]
          }
        } else {
          merged[key] = obj[key]
        }
      }
      return merged
    }, {})
  }

  function abort(sessionId) {
    const current = conversation.value.find((v) => v.sessionId === sessionId)
    if (current?.abortController) {
      current.abortController.abort()
    }
  }

  function isJSON(str: string) {
    if (typeof str === 'string') {
      try {
        const obj = JSON.parse(str)
        if (obj) {
          return true
        }
      } catch (e) {
        return false
      }
    }
    return false
  }

  function getThinkStepIndex(think: number, step: number, action: string) {
    if (think === 0) {
      if (action === 'think') {
        return '0'
      } else {
        console.error('索引错误，在初始化的时候有其他行为！')
        return '-1'
      }
    } else {
      return `${think}.${step}.${action}`
    }
  }

  async function sendMsg(sessionId) {
    if (!promote.value?.trim() || loading.sendMsg) {
      return
    }
    const userStore = useUserStore()
    const avatar = userStore.userDetails.avatar
    const realname = userStore.userDetails.realname
    loading.sendMsg = true
    // 先把上一个中断
    abort(sessionId)

    // 校验通过
    let params: any = {}
    if (deepThink.value) {
      params = {
        sessionId: undefined,
        deepThink: deepThink.value,
        promote: promote.value,
        interruptFeedback: deepThinkInfo.formData.operate,
        ...mergeObjectsToArray(quotes.value.map((v) => v.params))
      }
      deepThinkInfo.formData.operate = ''
      deepThinkInfo.formData.promote = ''
    } else {
      params = {
        sessionId: undefined,
        usePresetScoreThreshold: usePresetScoreThreshold.value,
        promote: promote.value,
        //mcp新增参数（使用的基础表数组、谈话历史消息）
        basic_data_table: undefined,
        history_messages: undefined,
        ...mergeObjectsToArray(quotes.value.map((v) => v.params))
      }
    }
    if (Array.isArray(params.analyseTag) && params.analyseTag.includes('大数据分析')) {
      // 单独的大数据分析处理
      //mcp新增参数（使用的基础表数组、谈话历史消息）
      const find = conversation.value.find((v) => v.sessionId === sessionId)
      if (find && find.list) {
        const messageArr = find.list.filter(
          (v) =>
            v.role !== 0 &&
            v.content &&
            v.content.content !== '以上内容为数字昆纪AI生成，请注意识别！' &&
            v.content.content !== ''
        )
        params.history_messages = messageArr
          .map((v) => {
            return {
              role: v.role === 2 ? 'system' : 'user',
              content: JSON.stringify(v.content),
              tool_calls: [],
              tool_call_id: ''
            }
          })
          .slice(-10)
      } else {
        params.history_messages = []
      }
    }

    if (deepThink.value) {
      if (
        (params.baseIds && params.baseIds?.length > 0) ||
        (params.fileIds && params.fileIds?.length > 0)
      ) {
        // 验证通过，必须选一个相关数据
      } else {
        message.warning('请选择知识库相关数据')
        loading.sendMsg = false
        return
      }
    }

    // 深度研究获取ai配置信息，考虑到用户存在更改的情况所以每次都实时获取
    let aiConfigInfo: Record<string, any> = {}
    if (deepThink.value) {
      const configData = await config.get()
      aiConfigInfo = configData.data
    }

    const msg = reactive({
      id: `${Date.now()}${uuid()}`,
      avatar: avatar,
      name: realname,
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      role: 1,
      loading: true,
      loadingText: '发送中',
      content: {
        files: params.minioFileDataList || [],
        quote: (params.analyseTag || [])
          .concat(params.baseNames || [])
          .concat(
            quotes.value
              .filter((v) => [mcpId.value, knowledgeFileId.value].includes(v.parentId))
              .map((v) => v.label) || []
          ),
        content: params.promote
      }
    })
    const aiMsg = reactive({
      id: `${Date.now()}${uuid()}`,
      avatar: '/assistant/<EMAIL>',
      name: '昆纪小智',
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      role: 2,
      loading: true,
      loadingText: '发送中',
      content: {
        unexpand: false,
        files: [],
        thinking: true,
        thinkStatus: true,
        thinkStatusText: '思考中',
        thinkText: '',
        content: '',
        progress: 0,
        error: [],
        dbTableInfo: {
          sql: '',
          columnComments: {}
        },
        deepThink: deepThink.value,
        multipartThink: [] as MultipartThinkItem[],
        deepThinkData: {
          originalContent: '',
          planContent: '',
          planData: {} as Record<string, any>,
          stageReportArray: [''] as string[], // 报告内容数组
          finallyReport: '' // 最终报告
        } as Record<string, any>
      }
    })
    const sysMsg = {
      id: `${Date.now()}${uuid()}`,
      role: 0,
      content: {
        content: '以上内容为数字昆纪AI生成，请注意识别！'
      }
    }

    //没有会话要先创建会话,如果有会话就直接push消息
    let current: Conversation = conversation.value.find((v) => v.sessionId === sessionId)
    if (current) {
      params.sessionId = current.sessionId
      current.list.push(msg, aiMsg, sysMsg)
    } else {
      const res = await addSession({
        title: params.promote,
        list: [msg, aiMsg, sysMsg]
      })
      params.sessionId = res.data?.sessionId
      current = conversation.value.find((v) => v.sessionId === params.sessionId)
    }
    // 保存用户消息
    saveMsg({
      sessionId: params.sessionId,
      role: 'user',
      content: JSON.stringify({
        ...params,
        mcpModuleNames: quotes.value.filter((v) => v.parentId === mcpId.value).map((v) => v.label),
        baseIds: undefined,
        sessionId: undefined,
        history_messages: undefined,
        minioFileDataList: undefined
      }),
      dataList: JSON.stringify(params.minioFileDataList)
    })

    // 大数据调用参数处理
    if (Array.isArray(params.analyseTag) && params.analyseTag.includes('大数据分析')) {
      //mcp如果选择模型需要去除掉
      if (quotes.value.some((item) => item.analyseType === 'model')) {
        params.promote = promote.value.replace('请帮我分析一下', '')
        // quotes.value = []
      }
    }

    current.abortController = new AbortController()

    // 流式互动数据
    const interactData = {
      // mcp 步骤名称
      step_name: '',

      // 深度研究 数据缓冲池
      spooling: {
        message_chunk: {} as Record<string, any>,
        tool_call_chunks: {} as Record<string, any>,
        tool_call_result: {} as Record<string, any>
      },
      think_index: 0,
      step_index: 0,
      beforeAgent: '',
      agent: ''
    }

    // 深度研究不自动接受逻辑的参数处理
    if (deepThinkInfo.planContent) {
      aiMsg.content.deepThinkData.planContent = deepThinkInfo.planContent
      aiMsg.content.deepThinkData.planData = deepThinkInfo.planData
      deepThinkInfo.planContent = ''
      deepThinkInfo.planData = {}
    }

    if (deepThink.value) {
      if (aiMsg.content.deepThinkData.planContent) {
        interactData.think_index++
        interactData.step_index++
        aiMsg.content.multipartThink.push({
          id: getThinkStepIndex(interactData.think_index, interactData.step_index, 'think'),
          thinking: true,
          thinkStatus: true,
          thinkStatusText: `第${interactData.think_index}轮计划方向${interactData.step_index}思考过程思考中`,
          thinkText: '',
          unexpand: false
        })
      } else {
        aiMsg.content.multipartThink.push({
          id: '0',
          thinking: true,
          thinkStatus: true,
          thinkStatusText: '研究计划思考中',
          thinkText: '',
          unexpand: false
        })
      }
    }

    fetchStream({
      url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
      method: 'post',
      signal: current.abortController.signal,
      headers: {
        'Content-Type': 'application/json',
        authorization: `Bearer ${userStore.token}`
      },
      body: JSON.stringify(params),
      success: (res: string) => {
        try {
          loading.sendMsg = false
          msg.loading = false
          aiMsg.loading = false
          // const template = jsonrepair(res)
          const data = JSON.parse(res)
          const result = data.result

          if (data.status === 'start') {
          } else if (data.status === 'wait') {
            aiMsg.content.thinkStatusText = '等待中···'
          } else if (data.status === 'streaming') {
            // 深度思考单独处理逻辑
            if (deepThink.value) {
              // const showData = {
              //   event: result.event,
              //   reasoning_content: result.reasoning_content,
              //   content: result.content,
              //   data: result.data,
              //   dataList: result.dataList,
              //   rawData: result.rawData,
              //   id: result.id
              // }
              // console.log("------showData----", showData)

              // 需要里面的agent属性来判断步骤
              let rawData
              if (isJSON(result.rawData)) {
                rawData = JSON.parse(result.rawData)
                if (interactData.agent) {
                  interactData.beforeAgent = interactData.agent
                }
                interactData.agent = rawData.agent
              }

              interactData.step_name = result?.step_name
              switch (result?.event) {
                case 'message_chunk':
                  const thinkItem = aiMsg.content.multipartThink.find(
                    (item) =>
                      item.id ===
                      getThinkStepIndex(interactData.think_index, interactData.step_index, 'think')
                  )
                  if (thinkItem && !thinkItem.thinkText.endsWith(result.reasoning_content)) {
                    thinkItem.thinkText += result.reasoning_content || ''
                  }
                  // 消息块事件
                  let spoolingData = interactData.spooling.message_chunk[result.id]
                  if (spoolingData) {
                    spoolingData.content += result.content || ''
                    spoolingData.think += result.reasoning_content || ''
                  } else {
                    spoolingData = {
                      content: result.content || '',
                      think: result.reasoning_content || '',
                      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                  interactData.spooling.message_chunk[result.id] = spoolingData
                  switch (interactData.agent) {
                    case 'planner': // 书写计划
                      if (isJSON(spoolingData.content)) {
                        const planData = JSON.parse(spoolingData.content)
                        if (planData.steps && planData.steps.length > 0) {
                          let userProblem = ''
                          if (deepThinkInfo.orginalPromote) {
                            userProblem = deepThinkInfo.orginalPromote
                          } else {
                            userProblem = params.promote
                          }
                          const studyThemeTemplate = `<p><span style="font-size: 36px; font-family: 黑体;"><strong>一、研究主题</strong></span></p>
                          <p style="text-indent: 2em;margin-top: 1rem"><span style="font-size: 18px; font-family: 仿宋;">${userProblem}</span></p><p style="text-indent: 2em;"><br></p>`

                          const planSteps = planData.steps.map(
                            (step: Record<string, any>, index: number) => {
                              return `<p style="text-indent: 1em;"><span style="font-size: 22px; font-family: 仿宋;"> ${index + 1}.${step.title}</span></p>
                                <p style="text-indent: 2em;"><span style="font-size: 18px; font-family: 仿宋;"> ${step.description}</span></p>`
                            }
                          )
                          const studyPlanTemplate = `<p><span style="font-size: 36px; font-family: 黑体;"><strong>二、研究计划</strong></span></p>
                          <p style="text-indent: 1em;"><span style="font-weight: bold;margin-right: 8px;">●</span><span style="font-size: 24px; font-family: 仿宋;">研究计划步骤：</span></p>
                          ${planSteps.join('')}
                          <p style="text-indent: 2em;"><br></p>`

                          aiMsg.content.deepThinkData.planContent =
                            studyThemeTemplate + studyPlanTemplate
                          aiMsg.content.deepThinkData.planData = planData

                          if (
                            aiMsg.content.multipartThink.filter((item) => item.id.includes('think'))
                              .length < 1
                          ) {
                            if (aiConfigInfo && aiConfigInfo.pyAutoAcceptedPlan) {
                              // 自动接受计划执行 （防止二次进入导致方向次数对不上）
                              let addThinkStep = true
                              if (
                                (interactData.think_index - 1) * aiConfigInfo.pyMaxStepNum +
                                  interactData.step_index >=
                                aiMsg.content.multipartThink.length
                              ) {
                                addThinkStep = false
                              } else if (
                                interactData.think_index === 0 ||
                                interactData.step_index === aiConfigInfo?.pyMaxStepNum
                              ) {
                                interactData.step_index = 1
                                interactData.think_index++
                              } else {
                                if (
                                  aiMsg.content.multipartThink[
                                    aiMsg.content.multipartThink.length - 1
                                  ].thinkText
                                ) {
                                  interactData.step_index++
                                } else {
                                  addThinkStep = false
                                }
                              }
                              if (addThinkStep) {
                                aiMsg.content.multipartThink = aiMsg.content.multipartThink.map(
                                  (item) => {
                                    item.thinkStatus = false
                                    item.unexpand = true
                                    item.thinkStatusText = item.thinkStatusText.replace(
                                      '思考中',
                                      '已结束'
                                    )
                                    return item
                                  }
                                )
                                aiMsg.content.thinkStatusText = `第${interactData.think_index}轮计划方向${interactData.step_index}思考过程思考中`
                                aiMsg.content.multipartThink.push({
                                  id: getThinkStepIndex(
                                    interactData.think_index,
                                    interactData.step_index,
                                    'think'
                                  ),
                                  thinking: true,
                                  thinkStatus: true,
                                  thinkStatusText: `第${interactData.think_index}轮计划方向${interactData.step_index}思考过程思考中`,
                                  thinkText: '',
                                  unexpand: false
                                })
                              }
                            } else {
                              // 人工接受计划执行
                              deepThinkInfo.planData = planData
                              deepThinkInfo.planContent = studyThemeTemplate + studyPlanTemplate
                              if (deepThinkInfo.orginalPromote && params.promote === '执行计划') {
                                // 多轮执行时需要获取最开始一次的用户问题作为主题
                              } else {
                                deepThinkInfo.orginalPromote = params.promote
                              }
                            }
                          }
                        }
                      }
                      break
                    case 'researcher':
                    case 'coder':
                      if (spoolingData.content.replace(/\n/g, '')) {
                        aiMsg.content.multipartThink = aiMsg.content.multipartThink.map((item) => {
                          item.thinkStatus = false
                          item.unexpand = true
                          item.thinkStatusText = item.thinkStatusText.replace('思考中', '已结束')
                          return item
                        })
                        aiMsg.content.thinkStatusText = `撰写${interactData.think_index}轮计划方向${interactData.step_index}的阶段报告`
                        aiMsg.content.deepThinkData.stageReportArray[
                          aiMsg.content.deepThinkData.stageReportArray.length - 1
                        ] = spoolingData.content || ''
                      }
                      break
                    case 'reporter':
                      if (spoolingData.content) {
                        aiMsg.content.thinkStatusText = '撰写最终报告'
                        // 关键引用 的引用为英文时处理
                        spoolingData.content = spoolingData.content.replace(
                          '关键 Citations',
                          '关键引用'
                        )
                        spoolingData.content = spoolingData.content.replace(
                          'Information not provided',
                          '未提供相关信息'
                        )
                        aiMsg.content.deepThinkData.finallyReport = spoolingData.content || ''
                      }
                      break
                    default:
                      aiMsg.content.deepThinkData.originalContent += result.content || ''
                      break
                  }
                  break
                case 'tool_call_chunks':
                  // 工具调用事件
                  let tooCallChunksData = interactData.spooling.tool_call_chunks[result.id]
                  if (tooCallChunksData) {
                    tooCallChunksData.content += result.content || ''
                    tooCallChunksData.think += result.reasoning_content || ''
                  } else {
                    tooCallChunksData = {
                      content: result.content || '',
                      think: result.reasoning_content || '',
                      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                  interactData.spooling.tool_call_chunks[result.id] = tooCallChunksData
                  break
                case 'tool_call_result':
                  // 工具结果事件 （主要输出知识库引用内容，需截取）
                  let tooCallResultData = interactData.spooling.tool_call_result[result.id]
                  if (tooCallResultData) {
                    tooCallResultData.content += result.content || ''
                    tooCallResultData.think = result.reasoning_content || ''
                  } else {
                    tooCallResultData = {
                      content: result.content || '',
                      think: result.reasoning_content || '',
                      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                    }
                  }
                  interactData.spooling.tool_call_result[result.id] = tooCallResultData

                  // 判断上一次的报告是否正常输出完
                  const stageReport =
                    aiMsg.content.deepThinkData.stageReportArray[
                      aiMsg.content.deepThinkData.stageReportArray.length - 1
                    ]
                  if (stageReport.replace(/\n/g, '').length > 10) {
                    aiMsg.content.deepThinkData.stageReportArray.push('')
                    if (
                      aiMsg.content.deepThinkData.stageReportArray.length <
                      aiConfigInfo.pyMaxPlanIterations * aiConfigInfo.pyMaxStepNum
                    ) {
                      aiMsg.content.deepThinkData.stageReportArray.push('')
                    }

                    let addThinkStep = true
                    if (
                      (interactData.think_index - 1) * aiConfigInfo.pyMaxStepNum +
                        interactData.step_index >=
                      aiMsg.content.multipartThink.length
                    ) {
                      addThinkStep = false
                    } else if (
                      interactData.think_index === 0 ||
                      interactData.step_index === aiConfigInfo?.pyMaxStepNum
                    ) {
                      interactData.step_index = 1
                      interactData.think_index++
                    } else {
                      if (
                        aiMsg.content.multipartThink[aiMsg.content.multipartThink.length - 1]
                          .thinkText
                      ) {
                        interactData.step_index++
                      } else {
                        addThinkStep = false
                      }
                    }
                    if (addThinkStep) {
                      aiMsg.content.multipartThink = aiMsg.content.multipartThink.map((item) => {
                        item.thinkStatus = false
                        item.unexpand = true
                        item.thinkStatusText = item.thinkStatusText.replace('思考中', '已结束')
                        return item
                      })
                      aiMsg.content.thinkStatusText = `第${interactData.think_index}轮计划方向${interactData.step_index}思考过程思考中`
                      aiMsg.content.multipartThink.push({
                        id: getThinkStepIndex(
                          interactData.think_index,
                          interactData.step_index,
                          'think'
                        ),
                        thinking: true,
                        thinkStatus: true,
                        thinkStatusText: `第${interactData.think_index}轮计划方向${interactData.step_index}思考过程思考中`,
                        thinkText: '',
                        unexpand: false
                      })
                    }
                  }

                  // 判断知识库引用的数据是否完成
                  let knowledgeData = tooCallResultData.content
                  if (knowledgeData.indexOf('Results: [') > -1) {
                    knowledgeData = knowledgeData.slice(knowledgeData.indexOf('Results: [') + 8)
                    if (isJSON(knowledgeData)) {
                      const resultArray = JSON.parse(knowledgeData)
                      if (Array.isArray(resultArray)) {
                        const thinkText =
                          '\n\n 知识库引用内容\n\n' +
                          resultArray
                            .map((result: Record<string, any>, index: number) => {
                              return result.content
                            })
                            .join('\n\n')
                        const knowledgeItem = aiMsg.content.multipartThink.find(
                          (item) =>
                            item.id ===
                            getThinkStepIndex(
                              interactData.think_index,
                              interactData.step_index,
                              'knowledge'
                            )
                        )
                        if (knowledgeItem) {
                          knowledgeItem.thinkText += thinkText || ''
                        } else {
                          aiMsg.content.multipartThink.push({
                            id: getThinkStepIndex(
                              interactData.think_index,
                              interactData.step_index,
                              'knowledge'
                            ),
                            thinking: true,
                            thinkStatus: false,
                            thinkStatusText: `第${interactData.think_index}轮计划方向${interactData.step_index}知识库引用详情`,
                            thinkText: thinkText,
                            unexpand: true
                          })
                        }
                      }
                    }
                  }

                  break
                case 'interrupt':
                  // 中断事件
                  aiMsg.content.deepThinkData.event = 'interrupt'
                  deepThinkInfo.visible = true
                  break
                default:
                  aiMsg.content.thinkText = ''
                  break
              }
              aiMsg.content.deepThinkData.interactData = interactData

              const planText = aiMsg.content.deepThinkData.planContent
              let planResult = ''
              if (aiMsg.content.deepThinkData.stageReportArray.length > 0) {
                for (let i = 0; i < interactData.think_index; i++) {
                  let stepReport = ''
                  for (let j = 0; j < aiConfigInfo.pyMaxStepNum || 0; j++) {
                    if (
                      i * aiConfigInfo?.pyMaxStepNum + j <
                      aiMsg.content.deepThinkData.stageReportArray.length
                    ) {
                      const reportContent =
                        aiMsg.content.deepThinkData.stageReportArray[
                          i * aiConfigInfo?.pyMaxStepNum + j
                        ]
                      if (reportContent) {
                        stepReport += `<p style="text-indent: 2em;"><span style="font-size: 28px; font-family: 仿宋;"> ${j + 1}. ${aiMsg.content.deepThinkData.planData.steps[j].title}</span></p>
                      <p style="text-indent: 3em;"><span style="font-weight: bold;">●</span><span style="font-size: 24px; font-family: 仿宋;">阶段结论：</span></p>
                      <div style="padding:0 4em;font-family: 仿宋;">${reportContent}</div>
                      <p style="text-indent: 4em;"><br></p>`
                      }
                    }
                  }
                  if (stepReport) {
                    planResult += `<p style="text-indent: 1em;"><span style="font-size: 28px; font-family: 仿宋;"> （${numberZh.numberToZh(i + 1)}） 第${numberZh.numberToZh(i + 1)}阶段计划</span></p>
                          <p style="">${stepReport}</p>`
                  }
                }
                if (planResult) {
                  planResult = `<p><span style="font-size: 36px; font-family: 黑体;"><strong>三、研究结果</strong></span></p>${planResult}`
                }
              }
              let finallyReport = aiMsg.content.deepThinkData.finallyReport || ''
              if (finallyReport) {
                finallyReport = `<p><span style="font-size: 36px; font-family: 黑体;"><strong>四、研究报告</strong></span></p>
                  <div style="padding:0 4em;font-family: 仿宋;">${finallyReport}</div>`
              }

              aiMsg.content.content = `${planText ? planText : ''} ${planResult} ${finallyReport}`
            } else {
              if (result?.reasoning_content) {
                aiMsg.content.thinkStatusText = '思考中···'
                aiMsg.content.thinkText += result.reasoning_content || ''
              }
              if (result?.content) {
                aiMsg.content.content += result.content || ''
              }
            }

            // 文件分析进度条
            if (result?.data?.progress) {
              aiMsg.content.thinkStatusText = '解析中···'
              aiMsg.content.progress = result.data.progress
            }
            // 音频解析
            if (result?.data?.transcript) {
              aiMsg.content.thinkStatusText = '解析完成'
              aiMsg.content.progress = 100
              const user = result.data.transcript.speakers
              const summary = `\`\`\`plaintext\n\n内容摘要：\n**${result.data.response.response}**\n\`\`\`\n`
              // const key_points = `\`\`\`plaintext\n\n关键点：\n**${result.data.summary.key_points.map((v, i) => `${i + 1}：${v}`).join('\n')}**\n\n\`\`\`\n`
              // const action_items = `\`\`\`plaintext\n\n行动项：\n**${result.data.summary.action_items.map((v, i) => `${i + 1}：${v}`).join('\n')}**\n\n\`\`\`\n`
              const utterances = `\`\`\`plaintext\n\n谈话内容：\n${result.data.transcript.utterances.map((v) => `[${formatTime(v.start_time)}-${formatTime(v.end_time)}]${user.find((i) => i.id === v.speaker_id).name}:${v.text}`).join('\n')}\n\n\`\`\`\n`
              const count = `\`\`\`plaintext\n\n完整文本:\n${result.data.transcript.utterances.map((v) => v.text).join('')}\n\n\`\`\`\n`

              // aiMsg.content.content = `\n\n\`\`\`plaintext\n${summary}\n${key_points}\n${action_items}\n${utterances}\n${count}\"\n\`\`\`\n\n`
              aiMsg.content.content = `${summary}\n${utterances}\n${count}`
              // aiMsg.content.content = `${summary}\n${key_points}\n${action_items}\n${utterances}\n${count}`
            }

            if (result?.data?.fileId) {
              aiMsg.content.files.push(result.data)
            }

            // mcp 对接逻辑
            if (result?.data && result.data.step_name) {
              console.log('mcp 对接逻辑', result.data.step_name)
              if (interactData.step_name !== result.data.step_name) {
                aiMsg.content.content += '\n'
              }
              aiMsg.content.content = aiMsg.content.content.replace(
                /```(?!json|sql)\b[\s\S]*?(\n|$)/g,
                '```\n'
              ) // 处理代码块合并问题
              // 处理出现/nn情况会解析多展示一个n
              aiMsg.content.content = aiMsg.content.content.replace('/nn', '/n')

              // 匹配出现次数、对奇数次进行数据填补
              const regex = /```/g
              if (result.data.step_name === '数据查询' || result.data.step_name === 'SQL结果分析') {
                const matches = aiMsg.content.content.match(regex)
                if (matches && matches.length % 2 === 1) {
                  aiMsg.content.content += '```'
                }
              }

              if (result.data.step_name === 'SQL结果分析') {
                aiMsg.content.content +=
                  '\n\n---\n\n ✅ 数据分析总结\n\n' + result.data.summary + '\n\n---\n\n'
              }

              if (result.data.sql_query && result.data.column_comments) {
                aiMsg.content.dbTableInfo.sql = result.data.sql_query
                aiMsg.content.dbTableInfo.columnComments = result.data.column_comments
              }
            }
          } else if (data.status === 'error') {
            errorLog.push({
              msg: data.error,
              stack: '发送消息节点失败',
              title: '发送消息失败',
              data: data
            })
            aiMsg.loading = false
            aiMsg.content.thinkStatus = false
            aiMsg.content.thinkStatusText = '出错了'
            aiMsg.content.error.push(data.error || '')

            // aiMsg.content.multipartMessage.forEach((v) => {
            //   v.thinkStatus = false
            //   v.thinkStatusText = '出错了'
            // })
          } else if (data.status === 'end') {
            aiMsg.loading = false
            aiMsg.content.thinkStatusText = '已结束'
            aiMsg.content.thinkStatus = false
            // aiMsg.content.multipartMessage.forEach((v) => {
            //   v.thinkStatus = false
            //   v.thinkStatusText = '已结束'
            // })
          }
        } catch (err) {
          errorLog.push({ msg: err.message, stack: err.stack, title: '发送消息失败', data: res })
          msg.loading = false
          aiMsg.loading = false
          aiMsg.content.thinkStatus = false
          aiMsg.content.error.push(err.message)
        }
      },
      error: (err: string) => {
        // deepthink单独处理
        aiMsg.content.multipartThink = aiMsg.content.multipartThink.map((item) => {
          item.thinkStatus = false
          item.unexpand = true
          item.thinkStatusText = item.thinkStatusText.replace('思考中', '已结束')
          return item
        })

        loading.sendMsg = false
        msg.loading = false
        aiMsg.loading = false
        aiMsg.content.thinkStatus = false
        aiMsg.content.thinkStatusText = '出错了'
        aiMsg.content.error.push(err)
      },
      complete: () => {
        if (params.deepThink) {
          aiMsg.content.multipartThink = aiMsg.content.multipartThink.map((item) => {
            item.thinkStatus = false
            item.unexpand = true
            item.thinkStatusText = item.thinkStatusText.replace('思考中', '已结束')
            return item
          })

          saveMsg({
            sessionId: params.sessionId,
            role: 'assistant',
            reasoningContent: aiMsg.content.thinkText,
            content: JSON.stringify({
              sessionId: undefined,
              deepThink: params.deepThink,
              multipartThink: aiMsg.content.multipartThink.map((v) => ({
                ...v,
                thinking: undefined,
                thinkStatus: undefined
              })),
              deepThinkData: aiMsg.content.deepThinkData,
              content: aiMsg.content.content,
              dbTableInfo: aiMsg.content.dbTableInfo,
              error: aiMsg.content.error
            }),
            dataList: JSON.stringify(aiMsg.content.files)
          })
        } else {
          saveMsg({
            sessionId: params.sessionId,
            role: 'assistant',
            reasoningContent: aiMsg.content.thinkText,
            content: JSON.stringify({
              sessionId: undefined,
              content: aiMsg.content.content,
              dbTableInfo: aiMsg.content.dbTableInfo,
              error: aiMsg.content.error
            }),
            dataList: JSON.stringify(aiMsg.content.files)
          })
        }

        current.abortController = undefined
        msg.loading = false
        aiMsg.loading = false
        aiMsg.content.thinkStatus = false
        aiMsg.content.thinkStatusText = '已结束'
      }
    })
    promote.value = ''
    return current
  }

  return {
    abort,
    sendMsg,
    reportId,
    knowledgeId,
    knowledgeFileId,
    mcpId,
    fileId,
    promote,
    loading,
    resetSession,
    usePresetScoreThreshold,
    addSession,
    sessionPrms,
    delSession,
    getSession,
    quotes,
    conversation,
    deepThink,
    deepThinkInfo
  }
})
