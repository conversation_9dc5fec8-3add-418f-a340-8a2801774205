package com.cb.ai.data.analysis.voucher.utils.task;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 任务帮助类
 */
public class TaskHelper {

    private static final ConcurrentHashMap<String,TaskCounter> taskCounterMap = new ConcurrentHashMap<>();

    /**
     * 获取任务计数器
     * @param taskId
     * @return
     */
    public static TaskCounter getCounter(String taskId){
        TaskCounter counter = taskCounterMap.get(taskId);
        return counter;
    }

    /**
     * 获取任务计数器,没有时创建
     * @param taskId
     * @return
     */
    public static TaskCounter getOrCreatCounter(String taskId){
        TaskCounter taskCounter = taskCounterMap.computeIfAbsent(taskId, k -> new TaskCounter());
        return taskCounter;
    }

    /**
     * 移除任务计数器
     * @param taskId
     */
    public static void removeTaskCounter(String taskId){
        taskCounterMap.remove(taskId);
    }

    public static class TaskCounter{
        private final AtomicLong handleNum = new AtomicLong(0);
        private final AtomicLong alertNum = new AtomicLong(0);

        public void incrementHandleNum(){
            handleNum.incrementAndGet();
        }

        public void incrementAlertNum(){
            alertNum.incrementAndGet();
        }

        public long getHandleNum(){
            return handleNum.get();
        }

        public long getAlertNum(){
            return alertNum.get();
        }
    }

}
