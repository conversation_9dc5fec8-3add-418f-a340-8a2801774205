package com.cb.ai.data.analysis.ai.domain.entity;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 大数据分析配置(AiBigDataAnalyseConfig)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-27 15:25:50
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class AiBigDataAnalyseConfig extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //部门Id
    @ExcelIgnore
    private String depId;

    //所属领域
    @ExcelProperty(value = "所属领域")
    private String domain;

    //分析类型
    @ExcelProperty(value = "分析类型")
    private String analyseType;

    //一级分类
    @ExcelProperty(value = "一级分类")
    @TableField(condition = SqlCondition.LIKE)
    private String oneType;

    //二级分类
    @ExcelProperty(value = "二级分类")
    @TableField(condition = SqlCondition.LIKE)
    private String twoType;

    //名称
    @ExcelProperty(value = "名称")
    @TableField(condition = SqlCondition.LIKE)
    private String name;

    //使用的数据库表
    @ExcelProperty(value = "使用的数据库表")
    private String tableNames;

}

