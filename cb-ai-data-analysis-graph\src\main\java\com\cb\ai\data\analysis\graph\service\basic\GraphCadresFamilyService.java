package com.cb.ai.data.analysis.graph.service.basic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresFamily;

import java.io.InputStream;
import java.util.List;

/**
 * 知识图谱-干部亲属表(GraphCadresFamily)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:17
 */
public interface GraphCadresFamilyService extends IService<GraphCadresFamily> {

    public boolean save(GraphCadresFamily graphCadresFamily);

    public boolean updateById(GraphCadresFamily graphCadresFamily);

    public boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<GraphCadresFamily> list);

}

