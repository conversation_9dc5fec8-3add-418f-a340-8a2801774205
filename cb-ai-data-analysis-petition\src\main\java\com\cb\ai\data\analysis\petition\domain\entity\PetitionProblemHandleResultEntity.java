package com.cb.ai.data.analysis.petition.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;

/***
 * <AUTHOR>
 * 问题批处理问题处理结果
 */
@Data
@TableName("ss_petition_problem_handle_result")
public class PetitionProblemHandleResultEntity extends BaseDomain implements Serializable {

    /***
     * 主键IDD
     */
    private String id;

    /***
     * 问题ID
     */
    private String problemId;

    /***
     * 问题序号，对应exce的序号
     */
    private String problemSerial;

    /***
     * 问题名称
     */
    private String problemName;
    /***
     * 思考过程
     */
    private String reasoningContent;

    /***
     * 问题结果
     */
    private String content;


    /***
     * 状态
     */
    private Integer status;

    /***
     * 文件列表json
     *
     */
    private String fileListJson;


}
