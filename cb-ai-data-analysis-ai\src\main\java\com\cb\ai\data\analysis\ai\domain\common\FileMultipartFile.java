package com.cb.ai.data.analysis.ai.domain.common;

import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xong.boot.common.exception.CustomException;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:52
 * @Copyright (c) 2025
 * @Description 多文件数据
 */
public class FileMultipartFile implements MultipartFile {
    private final String name;
    private final String originalFilename;
    private final String contentType;
    private final byte[] content;

    public FileMultipartFile(File file) {
        byte[] bytes = convertFileToByte(file);
        this.name = file.getName();
        this.originalFilename = file.getName();
        this.contentType = null;
        this.content = bytes;
    }

    public FileMultipartFile(String originalFilename, InputStream inputStream) {
        this(originalFilename, originalFilename, null, inputStream);
    }

    public FileMultipartFile(String name, String originalFilename, String contentType, InputStream inputStream) {
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        try {
            this.content = inputStream.readAllBytes();
        } catch (IOException e) {
            throw new CustomException("文件流读取失败", e);
        }
    }

    @NotNull
    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return OptionalUtil.ofBlankable(contentType).orElse("application/octet-stream");
    }

    @Override
    public boolean isEmpty() {
        return this.content.length == 0;
    }

    @Override
    public long getSize() {
        return this.content.length;
    }

    @NotNull
    @Override
    public byte[] getBytes() throws IOException {
        return this.content;
    }

    @NotNull
    @Override
    @JsonIgnore
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(getBytes());
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        Files.write(dest.toPath(), content);
    }

    private byte[] convertFileToByte(File file) {
        try (InputStream is = new FileInputStream(file)) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384];
            while ((nRead = is.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            return buffer.toByteArray();
        } catch (Exception e) {
            throw new CustomException("文件转换失败", e);
        }
    }
}
