package com.cb.ai.data.analysis.ai.domain.common;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 10:37
 * @Copyright (c) 2025
 * @Description 同一个接口不同类型的Url数据
 */
@Getter
@Setter
public class MultiUrlData extends UrlData {
    /*
     * 流式url
     */
    private String streamUrl;
    /*
     * 阻塞式url
     */
    private String blockUrl;

    /**
     * 获取url
     * @param isStream 是否是流式
     * @return
     */
    public String url(boolean isStream) {
        return isStream ? streamUrl : blockUrl;
    }
}
