package com.cb.ai.data.analysis.ai.constant;

import com.xong.boot.common.constant.CacheConstants;
import com.xong.boot.common.constant.Constants;

/**
 * AI缓存常量
 * <AUTHOR>
 */
public class AiConstants {
    /**
     * AI接口根路径
     */
    public static final String API_AI_ROOT_PATH = Constants.API_ROOT_PATH + "/ai";
    /**
     * AI用户配置
     */
    public static final String AI_USER_CONFIG_KEY = CacheConstants.PREFIX + ":ai:config:user";
    /**
     * AI向量文本配置
     */
    public static final String AI_EMBEDDING_CONFIG_KEY = CacheConstants.PREFIX + ":ai:embedding:content";

    /**
     * 获取用户AI配置
     * @param userId 用户ID
     */
    public static String getConfigKey(String userId) {
        return AI_USER_CONFIG_KEY + ":" + userId;
    }

    /**
     * 获取向量文本配置
     * @param requestId
     */
    public static String getEmbeddingKey(String requestId) {
        return AI_EMBEDDING_CONFIG_KEY + ":" + requestId;
    }
}
