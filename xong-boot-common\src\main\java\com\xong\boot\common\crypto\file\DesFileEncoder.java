package com.xong.boot.common.crypto.file;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * DES文件加解密器
 * <AUTHOR>
 */
public class DesFileEncoder implements FileEncoder {
    /**
     * 加密算法
     */
    private final DES des;

    public DesFileEncoder(String secretKey) {
        des = SecureUtil.des(HexUtil.decodeHex(secretKey));
    }

    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        des.encrypt(is, out, isClose);
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        des.decrypt(is, out, isClose);
    }
}
