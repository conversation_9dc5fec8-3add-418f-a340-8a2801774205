package com.cb.ai.data.analysis.ai.component.choreography.engine;

import cn.hutool.core.util.RandomUtil;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 17:07
 * @Copyright (c) 2025
 * @Description 基础节点
 */
public abstract class BaseNode implements INode {

    @Override
    public String getNodeId() {
        return RandomUtil.randomString(32).toLowerCase();
    }

    @Override
    public String getNodeName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getNodeDesc() {
        return this.getClass().getName();
    }

}
