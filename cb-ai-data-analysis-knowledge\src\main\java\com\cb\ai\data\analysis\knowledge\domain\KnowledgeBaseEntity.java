package com.cb.ai.data.analysis.knowledge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeCountResult;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库查询返回实体
 */
@Data
@TableName("cb_knowledge_base")
public class KnowledgeBaseEntity extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     *知识库ID 于ai知识库ID一致
     */
    private String id;
    /***
     *知识库名称
     */
    private String name;
    /***
     * 知识库标签
     */
    private String tag;

    /**
     * 类型
     */
    private Integer baseType;

    /**
     * 父级ID
     */
    private String parentId;

    /***
     * 权限标识 1、公开  2、本部门  3、部门及以下  4、私有
     */
    private Integer purviewMark;

    /***
     * 用户ID
     */
    private String createUserId;
    /**
     * 是否删除 0:未删除 1:已删除
     */
    private Integer isDel;

    /***
     * 文件数量
     */
    @TableField(exist = false)
    private Integer fileCount;

    @TableField(exist = false)
    private List<KnowledgeCountResult> countResult;

    /***
     * 部门ID  选择了权限对部门可见的时候有
     */
    @TableField(exist = false)
    private List<String> deptIds;
}
