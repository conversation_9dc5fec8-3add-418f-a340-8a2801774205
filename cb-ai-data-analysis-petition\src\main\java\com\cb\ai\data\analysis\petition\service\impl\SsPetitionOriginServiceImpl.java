package com.cb.ai.data.analysis.petition.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionOriginEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PageResult;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.SsPetitionDynamicResponseVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.TableHeaderVo;
import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import com.cb.ai.data.analysis.petition.enums.FixHeaderEnum;
import com.cb.ai.data.analysis.petition.enums.SsPetitionStatusEnum;
import com.cb.ai.data.analysis.petition.mapper.SsPetitionOriginMapper;
import com.cb.ai.data.analysis.petition.service.SsPetitionOriginService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SsPetitionOriginServiceImpl extends BaseServiceImpl<SsPetitionOriginMapper, SsPetitionOriginEntity> implements SsPetitionOriginService {

    @Autowired
    private SsPetitionOriginMapper ssPetitionOriginMapper;

    @Override
    public PageResult selectByPage(PetitionQueryPageQueryVo queryParams) {
        Page<SsPetitionOriginEntity> page = new Page<>(queryParams.getPageNo(), queryParams.getPageSize());
        LambdaQueryWrapper<SsPetitionOriginEntity> queryWrapper = new QueryWrapper<SsPetitionOriginEntity>().lambda();
        if(StringUtils.isNotBlank(queryParams.getBatchNo())){
            queryWrapper.eq(SsPetitionOriginEntity::getUploadBatchNo,queryParams.getBatchNo());
        }
        queryWrapper.orderByDesc(SsPetitionOriginEntity::getUploadTime);
        Page<SsPetitionOriginEntity> petitionOriginPage=ssPetitionOriginMapper.selectPage(page, queryWrapper);
        List<SsPetitionOriginEntity> petitionOriginList=petitionOriginPage.getRecords();
        SsPetitionDynamicResponseVo ssPetitionDynamicResponseVo = new SsPetitionDynamicResponseVo();
        if (!CollectionUtils.isEmpty(petitionOriginList)) {
            Integer fileType = petitionOriginList.get(0).getFileType();
            Boolean isExcel = FileContentTypeEnum.isExcel(fileType);

            ssPetitionDynamicResponseVo.setIsExcel(isExcel);

            String content = petitionOriginList.get(0).getContent();
            JSONObject jsonObject = JSONObject.parseObject(content);
            if (ssPetitionDynamicResponseVo.getTableHeader() == null) {
                Set<String> headerSet = new HashSet<>(jsonObject.keySet());

                List<TableHeaderVo> headers = new ArrayList<>();

                for (String header : headerSet) {
                    TableHeaderVo tableHeaderVo = new TableHeaderVo();
                    tableHeaderVo.setHeaderName(header);
                    headers.add(tableHeaderVo);
                }

                for (FixHeaderEnum fixHeaderEnum : FixHeaderEnum.values()) {
                    TableHeaderVo tableHeaderVo = new TableHeaderVo();
                    tableHeaderVo.setHeaderName(fixHeaderEnum.getHeaderName());
                    tableHeaderVo.setWidth(fixHeaderEnum.getWidth());
                    headers.add(tableHeaderVo);
                }

                ssPetitionDynamicResponseVo.setTableHeader(headers);
            }
            List<JSONObject> records = petitionOriginList.stream().map(ssPetitionOriginVo -> {
                JSONObject obj = JSONObject.parseObject(ssPetitionOriginVo.getContent());
                obj.put("状态", SsPetitionStatusEnum.getDescription(ssPetitionOriginVo.getStatus()));
                return obj;

            }).collect(Collectors.toList());
            ssPetitionDynamicResponseVo.setData(records);
        } else {
            ssPetitionDynamicResponseVo.setData(petitionOriginList);
        }
        PageResult result=new PageResult();
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setPages(page.getPages());
        result.setCurrent(page.getCurrent());
        result.setRecords(ssPetitionDynamicResponseVo);
        return result;
    }
}
