package com.cb.ai.data.analysis.ai.controller;

import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateEmbeddingSearch;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.cb.ai.data.analysis.ai.domain.request.context.ExtendCommonAIRequestContext;
import com.cb.ai.data.analysis.ai.service.ICommonService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:39
 * @Copyright (c) 2025
 * @Description 通用的AI控制器
 */
@RestController
@RequestMapping( Constants.API_AI_ROOT_PATH + "/common")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonAIController {
    private final ICommonService commonService;

    @PostMapping(value = "/execute", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<?> executeFlowChain(@ModelAttribute ExtendCommonAIRequestContext<?> context, @RequestParam Map<String, MultipartFile> fileMaps) {
        // 检查文件映射是否为空
        if (fileMaps != null && !fileMaps.isEmpty()) {
            context.setFileData(MultiFileData.of(fileMaps));
        }
        return executeFlowChain(context);
    }

    @PostMapping(value = "/execute", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<?> executeFlowChain(@RequestBody ExtendCommonAIRequestContext<?> context) {
        return commonService.invoke(context);
    }

    @PostMapping(value = "/sync/execute", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result syncExecute(@ModelAttribute ExtendCommonAIRequestContext<?> context, @RequestParam Map<String, MultipartFile> fileMaps) {
        // 检查文件映射是否为空
        if (fileMaps != null && !fileMaps.isEmpty()) {
            context.setFileData(MultiFileData.of(fileMaps));
        }
        return syncExecute(context);
    }

    @PostMapping(value = "/sync/execute")
    public Result syncExecute(@RequestBody ExtendCommonAIRequestContext<?> context) {
        return Result.successData(commonService.syncInvoke(context));
    }

    @PostMapping(value = "/test")
    public Result test(@RequestBody ExtendCommonAIRequestContext<?> context) {
        return Result.successData(new PrivateEmbeddingSearch().syncProcess(context));
    }

}
