package com.cb.ai.data.analysis.ai.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@DS("clickhouse")
public interface SqlParserMapper extends BaseMapper<Map<String, Object>> {

    Map<String, Object> selectData(@Param("sql") String sql);

    IPage<Map<String, Object>> selectDataPage(IPage<?> page, @Param("sql") String sql);

    List<Map<String, Object>> selectTableInfoList();

}
