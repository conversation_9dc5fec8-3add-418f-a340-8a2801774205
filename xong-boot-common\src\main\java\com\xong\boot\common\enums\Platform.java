package com.xong.boot.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 平台
 * <AUTHOR>
 */
public enum Platform {
    /**
     * 电脑端
     */
    PC("admin"),
    /**
     * 手机端
     */
    APP("app");

    /**
     * 请求路径
     */
    private final String path;

    Platform(String path) {
        this.path = path;
    }

    public String getPath() {
        return path;
    }

    /**
     * 根据模块名获取枚举
     * @param value 模块名称
     */
    public static Platform getByValue(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        for (Platform item : values()) {
            if (item.getPath().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
