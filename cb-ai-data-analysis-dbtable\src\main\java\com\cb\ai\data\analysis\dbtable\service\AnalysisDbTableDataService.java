package com.cb.ai.data.analysis.dbtable.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.converts.Convert;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.cb.ai.data.analysis.dbtable.model.ExcelConvert;
import com.cb.ai.data.analysis.dbtable.model.ExcelHeadColumn;
import com.cb.ai.data.analysis.dbtable.model.ExcelSheetName;
import com.cb.ai.data.analysis.dbtable.model.QueryItem;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 数据Service接口
 * <AUTHOR>
 */
public interface AnalysisDbTableDataService {
    /**
     * 读取Excel工作薄名称集
     * @param file 文件
     */
    List<ExcelSheetName> readExcelSheetNames(MultipartFile file);

    /**
     * 读取Excel头
     * @param file      文件
     * @param sheetNo   工作薄位置
     * @param headStart 表头开始位置
     * @param headEnd   表头结束位置
     */
    List<ExcelHeadColumn> readExcelHead(MultipartFile file, Integer sheetNo, Integer headStart, Integer headEnd);

    /**
     * 获取转换器
     */
    List<Convert<?>> getConvertOptions();

    /**
     * 启动导入工作
     * @param file          文件
     * @param tableId       表ID
     * @param sheetNo       工作薄
     * @param headStart     表头开始位置
     * @param headEnd       表头结束位置
     * @param startRow      开始行
     * @param endRow        结束行
     * @param excelConverts excel转换
     */
    AnalysisDbTableTask startJob(MultipartFile file,
                                 String tableId,
                                 Integer sheetNo,
                                 Integer headStart,
                                 Integer headEnd,
                                 Integer startRow,
                                 Integer endRow,
                                 List<ExcelConvert> excelConverts) throws IOException;

    /**
     * 分页查询表数据
     * @param tableName    表名
     */
    Page<Map<String, Object>> pageTableData(String tableName);

    /**
     * 获取指定表指定id的数据
     * @param tableName
     * @param id
     * @return
     */
    Map<String, Object> getTableDataById(String tableName, String id);

    /**
     * 新增记录
     * @param tableName
     * @param record
     * @return
     */
    String addTableData(String tableName, Map<String,Object> record);

    /**
     * 修改表数据
     * @param tableName
     * @param record
     */
    int editTableDataById(String tableName, String id, Map<String,Object> record);

    /**
     * 删除表数据
     * @param tableName
     * @param ids     数据ID集
     */
    void deleteTableData(String tableName, List<String> ids);
}
