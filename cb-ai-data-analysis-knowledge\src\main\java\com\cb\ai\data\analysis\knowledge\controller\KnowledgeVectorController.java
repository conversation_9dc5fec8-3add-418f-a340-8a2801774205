package com.cb.ai.data.analysis.knowledge.controller;


import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeVector;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeVectorVo;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeVectorService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.exception.XServiceException;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/***
 * <AUTHOR>
 * 向量库相关接口
 */
@Validated
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/vector")
public class KnowledgeVectorController {

    @Autowired
    private KnowledgeVectorService knowledgeVectorService;


    /***
     * 向量库查询
     * @param knowledgeVector
     * @return
     */
    @PostMapping("/rag/search")
    public Result searchVector(@RequestBody  KnowledgeVector knowledgeVector) {
        try{
            List<KnowledgeVectorVo> knowledgeVectorVo=knowledgeVectorService.searchVector(knowledgeVector);
            return Result.successData(knowledgeVectorVo);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("向量库查询失败！");
        }
    }

    /***
     * 知识库向量库查询
     * @param knowledgeVector
     * @return
     */
    @PostMapping("/rag/search/knowledge")
    public Result searchKnowledgeVector(@RequestBody  KnowledgeVector knowledgeVector) {
        try{
            List<KnowledgeVectorVo> knowledgeVectorVo=knowledgeVectorService.searchKnowledgeVector(knowledgeVector);
            return Result.successData(knowledgeVectorVo);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("向量库查询失败！");
        }
    }

    /**
     * 向量库删除
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "向量库删除", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "知识库ID不存在") String baseId, @NotEmpty(message = "向量库ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (knowledgeVectorService.deleteVectorByIds(baseId, list)) {
            return Result.success("向量库删除成功");
        } else {
            return Result.fail("向量库删除失败！");
        }
    }

}
