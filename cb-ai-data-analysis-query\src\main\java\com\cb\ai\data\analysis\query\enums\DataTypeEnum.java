package com.cb.ai.data.analysis.query.enums;

import com.cb.ai.data.analysis.query.constant.Constant;

/**
 * 数据类型枚举
 */
public enum DataTypeEnum {
    SUPERVISE_FILE("file", Constant.ES_SUPERVISE_FILE_INDEX, "文件"),
    GRAPH_DATA("graph", Constant.ES_GRAPH_DATA_INDEX, "知识图谱"),
    FINANCE_DATA("finance", Constant.ES_FINANCE_DATA_INDEX, "财务信息"),
    DYNAMIC_DATA("dynamic", Constant.ES_DYNAMIC_DATA_INDEX, "动态数据");
    // 类型
    private String type;
    // 索引
    private String index;
    private String desc;

    DataTypeEnum(String type, String index, String desc) {
        this.type = type;
        this.index = index;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }

    public static String ofType(String index) {
        for (DataTypeEnum value : DataTypeEnum.values()) {
            if(index.startsWith(value.getIndex())){
                return value.getType();
            }
        }
        return null;
    }
    public static DataTypeEnum ofEnum(String type) {
        for (DataTypeEnum value : DataTypeEnum.values()) {
            if(type.equals(value.getType())){
                return value;
            }
        }
        return null;
    }
}
