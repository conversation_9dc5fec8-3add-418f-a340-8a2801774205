package com.cb.ai.data.analysis.petition.mapper;


import com.cb.ai.data.analysis.petition.annotation.DataSource;
import com.cb.ai.data.analysis.petition.domain.FmFile;
import com.cb.ai.data.analysis.petition.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@DataSource(DataSourceType.SLAVE)
public interface FmFileMapper 
{
    /**
     * 查询文件
     * 
     * @param id 文件主键
     * @return 文件
     */
    public FmFile selectFmFileById(Long id);

    /**
     * 查询文件
     *
     * @param ids
     * @return 文件
     */
    public List<FmFile> selectFmFileByIds(Long [] ids);

    /**
     * 查询文件列表
     * 
     * @param fmFile 文件
     * @return 文件集合
     */
    public List<FmFile> selectFmFileList(FmFile fmFile);

    /**
     * 新增文件
     * 
     * @param fmFile 文件
     * @return 结果
     */
    public int insertFmFile(FmFile fmFile);
    /**
     * 批量新增文件
     *
     * @return 结果
     */
    public int insertBatch(@Param("entities") List<FmFile> entities);

    /**
     * 修改文件
     * 
     * @param fmFile 文件
     * @return 结果
     */
    public int updateFmFile(FmFile fmFile);

    /**
     * 删除文件
     * 
     * @param id 文件主键
     * @return 结果
     */
    public int deleteFmFileById(Long id);

    /**
     * 批量删除文件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFmFileByIds(Long[] ids);

    /**
     * 根据父路径和名称查询文件
     *
     * @param parentPath 父路径
     * @param name 名称
     * @return 文件
     */
    public FmFile selectFmFileByParentPathAndName(@Param("parentPath") String parentPath, @Param("name") String name);

    /**
     * 更新文件信息（用于扫描任务）
     *
     * @param fmFile 文件
     * @return 结果
     */
    public int updateFmFileForScan(FmFile fmFile);

    /**
     * 批量更新文件的parentPath
     *
     * @param oldPathPrefix 旧路径前缀
     * @param newPathPrefix 新路径前缀
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    public int updateParentPathBatch(@Param("oldPathPrefix") String oldPathPrefix,
                                     @Param("newPathPrefix") String newPathPrefix,
                                     @Param("updateBy") String updateBy,
                                     @Param("updateTime") String updateTime);


    int updateAnalysisStatus(@Param("id") Long id, @Param("analysisStatus") int analysisStatus);


    int updateAnalysisStatusByIds(@Param("ids") Long[] ids, @Param("analysisStatus") int analysisStatus);

}
