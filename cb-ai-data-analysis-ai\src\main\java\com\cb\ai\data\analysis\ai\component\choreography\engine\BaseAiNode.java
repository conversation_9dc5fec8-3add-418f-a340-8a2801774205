package com.cb.ai.data.analysis.ai.component.choreography.engine;

import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.component.choreography.flow.IFlowProcessNode;
import com.xong.boot.common.exception.CustomException;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.springframework.web.reactive.function.client.WebClient;



/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 17:07
 * @Copyright (c) 2025
 * @Description 基础AI节点
 */
public abstract class BaseAiNode<E, R, Rs> extends BaseAttributeNode<E, R, BaseAiNode<E, R, Rs>> implements IProcessNode<E, Rs>, ISyncNode<E, R>, IFlowProcessNode<E, R> {

    private GenericObjectPool<WebClient> webClientPool;

    public BaseAiNode() {
        this.webClientPool = SpringUtil.getBean("webClientPool");
    }

    protected WebClient getWebClient() {
        if (webClientPool == null) {
            throw new CustomException("webClientPool is null");
        }
        try {
            return webClientPool.borrowObject();
        } catch (Exception e) {
            throw new CustomException("webClientPool borrowObject error", e);
        }
    }

    protected void returnWebClient(WebClient webClient) {
        if (webClientPool == null) {
            throw new CustomException("webClientPool is null");
        }
        try {
            webClientPool.returnObject(webClient);
        } catch (Exception e) {
            throw new CustomException("webClientPool returnObject error", e);
        }
    }
}
