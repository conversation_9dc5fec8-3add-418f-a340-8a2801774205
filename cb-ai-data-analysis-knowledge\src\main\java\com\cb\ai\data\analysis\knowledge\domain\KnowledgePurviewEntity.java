package com.cb.ai.data.analysis.knowledge.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("cb_knowledge_purview")
public class KnowledgePurviewEntity extends BaseDomain implements Serializable {

    /***
     * 主键ID
     */
    private String id;
    /***
     * 知识库ID
     */
    private String baseId;


    /***
     * 权限类型，目前只有部门
     */
    private String purviewType;

    /***
     * 部门ID
     */
    private String deptId;
}
