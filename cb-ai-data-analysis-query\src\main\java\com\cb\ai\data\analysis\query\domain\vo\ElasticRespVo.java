package com.cb.ai.data.analysis.query.domain.vo;

import com.cb.ai.data.analysis.query.enums.DataTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Elastic 查询返回结果
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
public interface ElasticRespVo {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Resp {
        // 返回索引，否则获取详情无参数可传
        private String indexName;
        // 数据类型 file(文件) or dynamic(动态数据)
        private String dataType;
        private Object highlight;
        private Object source;
        // 表名
        private String tableComment;
        // 表字段列表
        private List<ColumnVo> columns;
    }
}
