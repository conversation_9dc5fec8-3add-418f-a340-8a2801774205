package com.cb.ai.data.analysis.petition.enums;

import java.util.Arrays;

/**
 * 文件解析状态枚举
 */
public enum FileAnalysisStatus {

    UNFINISHED(0, "未解析"),
    COMPLETED(1, "解析完成"),
    FAILED(2, "解析失败"),
    UPLOADED(3, "已上传知识库"),
    AIANALYSIS(4, "AI已解析"),
    //所有解析过程的中间状态
    INPROCESS(5, "解析中");

    private final Integer code;
    private final String dec;

    FileAnalysisStatus(Integer code, String dec)
    {
        this.code = code;
        this.dec = dec;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getDec()
    {
        return dec;
    }

    public static FileAnalysisStatus fromCode(Integer code){
        return Arrays.stream(FileAnalysisStatus.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }
}
