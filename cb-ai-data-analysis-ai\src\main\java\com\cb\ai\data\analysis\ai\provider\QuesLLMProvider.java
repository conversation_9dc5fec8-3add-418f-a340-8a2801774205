package com.cb.ai.data.analysis.ai.provider;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.ai.enums.EventStreamState;
import com.cb.ai.data.analysis.ai.exception.AiApiException;
import com.cb.ai.data.analysis.ai.model.AIContext;
import com.cb.ai.data.analysis.ai.model.AiResultData;
import com.cb.ai.data.analysis.ai.model.EventStreamResult;
import com.cb.ai.data.analysis.ai.model.body.AiLlmBody;
import com.cb.ai.data.analysis.ai.model.item.FileItem;
import com.cb.ai.data.analysis.ai.model.item.knowledgeItem;
import com.cb.ai.data.analysis.ai.model.options.QuesLLMOptions;
import com.cb.ai.data.analysis.ai.utils.AiWebClient;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;

/**
 * LLM问答
 * <AUTHOR>
 */
@Component
public class QuesLLMProvider extends AbstractAiProvider<AiResultData> {
    private final static String TAG = "QUES_LLM";

    private final AiWebClient aiWebClient;

    public QuesLLMProvider(AiProviderService aiProviderService, AiWebClient aiWebClient) {
        super(aiProviderService);
        this.aiWebClient = aiWebClient;
        setSaveChat(true);
    }

    @Override
    public boolean matcher(AIContext context) {
        List<knowledgeItem> knowledges = context.getKnowledges();
        List<FileItem> files = context.getFiles();
        return TAG.equals(context.getTag()) || (StringUtils.isBlank(context.getTag()) && (knowledges == null || knowledges.size() == 0) && (files == null || files.size() == 0));
    }

    @Override
    protected Flux<EventStreamResult<AiResultData>> run(String sessionId, String promote, AIContext context) {
        AiLlmBody aiLlmBody = new AiLlmBody();
        aiLlmBody.setSessionId(sessionId);
        aiLlmBody.setPromote(promote);
        aiLlmBody.setSystemPromote(context.getSystemPromote());
        // 设置参数级配置
        QuesLLMOptions options = context.getOptions(QuesLLMOptions.class);
        if (options != null) {
            aiLlmBody.setModel(options.getModel());
            aiLlmBody.setTemperature(options.getTemperature());
            aiLlmBody.setTop_p(options.getTopP());
            aiLlmBody.setMax_tokens(options.getMaxTokens());
            aiLlmBody.setFrequency_penalty(options.getFrequencyPenalty());
            aiLlmBody.setPresence_penalty(options.getPresencePenalty());
        }
        return Flux.from(aiWebClient.requestQuesLLM(aiLlmBody))
                .map(content -> {
                    try {
                        AiResultData resultData = new AiResultData();
                        JSONObject object = JSON.parseObject(String.valueOf(content));
                        if (object.containsKey("reasoning_content")) {
                            resultData.setReasoningContent(object.getString("reasoning_content"));
                        }
                        if (object.containsKey("content")) {
                            resultData.setContent(object.getString("content"));
                        }
                        return EventStreamResult.newInstance(sessionId, EventStreamState.streaming)
                                .setRawData(content)
                                .setData(resultData);
                    } catch (Exception e) {
                        return EventStreamResult.generateErrorStreamingResult(sessionId, e).setRawData(content);
                    }
                });
    }

//    public static void main(String[] args) throws InterruptedException {
//        Flux.create(fluxSink -> {
//            CompletableFuture.runAsync(() -> {
//                for (int i = 0; i < 1000; i++) {
//                    fluxSink.next("AAAAAAAAAA" + i);
//                }
//            });
//            CompletableFuture.runAsync(() -> {
//                for (int i = 0; i < 1000; i++) {
//                    fluxSink.next("BBBBBBBBBB" + i);
//                }
//            });
//            CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
//                for (int i = 0; i < 1000; i++) {
//                    fluxSink.next("CCCCCCCCCC" + i);
//                }
//            });
//            fluxSink.complete();
//        }).subscribe(System.out::println);
//        Thread.sleep(50000);
////        Flux.create(sink -> {
////            for (int i = 0; i < 5; i++) {
////                sink.next("javaedge" + i);
////            }
////            sink.complete();
////        }).subscribe(System.out::println);
//    }
}
