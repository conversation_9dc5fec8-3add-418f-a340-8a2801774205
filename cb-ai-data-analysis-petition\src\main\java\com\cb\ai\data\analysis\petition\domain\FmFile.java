package com.cb.ai.data.analysis.petition.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件对象 fm_file
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@Data
public class FmFile implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 路径
     */
    private String parentPath;

    /**
     * 原文件名
     */
    private String oldName;

    /**
     * 新文件名
     */
    private String name;

    /**
     * 文件后缀
     */
    private String extName;

    /**
     * 存储路径
     */
    private String path;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 是否删除 1 正常 2 删除
     */
    private Integer delFlag;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 解析状态
     */
    private Integer analysisStatus;

    private String createBy;
    private String createTime;
    private String updateBy;
    private String updateTime;


    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;


    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }


}
