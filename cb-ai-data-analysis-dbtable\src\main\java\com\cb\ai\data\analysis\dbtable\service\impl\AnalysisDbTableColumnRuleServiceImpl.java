package com.cb.ai.data.analysis.dbtable.service.impl;

import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumnRule;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableColumnRuleMapper;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableColumnRuleService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 动态数据表字段规则 ServiceImpl
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableColumnRuleServiceImpl extends BaseServiceImpl<AnalysisDbTableColumnRuleMapper, AnalysisDbTableColumnRule> implements AnalysisDbTableColumnRuleService {
}
