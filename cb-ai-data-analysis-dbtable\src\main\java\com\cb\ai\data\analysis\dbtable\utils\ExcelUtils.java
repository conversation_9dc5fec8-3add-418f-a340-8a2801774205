package com.cb.ai.data.analysis.dbtable.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.cb.ai.data.analysis.dbtable.model.ExcelHeadColumn;
import com.cb.ai.data.analysis.dbtable.model.ExcelSheetName;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel工具类
 * <AUTHOR>
 */
public class ExcelUtils {
    /**
     * 获取Excel工作薄名称集
     * @param inputStream 文件流
     * @param filename    文件名
     */
    public static List<ExcelSheetName> getSheetNames(InputStream inputStream, String filename) throws IOException {
        String suffix = FileUtil.getSuffix(filename);
        Workbook workbook;
        if (ReUtil.isMatch("^[xX][lL][sS]$", suffix)) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        List<ExcelSheetName> sheetNames = new ArrayList<>();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            ExcelSheetName excelSheetName = new ExcelSheetName();
            excelSheetName.setNo(i);
            excelSheetName.setName(workbook.getSheetName(i));
            sheetNames.add(excelSheetName);
        }
        return sheetNames;
    }

    /**
     * 解析Excel表头
     * @param excelHead 表头
     */
    public static List<ExcelHeadColumn> parseExcelHead(List<Map<Integer, String>> excelHead) {
        List<Map<Integer, String>> tableHead = fillHeadEmpty(excelHead);
        Map<Integer, List<String>> listData = new HashMap<>();
        // 把表头转为列集合
        for (Map<Integer, String> item : tableHead) {
            for (Map.Entry<Integer, String> entry : item.entrySet()) {
                if (!listData.containsKey(entry.getKey())) {
                    listData.put(entry.getKey(), new ArrayList<>());
                }
                listData.get(entry.getKey()).add(item.get(entry.getKey()));
            }
        }
        List<ExcelHeadColumn> headColumns = new ArrayList<>();
        for (Map.Entry<Integer, List<String>> entry : listData.entrySet()) {
            Integer key = entry.getKey();
            List<String> values = entry.getValue();
            List<String> columnNames = new ArrayList<>();
            List<String> columns = null;
            for (int i = 0; i < values.size(); i++) {
                String val = values.get(i);
                if (columnNames.size() > 0) {
                    if (!val.equals(columnNames.get(i - 1))) {
                        columns.add(val);
                    }
                } else {
                    columns = new ArrayList<>();
                    columns.add(val);
                }
                columnNames.add(val);
            }
            ExcelHeadColumn headColumn = new ExcelHeadColumn();
            headColumn.setNo(key);
            if (columns != null) {
                headColumn.setName(String.join("/", columns));
            }
            headColumn.setColumnNames(columnNames);
            headColumns.add(headColumn);
        }
        return headColumns;
    }

    /**
     * 填充头中空白单元格
     * @param excelHead 表头
     */
    private static List<Map<Integer, String>> fillHeadEmpty(List<Map<Integer, String>> excelHead) {
        if (excelHead == null || excelHead.size() == 0) {
            return excelHead;
        }
        List<Map<Integer, String>> tableHead = JSON.parseObject(JSON.toJSONString(excelHead), new TypeReference<List<Map<Integer, String>>>() {
        });
        for (int i = 0; i < tableHead.size(); i++) {
            Map<Integer, String> rows = tableHead.get(i);
            // 填充第一行数据
            if (i == 0) {
                for (Map.Entry<Integer, String> entry : rows.entrySet()) {
                    if (CellDataTypeEnum.EMPTY.name().equals(entry.getValue())) {
                        rows.put(entry.getKey(), rows.getOrDefault(entry.getKey() - 1, CellDataTypeEnum.EMPTY.name()));
                    }
                }
                continue;
            }
            // 填充列数据
            Map<Integer, String> prevRows = tableHead.get(i - 1); // 上一行数据
            for (Map.Entry<Integer, String> entry : rows.entrySet()) {
                String value = entry.getValue();
                if (!CellDataTypeEnum.EMPTY.name().equals(value)) {
                    // 当前位置内存在值就不做任何处理
                    continue;
                }
                String prevRowValue = prevRows.get(entry.getKey()); // 上一行当前列的值
                // 当前位置内不存在值且当前位置在第一列，就把上一行第一列的值赋值给当前位置
                if (entry.getKey() == 0) {
                    rows.put(0, prevRowValue);
                    continue;
                }
                String prevColValue = rows.getOrDefault(entry.getKey() - 1, CellDataTypeEnum.EMPTY.name()); // 上一列值
                String prevRowColumnValue = prevRows.get(entry.getKey() - 1); // 上一行上一列值
                // 当前位置内不存在值且上一行上一列值与上一行的值相等，就把上一列的值赋值到当前位置
                if (prevRowColumnValue.equals(prevRowValue)) {
                    rows.put(entry.getKey(), prevColValue);
                } else {
                    // 当前位置内不存在值且同行上一列值与同列上一行值相等，就把同行上一列的值赋值到当前位置
                    // 当前位置内不存在值且上一行上一列值与上一列的值相等，就把上一行的值赋值到当前位置
                    // 当前位置内不存在值且上一行上一列值与上一行的值与上一列的值都不相等，就把上一行的值赋值到当前位置
                    rows.put(entry.getKey(), prevRowValue);
                }
            }
        }
        return tableHead;
    }
}
