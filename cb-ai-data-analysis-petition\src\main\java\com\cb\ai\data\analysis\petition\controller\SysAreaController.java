//package com.cb.ai.data.analysis.petition.controller;
//
//
//import com.cb.ai.data.analysis.petition.constant.Constants;
//import com.cb.ai.data.analysis.petition.domain.entity.SysArea;
//import com.cb.ai.data.analysis.petition.service.SysAreaService;
//import com.xong.boot.common.api.Result;
//import jakarta.annotation.Resource;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//
//import java.util.List;
//
//
//@RestController
//@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/area")
//
//public class SysAreaController
//{
//    @Resource
//    private SysAreaService sysAreaService;
//
//    /**
//     * 查询区划信息列表
//     */
//    @GetMapping("/list")
//    public Result list(SysArea sysArea)
//    {
//        List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
//        return Result.successData(list);
//    }
//}
