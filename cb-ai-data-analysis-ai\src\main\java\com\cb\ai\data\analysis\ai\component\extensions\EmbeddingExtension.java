package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateEmbeddingSearch;
import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.EmbeddingResult;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.SecurityContextUtils;
import com.xong.boot.common.exception.CustomException;
import com.xong.boot.common.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 向量查询扩展
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmbeddingExtension {

    @Value("${cb.ai.embeddingChat.topK:80}")
    private Integer topK;

    private final RedisUtils redisUtils;

    public Flux<PrivateAIBackData> invoke(CommonAIRequestContext context, Consumer<CollectData> callback) {
        Assert.notEmpty(context.getBaseIds(), "向量查询知识库Id不能为空");
        SecurityContextUtils securityContext = new SecurityContextUtils();
        CompletableFuture<CollectData> future = CompletableFuture.supplyAsync(() ->
            securityContext.withSecurityContext(() -> queryAndGet(context))
        );
        return Flux.interval(Duration.ofSeconds(1))
            .map(tick -> new PrivateAIBackData(
                context.getSessionId(),
                RoleEnum.assistant,
                null,
                null,
                JsonMap.of("progress", calculatePercentage(tick)),
                null
            ))
            .takeUntilOther(Mono.fromFuture(future).then())
            .concatWith(
                Mono.fromFuture(future)
                    .timeout(Duration.ofSeconds(2))
                    .onErrorResume(e -> Mono.error(new CustomException("异步查询向量库获取异常，原因：" + e.getMessage(), e)))
                    .doOnNext(callback)
                    .thenReturn(
                        new PrivateAIBackData(
                            context.getSessionId(),
                            RoleEnum.assistant,
                            null,
                            null,
                            JsonMap.of("progress", 100),
                            null
                        )
                    )
            );
    }

    public CollectData invoke(CommonAIRequestContext context) {
        try {
            // 等待60s，超时则抛出异常
            return queryAndGet(context);
        } catch (Exception e) {
            CommonLog.error("向量查询异常", e);
            throw new CustomException("异步查询向量库获取异常，原因：" + e.getMessage(), e);
        }
    }

    private CollectData queryAndGet(CommonAIRequestContext context) {
        Assert.notEmpty(context.getBaseIds(), "向量查询知识库Id不能为空");
        // 查询同一请求向量缓存
        List<EmbeddingResult> embeddingResults = JsonUtil.toList(redisUtils.get(AiConstants.getEmbeddingKey(context.getRequestId())), EmbeddingResult.class);
        if (CollectionUtil.isEmpty(embeddingResults)) {
            // 查询向量
            context.setTopK(topK);
            embeddingResults = new PrivateEmbeddingSearch().syncProcess(context);
            Assert.notEmpty(embeddingResults, "知识库【" + String.join("，", context.getBaseNames() != null? context.getBaseNames() : context.getBaseIds()) + "】向量查询结果为空！");
            // 缓存向量（半小时有效期）
            redisUtils.set(AiConstants.getEmbeddingKey(context.getRequestId()), JsonUtil.toStr(embeddingResults), Duration.ofSeconds(1800));
        }
        /* 根据向量数据组装提示词 */
        return formatMarkdownTable(embeddingResults);
    }

    private CollectData formatMarkdownTable(List<EmbeddingResult> references) {
        // 已经根据文本内容长度从大到小排序， 去第一个就是最大值
        //int maxRefLength = references.get(0).getText().length();
        List<JsonMap> dataList = new ArrayList<>();
        // 填充数据
        StringBuilder markdown = new StringBuilder();
        references.forEach(e -> {
            markdown.append(
                String.format(
                    " | %d | %s |\n",
                    e.getIndex(),
                    e.getText()
                )
            );
            if (e.getMetadata() != null) {
                dataList.add(JsonMap.of("fileId", e.getMetadata().fileId())
                    .putOpt("fileName", e.getMetadata().originFileName())
                    .putOpt("fileUrl", e.getMetadata().originFileUrl())
                    .putOpt("index", e.getIndex())
                    .putOpt("page", e.getMetadata().page())
                    .putOpt("references", e.getMetadata().content())
                    .putOpt("sourceFileName", e.getMetadata().sourceFileName())
                    .putOpt("sourceFileUrl", e.getMetadata().sourceFileUrl())
                    .putOpt("traceProperties", JsonMap.of("startRow", e.getMetadata().startRow()).putOpt("endRow", e.getMetadata().endRow())
                ));
            }

        });
        return new CollectData(markdown.toString(), dataList);
    }

    /**
     * 计算进度百分比
     * @param tick 当前进度
     * @return 进度百分比
     */
    public double calculatePercentage(long tick) {
        if (tick < 10) {
            return tick * 10.0;
        } else {
            // 使用渐近函数，确保永远不会达到100
            double base = 90.0;
            double increment = 0.1 * (1 - Math.exp(-0.1 * (tick - 9)));
            return base + increment * 10;
        }
    }

    public record CollectData(String embeddingContext, List<JsonMap> dataList) {}

}
