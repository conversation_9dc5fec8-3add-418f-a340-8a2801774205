package com.cb.ai.data.analysis.voucher.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTask;
import com.cb.ai.data.analysis.voucher.mapper.VoucherTaskMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherAlertRecordService;
import com.cb.ai.data.analysis.voucher.service.VoucherCheckRuleService;
import com.cb.ai.data.analysis.voucher.service.VoucherInfoService;
import com.cb.ai.data.analysis.voucher.service.VoucherTaskService;
import com.cb.ai.data.analysis.voucher.utils.task.TaskHelper;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class VoucherTaskServiceImpl extends BaseServiceImpl<VoucherTaskMapper, VoucherTask> implements VoucherTaskService {

    @Autowired
    private VoucherInfoService voucherInfoService;

    @Autowired
    private VoucherCheckRuleService voucherCheckRuleService;

    @Autowired
    private VoucherAlertRecordService voucherAlertRecordService;

    /**
     * 添加任务
     * @param task
     */
    public void add(VoucherTask task) {
        String username = SecurityUtils.getUsername();
        LocalDateTime now = LocalDateTime.now();
        String taskId = IdUtil.getSnowflakeNextIdStr();
        task.setId(taskId);
        task.setStatus(0);
        task.setCreateBy(username);
        task.setCreateTime(now);
        task.setUpdateBy(username);
        task.setUpdateTime(now);
        String tags = task.getTags();
        String[] split = tags.split(",");
        long total = voucherInfoService.countByTagsAndDate(split, now);
        task.setTotalNum(total);
        task.setAlertNum(0L);
        task.setHandleNum(0L);
        //入库保存
        baseMapper.insert(task);
        //异步执行分析任务
        asyncRunTask(taskId);
    }

    @Override
    public void asyncRunTask(String taskId) {
        VoucherTask task = baseMapper.selectById(taskId);
        if (null == task) {
            throw new RuntimeException("任务不存在！");
        }
        Integer status = task.getStatus();
        String username = task.getCreateBy();
        if(Integer.valueOf(1).equals( status)){
            throw new RuntimeException("任务正在分析中！请等待");
        }
        //更新状态为执行中
        LambdaUpdateWrapper<VoucherTask> updateWrapper = Wrappers.lambdaUpdate(VoucherTask.class)
                .eq(VoucherTask::getId, taskId)
                .set(VoucherTask::getStatus, 1)
                .set(VoucherTask::getErrMsg, null)
                .set(VoucherTask::getHandleNum, 0L)
                .set(VoucherTask::getAlertNum, 0L)
                .set(VoucherTask::getTaskBeginTime, LocalDateTime.now())
                .set(VoucherTask::getTaskEndTime, null)
                .set(VoucherTask::getUpdateBy, username)
                .set(VoucherTask::getUpdateTime, LocalDateTime.now());
        baseMapper.update(updateWrapper);
        // 删除预警记录，重新分析
        voucherAlertRecordService.deleteByTaskId(taskId);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            // 获取计数器
            TaskHelper.TaskCounter counter = TaskHelper.getOrCreatCounter(taskId);
            VoucherTask voucherTask = baseMapper.selectById(taskId);
            String rules = voucherCheckRuleService.buildCheckRuleText();
            long total = voucherTask.getTotalNum();
            String tags = voucherTask.getTags();
            String[] split = tags.split(",");
            LocalDateTime cutTime = voucherTask.getCreateTime();
            try{
                int pageSize = 10;
                int pages = (int) ((total + pageSize - 1) / pageSize);
                for (int i = 1; i <= pages; i++) {
                    Page<VoucherInfo> queryPage = new Page<>(i, 10);
                    List<VoucherInfo> list = voucherInfoService.pageByTagsAndDate(queryPage, split, cutTime).getRecords();
                    for (VoucherInfo voucherInfo : list) {
                        //已经ocr的才去分析
                        if(Integer.valueOf(2).equals(voucherInfo.getStatus()) && StringUtils.isNotBlank(voucherInfo.getOcrText())){
                            boolean ok = voucherInfoService.analysisByTaskAndId(rules, voucherTask, voucherInfo.getId());
                            if (!ok) {
                                counter.incrementAlertNum();
                            }
                        }
                        counter.incrementHandleNum();
                    }
                }
                log.info("凭证分析任务处理完成。taskId：{}", taskId);
                voucherTask.setStatus(2);
            } catch (Exception e) {
                log.info("凭证分析任务处理异常。taskId：{}, 错误信息：{}", taskId, e.getMessage());
                voucherTask.setStatus(3);
                voucherTask.setErrMsg(e.getMessage());
            } finally {
                voucherTask.setAlertNum(counter.getAlertNum());
                voucherTask.setHandleNum(counter.getHandleNum());
                voucherTask.setTaskEndTime(LocalDateTime.now());
                voucherTask.setUpdateBy(voucherTask.getCreateBy());
                voucherTask.setUpdateTime(LocalDateTime.now());
                baseMapper.updateById(voucherTask);
                TaskHelper.removeTaskCounter(voucherTask.getId());
            }
        });
    }

    @Override
    public void deleteById(String taskId) {
        VoucherTask task = baseMapper.selectById(taskId);
        if(Integer.valueOf(1).equals(task.getStatus())){
            throw new RuntimeException("任务正在执行中，不允许删除");
        }
        //删除预警记录
        voucherAlertRecordService.deleteByTaskId(taskId);
        baseMapper.deleteById(taskId);
    }
}
