package com.cb.ai.data.analysis.file.controller;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.constant.Constants;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.domain.dto.IdsDto;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Validated
@RestController
@RequestMapping(Constants.API_UNI_SUPERVISE_ROOT_PATH + "/resource/folder")
public class SuperviseResourceFolderController extends BaseController<SuperviseResourceFolderService, SuperviseResourceFolder> {

    private final SuperviseResourceFileService superviseResourceFileService;
    public SuperviseResourceFolderController(SuperviseResourceFileService superviseResourceFileService) {
        this.superviseResourceFileService = superviseResourceFileService;
    }

    @GetMapping("/getList")
    public Result getList(){
        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
        String username = userDetails.getUsername();
        LambdaQueryWrapper<SuperviseResourceFolder> superviseResourceFolderLambdaQueryWrapper = new QueryWrapper<SuperviseResourceFolder>().lambda()
                .eq(SuperviseResourceFolder::getCreateBy, username)
                .orderByAsc(SuperviseResourceFolder::getSortOn);
        List<SuperviseResourceFolder> list = baseService.list(superviseResourceFolderLambdaQueryWrapper);
        List<SuperviseResourceFolder> list1 = list.stream().filter(e -> {
            return "我的文件夹".equals(e.getFolderName());
        }).toList();
        //如果没有我的文件夹，就初始化一个
        if (list1.isEmpty()) {
            SuperviseResourceFolder superviseResourceFolder = new SuperviseResourceFolder();
            String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
            superviseResourceFolder.setId(snowflakeNextIdStr);
            superviseResourceFolder.setFolderName("我的文件夹");
            superviseResourceFolder.setParentId("0");
            superviseResourceFolder.setFullPath("0,"+snowflakeNextIdStr);
            baseService.save(superviseResourceFolder);
            list.add(superviseResourceFolder);
        }
        return Result.successData(list);
    }
    @PostMapping
    public Result add(@Validated(AddGroup.class) @RequestBody SuperviseResourceFolder superviseResourceFolder){
        //获取全路径
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        superviseResourceFolder.setId(snowflakeNextIdStr);
        if ("0".equals(superviseResourceFolder.getParentId())){
            superviseResourceFolder.setFullPath("0,"+snowflakeNextIdStr);
        }else{
            SuperviseResourceFolder byId = baseService.getById(superviseResourceFolder.getParentId());
            superviseResourceFolder.setFullPath(byId.getFullPath()+","+snowflakeNextIdStr);
        }
        //计算排序，把他放到同级目录的最后面
        LambdaQueryWrapper<SuperviseResourceFolder> last = new QueryWrapper<SuperviseResourceFolder>().lambda()
                .eq(SuperviseResourceFolder::getParentId, superviseResourceFolder.getParentId())
                .orderByDesc(SuperviseResourceFolder::getSortOn).last("limit 1");
        SuperviseResourceFolder one = baseService.getOne(last);

        superviseResourceFolder.setSortOn(one==null?0:one.getSortOn()+1);
        boolean b = baseService.save(superviseResourceFolder);
        if (b){
            return Result.success("创建成功！");
        }
        return Result.fail("创建失败！");

    }
    @PutMapping
    public Result update(@Validated(UpdateGroup.class) @RequestBody SuperviseResourceFolder SuperviseResourceFolder){
        boolean b = baseService.updateById(SuperviseResourceFolder);
        if (b){
            return Result.success("修改成功！");
        }
        return Result.fail("修改失败！");
    }

    @Transactional
    @DeleteMapping
    @XLog(title = "删除文件夹", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "文件夹ID不存在") String[] ids) {
        //需要删除子级目录以及文件夹
        if (baseService.removeFolders(ids)) {
            return Result.success("删除成功");
        }
        return Result.fail("删除失败");
    }
    /**
     * 拖拽修改
     * @param params
     * @return
     */
    @PutMapping("/drop")
//    @PreAuthorize("hasAuthority('system:dept:edit')")
    public Result drop(@RequestBody DropParams params) {
        baseService.dropFolder(params);
        return Result.success("移动成功");
    }


//    /**
//     * 获取已删除的文件
//     * @param superviseResourceFolder
//     * @return
//     */
//    @GetMapping("/getFolderListByAlreadyDeleted")
//    public Result getFileListByAlreadyDeleted(SuperviseResourceFolder superviseResourceFolder){
//        QueryWrapper<SuperviseResourceFolder> queryWrapper =new QueryWrapper<>();
//        queryWrapper.lambda().like(SuperviseResourceFolder::getFolderName, superviseResourceFolder.getFolderName())
//                .eq(SuperviseResourceFolder::getDelFlag, true);
//        Page<SuperviseResourceFolder> page = baseService.page(QueryHelper.getPage(), queryWrapper);
//        return Result.successData(page);
//    }
    /**
     * 获取已删除的文件夹
     *
     * @param superviseResourceFolder
     * @return
     */
    @GetMapping("/getFolderListByAlreadyDeleted")
    public Result getFolderListByAlreadyDeleted(SuperviseResourceFolder superviseResourceFolder) {
        Page<SuperviseResourceFolder> page = baseService.pageDeletedFolders(QueryHelper.getPage(), superviseResourceFolder);
        return Result.successData(page);
    }
    @PostMapping("/restoreFolder")
    public Result restoreFolder(@RequestBody IdsDto idsDto) {
        baseService.restoreFolder(idsDto.getIds(),idsDto.getFolderId());
        return Result.success("还原成功！");
    }

    @DeleteMapping("/permanentlyDelete")
    @XLog(title = "删除文件", execType = ExecType.DELETE)
    public Result permanentlyDelete(@NotEmpty(message = "文件ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        //更新被删除文件的原路径
        List<SuperviseResourceFolder> folderList = baseService.permanentlyDelete(idList);
        List<String> folderIds = folderList.stream().map(SuperviseResourceFolder::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SuperviseResourceFile> in = new QueryWrapper<SuperviseResourceFile>().lambda().in(SuperviseResourceFile::getFolderId, folderIds);
        List<SuperviseResourceFile> fileList = superviseResourceFileService.getFileList(in);
        List<String> fileIds = fileList.stream().map(SuperviseResourceFile::getId).collect(Collectors.toList());
        //删除文件
        boolean b = superviseResourceFileService.permanentlyDelete(fileIds);
        //删除文件夹
        LambdaQueryWrapper<SuperviseResourceFolder> in1 = new QueryWrapper<SuperviseResourceFolder>().lambda().in(SuperviseResourceFolder::getId, folderIds);
        Integer i = baseService.deleteByCustom(in1);
        if (i>0) {
            return Result.success("删除成功");
        }
        return Result.fail("删除失败");
    }
}
