package com.cb.ai.data.analysis.petition.domain.vo.response;


import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateDeserializer;
import com.xong.boot.common.json.serializer.LocalDateSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class SsPetitionFileSearchResultVo extends BaseDomain {
    private static final long serialVersionUID = 1L;

    private String fileName;

    private Integer fileType;

    private String fileTypeName;

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate uploadTime;

    private Integer dataSize;


    public void setFileType(Integer fileType) {
        this.fileType = fileType;
        FileContentTypeEnum fileContentTypeEnum = FileContentTypeEnum.fromCode(fileType);
        if (fileContentTypeEnum != null) {
            this.fileTypeName = fileContentTypeEnum.getSuffix();
        } else {
            this.fileTypeName = "未知的文件类型";
        }
    }

}
