package com.cb.ai.data.analysis.dbtable.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableTaskItemMapper;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableTaskItemService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.framework.utils.QueryHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 数据导入工作日志Service业务层处理
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableTaskItemServiceImpl extends BaseServiceImpl<AnalysisDbTableTaskItemMapper, AnalysisDbTableTaskItem> implements AnalysisDbTableTaskItemService {

    /**
     * 查询数据导入工作列表
     * @param bigdataPoolJobLog 数据导入工作
     * @return 数据导入工作
     */
    @Override
    public Page<AnalysisDbTableTaskItem> pageTableTaskItemList(AnalysisDbTableTaskItem bigdataPoolJobLog) {
        return baseMapper.pageTableTaskItemList(QueryHelper.getPage(true),bigdataPoolJobLog);
    }

    /**
     * 批量删除数据导入工作
     * @param ids 需要删除的数据导入工作主键
     * @return 结果
     */
    @Override
    public int deleteDynamicTableTaskItemByIds(String[] ids) {
        return baseMapper.deleteByIds(Arrays.asList(ids));
    }

}
