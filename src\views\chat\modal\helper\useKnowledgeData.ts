import { ref,nextTick } from 'vue'
import { base } from '@/api/knowledge'
import { page } from '@/api/knowledge/file'
import {message} from 'ant-design-vue'

interface DirectoryItem {
  id: string
  name: string
  type: 'file' | 'folder'
  [key: string]: any
}

const fileExtensions = [
  '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
  '.pdf', '.txt', '.md', '.csv', '.json', '.xml',
  '.zip', '.rar', '.7z', '.html', '.js', '.ts',
  '.jpg', '.jpeg', '.png', '.gif', '.mp3', '.mp4',
  '.avi', '.mov', '.wmv', '.flv','.nl2sql','.wps'
]

function isFileByName(name: string): boolean {
  const lower = name.toLowerCase()
  return fileExtensions.some(ext => lower.endsWith(ext))
}

function getFileExtension(name: string): string {
  if (!name || typeof name !== 'string') return ''
  const parts = name.split('.')
  return parts.length > 1 ? parts.pop()!.toLowerCase() : ''
}

export function useKnowledgeData() {
  const knowLedgeFolderOrFileData = ref<DirectoryItem[]>([]);//源数据
  const knowLedgeFolderOrFileDataRaw = ref<any[]>([])//用于重新赋值或者搜素的数据

  const knowLedgeFolderOrFileModalLoading = ref(false);

  // --- 新增：列表容器 ref
  const knowledgeContentRef = ref<HTMLElement | null>(null)

  // --- 新增：滚到顶部
  async function scrollListToTop() {
    await nextTick() // 等新数据渲染完
    knowledgeContentRef.value?.scrollTo({ top: 0, behavior: 'auto' })
  }


  function setData(records: any[]) {
    knowLedgeFolderOrFileData.value = records
    knowLedgeFolderOrFileDataRaw.value = [...records]
  }

  function filterDataByKeyword(keyword: string) {
    knowLedgeFolderOrFileData.value = knowLedgeFolderOrFileDataRaw.value.filter(item =>
      (item.name || item.fileName || '').toLowerCase().includes(keyword.toLowerCase())
    )
  }


  /**
   * 加载根目录数据
   */
  async function loadKnowLedgeRootData() {
    knowLedgeFolderOrFileModalLoading.value = true
    try {
      const { data } = await base.list()
      const recordsRootList = (data || []).map((item: any) => ({
        ...item,
        type: isFileByName(item.name) ? 'file' : 'folder'
      }))
      setData(recordsRootList)
    } catch (err) {
      message.error('加载知识库失败')
      throw err
    } finally {
      knowLedgeFolderOrFileModalLoading.value = false
    }
  }

  /**
   * 加载某个目录下的内容
   */
  async function loadKnowLedgeFolderData(params: { baseId: string; fileName?: string }) {
    knowLedgeFolderOrFileModalLoading.value = true
    try {
      const { data } = await page({
        pageCurrent: 1,
        pageSize: 200,
        baseId: params.baseId,
        ...(params.fileName?.trim() ? { fileName: params.fileName.trim() } : {})
      })

      const records = data?.records || []

      const recordsFileList = records.map((item: any) => {
        const name = item.fileName || item.name || ''
        const isFile = isFileByName(name)
        const fileType = isFile ? getFileExtension(name) : undefined

        return {
          ...item,
          type: isFile ? 'file' : 'folder',
          fileType
        }
      })
      setData(recordsFileList);
      await scrollListToTop()
    } catch (err) {
      message.error('加载文件夹内容失败')
      throw err
    } finally {
      knowLedgeFolderOrFileModalLoading.value = false
    }
  }

  function resetFilteredData() {
    knowLedgeFolderOrFileData.value = [...knowLedgeFolderOrFileDataRaw.value]
  }

  function normalizeFileType(ext: string): string {
    const map: Record<string, string> = {
      doc: 'doc',
      docx: 'doc',
      xls: 'exc',
      xlsx: 'exc',
      pdf: 'pdf',
      png: 'img',
      jpg: 'img',
      jpeg: 'img'
    }
    return map[ext] || 'other'
  }

  const fileIcons: Record<string, string> = {
    doc: new URL('@/assets/images/fileType/<EMAIL>', import.meta.url).href,
    exc: new URL('@/assets/images/fileType/<EMAIL>', import.meta.url).href,
    pdf: new URL('@/assets/images/fileType/<EMAIL>', import.meta.url).href,
    img: new URL('@/assets/images/fileType/<EMAIL>', import.meta.url).href,
    other: new URL('@/assets/images/fileType/<EMAIL>', import.meta.url).href
  }


  function getFileIconByType(ext: string): string {
    const type = normalizeFileType(ext)
    return fileIcons[type] || fileIcons.other
  }

  function formatFileSize(bytes: number, decimal = 2): string {
    if (isNaN(bytes) || bytes < 0) return '0 B'

    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
    const index = Math.floor(Math.log(bytes) / Math.log(1024))
    const size = bytes / Math.pow(1024, index)
    return `${size.toFixed(decimal)} ${units[index]}`
  }

  return {
    knowledgeContentRef,
    knowLedgeFolderOrFileData,
    knowLedgeFolderOrFileModalLoading,
    formatFileSize,
    getFileIconByType,
    loadKnowLedgeRootData,
    loadKnowLedgeFolderData,
    resetFilteredData,
    filterDataByKeyword
  }
}
