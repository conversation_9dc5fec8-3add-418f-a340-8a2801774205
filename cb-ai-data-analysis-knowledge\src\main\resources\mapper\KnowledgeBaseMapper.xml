<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.knowledge.mapper.KnowledgeBaseMapper">

    <resultMap type="com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity" id="KnowledgeBaseMap">
        <result property="id"               column="id"    />
        <result property="name"             column="name"    />
        <result property="tag"              column="tag"    />
        <result property="parentId"         column="parent_id"    />
        <result property="purviewMark"      column="purview_mark"    />
        <result property="createUserId"     column="create_user_id"    />
        <result property="isDel"            column="is_del"    />
        <result property="createBy"         column="create_by"    />
        <result property="createTime"       column="create_time"    />
        <result property="updateBy"         column="update_by"    />
        <result property="updateTime"       column="update_time"    />
        <result property="remark"           column="remark"    />
    </resultMap>

    <select id="pageKnowledgeBase" resultMap="KnowledgeBaseMap">
        SELECT DISTINCT t1.* FROM cb_knowledge_base t1
        LEFT JOIN cb_knowledge_purview t2 ON t2.base_id = t1.id
        LEFT JOIN sys_dept t3 ON t3.dept_id = t2.dept_id
        ${ew.customSqlSegment}
        <if test="userId != null and userId != ''">
            and (
                t1.purview_mark=1
                <if test="userDeptId != null and userDeptId != '' ">
                    <![CDATA[
                    or (t1.purview_mark=2 and t2.dept_id =#{userDeptId})
                    or (t1.purview_mark=3 and (t2.dept_id =#{userDeptId} OR FIND_IN_SET(#{userDeptId}, t3.full_path) > 0))
                     ]]>
                </if>
            or (t1.purview_mark=4 and t1.create_user_id=#{userId})
            )
        </if>
        order by t1.create_time desc
    </select>


    <select id="getKnowledgeBaseList" resultMap="KnowledgeBaseMap">
        SELECT DISTINCT t1.* FROM cb_knowledge_base t1
        LEFT JOIN cb_knowledge_purview t2 ON t2.base_id = t1.id
        LEFT JOIN sys_dept t3 ON t3.dept_id = t2.dept_id
        where t1.is_del=0
        <if test="searchKey != null and searchKey != ''">
            and  t1.name like CONCAT('%',#{searchKey},'%')
        </if>
        <if test="parentId != null and parentId != ''">
            and t1.parent_id = #{parentId}
        </if>
        <if test="userId != null and userId != ''">
            and (
            t1.purview_mark=1
            <if test="userDeptId != null and userDeptId != '' ">
                <![CDATA[
                    or (t1.purview_mark=2 and t2.dept_id =#{userDeptId})
                    or (t1.purview_mark=3 and (t2.dept_id =#{userDeptId} OR FIND_IN_SET(#{userDeptId}, t3.full_path) > 0))
                     ]]>
            </if>
            or (t1.purview_mark=4 and t1.create_user_id=#{userId})
            )
        </if>
        order by t1.create_time desc
    </select>
</mapper>
