<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.graph.mapper.basic.GraphEnterpriseInfoMapper">

    <resultMap type="com.cb.ai.data.analysis.graph.domain.entity.basic.GraphEnterpriseInfo" id="GraphEnterpriseInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="enterpriseName" column="enterprise_name" jdbcType="VARCHAR"/>
        <result property="legalRepresentative" column="legal_representative" jdbcType="VARCHAR"/>
        <result property="registerCapital" column="register_capital" jdbcType="VARCHAR"/>
        <result property="registerStatus" column="register_status" jdbcType="VARCHAR"/>
        <result property="unifySocialCreditCode" column="unify_social_credit_code" jdbcType="VARCHAR"/>
        <result property="foundDate" column="found_date" jdbcType="TIMESTAMP"/>
        <result property="industryCommerceRegisterNo" column="industry_commerce_register_no" jdbcType="VARCHAR"/>
        <result property="taxpayerIdentifyNo" column="taxpayer_identify_no" jdbcType="VARCHAR"/>
        <result property="enterpriseType" column="enterprise_type" jdbcType="VARCHAR"/>
        <result property="approveDate" column="approve_date" jdbcType="TIMESTAMP"/>
        <result property="formerName" column="former_name" jdbcType="VARCHAR"/>
        <result property="contributedCapital" column="contributed_capital" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="shareholder" column="shareholder" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

