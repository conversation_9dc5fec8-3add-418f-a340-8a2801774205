package com.cb.ai.data.analysis.ai.component.http;

import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.xong.boot.common.exception.CustomException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import reactor.core.publisher.Flux;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:06
 * @Copyright (c) 2025
 * @Description 统一http客户端接口
 */
public interface HttpClient {
    /**
     * @description 设置请求方法
     * <AUTHOR>
     */
    HttpClient method(HttpMethod method);
    /**
     * @description 设置请求地址
     * <AUTHOR>
     */
    HttpClient url(String url);
    /**
     * @description 设置请求头
     * <AUTHOR>
     */
    HttpClient headers(Consumer<HttpHeaders> headersConsumer);
    /**
     * @description 设置请求体
     * <AUTHOR>
     */
    HttpClient body(Object body);
    /**
     * @description 设置上传文件请求体
     * <AUTHOR>
     */
    HttpClient fileBody(MultiFileData fileBody);

    /**
     * @description 设置响应失败回调
     * <AUTHOR>
     */
    HttpClient onError(Function<Throwable, CustomException> onError);

    /**
     * @description 设置响应成功回调
     * <AUTHOR>
     */
    HttpClient onSuccess(Function<String, ?> onSuccess);

    /**
     * @description 流式返回执行请求
     * @return Object
     * @createtime 2025/7/3 下午10:31
     * <AUTHOR>
     * @version 1.0
     */
    <V> Flux<V> executeStream();

    /**
     * @description 阻塞式执行请求
     * @return Object
     * @createtime 2025/7/3 下午10:31
     * <AUTHOR>
     * @version 1.0
     */
    <V> V executeBlock();

}
