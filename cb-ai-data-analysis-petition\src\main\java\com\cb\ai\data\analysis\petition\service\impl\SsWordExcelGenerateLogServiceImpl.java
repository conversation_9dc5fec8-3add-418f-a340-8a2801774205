package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.petition.domain.entity.SsWordExcelGenerateLogEntity;
import com.cb.ai.data.analysis.petition.domain.excel.CustomColumnWidthHandler;
import com.cb.ai.data.analysis.petition.domain.vo.WordExcelRowVo;
import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import com.cb.ai.data.analysis.petition.enums.WordExcelGenerateStatusEnum;
import com.cb.ai.data.analysis.petition.mapper.SsWordExcelGenerateLogMapper;
import com.cb.ai.data.analysis.petition.service.SsWordExcelGenerateLogService;
import com.cb.ai.data.analysis.petition.utils.FileReaderFactory;
import com.cb.ai.data.analysis.petition.utils.FileReaderHandler;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * (SsWordExcelGenerateLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21 16:07:26
 */
@Service("ssWordExcelGenerateLogService")
public class SsWordExcelGenerateLogServiceImpl extends ServiceImpl<SsWordExcelGenerateLogMapper, SsWordExcelGenerateLogEntity> implements SsWordExcelGenerateLogService {

    @Resource(name = "petitionFileReaderFactory")
    private FileReaderFactory fileReaderFactory;
    @Resource
    private SuperviseResourceFileService superviseResourceFileService;

    @Override
    public void wordToExcel(List<SuperviseResourceFile> fileList) {
        String targetFileName = "Word内容汇总_" + IdUtil.simpleUUID() + ".xlsx";
        SsWordExcelGenerateLogEntity ssWordExcelGenerateLogEntity = new SsWordExcelGenerateLogEntity();
        ssWordExcelGenerateLogEntity.setFileName(targetFileName);
        ssWordExcelGenerateLogEntity.setStatus(WordExcelGenerateStatusEnum.GENERATING.getStatus());
        ssWordExcelGenerateLogEntity.setFileIds(fileList.stream().map(SuperviseResourceFile::getId).reduce((a, b) -> a + "," + b).orElse(""));
        baseMapper.insert(ssWordExcelGenerateLogEntity);
        String username = SecurityUtils.getUsername();
        // 异步处理
        new Thread(() -> {
            Integer status = WordExcelGenerateStatusEnum.GENERATING.getStatus();
            String remark = "";
            try {
                ssWordExcelGenerateLogEntity.setFilePath(generate(fileList, targetFileName, username));
                status = WordExcelGenerateStatusEnum.FINISHED.getStatus();
                remark = "生成成功";
            } catch (Exception e) {
                e.printStackTrace();
                status = WordExcelGenerateStatusEnum.FAILED.getStatus();
                remark = e.getMessage();
                log.error("word生成excel出现异常:{}", e);
            } finally {
                ssWordExcelGenerateLogEntity.setStatus(status);
                ssWordExcelGenerateLogEntity.setRemark(remark);
                baseMapper.updateById(ssWordExcelGenerateLogEntity);
            }
        }).start();
    }

    public String generate(List<SuperviseResourceFile> fileList, String targetFileName, String username) throws Exception {
        // 生成excel
        List<WordExcelRowVo> rows = new ArrayList<>();
        int index = 1;
        for (SuperviseResourceFile file : fileList) {
            // 判断扩展名
            String extension = file.getFileSuffix();
            FileReaderHandler fileReaderHandler;
            if (extension.equals("wps") || extension.equals("wpt")) {
                fileReaderHandler = fileReaderFactory.getReaderByContentType(FileContentTypeEnum.DOC.getContentType());
            } else {
                fileReaderHandler = fileReaderFactory.getReaderBySuffix(extension);
            }
            String content = (String) fileReaderHandler.read(superviseResourceFileService.getFileStream(file.getId()));
            content = content.replaceAll("(.{1,50})(\\s+|$)", "$1\n");
            WordExcelRowVo row = new WordExcelRowVo(index++, file.getFilename(), content);
            rows.add(row);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, WordExcelRowVo.class)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .sheet("Word内容汇总")
                .doWrite(rows);
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        SuperviseResourceFile targetFile = superviseResourceFileService.uploadFileStreamByFolderName(inputStream, "", targetFileName, "word转换",
                Long.valueOf(inputStream.available()), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "", username);
        return targetFile.getFilePath();
    }
}

