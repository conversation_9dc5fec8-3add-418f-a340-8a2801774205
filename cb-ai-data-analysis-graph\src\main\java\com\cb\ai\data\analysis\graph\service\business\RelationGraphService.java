package com.cb.ai.data.analysis.graph.service.business;


import com.cb.ai.data.analysis.graph.domain.vo.RelationGraphVo;

import java.util.List;
import java.util.Map;


public interface RelationGraphService {
    RelationGraphVo.QueryResp query(String cypher, Map<String, Object> params);

    long execute(String cypher, Map<String, Object> params);

    /**
     * 纯纯的创建节点，不做任何合并
     *
     * @param label      初始label
     * @param node       节点数据
     * @param extraLabel 额外添加的label
     * @return prop的id
     */
    String createNode(String label, Map<String, Object> node, String extraLabel);

    /**
     * 单个匹配创建节点,支持合并条件
     *
     * @param label          用于匹配的label
     * @param node           节点信息
     * @param extraLabel     额外添加的label
     * @param mergeCondition 合并的条件
     * @return prop的id
     */
    String createNode(String label, Map<String, Object> node, String extraLabel, String mergeCondition);

    /**
     * 多级匹配创建节点
     *
     * @param label          用于匹配的label
     * @param node           节点信息
     * @param extraLabel     额外添加的label
     * @param mergeCondition 合并的条件
     * @return prop的id
     */
    String createNodeMatchOrCreate(String label, Map<String, Object> node, String extraLabel, String[] mergeCondition);

    /**
     * 根据数据的属性匹配值创建节点,存在满足条件的节点就合并，否则就创建新的
     *
     * @param label             用于匹配的label
     * @param node              节点数据
     * @param extraLabel        额外添加的label
     * @param matchPropAndScore 用于匹配的属性值及score 示例：{"身份证号:1.0","手机号:0.8","name:0.4","工作单位:0.4"}
     * @param passScore         及格线，超过多少分的记录才返回 示例: 0.6
     * @return prop的id
     */
    String createNodeMatchByPropScoreOrCreate(String label, Map<String, Object> node, String extraLabel, String[] matchPropAndScore, String passScore);

    /**
     * 根据数据的属性匹配值查询满足匹配度条件的节点
     *
     * @param label             用于匹配的label
     * @param node              节点数据
     * @param matchPropAndScore 用于匹配的属性值及score 示例：{"身份证号:1.0","手机号:0.8","name:0.4","工作单位:0.4"}
     * @param passScore         及格线，超过多少分的记录才返回 示例: 0.6
     * @return
     */
    List<RelationGraphVo.ScoreNodeMatchItem> getPropScoreMatchNodeList(String label, Map<String, Object> node, String[] matchPropAndScore, String passScore);

    List<String> batchCreateNodes(String label, List<Map<String, Object>> nodes, String mergeCondition);

    List<String> batchCreateNodesMatchOrCreate(String label, List<Map<String, Object>> nodes, String mergeCondition);

    long deleteNodesByProp(String label, Map<String, Object> prop);

    void batchCreateRelationships(RelationGraphVo.BatchAddRelationshipReq req);

    long batchDeleteRelationships(RelationGraphVo.BatchDeleteRelationshipReq req);

    /**
     * 根据节点的属性id值创建关系 (是创建节点时生成的 n.id = randomUUID())
     *
     * @param startPropId   开始节点的属性id
     * @param endPropId     结束节点的属性id
     * @param relationLabel 关系label
     * @param relationProps 关系数据
     * @return 返回节点及关系的信息
     */
    RelationGraphVo.QueryResp createRelationByPropId(String startPropId, String endPropId, String relationLabel, Map<String, Object> relationProps);

    /**
     * 将两类类节点，根据节点的某一个label进行关系创建
     *
     * @param nodeLabels
     * @param relationCondition
     * @param relationPattern
     */
    void createOrUpdateRelation(
            String[] nodeLabels,
            String relationCondition,
            String relationPattern,
            String onCreateSet,
            String onMatchSet
    );


    void createFullTextIndex(String indexName, String label, String[] props, boolean force);

    void cleanAll();

    RelationGraphVo.QueryResp search(String name, int layers);

    /**
     * 搜索匹配的节点名称
     *
     * @param name
     * @return
     */
    List<Map<String, Object>> nameMatch(String name);

    Map<String, List<String>> baseAttributes();

    /**
     * 保存节点（新增或者修改属性）
     *
     * @param req
     * @return
     */
    Map<String, Object> saveNode(RelationGraphVo.AddNodeReq req);

    /**
     * 根据属性id删除节点
     */
    void delNodeById(String propId);

    /**
     * 添加节点和关系
     *
     * @param req
     * @return 返回节点信息和关系信息
     */
    RelationGraphVo.QueryResp addNodeAndRelation(RelationGraphVo.AddNodeAndRelationReq req);

    /**
     * 根据属性id添加关系
     *
     * @param req
     * @return 返回关系信息 {source:xxx, target:xxx, type:xxx, data:{} }
     */
    Map<String, Object> addRelationById(RelationGraphVo.AddRelationByPropIdReq req);

    /**
     * 修改关系
     *
     * @param req
     * @return 返回新的关系信息 {source:xxx, target:xxx, type:xxx, data:{} }
     */
    Map<String, Object> changeRelation(RelationGraphVo.ChangeRelationReq req);

    /**
     * 删除关系
     *
     * @param req
     */
    void delRelation(RelationGraphVo.DelRelationReq req);

    /**
     * 合并节点
     *
     * @param req
     */
    void mergeNode(RelationGraphVo.MergeNodeReq req);

}
