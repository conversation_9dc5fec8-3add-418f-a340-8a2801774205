package com.cb.ai.data.analysis.dbtable.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.model.clickhouse.DBColumn;

import java.util.List;
import java.util.Map;

/**
 * clickhouse Service
 * <AUTHOR>
 */
public interface ClickHouseTableService {
    /**
     * 判断是否存在表
     * @param tableName 表名称
     */
    boolean existSqlTable(String tableName);

    /**
     * 表中存在数据
     * @param tableName 表名
     */
    boolean existTableData(String tableName);

    /**
     * 创建表
     * @param tableName    表名
     * @param tableComment 表注释
     * @param columns      字段集
     */
    void createTable(String tableName, String tableComment, List<DBColumn> columns);

    /**
     * 删除表
     * @param tableName 表名
     */
    void removeTable(String tableName);

    /**
     * 修改表名
     * @param tableName    表名
     * @param nowTableName 修改后表名
     */
    boolean renameTableName(String tableName, String nowTableName);

    /**
     * 拷贝表数据
     * @param columnNames     表字段名集
     * @param sourceTableName 源数据表名
     * @param targetTableName 目标数据表名
     */
    boolean copyTableData(List<String> columnNames, String sourceTableName, String targetTableName);

    /**
     * 获取表字段名列表
     * @param tableName 表名
     */
    List<String> listTableColumnName(String tableName);

    /**
     * 分页或去表行数据
     */
    Page<Map<String, Object>> pageTableRow(Page<?> page, String tableName);

    /**
     * 删除表行数据
     * @param tableName
     * @param ids
     * @return
     */
    int deleteTableData(String tableName, List<String> ids);
}
