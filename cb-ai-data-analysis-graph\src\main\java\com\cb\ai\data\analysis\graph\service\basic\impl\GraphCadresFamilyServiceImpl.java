package com.cb.ai.data.analysis.graph.service.basic.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresFamily;
import com.cb.ai.data.analysis.graph.mapper.basic.GraphCadresFamilyMapper;
import com.cb.ai.data.analysis.graph.repository.GraphCadresFamilyRepository;
import com.cb.ai.data.analysis.graph.repository.esBo.GraphCadresFamilyBo;
import com.cb.ai.data.analysis.graph.service.basic.GraphCadresFamilyService;
import com.cb.ai.data.analysis.graph.utils.GraphUtil;
import com.xong.boot.framework.utils.SecurityUtils;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识图谱-干部亲属表(GraphCadresFamily)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:15
 */
@Service("graphCadresFamilyService")
public class GraphCadresFamilyServiceImpl extends ServiceImpl<GraphCadresFamilyMapper, GraphCadresFamily> implements GraphCadresFamilyService {

    @Autowired
    private GraphCadresFamilyRepository repository;

    @Override
    @Transactional
    public boolean save(GraphCadresFamily graphCadresFamily) {
        if (baseMapper.insert(graphCadresFamily) > 0) {
            GraphUtil.handleOfficerFamily(List.of(graphCadresFamily));

            // ES 新增数据
            GraphCadresFamilyBo bo = convert2Bo(graphCadresFamily);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateById(GraphCadresFamily graphCadresFamily) {
        if (baseMapper.updateById(graphCadresFamily) > 0) {
            GraphUtil.handleOfficerFamily(List.of(graphCadresFamily));

            // ES 修改数据
            GraphCadresFamilyBo bo = convert2Bo(graphCadresFamily);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        // 从ES 中删除
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<GraphCadresFamily> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            GraphUtil.handleOfficerFamily(list);
            // ES 新增数据
            List<GraphCadresFamilyBo> bos = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(bos);
        }
        return b;
    }

    private GraphCadresFamilyBo convert2Bo(GraphCadresFamily graphCadresFamily) {
        GraphCadresFamilyBo bo = new GraphCadresFamilyBo();
        BeanUtils.copyProperties(graphCadresFamily, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }

}

