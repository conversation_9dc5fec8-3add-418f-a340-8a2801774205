package com.cb.ai.data.analysis.ai.component.flows.opensource.model;

import com.cb.ai.data.analysis.ai.component.choreography.engine.BaseAiCallNode;
import com.cb.ai.data.analysis.ai.domain.request.context.AIRequestContext;
import org.springframework.http.codec.ServerSentEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 09:27
 * @Copyright (c) 2025
 * @Description 通义千问3-30b-a3b模型
 */
public class QW3_30b_a3b_Model extends BaseAiCallNode<AIRequestContext, Object> {

    @Override
    public boolean isThinking(Object rawData) {
        return false;
    }

    @Override
    public boolean isContent(Object rawData) {
        return false;
    }

    @Override
    public String setRequestUrl() {
        return "";
    }

    @Override
    public <E> E setRequestBody() {
        return null;
    }

    @Override
    public Object resultConvert(ServerSentEvent<String> ssEvent) {
        return null;
    }
}
