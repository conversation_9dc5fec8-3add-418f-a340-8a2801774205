package com.xong.boot.common.crypto.password;

/**
 * 密码算法加解密接口
 * <AUTHOR>
 */
public interface PasswordEncoder extends org.springframework.security.crypto.password.PasswordEncoder {
    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    String encode(CharSequence rawPassword, CharSequence salt);

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt);
}
