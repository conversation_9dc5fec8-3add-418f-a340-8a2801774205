package com.cb.ai.data.analysis.graph.handler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.enums.*;
import com.cb.ai.data.analysis.graph.service.business.RelationGraphService;
import com.cb.ai.data.analysis.graph.utils.FileProcessCache;
import com.cb.ai.data.analysis.graph.utils.FileReaderFactory;
import com.cb.ai.data.analysis.graph.utils.JacksonUtil;
import com.cb.ai.data.analysis.graph.utils.TxtFileUtil;
import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import com.cb.ai.data.analysis.petition.utils.ExcelReaderHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.base.Throwables;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.utils.spring.SpringUtils;
import com.xong.boot.framework.service.UserDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import reactor.core.publisher.Flux;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

/**
 * 通用文档处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class CommonProcessor implements FileProcessor {

    private final RelationGraphService relationGraphService;
    private final FileReaderFactory readerFactory;
    //    private final ThreadPoolExecutor workFlowThreadPoolExecutor;
//    private final WorkFlowProperties workFlowProperties;
//    private final WorkFlowRequestService workFlowRequestService;
    private final FileProcessCache fileProcessCache;
    private final TxtFileUtil txtFileUtil;
    private final UserDetailsService userDetailsService;

    {
        relationGraphService = SpringUtils.getBean(RelationGraphService.class);
        readerFactory = SpringUtils.getBean(FileReaderFactory.class);
//        workFlowThreadPoolExecutor = SpringUtils.getBean("workFlowThreadPoolExecutor");
//        workFlowProperties = SpringUtils.getBean("workFlowProperties");
//        workFlowRequestService = SpringUtils.getBean(WorkFlowRequestService.class);
        fileProcessCache = SpringUtils.getBean(FileProcessCache.class);
        txtFileUtil = SpringUtils.getBean(TxtFileUtil.class);
        userDetailsService = SpringUtils.getBean(UserDetailsService.class);
    }


    @Override
    public void process(InputStream fileInputStream, GraphFileRecord graphFileRecord, String promote) {
        try {
            String fileName = graphFileRecord.getFileName();
            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);

            if (FileContentTypeEnum.XLS.getSuffix().equals(suffix) || FileContentTypeEnum.XLSX.getSuffix().equals(suffix)) {
                ExcelReaderHandler excelReaderHandler = (ExcelReaderHandler) readerFactory.getReaderBySuffix(suffix);

                Map<String, List<JSONObject>> sheetMap = excelReaderHandler.read(fileInputStream);

                int totalCount = sheetMap.values().stream()
                        .filter(CollectionUtils::isNotEmpty)
                        .mapToInt(List::size)
                        .sum();

                fileProcessCache.initProgress(graphFileRecord.getId(), totalCount);

                log.info("============================ starting to process excel file: [{}],total {} sheet ============================", fileName, sheetMap.keySet().size());
                for (Map.Entry<String, List<JSONObject>> entry : sheetMap.entrySet()) {
                    String sheetName = entry.getKey();
                    List<JSONObject> dataList = entry.getValue();
                    if (CollectionUtils.isEmpty(dataList)) {
                        continue;
                    }

                    log.info("start to process sheet:{},data size:{} ", sheetName, dataList.size());

                    Integer analyzeContentLength = 10;

                    for (int i = 0; i < dataList.size(); i += analyzeContentLength) {
                        List subList;
                        if (i + analyzeContentLength > dataList.size()) {
                            subList = dataList.subList(i, dataList.size());
                        } else {
                            subList = dataList.subList(i, i + analyzeContentLength);
                        }
                        try {
                            extract(subList, promote);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        fileProcessCache.incrementProgress(graphFileRecord.getId(), subList.size());

                    }
                    log.info("process sheet finished:{}", sheetName);
                }
            } else {
                log.info("============================ starting to process txt file: [{}] ============================", fileName);
                String content = (String) readerFactory.getReaderBySuffix(suffix).read(fileInputStream);
                // 根据模型能力设置块大小（示例值，需实际调整）
                int maxChunkSize = 6000;  // 对应约12k tokens（假设1汉字≈2 tokens）
                List<String> chunks = txtFileUtil.splitTextIntoChunks(content, maxChunkSize);

                fileProcessCache.initProgress(graphFileRecord.getId(), chunks.size());

                for (String chunk : chunks) {
                    try {
                        extract(chunk, promote);
                    } catch (Exception e) {
                        throw e;
                    }
                    fileProcessCache.incrementProgress(graphFileRecord.getId(), 1);  // 逐块更新进度
                }

            }
            log.info("============================ process [{}] finished  ============================", fileName);
        } catch (Exception e) {
           handleCatchException("ai 抽取人物关系失败！", e);
        }
    }


    /**
     * 调用BISHENG工作流接口，解析数据，获取关系图谱既定的字段信息.
     *
     * @param data 需要解析的数据
     */
    private void extract(Object data, String promote) throws Exception {
        String message = "";
        try {
            Long start = System.currentTimeMillis();
            log.info("extract start");

            // 调用模型抽取
//            message = workFlowRequestService.singleRequest(data, promote, 10);
            message = llmExtract(data, promote);

            Long end = System.currentTimeMillis();
            log.info("extract result,time cost:{} s", (end - start) / 1000);

            JSONArray orgArray = null;
            JSONArray personArray = null;
            JSONArray relationArray = null;
            try {
                JSONObject relationResult = JacksonUtil.parseObject(message, JSONObject.class);
                orgArray = relationResult.getJSONArray(BasicGraphCategoryEnum.ORG.getName());
                personArray = relationResult.getJSONArray(BasicGraphCategoryEnum.PERSON.getName());
                relationArray = relationResult.getJSONArray(BasicGraphCategoryEnum.RELATION.getName());
            } catch (Exception e) {
                ArrayNode orgArrayNode = JacksonUtil.extractSafeJsonArray(message, BasicGraphCategoryEnum.ORG.getName());
                ArrayNode personArrayNode = JacksonUtil.extractSafeJsonArray(message, BasicGraphCategoryEnum.PERSON.getName());
                ArrayNode relationArrayNode = JacksonUtil.extractSafeJsonArray(message, BasicGraphCategoryEnum.PERSON.getName());

                if (orgArrayNode != null && orgArrayNode.isArray()) {
                    orgArray = new JSONArray();
                    for (JsonNode node : orgArrayNode) {
                        try {
                            orgArray.add(JSONObject.parseObject(node.toString()));
                        } catch (Exception ex) {
                            System.err.println("跳过非法企业节点: " + node);
                        }
                    }
                }

                if (personArrayNode != null && personArrayNode.isArray()) {
                    personArray = new JSONArray();
                    for (JsonNode node : personArrayNode) {
                        try {
                            personArray.add(JSONObject.parseObject(node.toString()));
                        } catch (Exception ex) {
                            System.err.println("跳过非法人员节点: " + node);
                        }
                    }
                }

                if (relationArrayNode != null && relationArrayNode.isArray()) {
                    relationArray = new JSONArray();
                    for (JsonNode node : relationArrayNode) {
                        try {
                            relationArray.add(JSONObject.parseObject(node.toString()));
                        } catch (Exception ex) {
                            System.err.println("跳过非法关系节点: " + node);
                        }
                    }
                }

            }

            handleOrgArray(orgArray);
            Map<String, String> orginIdToNeo4jIdMap = handlePersonArray(personArray);
            handleRelation(relationArray, orginIdToNeo4jIdMap);

            relationGraphService.createOrUpdateRelation(
                    new String[]{"p:人员", "c:企业"},
                    "p.`工作单位` = c.name",
                    "(p)-[r:任职于]->(c)",
                    "r.职务 = p.`现职务`",
                    "r.职务 = p.`现职务`"
            );
        } catch (Exception e) {
            e.printStackTrace();
            log.error("================================== extract relation failed =================================");
            log.error("input: {}", data);
            log.error("output: {}", message);
            log.error("exception: {}", e);
            log.error("============================================================================================");
            throw e;
        }
    }

    private void handleOrgArray(JSONArray orgArray) {
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            for (int i = 0; i < orgArray.size(); i++) {
                JSONObject orgJsonObj = orgArray.getJSONObject(i);
                Object companyName = orgJsonObj.get(BasicGraphLabelEnum.ORG_NAME.getAttribute());
                if (companyName == null || StringUtils.isEmpty(companyName.toString())) {
                    continue;
                }

                String companyId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.ENTERPRISE.getLabel(), orgJsonObj, null,
                        new String[]{"{name: $item.name}", "{name: $item.公司名称}"});

            }
        } catch (Exception e) {
            handleCatchException("企业信息表处理报错！", e);
        }
    }

    /**
     * LLM模型抽取
     * @param paramsData
     * @param promote
     * @return
     */
    private String llmExtract(Object paramsData, String promote){
        // 设置管理员权限
        UserDetails admin1 = userDetailsService.loadUserByUsername("admin");
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(admin1, null, admin1.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);

        CommonAIRequestContext aiContext = new CommonAIRequestContext();
        aiContext.setSystemPromote(promote);
        aiContext.setPromote(JSONObject.toJSONString(paramsData));

        PrivateAIChat chat = new PrivateAIChat();
        chat.nodeName("人物关系抽取")
            .context(context -> {
                return aiContext;
            })
            .dataDispose(data -> {
                Function<String,String> replaceFun = (str) -> {
                    if (StrUtil.isNotBlank(str)) {
                        return str.replaceAll("[*#\\- ]+", "");
                    }
                    return str;
                };
                data.setReasoning_content(replaceFun.apply(data.getReasoning_content()));
                data.setContent(replaceFun.apply(data.getContent()));
                return data;
            })
            .hideThinking();
        StringBuilder answerBuilder = new StringBuilder();
        CountDownLatch countDownLatch = new CountDownLatch(1);
        AtomicReference<Throwable> throwable = new AtomicReference<>();
        try{
            Flux<PrivateAIBackData> process = chat.process(aiContext);
            process.subscribe(
                    aiBackData -> {
                        if (StrUtil.isNotBlank(aiBackData.getContent())) {
                            answerBuilder.append(aiBackData.getContent());
                        }
                    },
                    error -> {
                        CommonLog.error("人物关系AI抽取失败!", Throwables.getRootCause(error));
                        throwable.set(error);
                        countDownLatch.countDown();
                    },
                    () -> {
                        countDownLatch.countDown();
                    }
            );
        } catch (Exception e) {
            throwable.set( e );
            countDownLatch.countDown();
        }

        // 最多等待10分钟
        try {
            countDownLatch.await(10 * 60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException("图谱解析等待超时！");
        }
        if (throwable.get() != null) {
            throw new RuntimeException(throwable.get());
        }
        return answerBuilder.toString();
    }


    private Map<String, String> handlePersonArray(JSONArray personArray) {
        Map<String, String> orginIdToNeo4jIdMap = new HashMap<>();
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            for (int i = 0; i < personArray.size(); i++) {
                JSONObject personJsonObj = personArray.getJSONObject(i);
                Object personName = personJsonObj.get(BasicGraphLabelEnum.PERSON_NAME.getAttribute());
                if (personName == null || StringUtils.isEmpty(personName.toString())) {
                    continue;
                }

                Object personOriginId = personJsonObj.remove(BasicGraphLabelEnum.PERSON_ID.getAttribute());
                String personId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), personJsonObj, NodeLabelEnum.LEADER.getLabel(),
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});

                orginIdToNeo4jIdMap.put(String.valueOf(personOriginId), personId);

            }
        } catch (Exception e) {
            handleCatchException("人员信息表处理报错！", e);
        }
        return orginIdToNeo4jIdMap;
    }


    private void handleRelation(JSONArray relationArray, Map<String, String> orginIdToNeo4jIdMap) {
        try {
            for (int i = 0; i < relationArray.size(); i++) {
                JSONObject relationJsonObj = relationArray.getJSONObject(i);
                String sourcePersonId = relationJsonObj.getString(BasicGraphRelationEnum.SOURCE_PERSON_ID.getName());
                String targetPersonId = relationJsonObj.getString(BasicGraphRelationEnum.TARGET_PERSON_ID.getName());
                String relationType = relationJsonObj.getString(BasicGraphRelationEnum.RELATION_DESC.getName());

                if (StringUtils.isEmpty(sourcePersonId) || StringUtils.isEmpty(targetPersonId) || StringUtils.isEmpty(relationType)) {
                    continue;
                }

                String neo4JSourceId = orginIdToNeo4jIdMap.get(sourcePersonId);
                String neo4jTargetId = orginIdToNeo4jIdMap.get(targetPersonId);

                if (StringUtils.isEmpty(neo4JSourceId) || StringUtils.isEmpty(neo4jTargetId)) {
                    continue;
                }
                Map<String, Object> relationProps = MapUtil.of("关系确定性", RelationCertaintyEnum.ACCURATE.getLabel());

                relationGraphService.createRelationByPropId(neo4JSourceId, neo4jTargetId,
                        relationType, relationProps);
            }

        } catch (Exception e) {
            handleCatchException("人员信息表处理报错！", e);
        }

    }


}
