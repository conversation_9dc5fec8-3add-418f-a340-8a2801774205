package com.cb.ai.data.json.tool.service;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * 2025/7/31
 */
public interface JsonToolService {
    void analysisZipJsonFile(MultipartFile file);

    void reAnalyzeJsonFile(String taskId) throws Exception;

    void downloadCompressFile(HttpServletResponse response, String taskId);
}
