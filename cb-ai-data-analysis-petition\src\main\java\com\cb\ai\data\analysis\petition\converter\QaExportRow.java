package com.cb.ai.data.analysis.petition.converter;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

@Data
public class QaExportRow {

    @ExcelProperty("序号")
    @ColumnWidth(10)
    @ContentStyle(
            borderBottom = BorderStyleEnum.THIN,
            borderLeft = BorderStyleEnum.THIN,
            borderTop = BorderStyleEnum.THIN,
            borderRight = BorderStyleEnum.THIN,
            verticalAlignment = VerticalAlignmentEnum.CENTER,
            horizontalAlignment  =HorizontalAlignmentEnum.CENTER

    )
    private Integer index;

    @ExcelProperty("问题描述")
    @ColumnWidth(40)
    @ContentStyle(
            fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND,
            wrapped = BooleanEnum.TRUE,
            shrinkToFit = BooleanEnum.TRUE,
            borderBottom = BorderStyleEnum.THIN,
            borderLeft = BorderStyleEnum.THIN,
            borderTop = BorderStyleEnum.THIN,
            borderRight = BorderStyleEnum.THIN,
            horizontalAlignment  =HorizontalAlignmentEnum.CENTER

    )
    private String question;

    @ExcelProperty("知识库/大语言模型回答")
    @ColumnWidth(80)
    @ContentStyle(
            fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND,
            wrapped = BooleanEnum.TRUE,
            shrinkToFit = BooleanEnum.TRUE,
            borderBottom = BorderStyleEnum.THIN,
            borderLeft = BorderStyleEnum.THIN,
            borderTop = BorderStyleEnum.THIN,
            borderRight = BorderStyleEnum.THIN,
            horizontalAlignment  =HorizontalAlignmentEnum.CENTER

    )
    private String answer;



    @ExcelProperty("参考文献")
    @ColumnWidth(80)
    @ContentStyle(
            fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND,
            wrapped = BooleanEnum.TRUE,
            shrinkToFit = BooleanEnum.TRUE,
            borderBottom = BorderStyleEnum.THIN,
            borderLeft = BorderStyleEnum.THIN,
            borderTop = BorderStyleEnum.THIN,
            borderRight = BorderStyleEnum.THIN,
            horizontalAlignment  =HorizontalAlignmentEnum.CENTER

    )
    private String fileName;

//    @ExcelProperty("问题备注")
//    @ColumnWidth(40)
//    private String remark;
}
