package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 发文字号
 *
 * <AUTHOR>
 */
public class IssuedNumberPipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.contains("^[\\u4e00-\\u9fa5]+〔20[0-9Xx× ]{2}〕[0-9Xx× ]+号$", text);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化段落
        FormatTools.formatParagraph(paragraph, config).setCTJcCenter();
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
