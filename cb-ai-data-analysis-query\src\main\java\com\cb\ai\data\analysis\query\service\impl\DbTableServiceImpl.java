package com.cb.ai.data.analysis.query.service.impl;

import com.cb.ai.data.analysis.query.domain.vo.ColumnVo;
import com.cb.ai.data.analysis.query.domain.vo.DbTableVo;
import com.cb.ai.data.analysis.query.enums.DataTypeEnum;
import com.cb.ai.data.analysis.query.mapper.DbTableMapper;
import com.cb.ai.data.analysis.query.service.DbTableService;
import com.xong.boot.common.constant.CacheConstants;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DbTableServiceImpl implements DbTableService {
    @Autowired
    private DbTableMapper dbTableMapper;


    @Override
    @Cacheable(cacheNames = CacheConstants.DB_INFO_CACHE_KEY, key = "#type + ':' + #tableName")
    public DbTableVo selectTableColumnList(String type, String tableName) {
        DataTypeEnum typeEnum = DataTypeEnum.ofEnum(type);
        if (StringUtils.isBlank(tableName) || typeEnum == null) {
            return null;
        }
        return switch (typeEnum) {
            case GRAPH_DATA -> selectMysqlTableColumnList(tableName);
            case FINANCE_DATA -> selectClickhouseTableColumnList(tableName);
            case DYNAMIC_DATA -> selectClickhouseTableColumnList(tableName);
            default -> null;
        };
    }

    private DbTableVo selectMysqlTableColumnList(String tableName) {
        DbTableVo dbTableVo = dbTableMapper.selectMysqlDbTableVo(tableName);
        List<ColumnVo> columnVoList = dbTableMapper.selectMysqlTableColumnList(tableName);
        setColumnName(dbTableVo, columnVoList);
        return dbTableVo;
    }

    private DbTableVo selectClickhouseTableColumnList(String tableName) {
        DbTableVo dbTableVo = dbTableMapper.selectClickHouseDbTableVo(tableName);
        List<ColumnVo> columnVoList = dbTableMapper.selectClickHouseTableColumnList(tableName);
        setColumnName(dbTableVo, columnVoList);
        return dbTableVo;
    }

//    @Deprecated
//    private DbTableVo selectSlaveTableColumnList(String tableName) {
//        DbTableVo dbTableVo = dbTableMapper.selectSlaveDbTableVo(tableName);
//        List<ColumnVo> columnVoList = dbTableMapper.selectSlaveTableColumnList(tableName);
//        setColumnName(dbTableVo, columnVoList);
//        return dbTableVo;
//    }

    private void setColumnName(DbTableVo dbTableVo, List<ColumnVo> columnVoList) {
        columnVoList.forEach(vo -> {
            String columnName = StringUtils.toCamelCase(vo.getColumnName());
            vo.setColumnName(columnName);
        });
        if (dbTableVo != null){
            dbTableVo.setColumns(columnVoList);
        }
    }
}
