package com.cb.ai.data.analysis.petition.converter.pipe;

import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;
import java.util.regex.Pattern;

/**
 * 标点符号检查
 * <AUTHOR>
 */
public class SymbolPipe extends IPipe {

    private static final String REG1 = "^.*《.*》、《.*》.*$";
    private static final String REG2 = "^.*“.*”、“.*”.*$";

    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        if(!config.isCheckSymbol()) {
            return false;
        }
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return Pattern.matches(REG1, text) || Pattern.matches(REG2, text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<XWPFRun> runs = paragraph.getRuns();
        Pattern pattern1 = Pattern.compile(REG1);
        Pattern pattern2 = Pattern.compile(REG2);
        for (XWPFRun run : runs) {
            String runText = run.text();
            if(pattern1.matcher(runText).find()) {
                runText = runText.replaceAll("》、《", "》《");
            }
            if(pattern2.matcher(runText).find()) {
                runText = runText.replaceAll("”、“", "”“");
            }
            run.setText(runText, 0);
        }
    }

    @Override
    public boolean isBreak() {
        return false;
    }
}
