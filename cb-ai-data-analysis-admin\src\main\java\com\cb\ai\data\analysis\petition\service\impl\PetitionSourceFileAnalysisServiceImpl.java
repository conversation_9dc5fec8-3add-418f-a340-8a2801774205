package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionSourceFileEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionOriginEntity;
import com.cb.ai.data.analysis.petition.enums.FileAnalysisStatus;
import com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum;
import com.cb.ai.data.analysis.petition.enums.SsPetitionStatusEnum;
import com.cb.ai.data.analysis.petition.redis.RedisCache;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileAnalysisService;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileService;
import com.cb.ai.data.analysis.petition.service.SsPetitionOriginService;
import com.cb.ai.data.analysis.petition.utils.ExcelReaderHandler;
import com.cb.ai.data.analysis.petition.utils.FileReaderFactory;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServerException;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/***
 * 信访源文件
 * <AUTHOR>
 */
@Service
public class PetitionSourceFileAnalysisServiceImpl implements PetitionSourceFileAnalysisService {

    @Autowired
    private  SuperviseResourceFileService fileService;

    @Autowired
    private PetitionSourceFileService petitionSourceFileService;

    @Resource(name="petitionFileReaderFactory")
    private FileReaderFactory readerFactory;


    private static final Integer INSERT_BATCH_SIZE = 1000;

    @Resource(name="databaseThreadPoolExecutor")
    private ThreadPoolExecutor databaseThreadPoolExecutor;

    @Autowired
    private SsPetitionOriginService ssPetitionOriginService;

    @Resource
    private RedisCache redisCache;

    @Override
    public Result aiAnalysis(String[] fileIds) throws Exception {
        StringBuilder errorMessage = new StringBuilder();
        for (String fileId : fileIds) {
            SuperviseResourceFile rawFile = fileService.getById(fileId);
            if (ObjectUtils.isEmpty(rawFile)) {
                throw new XServerException("未找到对应的文件");
            }
            String originalFilename=rawFile.getFilename();
            PetitionSourceFileEntity sourceFileEntity=petitionSourceFileService.getById(fileId);
            String contentType=rawFile.getContentType();
            try (InputStream inputStream = fileService.getFileStream(fileId)) {
                Optional<FileContentTypeEnum> fileTypeOptional = Arrays.stream(FileContentTypeEnum.values()).filter(fileContentTypeEnum -> fileContentTypeEnum.getContentType().equals(contentType)).findAny();
                if (fileTypeOptional.isPresent()) {
                    if(ObjectUtils.isEmpty(sourceFileEntity)){
                        sourceFileEntity=new PetitionSourceFileEntity();
                        sourceFileEntity.setFileId(fileId);
                        sourceFileEntity.setAnalysisStatus(FileAnalysisStatus.INPROCESS.getCode());
                        sourceFileEntity.setId(fileId);
                        petitionSourceFileService.save(sourceFileEntity);
                    }else{
                        sourceFileEntity.setAnalysisStatus(FileAnalysisStatus.INPROCESS.getCode());
                        petitionSourceFileService.updateById(sourceFileEntity);
                    }
                    if (fileTypeOptional.get().getContentType().equals(FileContentTypeEnum.XLS.getContentType()) || fileTypeOptional.get().getContentType().equals(FileContentTypeEnum.XLSX.getContentType())) {
                        ExcelReaderHandler excelReaderHandler = (ExcelReaderHandler) readerFactory.getReaderByContentType(contentType);
                        Map<String, List<JSONObject>> dataMap = excelReaderHandler.read(inputStream);

                        if (CollectionUtils.isEmpty(dataMap)) {
                            return Result.fail("处理失败：excel内容为空或未解析到数据");
                        }
                        Long batchNo = Long.parseLong(fileId);

                        Integer totalDataSize = 0;
                        redisCache.setCacheObject("batchNo_" + batchNo,fileId);
                        for (Map.Entry<String, List<JSONObject>> entry : dataMap.entrySet()) {

                            List<JSONObject> dataList = entry.getValue();
                            Integer dataSize = handleExcel(dataList, originalFilename, contentType, batchNo);

                            totalDataSize += dataSize;
                        }
                    } else {
                        Long batchNo = IdUtil.getSnowflakeNextId();
                        redisCache.setCacheObject("batchNo_" + batchNo,fileId);
                        handleText(originalFilename, contentType, inputStream, batchNo);
                    }
                } else {
                    String message = "文件【%s】解析excel失败,只支持 .xlsx / .xls / .doc / .docx / .pdf 格式文件 \n";
                    errorMessage.append(String.format(message, originalFilename));
                }
            } catch (Exception e) {
                String message = "文件【%s】上传失败,异常信息:%s \n";
                errorMessage.append(String.format(message, rawFile.getFilename(), e.getMessage()));
                e.printStackTrace();
                //后期考虑任务中处理
                sourceFileEntity.setAnalysisStatus(FileAnalysisStatus.FAILED.getCode());
                petitionSourceFileService.updateById(sourceFileEntity);
            }
        }
        if (errorMessage.length() > 0) {
            return Result.fail(errorMessage.toString());
        } else {
            return Result.success("文件解析到信访库提交成功");
        }
    }


    /**
     * 处理单个sheet中的数据
     * @param dataList 单个sheet数据
     * @return 处理数据量
     */
    private Integer handleExcel(List<JSONObject> dataList, String fileName, String contentType, Long batchNo) {
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }

        List<SsPetitionOriginEntity> ssPetitionOriginEntities = dataList.stream().map(item -> {
            SsPetitionOriginEntity ssPetitionOriginEntity = new SsPetitionOriginEntity();

            ssPetitionOriginEntity.setId(IdUtil.getSnowflakeNextId());
            ssPetitionOriginEntity.setContent(item.toJSONString());
            ssPetitionOriginEntity.setFileName(fileName);
            ssPetitionOriginEntity.setUploadTime(new Date());
            ssPetitionOriginEntity.setStatus(SsPetitionStatusEnum.ANALYZING.getStatus());
            ssPetitionOriginEntity.setUploadBatchNo(batchNo);
            ssPetitionOriginEntity.setFileType(FileContentTypeEnum.fromContentType(contentType).getCode());

            return ssPetitionOriginEntity;
        }).collect(Collectors.toList());

        for (int i = 0; i < ssPetitionOriginEntities.size(); i += INSERT_BATCH_SIZE) {
            List<SsPetitionOriginEntity> subList;
            if (i + INSERT_BATCH_SIZE > ssPetitionOriginEntities.size()) {
                subList = ssPetitionOriginEntities.subList(i, ssPetitionOriginEntities.size());
            } else {
                subList = ssPetitionOriginEntities.subList(i, i + INSERT_BATCH_SIZE);
            }
            databaseThreadPoolExecutor.submit(() -> {
                try {
                    ssPetitionOriginService.saveBatch(subList);
                    List<Long> ids = subList.stream().map(SsPetitionOriginEntity::getId).collect(Collectors.toList());
                    petitionSourceFileService.submitAnalyze(ids, false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

        }
        return dataList.size();
    }

    private void handleText(String fileName, String contentType, InputStream inputStream, Long batchNo) throws IOException {
        String content = (String) readerFactory.getReaderByContentType(contentType).read(inputStream);

        SsPetitionOriginEntity ssPetitionOriginEntity = new SsPetitionOriginEntity();

        Long id = IdUtil.getSnowflakeNextId();

        JSONObject contentObj = new JSONObject();
        contentObj.put("内容", content);

        ssPetitionOriginEntity.setId(id);
        ssPetitionOriginEntity.setContent(contentObj.toJSONString());
        ssPetitionOriginEntity.setFileName(fileName);
        ssPetitionOriginEntity.setUploadTime(new Date());
        ssPetitionOriginEntity.setStatus(SsPetitionStatusEnum.ANALYZING.getStatus());
        ssPetitionOriginEntity.setUploadBatchNo(batchNo);
        ssPetitionOriginEntity.setFileType(FileContentTypeEnum.fromContentType(contentType).getCode());
        ssPetitionOriginService.save(ssPetitionOriginEntity);
        petitionSourceFileService.submitAnalyze(Arrays.asList(id), false);
    }
}
