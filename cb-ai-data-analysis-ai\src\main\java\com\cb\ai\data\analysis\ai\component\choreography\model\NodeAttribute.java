package com.cb.ai.data.analysis.ai.component.choreography.model;

import cn.hutool.core.util.RandomUtil;
import com.cb.ai.data.analysis.ai.domain.func.TriConsumer;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 16:08
 * @Copyright (c) 2025
 * @Description 节点属性
 */
@Data
@Accessors(chain = true)
public class NodeAttribute<E, R> {
    /* 父节点ID */
    private String parentNodeId;

    /* 节点ID */
    private String nodeId;

    /* 节点名称 */
    private String nodeName;

    /* 节点描述 */
    private String nodeDesc;

    /* 外部入参请求上下文 */
    private E requestContext;

    /* 节点请求上下文函数 */
    private Function<FlowContext, E> requestContextFun;

    /* 节点上下文 */
    private NodeContext nodeContext;

    /* 节点输出隐藏思考过程 */
    private boolean hideThinking;

    /* 节点输出隐藏内容 */
    private boolean hideContent;

    /* 节点数据处理函数 */
    private Function<R, R> dataDisposeFun;

    private Consumer<R> dataDisposeCon;

    /* 节点数据收集完成后的数据处理函数 */
    private TriConsumer<String, R, FlowContext> processDataFun;

    /* 数据产生前的数据插入集合 */
    private List<InsertData<E, R>> beforeFunList;

    /* 数据产生后的数据插入集合 */
    private List<InsertData<E, R>> afterFunList;

    /* 自定义最大tokens */
    private Function<Integer, Integer> maxTokensFun;

    /* 自定义topK */
    private Function<Integer, Integer> topKFun;

    /* 自定义topP */
    private Function<Float, Float> topPFun;

    /* 自定义采样率 */
    private Function<Float, Float> temperatureFun;

    public NodeAttribute() {
        this.nodeId = RandomUtil.randomNumbers(32).toLowerCase();
        this.nodeName = this.getClass().getSimpleName();
        this.nodeDesc = this.getClass().getName();
        this.nodeContext = new NodeContext();
        this.beforeFunList = new ArrayList<>();
        this.afterFunList = new ArrayList<>();
    }

    public String getNodeId() {
        return OptionalUtil.ofBlankable(nodeId).orElse(RandomUtil.randomNumbers(32).toLowerCase());
    }

    public String getNodeName() {
        return OptionalUtil.ofBlankable(nodeName).orElse(this.getClass().getSimpleName());
    }

    public String getNodeDesc() {
        return OptionalUtil.ofBlankable(nodeDesc).orElse(this.getClass().getName());
    }

    public void addBeforeFun(InsertData<E, R> insertData) {
        if (insertData != null) {
            beforeFunList.add(insertData);
        }
    }

    public void addAfterFun(InsertData<E, R> insertData) {
        if (insertData != null) {
            afterFunList.add(insertData);
        }
    }

    public NodeAttribute<E, R> setHideThinking(boolean hideThinking) {
        this.hideThinking = hideThinking;
        nodeContext.setMergeThinking(!hideThinking);
        return this;
    }

    public NodeAttribute<E, R> setHideContent(boolean hideContent) {
        this.hideContent = hideContent;
        nodeContext.setMergeContent(!hideContent);
        return this;
    }

    public Function<Integer, Integer> getMaxTokensFun() {
        return Objects.requireNonNullElseGet(maxTokensFun, () -> token -> (int) (token * 0.8));
    }

    public Function<Integer, Integer> getTopKFun() {
        return Objects.requireNonNullElseGet(topKFun, () -> topK -> topK);
    }

    public Function<Float, Float> getTopPFun() {
        return Objects.requireNonNullElseGet(topPFun, () -> topP -> topP);
    }

    public Function<Float, Float> getTemperatureFun() {
        return Objects.requireNonNullElseGet(temperatureFun, () -> temperature -> temperature);
    }

    public void clear() {
        nodeContext.clear();
        beforeFunList.clear();
        afterFunList.clear();
    }
}
