package com.cb.ai.data.analysis.ai.domain.response;

import cn.hutool.core.date.DatePattern;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/6 22:26
 * @Copyright (c) 2025
 * @Description 私有化底座原始返回数据类
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class PrivateAIBackData {
    /**
     * 内容对应思考的Id
     */
    private String id;
    /**
     * 会话Id
     */
    private String sessionId;
    /**
     * 角色
     */
    private String role;
    /**
     * 事件
     */
    private String event;
    /**
     * 思考内容
     */
    private String reasoning_content;
    /**
     * 回答内容
     */
    private String content;
    /**
     * 返回的其他Data
     */
    private JsonMap data;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 原始数据
     */
    private String rawData;
    /**
     * 合并的data列表
     */
    private List<Object> dataList;

    public PrivateAIBackData(RoleEnum role, String reasoning_content, String content) {
        this(null, role, reasoning_content, content, null);
    }

    public PrivateAIBackData(RoleEnum role, String reasoning_content, String content, String rawData) {
        this(null, role, reasoning_content, content, rawData);
    }

    public PrivateAIBackData(String sessionId, RoleEnum role, String reasoning_content, String content, String rawData) {
        this(sessionId, role, reasoning_content, content, null, rawData);
    }

    public PrivateAIBackData(String sessionId, String role, String reasoning_content, String content, String rawData) {
        this(sessionId, role, reasoning_content, content, null, rawData);
    }

    public PrivateAIBackData(String sessionId, RoleEnum role, String reasoning_content, String content, JsonMap data, String rawData) {
        this(sessionId, role.name(), reasoning_content, content, data, rawData);
    }

    public PrivateAIBackData(String sessionId, String role, String reasoning_content, String content, JsonMap data, String rawData) {
        this.sessionId = sessionId;
        this.role = role;
        this.reasoning_content = reasoning_content;
        this.content = content;
        this.data = data;
        this.rawData = rawData;
        this.createTime = LocalDateTime.now().format(DatePattern.NORM_DATETIME_MS_FORMATTER);
    }


    public void dataList(Object data) {
        if (this.dataList == null) {
            this.dataList = new ArrayList<>();
        }
        if (data != null) {
            this.dataList.add(data);
        }
    }

    /**
     * 处理扩展属性
     */
    @JsonAnySetter
    public void handleDataProperty(String key, Object value) {
        if (data == null) {
            data = new JsonMap();
        }
        data.putOpt(key, value);
    }

    @Override
    public String toString() {
        String str = JsonUtil.toStr(this);
        return "{}".equals(str)? "" : str;
    }
}
