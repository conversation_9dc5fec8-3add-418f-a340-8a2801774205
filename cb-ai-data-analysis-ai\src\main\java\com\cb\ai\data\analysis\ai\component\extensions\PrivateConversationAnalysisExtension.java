package com.cb.ai.data.analysis.ai.component.extensions;

import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChain;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIFileAnalysis;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIKnowledge;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.MdPromoteExtractorUtil;
import reactor.core.publisher.Flux;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/10 13:52
 * @Copyright (c) 2025
 * @Description 私有化方案审核扩展
 */
@ExtensionProvider(desc = "私有化方案审核扩展", businessScenes = {
    @Route(tag = "方案审核", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivateConversationAnalysisExtension implements PrivateAIExtension<CommonAIRequestContext> {

    @Override
    @SuppressWarnings("unchecked")
    public Flux<?> invoke(CommonAIRequestContext aiMergeRequestContext) {
        return FlowChain.newChain(aiMergeRequestContext.getRequestId())
            .addSerialNode(PrivateAIFileAnalysis.class, node ->
                node.nodeName("谈话方案智能审查-信息提取")
                    .context(() -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-信息提取.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.getUserPromote(mdPath));
                        aiContext.setMinioFileDataList(aiMergeRequestContext.getMinioFileDataList());
                        return aiContext;
                    })
                    .dispose((nodeId, data, context) -> {
                        structuredTextAnalysis(context, data.getContent());
                        context.set("structured_text", data.getContent());
                        //context.set("text_input_object", aiMergeRequestContext.getExtendData().get("text_input_object"));
                    })
            )
            // 需有谈话方案知识库
            .addParallelNode(PrivateAIKnowledge.class, node ->
                node.nodeName("谈话方案智能审查-方案基础信息与完整性审查")
                    .context(context -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-方案基础信息与完整性审查.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, context.getData()));
                        aiContext.setBaseIds(aiMergeRequestContext.getBaseIds());
                        return aiContext;
                    })
                    .dispose((nodeId, data, context) -> context.set("方案基础信息与完整性审查/output", CommonUtil.replaceMarks(data.getContent())))
            )
            .addParallelNode(PrivateAIChat.class, node ->
                node.nodeName("谈话方案智能审查-风险评估与安全预案严密性审查")
                    .context(context -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-风险评估与安全预案严密性审查.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, context.getData()));
                        return aiContext;
                    })
                    .dispose((nodeId, data, context) -> context.set("风险评估与安全预案严密性审查/output", data.getContent()))
            )
            .addParallelNode(PrivateAIChat.class, node ->
                node.nodeName("谈话方案智能审查-人员与职责审查")
                    .context(context -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-人员与职责审查.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, context.getData()));
                        return aiContext;
                    })
                    .dispose((nodeId, data, context) -> context.set("人员与职责审查/output", data.getContent()))
            )
            .addParallelNode(PrivateAIChat.class, node ->
                node.nodeName("谈话方案智能审查-程序与逻辑性审查")
                    .context(context -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-程序与逻辑性审查.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, context.getData()));
                        return aiContext;
                    })
                    .dispose((nodeId, data, context) -> context.set("程序与逻辑性审查/output", data.getContent()))
            )
            // 需要谈话方案模板知识库
            .addSerialNode(PrivateAIKnowledge.class, node ->
                node.nodeName("谈话方案智能审查-报告汇总")
                    .context(context -> {
                        String mdPath = "promote/lrocp/谈话方案智能审查-报告汇总.md";
                        CommonAIRequestContext aiContext = new CommonAIRequestContext();
                        aiContext.setSystemPromote(MdPromoteExtractorUtil.getSysPromote(mdPath));
                        aiContext.setPromote(MdPromoteExtractorUtil.replaceUserPromoteTags(mdPath, context.getData()));
                        aiContext.setBaseIds(aiMergeRequestContext.getBaseIds());
                        aiContext.setSource(false);
                        return aiContext;
                    })
            )
            .execute();

    }

    private void structuredTextAnalysis(FlowContext flowContext, String structuredText) {
        // 1. 标题
        flowContext.set("var_title", extractContent("\\*\\*方案标题\\*\\*:\\s*(.*)", structuredText));

        // 2. 背景描述
        flowContext.set("var_background_source", extractContent("\\*\\*线索来源及情况\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_background_phase", extractContent("\\*\\*谈话阶段\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_background_basis", extractContent("\\*\\*谈话依据\\*\\*:\\s*(.*)", structuredText));

        // 3. 谈话对象基本情况
        flowContext.set("var_subject_info", extractContent("\\*\\*个人信息\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_subject_issue", extractContent("\\*\\*涉及主要问题\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_subject_seven_knows", extractContent("\\*\\*“七必知”信息评估\\*\\*:\\s*(.*?)(?=\\n\\n#### 2\\. 风险评估)", structuredText, Pattern.DOTALL));

        // 4. 风险评估
        flowContext.set("var_risk_assessment_general", extractContent("\\*\\*一般风险评估\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_risk_assessment_special", extractContent("\\*\\*特殊情况风险评估\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_risk_assessment_level", extractContent("\\*\\*安全风险等级\\*\\*:\\s*(.*)", structuredText));

        // 5. 谈话组成员
        flowContext.set("var_team_leader", extractContent("\\*\\*组长\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_team_recorder", extractContent("\\*\\*组员 \\(记录员\\)\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_team_safety_officer", extractContent("\\*\\*安全员\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_team_remarks", extractContent("\\*\\*备注\\*\\*:\\s*(.*)", structuredText));

        // 6. 谈话提纲
        flowContext.set("var_outline_purpose", extractContent("\\*\\*谈话目的\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_outline_issues", extractContent("\\*\\*涉及问题和事由\\*\\*:\\s*(.*?)(?=\\n- \\*\\*政策宣讲\\*\\*:\\s*)", structuredText, Pattern.DOTALL));
        flowContext.set("var_outline_policy", extractContent("\\*\\*政策宣讲\\*\\*:\\s*(.*)", structuredText));

        // 7. 时间、地点及须知
        flowContext.set("var_logistics_time", extractContent("\\*\\*谈话时间\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_logistics_location", extractContent("\\*\\*谈话地点\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_logistics_notice_before", extractContent("\\*\\*谈话前\\*\\*:\\s*(.*?)(?=\\n\\s{3,}- \\*\\*谈话中\\*\\*:\\s*)", structuredText, Pattern.DOTALL));
        flowContext.set("var_logistics_notice_during", extractContent("\\*\\*谈话中\\*\\*:\\s*(.*?)(?=\\n\\s{3,}- \\*\\*谈话后\\*\\*:\\s*)", structuredText, Pattern.DOTALL));
        flowContext.set("var_logistics_notice_after", extractContent("\\*\\*谈话后\\*\\*:\\s*(.*?)(?=\\n- \\*\\*谈话组成员签字\\*\\*:\\s*)", structuredText, Pattern.DOTALL));
        flowContext.set("var_logistics_team_signature", extractContent("\\*\\*谈话组成员签字\\*\\*:\\s*(.*)", structuredText));

        // 8. 安全预案
        flowContext.set("var_safety_plan_emergency", extractContent("\\*\\*安全应急保障\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_safety_plan_medical", extractContent("\\*\\*医疗应急保障\\*\\*:\\s*(.*)", structuredText));

        // 9. 其他注意事项
        flowContext.set("var_other_notes", extractContent("### 其他注意事项\\n(.*?)(?=\\n\\n### 科室主要负责人签字及日期)", structuredText, Pattern.DOTALL));

        // 10. 负责人与日期
        flowContext.set("var_approver_signature", extractContent("\\*\\*负责人签字\\*\\*:\\s*(.*)", structuredText));
        flowContext.set("var_approver_date", extractContent("\\*\\*日期\\*\\*:\\s*(.*)", structuredText));

    }

    private String extractContent(String pattern, String sourceText) {
        return extractContent(pattern, sourceText, 0);
    }

    private String extractContent(String pattern, String sourceText, int flags) {
        Pattern p = Pattern.compile(pattern, flags);
        Matcher m = p.matcher(sourceText);
        return m.find() ? m.group(1).trim() : "未提取到";
    }


}
