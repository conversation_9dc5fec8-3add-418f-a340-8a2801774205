package com.cb.ai.data.analysis.query.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 索引请求 BO
 */
public interface ElasticIndexReqBo {
    // 关系映射
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class FieldMapping {
        private String fieldName;
        private FieldType fieldType;
        private String fieldAnalyzer;
        private String fieldSearchAnalyzer;
    }
}
