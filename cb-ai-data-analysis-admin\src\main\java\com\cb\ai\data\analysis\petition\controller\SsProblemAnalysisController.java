package com.cb.ai.data.analysis.petition.controller;

import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.service.SsProblemAnalysisService;
import com.cb.ai.data.analysis.petition.vo.ProblemAnalysisVo;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/problem/market")
public class SsProblemAnalysisController {

    @Autowired
    private SsProblemAnalysisService problemAnalysisService;


    @PostMapping("/batchAnalysis")
    public Result batchAnalysis(@RequestBody ProblemAnalysisVo problemAnalysis) {
        try{
            problemAnalysisService.batchAnalysis(problemAnalysis.getId(),problemAnalysis.getBaseIds());
            return Result.success("批量解析成功");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("批量解析失败！");
        }
    }
}

