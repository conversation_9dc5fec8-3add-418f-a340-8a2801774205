package com.cb.ai.data.analysis.ai.component.flows.chuangbo;

import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.common.properties.PrivateAIBaseProperties;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.component.flows.BaseAiPropertiesNode;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.model.PrivateRequestContext;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.xong.boot.common.exception.CustomException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.ServerSentEvent;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 10:04
 * @Copyright (c) 2025
 * @Description 私有化底座AI属性配置类
 */
public abstract class BasePrivateAiPropertiesNode extends BaseAiPropertiesNode<CommonAIRequestContext, PrivateAIBaseProperties, PrivateAIBackData> {

    private PrivateRequestContext privateRequestContext;

    @Override
    public void executeBefore() {
        String defined = "你是一个名为'昆纪小智'的AI助手。";
        CommonAIRequestContext context = getRequestContext();
        if (StrUtil.isBlank(context.getSystemPromote())) {
            context.setSystemPromote(defined);
        } else {
            if (context.getSystemPromote().startsWith("你是")) {
                context.setSystemPromote(defined + "\n同时" + context.getSystemPromote());
            } else {
                context.setSystemPromote(defined + "\n" + context.getSystemPromote());
            }
        }
    }

    @Override
    public void setRequestHeader(HttpHeaders headers) {
        headers.setAll(aiProp.getHeaderMap());
    }

    @Override
    public Object setRequestBody() {
        PrivateRequestContext privateRequestContext = JsonUtil.toBean(getRequestContext(), PrivateRequestContext.class);
        AiConfig aiConfig = aiConfigService.getAiConfig();
        privateRequestContext.setFrequencyPenalty(aiConfig.getFrequencyPenalty());
        privateRequestContext.setPresencePenalty(aiConfig.getPresencePenalty());
        privateRequestContext.setTemperature(getNode().getTemperatureFun().apply(aiConfig.getTemperature()));
        privateRequestContext.setTopK(getNode().getTopKFun().apply(aiConfig.getTopK()));
        privateRequestContext.setTopP(getNode().getTopPFun().apply(aiConfig.getTopP()));
        privateRequestContext.setMaxTokens(getNode().getMaxTokensFun().apply(aiConfig.getMaxTokens()));
        this.privateRequestContext = privateRequestContext;
        return privateRequestContext;
    }

    @Override
    public PrivateAIBackData resultConvert(ServerSentEvent<String> ssEvent) {
        // 默认不做任何处理
        CommonAIRequestContext context = getRequestContext();
        String rawData = ssEvent.data();
        // 增加判断是否是错误对象
        //if (StrUtil.isNotBlank(rawData)) {
        //    if (ssEvent.data().contains("error")) {
        //        throw new CustomException(ssEvent.data());
        //    }
        //}
        try {
            PrivateAIBackData bean = JsonUtil.toBean(rawData, PrivateAIBackData.class);
            return bean.setSessionId(context.getSessionId()).setRawData(rawData);
        } catch (Exception e) {
            CommonLog.error(getNodeName() + "数据解析失败，返回的原数据：（" + rawData +  "）", e);
            return new PrivateAIBackData(context.getSessionId(), RoleEnum.system, null, null, rawData);
            //throw new CustomException(getNodeName() + "数据解析失败，返回的原数据：（" + rawData +  "）", e);
        }
    }

    @Override
    public CustomException errorConvert(Throwable throwable) {
        getFlowContext().stop();
        return new CustomException(getNodeName() + "节点异常，节点上下文：%s；异常原因：%s".formatted(JsonUtil.toStr(privateRequestContext), throwable.getMessage()), throwable);
    }

    @Override
    public void collectNodeData(PrivateAIBackData rawData, NodeContext nodeContext) {
        if (rawData != null) {
            nodeContext.collectContent(rawData.getContent());
            nodeContext.collectThinking(rawData.getReasoning_content());
            nodeContext.collectData(rawData.getData());
        }
    }

    @Override
    public PrivateAIBackData nodeContextToData(NodeContext nodeContext) {
        return new PrivateAIBackData(RoleEnum.assistant, nodeContext.getFullThinking(), nodeContext.getFullContent())
                .setSessionId(getRequestContext().getSessionId())
                .setDataList(nodeContext.getFullDataList());
    }

    @Override
    public boolean isThinking(PrivateAIBackData rawData) {
        return StrUtil.isNotBlank(rawData.getReasoning_content()) && StrUtil.isBlank(rawData.getContent());
    }

    @Override
    public boolean isContent(PrivateAIBackData rawData) {
        return StrUtil.isBlank(rawData.getReasoning_content()) && (StrUtil.isNotBlank(rawData.getContent()) || (rawData.getData() != null && !rawData.getData().isEmpty()));
    }

}
