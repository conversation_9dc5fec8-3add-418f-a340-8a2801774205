package com.cb.ai.data.analysis.graph.handler;

import cn.hutool.core.io.FileUtil;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description:
 * @Author: ARPHS
 * @Date: 2025-05-01 11:08
 * @Version: 1.0
 **/
public class FileProcessorFactory {
    private static final ConcurrentHashMap<String, FileProcessor> fileProcessorMap = new ConcurrentHashMap<>();

    static {
        fileProcessorMap.put("excel", new ExcelProcessor());
        fileProcessorMap.put("common", new CommonProcessor());
    }

    public static FileProcessor getFileProcessor(String filePath, Boolean precise, String promote) {
//        if (StringUtils.isEmpty(promote)) {
        if (precise) {
            String suffix = FileUtil.getSuffix(filePath);
            if ("xlsx".equals(suffix) || "xls".equals(suffix)) {
                return fileProcessorMap.get("excel");
            } else {
                throw new RuntimeException("暂不支持的文件类型");
            }
        } else {
            return fileProcessorMap.get("common");
        }
    }

}
