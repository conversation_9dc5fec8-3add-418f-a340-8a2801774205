package com.cb.ai.data.analysis.query.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import com.cb.ai.data.analysis.query.constant.Constant;

import java.util.Date;

/**
 * ES 权限过滤查询基类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = Constant.ES_SUPERVISE_FILE_INDEX)
@Setting(shards = 1, replicas = 0)
public class SuperviseFileBo extends EsPermBo {
    @Id
    private String id;
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String fileName;
    // 文件摘要
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String fileDigest;
    // 文件内容
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String fileContent;
    private String filePath;
    private String fileTags;

    @Field(type = FieldType.Keyword)
    private String createBy;
    @Field(type = FieldType.Long)
    private Date createTime;
    @Field(type = FieldType.Keyword)
    private String updateBy;
    @Field(type = FieldType.Long)
    private Date updateTime;
}
