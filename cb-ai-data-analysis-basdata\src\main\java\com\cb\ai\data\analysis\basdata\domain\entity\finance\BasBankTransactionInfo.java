package com.cb.ai.data.analysis.basdata.domain.entity.finance;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 银行交易信息表(BasBankTransactionInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:57:31
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class BasBankTransactionInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //部门id
    @ExcelIgnore
    private String deptId;

    //区域编码
    @ExcelIgnore
    private String areaCode;

    //客户名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "客户名称")
    private String customerName;

    //客户编号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "客户编号")
    private String customerId;

    //客户账号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "客户账号")
    private String customerAccount;

    //交易卡号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "交易卡号")
    private String transactionNumber;

    //交易币种
    @ExcelProperty(value = "交易币种")
    private String transactionCurrency;

    //交易日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "交易日期")
    private LocalDateTime transactionTime;

    //交易类型
    @ExcelProperty(value = "交易类型")
    private String transactionType;

    //摘要
    @ExcelProperty(value = "摘要")
    private String abstractInfo;

    //借方标志
    @ExcelProperty(value = "借方标志")
    private String borrowersSign;

    //交易金额
    @ExcelProperty(value = "交易金额")
    private BigDecimal transactionAmount;

    //交易余额
    @ExcelProperty(value = "交易余额")
    private BigDecimal transactionsBalances;

    //交易机构号
    @ExcelProperty(value = "交易机构号")
    private String transDeptId;

    //交易机构名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "交易机构名称")
    private String transDeptName;

    //对方账号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方账号")
    private String reciprocalAccount;

    //对方姓名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方姓名")
    private String reciprocalName;

    //对方户名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方户名")
    private String reciprocalAccountName;

    //对方证件号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方证件号")
    private String reciprocalIdNumber;

    //对方行号
    @ExcelIgnore
    private String reciprocalBankNumber;

    //对方行名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "对方行名")
    private String reciprocalBankName;

    //柜员号
    @ExcelProperty(value = "柜员号")
    private String tellerNumber;

    //交易流水号
    @TableField(value = "transaction_serial_No")
    @ExcelProperty(value = "交易流水号")
    private String transactionSerialNo;

    //交易渠道
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "交易渠道")
    private String transactionChannel;

    //交易备注
    @ExcelProperty(value = "交易备注")
    private String transactionRemark;

    //交易网点代码
    @ExcelProperty(value = "交易网点代码")
    private String tradingOutletCode;

    //交易网点名称
    @ExcelProperty(value = "交易网点名称")
    private String tradingOutletName;

    //现金标志
    @ExcelProperty(value = "现金标志")
    private String isCash;

}

