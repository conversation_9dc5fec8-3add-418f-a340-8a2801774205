package com.cb.ai.data.analysis.docassist.converter.decoration;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;
import java.util.List;

/**
 * 段落格式操作
 *
 * <AUTHOR>
 */
public class ParagraphDecoration extends BaseDecoration {
    private final XWPFParagraph paragraph;
    private final CTPPr ctpPr;
    private boolean hasImage = false;

    public ParagraphDecoration(XWPFParagraph paragraph) {
        this.paragraph = paragraph;
        this.ctpPr = paragraph.getCTPPr();
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            CTR ctr = run.getCTR();
            if (ctr.sizeOfDrawingArray() > 0) {
                this.hasImage = true;
                break;
            }
        }
    }

    public static ParagraphDecoration newInstance(XWPFParagraph paragraph) {
        return new ParagraphDecoration(paragraph);
    }

    public boolean isHasImage() {
        return hasImage;
    }

    public XWPFParagraph getParagraph() {
        return paragraph;
    }

    public CTPPr getCtpPr() {
        return ctpPr;
    }

    /**
     * 初始化段落格式
     */
    public ParagraphDecoration init() {
        if (ctpPr.isSetPStyle()) {
            ctpPr.unsetPStyle();
        }
        if (ctpPr.isSetCnfStyle()) {
            ctpPr.unsetCnfStyle();
        }
        if (ctpPr.isSetRPr()) {
            ctpPr.unsetRPr();
        }
        if (ctpPr.isSetKeepNext()) {
            ctpPr.setKeepNext(ctOff());
        }
        if (ctpPr.isSetKeepLines()) {
            ctpPr.setKeepLines(ctOff());
        }
        if (ctpPr.isSetPageBreakBefore()) {
            ctpPr.unsetPageBreakBefore();
        }
        // 设置孤行控制
        if (!ctpPr.isSetWidowControl()) {
            ctpPr.addNewWidowControl();
        }
//        ctpPr.setWidowControl(ctOff());   // 关闭孤行控制
        if (ctpPr.isSetPBdr()) {
            ctpPr.unsetPBdr();
        }
        if (ctpPr.isSetSuppressLineNumbers()) {
            ctpPr.unsetSuppressLineNumbers();
        }
        if (ctpPr.isSetNumPr()) {
            ctpPr.unsetNumPr();
        }
        if (ctpPr.isSetKinsoku()) {
            ctpPr.setKinsoku(ctNil());
        }
        // 自动折行
        if (!ctpPr.isSetWordWrap()) {
            ctpPr.addNewWordWrap();
        }
        ctpPr.setWordWrap(ctOff());
        if (ctpPr.isSetOverflowPunct()) {
            ctpPr.setOverflowPunct(ctNil());
        }
        if (ctpPr.isSetTopLinePunct()) {
            ctpPr.setTopLinePunct(ctOff());
        }
        if (ctpPr.isSetAutoSpaceDE()) {
            ctpPr.setAutoSpaceDE(ctNil());
        }
        if (ctpPr.isSetAutoSpaceDN()) {
            ctpPr.setAutoSpaceDN(ctNil());
        }
        ctpPr.setBidi(ctOff());
        if (ctpPr.isSetAdjustRightInd()) {
            ctpPr.setAdjustRightInd(ctNil());
        }
        if (ctpPr.isSetSnapToGrid()) {
            ctpPr.setSnapToGrid(ctNil());
        }
        if (ctpPr.isSetTextAlignment()) {
            ctpPr.setTextAlignment(getCTTextAlignAuto());
        }
        ctpPr.setInd(initCTInd());
        return this;
    }

    /**
     * 设置右对齐
     */
    public ParagraphDecoration setCTJcRight() {
        return setCTJc(STJc.RIGHT);
    }

    /**
     * 设置居中对齐
     */
    public ParagraphDecoration setCTJcCenter() {
        return setCTJc(STJc.CENTER);
    }

    /**
     * 设置右对齐
     *
     * @return
     */
    public ParagraphDecoration setCTJcLeft() {
        return setCTJc(STJc.LEFT);
    }

    /**
     * 设置两端对齐
     */
    public ParagraphDecoration setCTJcBoth() {
        return setCTJc(STJc.BOTH);
    }

    /**
     * 设置对齐方式
     */
    public ParagraphDecoration setCTJc(STJc.Enum val) {
        ctpPr.setJc(getCTJc(val));
        return this;
    }

    /**
     * 设置行间距
     *
     * @param lineRule 间距模式
     * @param line     间距值
     */
    public ParagraphDecoration setCTSpacing(STLineSpacingRule.Enum lineRule, Object line) {
        if (isHasImage()) {
            ctpPr.setSpacing(getCTSpacing(STLineSpacingRule.AT_LEAST, 0));
        } else {
            ctpPr.setSpacing(getCTSpacing(lineRule, line));
        }
        return this;
    }

    /**
     * 设置间距
     *
     * @param ctSpacing CTSpacing
     */
    public ParagraphDecoration setSpacing(CTSpacing ctSpacing) {
        if (isHasImage()) {
            ctpPr.setSpacing(getCTSpacing(STLineSpacingRule.AT_LEAST, 0));
        } else {
            ctpPr.setSpacing(ctSpacing);
        }
        return this;
    }

    /**
     * 设置首行缩进
     *
     * @param val 缩进值
     */
    public ParagraphDecoration setFirstLineChars(Long val) {
        CTInd ctInd = ctpPr.getInd();
        if (isHasImage()) {
            if (ctInd != null) {
                ctInd.setFirstLineChars(BigInteger.valueOf(0));
            }
        } else {
            if (ctInd == null) {
                ctInd = ctpPr.addNewInd();
            }
            ctInd.setFirstLineChars(BigInteger.valueOf(val));
        }
        return this;
    }

    /**
     * 设置左缩进
     *
     * @param val 缩进值
     */
    public ParagraphDecoration setLeftChars(Long val) {
        CTInd ctInd = ctpPr.getInd();
        if (isHasImage()) {
            if (ctInd != null) {
                ctInd.setLeftChars(BigInteger.valueOf(0));
            }
        } else {
            if (ctInd == null) {
                ctInd = ctpPr.addNewInd();
            }
            ctInd.setLeftChars(BigInteger.valueOf(val));
        }
        return this;
    }

    /**
     * 设置右缩进
     *
     * @param val 缩进值
     */
    public ParagraphDecoration setRightChars(Long val) {
        CTInd ctInd = ctpPr.getInd();
        if (isHasImage()) {
            if (ctInd != null) {
                ctInd.setRightChars(BigInteger.valueOf(0));
            }
        } else {
            if (ctInd == null) {
                ctInd = ctpPr.addNewInd();
            }
            ctInd.setRightChars(BigInteger.valueOf(val));
        }
        return this;
    }

    /**
     * 设置悬挂缩进
     *
     * @param val 缩进值
     */
    public ParagraphDecoration setHangingChars(Long val) {
        CTInd ctInd = ctpPr.getInd();
        if (ctInd == null) {
            ctInd = ctpPr.addNewInd();
        }
        ctInd.setHangingChars(BigInteger.valueOf(val));
        return this;
    }

    /**
     * 设置悬挂缩进
     *
     * @param val 缩进值
     */
    public ParagraphDecoration setHanging(Object val) {
        CTInd ctInd = ctpPr.getInd();
        if (ctInd == null) {
            ctInd = ctpPr.addNewInd();
        }
        ctInd.setHanging(val);
        return this;
    }

    /**
     * 设置段前分页
     *
     * @param val 是否开启
     */
    public ParagraphDecoration setPageBreakBefore(boolean val) {
        if (!ctpPr.isSetPageBreakBefore()) {
            ctpPr.addNewPageBreakBefore();
        }
        if (val) {
            ctpPr.setPageBreakBefore(ctOn());
        } else {
            ctpPr.setPageBreakBefore(ctOff());
        }
        return this;
    }

    /**
     * 设置孤控制
     *
     * @param val
     * @return
     */
    public ParagraphDecoration setWidowControl(boolean val) {
        if (val) {
            ctpPr.setWidowControl(ctNil());   // 开启孤行控制
        } else {
            ctpPr.setWidowControl(ctOff());   // 关闭孤行控制
        }
        return this;
    }
}
