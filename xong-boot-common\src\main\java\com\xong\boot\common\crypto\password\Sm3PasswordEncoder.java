package com.xong.boot.common.crypto.password;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.digest.SM3;
import com.xong.boot.common.utils.StringUtils;

import java.security.MessageDigest;

/**
 * SM3加密密码
 * <AUTHOR>
 */
public class Sm3PasswordEncoder implements PasswordEncoder {
    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return encode(password);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return matches(password, encodedPassword);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     */
    @Override
    public String encode(CharSequence rawPassword) {
        if (StringUtils.isBlank(rawPassword)) {
            throw new IllegalArgumentException("rawPassword cannot be null");
        }
        return SmUtil.sm3(rawPassword.toString());
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        if (StringUtils.isBlank(rawPassword)) {
            return false;
        }
        if (StringUtils.isBlank(encodedPassword)) {
            return false;
        }
        SM3 sm3 = SM3.create();
        String encPwd = sm3.digestHex(encodedPassword);
        return MessageDigest.isEqual(SmUtil.sm3(rawPassword.toString()).getBytes(), encPwd.getBytes());
    }
}
