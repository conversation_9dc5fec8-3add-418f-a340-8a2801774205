package com.cb.ai.data.analysis.petition.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionOriginEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PageResult;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.SsPetitionDynamicResponseVo;
import com.xong.boot.common.service.BaseService;

public interface SsPetitionOriginService extends BaseService<SsPetitionOriginEntity> {

    PageResult selectByPage(PetitionQueryPageQueryVo queryPageVo);
}
