package com.cb.ai.data.analysis.ai.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.cb.ai.data.analysis.ai.enums.ChatRole;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * AI历史会话消息
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AiChatHistorySessionMessage extends SimpleBaseDomain {
    /**
     * ID
     */
    @TableId
    private String id;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 会话角色
     */
    private ChatRole role;
    /**
     * 结果内容&提示词
     */
    private String content;
    /**
     * 思考内容
     */
    @TableField(whereStrategy = FieldStrategy.NEVER)
    private String reasoningContent;
    /**
     * 错误内容
     */
    private String errorContent;
    /**
     * 请求上下文内容
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private Map<String, Object> contextContent;
    /**
     * 发送时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime sendTime;
}
