package com.cb.ai.data.analysis.petition.domain.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ss_petition_analyzed
 * <AUTHOR>
@Data
@TableName("ss_petition_analyzed")
public class SsPetitionAnalyzedEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @ExcelIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 元数据id
     */
    @ExcelIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long originId;

    /**
     * 提交信访者姓名
     */
    @ExcelProperty("提交信访者姓名")
    private String petitionPerson;

    /**
     * 所属省份
     */
    @ExcelProperty("所属省份")
    private String petitionProvince;

    /**
     * 所属城市
     */
    @ExcelProperty("所属城市")
    private String petitionCity;

    /**
     * 所属区县
     */
    @ExcelProperty("所属区县")
    private String petitionDistrict;

    /**
     * 所属街道
     */
    @ExcelProperty("所属街道")
    private String petitionStreet;

    /**
     * 举报人居住省份
     */
    @ExcelProperty("举报人居住省份")
    private String userResidenceProvince;

    /**
     * 举报人居住市
     */
    @ExcelProperty("举报人居住市")
    private String userResidenceCity;

    /**
     * 举报人居住区县
     */
    @ExcelProperty("举报人居住区县")
    private String userResidenceDistrict;

    /**
     * 举报人居住街道
     */
    @ExcelProperty("举报人居住街道")
    private String userResidenceStreet;

    /**
     * 所属领域
     */
    @ExcelProperty("所属领域")
    private String petitionDomain;
    /**
     * 所属领域（一级分类）
     */
    @ExcelIgnore
    private String petitionDomainCategory;


    /**
     * 信访目的
     */
    @ExcelProperty("信访目的")
    private String petitionPurpose;
    /**
     * 信访目的（一级分类）
     */
    @ExcelIgnore
    private String petitionPurposeCategory;

    /**
     * 处理建议
     */
    @ExcelProperty("处理建议")
    private String petitionHandleSuggestion;

    /**
     * 摘要
     */
    @ExcelProperty("摘要")
    private String brief;
    /**
     * 如果信访件中包含对某某公务员/领导，在贪污腐败或渎职等问题上的检举/控告/投诉 则填写被检举/举报公务员的姓名 没有则空 多个则以,分隔返回
     */
    @ExcelProperty("被举报人姓名")
    private String petitionAccusedPerson;

    /**
     * 人物关系
     */
    @ExcelIgnore
    private String petitionRelation;

    /**
     * 信访接收机构或信访去向机构
     */
    @ExcelProperty("信访去向机构")
    private String petitionBelongOrg;

    @ExcelProperty("登记日期")
    private String registerDate;

    @ExcelProperty("信访日期")
    private String petitionDate;
    /**
     * 创建时间
     */
    @ExcelIgnore
    @TableField(updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

}
