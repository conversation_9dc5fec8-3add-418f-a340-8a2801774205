package com.cb.ai.data.analysis.docassist.converter.pipe;

import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.decoration.ParagraphDecoration;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;

/**
 * 红头标题
 *
 * <AUTHOR>
 */
public class RedTitlePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        for (XWPFRun run : paragraph.getRuns()) {
            String color = run.getColor();
            if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                return true;
            }
        }
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        if (!paragraph.getText().trim().equals("★")) {
            ParagraphDecoration paragraphDecoration = ParagraphDecoration.newInstance(paragraph);
            paragraphDecoration.setCTSpacing(STLineSpacingRule.AT_LEAST, 0);
        }
    }
}
