package com.cb.ai.data.analysis.ai.component.flows.opensource.codes;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.component.choreography.engine.BaseAttributeNode;
import com.cb.ai.data.analysis.ai.component.choreography.engine.IProcessNode;
import com.cb.ai.data.analysis.ai.component.choreography.flow.IFlowProcessNode;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.xong.boot.common.exception.CustomException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jsoup.Jsoup;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/30 15:44
 * @Copyright (c) 2025
 * @Description 文档文件解析
 */
public class WordFileAnalysisNode extends BaseAttributeNode<MultipartFile, String, WordFileAnalysisNode> implements IFlowProcessNode<MultipartFile, String>, IProcessNode<MultipartFile, String> {

    @Override
    public String process(MultipartFile requestContext) {
        Assert.notNull(requestContext, "文档文件不能为空");
        String fileName = requestContext.getOriginalFilename();
        String fileType = "";
        if (StrUtil.isNotBlank(fileName)) {
            fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }
        // 验证文件格式的正则表达式
        if (fileType.matches("^(txt|markdown|mdx|html?|xlsx?|docx|csv|vtt|properties|md|htm)$")) {
            // 处理支持的文档类型
            try {
                return extractFileContent(requestContext, fileType);
            } catch (Exception e) {
                throw new IllegalArgumentException(getNodeName() + " -> 文档文件解析失败: " + e.getMessage());
            }
        } else {
            throw new IllegalArgumentException(getNodeName() + " -> 不支持的文档类型: " + fileType + "。（请上传txt、markdown、mdx、html、xlsx、docx、csv、vtt、properties、md、htm文件）");
        }
    }

    @Override
    public Flux<ResultData<String>> processData(MultipartFile context, FlowAttribute flowAttribute) {
        try {
            if (flowAttribute != null) {
                setFlow(flowAttribute);
            }
            getNode().setHideThinking(true).setHideContent(true);
            String content = process(context);
            Consumer<String> dataDisposeCon = getNode().getDataDisposeCon();
            if (dataDisposeCon != null) {
                dataDisposeCon.accept(content);
            }
            return Flux.just(convertResultData(ResultDataStatusEnum.WAIT, content));
        } catch (Exception e) {
            throw new CustomException(getNodeName() + "节点执行异常, 原因：" + e.getMessage(), e);
        }
    }

    @Override
    public String getNodeName() {
        return "文档文件解析节点";
    }

    /**
     * 提取文件内容
     * @param file 上传的文件
     * @param fileType 文件类型
     * @return 文件内容字符串
     */
    private String extractFileContent(MultipartFile file, String fileType) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            switch (fileType) {
                case "txt", "properties", "md", "markdown", "mdx", "csv", "vtt" ->
                    new BufferedReader(new InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"));
                case "html", "htm" ->
                    // 使用Jsoup提取纯文本
                    Jsoup.parse(inputStream, "UTF-8", "").text();
                case "docx" -> {
                    try (XWPFDocument doc = new XWPFDocument(inputStream)) {
                        XWPFWordExtractor extractor = new XWPFWordExtractor(doc);
                        return extractor.getText();
                    }
                }
                case "xlsx" -> {
                    StringBuilder excelContent = new StringBuilder();
                    try (Workbook workbook = new XSSFWorkbook(inputStream)) {
                        for (Sheet sheet : workbook) {
                            for (Row row : sheet) {
                                for (Cell cell : row) {
                                    switch (cell.getCellType()) {
                                        case STRING:
                                            excelContent.append(cell.getStringCellValue()).append("\t");
                                            break;
                                        case NUMERIC:
                                            excelContent.append(cell.getNumericCellValue()).append("\t");
                                            break;
                                        case BOOLEAN:
                                            excelContent.append(cell.getBooleanCellValue()).append("\t");
                                            break;
                                        default:
                                            excelContent.append("\t");
                                    }
                                }
                                excelContent.append("\n");
                            }
                        }
                    }
                    return excelContent.toString();
                }
                default -> throw new IllegalArgumentException("不支持的文件类型: " + fileType);
            }
        }
        return null;
    }

    private ResultData<String> convertResultData(ResultDataStatusEnum statusEnum, String data) {
        return new ResultData<>(data)
            .setNodeId(getNodeId())
            .setNodeName(getNodeName())
            .setNodeDesc(getNodeDesc())
            .setStatus(statusEnum);
    }

}
