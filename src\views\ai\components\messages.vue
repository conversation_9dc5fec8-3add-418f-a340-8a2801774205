<script lang="ts" setup>
import msg from './msg.vue'
import { computed, onUnmounted, ref, useTemplateRef, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { dayjs } from '@/core'
import { messages } from '@/api/ai/index'
import { useImStore, useUserStore } from '@/stores'
import { message } from 'ant-design-vue'
import { uuid } from '@/utils'
import errorLog from '@/utils/errorLog'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const imStore = useImStore()
const loading = ref(false)
const currentSession = computed(() => {
  return imStore.conversation.find((v) => v.sessionId === route.query.id)
})
const msgList = computed(() => {
  const data = imStore.conversation.find((v) => v.sessionId === route.query.id)
  if (data) {
    return data.list || []
  } else {
    return []
  }
})
const scrollViewRef = useTemplateRef('scrollViewRef')

function formatDateTime(str) {
  return dayjs(str).format('YYYY-MM-DD HH:mm:ss')
}

async function addMsgBySession(res, sessionId) {
  const cur = imStore.conversation.find((v) => v.sessionId === sessionId)
  if (cur) {
    cur.more = res.data.hasMore
    // 只要user和ai的消息
    const msgList = res.data?.result || []
    if (msgList.length > 0) {
      const list = []
      for (const key in msgList) {
        const v = msgList[key]
        try {
          if (v.role === 'user') {
            const msgItem = JSON.parse(v.content)
            list.push({
              id: v.id,
              avatar: userStore.userDetails?.avatar,
              name: userStore.userDetails?.realname,
              createTime: v.createTime,
              role: 1,
              loading: false,
              content: {
                content: msgItem.promote,
                files: JSON.parse(v.dataList || '[]'),
                quote: (msgItem.analyseTag || [])
                  .concat(msgItem.baseNames || [])
                  .concat(msgItem.mcpModuleNames || []),
                error: msgItem.error || []
              }
            })
          } else if (v.role === 'assistant') {
            const msgItem = JSON.parse(v.content)
            list.push({
              id: v.id,
              avatar: '/assistant/<EMAIL>',
              name: '昆纪小智',
              createTime: v.createTime,
              role: 2,
              loading: false,
              loadingText: '',
              content: {
                unexpand: true,
                files: JSON.parse(v.dataList || '[]'),
                thinking: true,
                thinkStatus: false,
                deepThink: msgItem.deepThink,
                thinkStatusText: '已结束',
                thinkText: v.reasoningContent,
                content: msgItem.content,
                multipartThink: msgItem.multipartThink?.map((v) => ({
                  ...v,
                  thinking: true,
                  thinkStatus: false
                })),
                deepThinkData: msgItem.deepThinkData,
                dbTableInfo: msgItem.dbTableInfo,
                error: msgItem.error || []
              }
            })
          } else {
            //   其他角色以后再说
          }
        } catch (err) {
          message.error(err.message)
        }
      }
      cur.list?.unshift(...list)
    } else {
      cur.list?.unshift({
        id: `${Date.now()}${uuid()}`,
        role: 0,
        content: {
          content: '暂无更多消息'
        }
      })
    }
  } else {
    // 不存在会话或者会话接口还没返回,如果没返回就等一下,然后再设置一次
    if (imStore.sessionPrms) {
      await imStore.getSession()
      await addMsgBySession(res, sessionId)
    } else {
      router.replace({ name: 'AiWelcome' })
    }
  }
}

async function loadMoreMsg({ sessionId, createTime, pageSize }: any) {
  if (loading.value) {
    return
  }
  try {
    loading.value = true
    const res = await messages.getMsgs({
      sessionId,
      createTime,
      pageSize
    })
    if (res.code === 200) {
      await addMsgBySession(res, sessionId)
    } else {
      throw new Error(res.message)
    }
    return res
  } catch (error: any) {
    errorLog.push({
      error: error.message,
      stack: error.stack,
      title: '获取消息历史失败',
      data: { sessionId, createTime, pageSize }
    })
    message.error('获取消息历史失败')
  } finally {
    loading.value = false
  }
}

async function scroll(e: Event) {
  const threshold = 10 // 阈值
  const div = scrollViewRef.value as HTMLDivElement
  // 上拉加载
  if (div.scrollTop <= threshold && currentSession.value.more) {
    div.style.overflow = 'hidden'
    e.preventDefault()
    e.stopPropagation()
    let createTime
    if (currentSession.value.list.length > 0) {
      createTime = currentSession.value.list[0].createTime
    } else {
      createTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
    await loadMoreMsg({
      sessionId: currentSession.value.sessionId,
      createTime: dayjs(createTime).valueOf(),
      pageSize: 20
    })
    div.style.overflow = ''
  }
}

const unWatch = watch(
  () => route.query.id,
  async (v) => {
    if (v) {
      if (!currentSession.value || currentSession.value.more) {
        await loadMoreMsg({
          sessionId: v,
          createTime: `${Date.now()}`,
          pageSize: 20
        })
      }
    } else {
      // 没有会话id
      router.replace({ name: 'AiWelcome' })
    }
  },
  {
    immediate: true
  }
)
onUnmounted(() => {
  unWatch()
})
imStore.getSession()
</script>

<template>
  <div class="aiMessages">
    <div id="sourceReferences" class="scrollBeauty"></div>
    <!--    <div class="msgTop">{{ currentSession }}</div>-->
    <div ref="scrollViewRef" v-autoScroll class="scrollView scrollBeauty" @scroll="scroll">
      <div v-for="item in msgList" :key="item.id" class="message">
        <div v-if="item.role === 0" class="sys">{{ item.content.content }}</div>
        <div v-else :class="['other', item.role === 1 ? 'user' : 'ai']">
          <a-avatar :src="item.avatar" class="avatar" size="large">{{ item.name[0] }}</a-avatar>
          <div class="msgBody">
            <div :class="['msgHeader', item.role === 1 ? 'user' : 'ai']">
              <span class="userName">{{ item.name }}</span>
              <span class="dateTime">{{ formatDateTime(item.createTime) }}</span>
              <template v-if="item.loading">
                <a-spin />
                <span>{{ item.loadingText }}</span>
              </template>
            </div>
            <div class="msgContainer">
              <msg v-model:content="item.content" :role="item.role" />
            </div>
            <!--            <div :class="['msgFooter', item.role === 1 ? 'user' : 'ai']"></div>-->
          </div>
          <a-radio v-if="false" v-model:checked="item.check" class="check"></a-radio>
        </div>
      </div>
    </div>
    <!--  <div class="msgBottom">预留消息工具</div>-->
  </div>
</template>

<style lang="less">
.aiMessages {
  width: 100%;
  height: 0;
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;

  #sourceReferences {
    display: none;
    position: absolute;
    background-color: var(--layout-light-bgcolor);
    z-index: 50;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(16, 171, 175, 0.15);
    padding: 10px;
    white-space: break-spaces;
    word-break: break-all;
    color: var(--layout-light-color);
    max-width: 25vw;
    max-height: 20vh;
  }

  .msgTop {
    flex-shrink: 0;
    flex-grow: 0;
    width: 100%;
  }

  .scrollView {
    width: 100%;

    height: 0;
    flex-grow: 1;
    flex-shrink: 1;
    display: flex;
    flex-direction: column;

    .message {
      width: 99%;
      margin: 10px auto;

      .sys {
        width: 100%;
        text-align: center;
        color: var(--layout-light-color);
        font-size: 0.8rem;
      }

      .other {
        width: 100%;

        .avatar {
          flex-shrink: 0;
        }

        .check {
          flex-shrink: 0;
        }

        .msgBody {
          width: 0;
          flex-grow: 1;
          flex-shrink: 1;
          padding: 0 5px;
          box-sizing: border-box;

          .msgHeader {
            width: 100%;
            margin-bottom: 5px;
            gap: 5px;
            align-items: center;

            .userName {
              color: var(--layout-light-color);
            }

            .dateTime {
              font-size: 0.9rem;
              color: #7f7f7f;
            }
          }

          .msgContainer {
            width: 100%;
            display: flex;
            flex-direction: column;
          }

          .msgFooter {
            width: 100%;
          }
        }
      }

      .user {
        display: flex;
        flex-direction: row-reverse;
        flex-wrap: wrap;

        .msgContainer {
          align-items: flex-end;
        }

        .msgContent {
          margin: 0 0 0 auto;
          border-radius: 10px 0 10px 10px;
        }

        .quotes {
          flex-direction: row-reverse;
        }
      }

      .ai {
        display: flex;
        flex-wrap: wrap;

        .msgContainer {
          align-items: flex-start;
        }

        .msgContent {
          border-radius: 0 10px 10px 10px;
        }
      }
    }
  }

  .msgBottom {
    flex-shrink: 0;
    flex-grow: 0;
    width: 100%;
  }
}
</style>
