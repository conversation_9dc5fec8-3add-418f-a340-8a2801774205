package com.cb.ai.data.analysis.petition.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatTask;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

public interface SsRepeatTaskMapper extends BaseMapper<SsRepeatTask> {

    Page<SsRepeatTask> pageByWrapper(Page<SsRepeatTask> page, @Param(Constants.WRAPPER) Wrapper<SsRepeatTask> wrapper);

}
