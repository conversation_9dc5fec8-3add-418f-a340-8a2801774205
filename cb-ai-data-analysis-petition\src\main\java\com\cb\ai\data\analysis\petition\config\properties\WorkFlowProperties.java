//package com.cb.ai.data.analysis.petition.config.properties;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
//@Data
//@Component
//@ConfigurationProperties(prefix = "cbxxjs.workflow")
//public class WorkFlowProperties {
//
//    private Integer maxContentLength;
//
//    private Integer analyzeContentLength;
//
//    private String baseUrl;
//
//    private String generateReportId;
//
//    private String analyzeData;
//
//    private String conversationBrief;
//
//    private String knowledgeFlowId;
//
//    private String callBackUrl;
//
//    private String extractRelation;
//
//    private String commonReportGenId;// 通用报告生成工作流id
//
//    private String materialAnalysisId;
//
//    private String titleGenerateId;
//
//    private String contentGenerateId;
//
//    private String contentRegenerateId;
//
//    private String talkPlanReviewId;
//
//}
