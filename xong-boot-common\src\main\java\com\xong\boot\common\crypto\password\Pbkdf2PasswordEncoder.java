package com.xong.boot.common.crypto.password;

/**
 * PBKDF2密码加密
 * <AUTHOR>
 */
public class Pbkdf2PasswordEncoder extends org.springframework.security.crypto.password.Pbkdf2PasswordEncoder implements PasswordEncoder {
    public Pbkdf2PasswordEncoder() {
        super("", 16, 310000, SecretKeyFactoryAlgorithm.PBKDF2WithHmacSHA256);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return encode(password);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return matches(password, encodedPassword);
    }
}
