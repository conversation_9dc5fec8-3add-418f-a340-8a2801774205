package com.cb.ai.data.analysis.knowledge.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBase;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识库 Mapper
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBaseEntity> {


    /***
     * 分页查询知识库信息
     * @param page
     * @param wrapper
     * @return
     */
    Page<KnowledgeBaseEntity> pageKnowledgeBase(Page<KnowledgeBaseEntity> page,
                                                @Param(Constants.WRAPPER) Wrapper<KnowledgeBaseEntity> wrapper,
                                                @Param("userId") String userId,
                                                @Param("userDeptId") String userDeptId);

    /***
     * 获取知识库列表，不分页
     * @param userId
     * @param userDeptId
     * @return
     */
    List<KnowledgeBaseEntity> getKnowledgeBaseList(
            @Param("searchKey") String searchKey,
            @Param("parentId") String parentId,
            @Param("userId") String userId,
            @Param("userDeptId") String userDeptId);

}
