package com.cb.ai.data.analysis.dbtable.model;

import com.cb.ai.data.analysis.dbtable.converts.Convert;
import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import com.cb.ai.data.analysis.dbtable.enums.ConvertErrorType;
import com.cb.ai.data.analysis.dbtable.enums.ConvertMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字段数据
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ColumnData extends ExcelConvert {
    /**
     * 数据库字段值
     */
    private Object columnValue;
    /**
     * 转换模式
     */
    private ConvertMode convertMode;
    /**
     * 字段转换错误类型
     */
    private ConvertErrorType convertErrorType;
    /**
     * 错误信息
     */
    private String errorMessage;

    public static ColumnData newInstance(String columnName, String columnComment, CHColumnType columnType, Object columnValue) {
        ColumnData columnInfo = new ColumnData();
        columnInfo.setColumnName(columnName);
        columnInfo.setColumnComment(columnComment);
        columnInfo.setColumnType(columnType);
        columnInfo.setColumnValue(columnValue);
        return columnInfo;
    }

    public static ColumnData newInstance(ExcelConvert excelConvert) {
        ColumnData columnInfo = new ColumnData();
        columnInfo.setColumnName(excelConvert.getColumnName());
        columnInfo.setColumnComment(excelConvert.getColumnComment());
        columnInfo.setColumnType(excelConvert.getColumnType());
        columnInfo.setConvertMode(ConvertMode.COLUMN_NO);
        columnInfo.setExcelColumnNo(excelConvert.getExcelColumnNo());
        return columnInfo;
    }

    public static ColumnData newInstance(ExcelConvert excelConvert, Convert<?> convert) {
        ColumnData columnInfo = new ColumnData();
        columnInfo.setColumnName(excelConvert.getColumnName());
        columnInfo.setColumnComment(excelConvert.getColumnComment());
        columnInfo.setColumnType(excelConvert.getColumnType());
        columnInfo.setConvertKey(convert.getKey());
        columnInfo.setConvertMode(convert.getMode());
        if (convert.getMode() == ConvertMode.USER_FILL) {
            columnInfo.setInValue(excelConvert.getInValue());
        } else if (convert.getMode() == ConvertMode.VARIABLE) {
            columnInfo.setVariables(excelConvert.getVariables());
        } else {
            columnInfo.setExcelColumnNo(excelConvert.getExcelColumnNo());
        }
        return columnInfo;
    }
}
