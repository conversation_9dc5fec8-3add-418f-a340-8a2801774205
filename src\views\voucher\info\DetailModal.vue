<template>
  <a-modal
    :open="visible"
    :title="`凭证详情 - ${voucherData?.name || ''}`"
    width="80%"
    :footer="null"
    @cancel="onCancel"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="voucherData" class="detail-content">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="info-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="凭证名称">
            {{ voucherData.name }}
          </a-descriptions-item>
          <a-descriptions-item label="文件ID">
            {{ voucherData.fileId }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(voucherData.status)">
              {{ getStatusText(voucherData.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="标签">
            <a-tag v-for="tag in voucherData.tags" :key="tag" color="blue">
              {{ tag }}
            </a-tag>
            <span v-if="!voucherData.tags || voucherData.tags.length === 0" class="text-muted">
              暂无标签
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="OCR开始时间">
            {{ voucherData.ocrStartTime ? $date.formatDateTime(voucherData.ocrStartTime) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="OCR结束时间">
            {{ voucherData.ocrEndTime ? $date.formatDateTime(voucherData.ocrEndTime) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ voucherData.createBy }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ $date.formatDateTime(voucherData.createTime) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      
      <!-- OCR解析内容 -->
      <a-card title="OCR解析内容" class="content-card">
        <div v-if="voucherData.ocrText" class="ocr-content">
          <a-textarea
            :value="voucherData.ocrText"
            :rows="10"
            readonly
            placeholder="暂无OCR解析内容"
          />
        </div>
        <div v-else class="no-content">
          <a-empty description="暂无OCR解析内容" />
        </div>
      </a-card>
      
      <!-- 错误信息 -->
      <a-card v-if="voucherData.ocrErr" title="错误信息" class="error-card">
        <div class="error-content">
          <a-alert
            :message="voucherData.ocrErr"
            type="error"
            show-icon
          />
        </div>
      </a-card>
    </div>
    
    <div v-else class="no-data">
      <a-empty description="获取凭证详情失败" />
    </div>
  </a-modal>
</template>

<script setup lang="ts" name="InfoDetailModal">
import { getCurrentInstance, ref, watch, type ComponentCustomProperties } from 'vue'
import { info } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean
  voucherId?: string
}>()

const loading = ref(false)
const voucherData = ref(null)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.voucherId) {
      loadVoucherData()
    }
  }
)

function onCancel() {
  voucherData.value = null
  emits('update:visible', false)
}

/**
 * 加载凭证数据
 */
async function loadVoucherData() {
  if (!props.voucherId) return
  
  try {
    loading.value = true
    // 这里需要添加获取详情的API，暂时使用分页接口模拟
    const { data } = await info.page({ id: props.voucherId })
    if (data && data.records && data.records.length > 0) {
      voucherData.value = data.records[0]
    }
  } catch (error) {
    _this.$message.error('获取凭证详情失败')
    voucherData.value = null
  } finally {
    loading.value = false
  }
}

/**
 * 获取状态颜色
 */
function getStatusColor(status: number) {
  const colorMap: Record<number, string> = {
    0: 'default',
    1: 'processing',
    2: 'success',
    3: 'error'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
function getStatusText(status: number) {
  const textMap: Record<number, string> = {
    0: '未解析',
    1: '解析中',
    2: '解析成功',
    3: '解析失败'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped lang="less">
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  .info-card,
  .content-card,
  .error-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .ocr-content {
    :deep(.ant-input) {
      font-family: 'Courier New', monospace;
      line-height: 1.6;
    }
  }
  
  .no-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .error-content {
    :deep(.ant-alert-message) {
      word-break: break-word;
      white-space: pre-wrap;
    }
  }
  
  .text-muted {
    color: #999;
    font-style: italic;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
