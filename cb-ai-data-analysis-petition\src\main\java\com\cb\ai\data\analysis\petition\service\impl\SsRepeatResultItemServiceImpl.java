package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultItem;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.mapper.SsRepeatResultItemMapper;
import com.cb.ai.data.analysis.petition.service.SsPetitionAnalyzedService;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultItemService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SsRepeatResultItemServiceImpl extends BaseServiceImpl<SsRepeatResultItemMapper, SsRepeatResultItem> implements SsRepeatResultItemService {

    @Autowired
    private SsPetitionAnalyzedService petitionAnalyzedService;

    @Override
    public Map<String, List<SsPetitionAnalyzedEntity>> getGroupIdAnalyzedMap(Collection<String> groupIds) {
        if(ObjectUtil.isEmpty(groupIds)){
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<SsRepeatResultItem> wrapper = Wrappers.<SsRepeatResultItem>lambdaQuery()
                .in(SsRepeatResultItem::getGroupId, groupIds);
        List<SsRepeatResultItem> items = baseMapper.selectList(wrapper);
        if(ObjectUtil.isEmpty(items)){
            return Collections.emptyMap();
        }
        Set<Long> analyzedIds = items.stream().map(item -> item.getAnalyzedId()).collect(Collectors.toSet());
        Map<Long,SsPetitionAnalyzedEntity> entityMap = petitionAnalyzedService.listByIds(analyzedIds).stream()
                .collect(Collectors.toMap(SsPetitionAnalyzedEntity::getId, item -> item, (k1, k2) -> k1));
        Map<String, List<SsPetitionAnalyzedEntity>> rst = new HashMap<>();
        for (SsRepeatResultItem item : items) {
            String groupId = item.getGroupId();
            Long analyzedId = item.getAnalyzedId();
            SsPetitionAnalyzedEntity entity = entityMap.get(analyzedId);
            if(null != entity){
                if(rst.containsKey(groupId)){
                    rst.get(groupId).add(entity);
                } else {
                    List<SsPetitionAnalyzedEntity> list = new LinkedList<>();
                    list.add(entity);
                    rst.put(groupId, list);
                }
            }
        }
        return rst;
    }
}
