package com.cb.ai.data.analysis.query.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/**
 * 动态字段映射
 */
public class DynamicFieldMapping implements Serializable {
    private static final long serialVersionUID = 1L;
    // 子段名
    private String fieldName;
    // 字段类型 Auto:自动判断
    private FieldType fieldType = FieldType.Auto;
    // 字段分词器
    private String analyzer;
    // 字段搜索分词器
    private String searchAnalyzer;

    public DynamicFieldMapping() {
    }

    public DynamicFieldMapping(String fieldName, FieldType fieldType, String analyzer) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.analyzer = analyzer;
    }

    public DynamicFieldMapping(String fieldName, String analyzer, String searchAnalyzer) {
        this.fieldName = fieldName;
        this.analyzer = analyzer;
        this.searchAnalyzer = searchAnalyzer;
    }

    public DynamicFieldMapping(String fieldName, FieldType fieldType, String analyzer, String searchAnalyzer) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.analyzer = analyzer;
        this.searchAnalyzer = searchAnalyzer;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }

    public String getAnalyzer() {
        return analyzer;
    }

    public void setAnalyzer(String analyzer) {
        this.analyzer = analyzer;
    }

    public String getSearchAnalyzer() {
        return searchAnalyzer;
    }

    public void setSearchAnalyzer(String searchAnalyzer) {
        this.searchAnalyzer = searchAnalyzer;
    }
}
