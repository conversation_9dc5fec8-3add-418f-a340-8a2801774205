package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.CharLenConstant;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 发文时间
 * <AUTHOR>
 */
public class DatePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.isMatch("^[\\dxX]+年[\\dxX]+月[\\dxX]+日$", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
//        FormatTools.formatParagraphRightInd(paragraph, config, 400L);
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
//            if (documentInfo.isPrintSend()) {
//                FormatTools.formatSerialNumber2(run, config);
//            } else {
//                FormatTools.format(run, config);
//            }
            FormatTools.format(run, config);
        }
        FormatTools.formatParagraphRightInd(paragraph, config, 2 * CharLenConstant.CHINESE_CHAR);
    }
}
