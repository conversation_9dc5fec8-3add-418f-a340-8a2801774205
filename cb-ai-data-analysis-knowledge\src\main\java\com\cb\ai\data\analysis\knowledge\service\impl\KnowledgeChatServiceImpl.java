package com.cb.ai.data.analysis.knowledge.service.impl;


import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeChat;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeChatService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;


/***
 * <AUTHOR>
 * 聊天
 */
@Service
public class KnowledgeChatServiceImpl implements KnowledgeChatService {

    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/chat'}")
    private String chatUrl;

    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization;


    @Override
    public String knowledgeChat(KnowledgeChat knowledgeChat)throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeChat)){
                throw new XServiceException("请求对象为空！");
            }
            if(StringUtils.isEmpty(knowledgeChat.getPromote())){
                throw new XServiceException("请输入聊天内容！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            headers.put(Header.CONNECTION.toString(), "Keep-Alive");
            HttpResponse httpResponse = HttpUtils.sendPost(chatUrl+"/knowledge",JSON.toJSONString(knowledgeChat),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return httpResponse.body();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String memoryChat(KnowledgeChat knowledgeChat) throws Exception {
        try{
            if(ObjectUtils.isEmpty(knowledgeChat)){
                throw new XServiceException("请求对象为空！");
            }
            if(StringUtils.isEmpty(knowledgeChat.getPromote())){
                throw new XServiceException("请输入聊天内容！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            headers.put(Header.CONNECTION.toString(), "Keep-Alive");
            HttpResponse httpResponse = HttpUtils.sendPost(chatUrl+"/memory",JSON.toJSONString(knowledgeChat),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return httpResponse.body();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }


    @Override
    public Result chatKnowledge(KnowledgeChat knowledgeChat) {
        try{
            if(ObjectUtils.isEmpty(knowledgeChat)){
                throw new XServiceException("请求对象为空！");
            }
            if(StringUtils.isEmpty(knowledgeChat.getPromote())){
                throw new XServiceException("请输入聊天内容！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(chatUrl+"/sync-knowledge",JSON.toJSONString(knowledgeChat),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return Result.successData(httpResponse.body());
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(500,e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail(500,"调用知识库问答接口失败");
        }
    }
}
