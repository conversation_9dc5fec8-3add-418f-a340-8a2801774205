package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.*;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 公文详情
 *
 * <AUTHOR>
 */
public class DocumentInfoHelper {
    public static DocumentInfo getDocumentInfo(XWPFDocument xwpfDocument) {
        DocumentInfo documentInfo = new DocumentInfo();
//        Integer pos = 0;
        // 红头
        DocRedhead docRedhead = findRedhead(xwpfDocument);
        if (docRedhead != null && docRedhead.getEnd() != null) {
//            pos = docRedhead.getEnd();
//            documentInfo.addInPos(docRedhead);
            documentInfo.setRedhead(docRedhead);
        }
        // 标题
        List<DocTitle> titles = findTitle(xwpfDocument);
        if (titles.size() > 0) {
            List<DocTitle> nowTitles = new ArrayList<>();
            Integer n = null;
            for (DocTitle docTitle : titles) {
                if (n == null) {
                    nowTitles.add(docTitle);
                    n = docTitle.getPos();
                    continue;
                }
                if (docTitle.getPos() - 1 == n) {
                    nowTitles.add(docTitle);
                    continue;
                }
                break;
            }
            titles = nowTitles;
            documentInfo.setTitles(titles);
        }
        // 主送机关
//        DocMainDelivery docMainDelivery = findMainDelivery(xwpfDocument, pos);
//        if (docMainDelivery != null) {
//            pos = docMainDelivery.getEnd();
//            documentInfo.addInPos(docMainDelivery);
//            documentInfo.setMainDelivery(docMainDelivery);
//        }
        // 提取贯彻目标
//        DocImplement docImplement = findImplement(xwpfDocument, pos);
//        if (docImplement != null) {
//            pos = docImplement.getEnd();
//            documentInfo.addInPos(docImplement);
//            documentInfo.setImplement(docImplement);
//        }
        // 提取公文目的
//        DocPurpose docPurpose = findPurpose(xwpfDocument, pos);
//        if (docPurpose != null) {
//            pos = docPurpose.getEnd();
//            documentInfo.addInPos(docPurpose);
//            documentInfo.setPurpose(docPurpose);
//        }
//        // 提取公文标题
//        DocBodyTitle docBodyTitle = findBodyTitle(xwpfDocument, docMainTitle, pos);
//        if (docBodyTitle != null) {
//            documentInfo.addInPos(docBodyTitle);
//            documentInfo.setBodyTitle(docBodyTitle);
//        }
        // 获取成文时间
        DocCompleteDate docCompleteDate = findCompleteDate(xwpfDocument);
        if (docCompleteDate != null) {
            documentInfo.setCompleteDate(docCompleteDate);
        }
        // 提取组织名称
        DocOrgName docOrgName = findOrgName(xwpfDocument, docCompleteDate);
        if (docOrgName != null) {
            documentInfo.setOrgName(docOrgName);
        }
//        // 提取出席|请假|列席
//        DocMeetingPeople docMeetingPeople = findMeetingPeople(xwpfDocument);
//        if (docMeetingPeople != null) {
//            docContent.setMeetingPeople(docMeetingPeople);
//            DocMeetingCX docMeetingCX = docMeetingPeople.getMeetingCX();
//            DocMeetingQJ docMeetingQJ = docMeetingPeople.getMeetingQJ();
//            DocMeetingLX docMeetingLX = docMeetingPeople.getMeetingLX();
//            if (docMeetingCX != null) {
//                docContent.addInPos(docMeetingCX);
//            }
//            if (docMeetingQJ != null) {
//                docContent.addInPos(docMeetingQJ);
//            }
//            if (docMeetingLX != null) {
//                docContent.addInPos(docMeetingLX);
//            }
//        }
//        // 获取公文附注
//        DocAnnotation docAnnotation = findAnnotation(xwpfDocument, docCompleteDate);
//        if (docAnnotation != null) {
//            docContent.setAnnotation(docAnnotation);
//            docContent.addInPos(docAnnotation);
//        }
//        // 提取附件信息
//        DocAttachInfo docAttachInfo = findAttachInfo(xwpfDocument);
//        if (docAttachInfo != null) {
//            docContent.setAttachInfo(docAttachInfo);
//            DocAttachExplain docAttachExplain = docAttachInfo.getAttachExplain();
//            List<DocAttachHead> docAttachHeadList = docAttachInfo.getAttachHeads();
//            List<DocAttachTitle> docAttachTitleList = docAttachInfo.getAttachTitles();
//            if (docAttachExplain != null) {
//                docContent.addInPos(docAttachExplain);
//            }
//            if (docAttachHeadList != null && docAttachHeadList.size() > 0) {
//                for (DocAttachHead docAttachHead : docAttachHeadList) {
//                    docContent.addInPos(docAttachHead);
//                }
//            }
//            if (docAttachTitleList != null && docAttachTitleList.size() > 0) {
//                for (DocAttachTitle docAttachTitle : docAttachTitleList) {
//                    docContent.addInPos(docAttachTitle);
//                }
//            }
//        }
        if (titles.size() > 0) {
            for (int i = 0; i < titles.size() && i < 3; i++) {
                DocTitle docTitle = titles.get(i);
                if (docTitle.getText().contains("关于印发")) {
                    if (documentInfo.getCompleteDate() != null && documentInfo.getCompleteDate().getStart() != null && documentInfo.getCompleteDate().getStart() - docTitle.getPos() < 8) {
                        documentInfo.setPrintSend(true); // 处理印发类公文成文时间前有大量文字无法排版为楷体问题
                    }
                    break;
                }
            }
            // 处理把落款识别为标题问题
            if (docOrgName != null) {
                for (int i = titles.size() - 1; i >= 0; i--) {
                    DocTitle docTitle = titles.get(i);
                    if (docTitle.getPos() >= docOrgName.getStart() && docTitle.getPos() <= docOrgName.getEnd()) {
                        titles.remove(i);
                    }
                }
            }
        }
        return documentInfo;
    }

    /**
     * 查找红头
     */
    private static DocRedhead findRedhead(XWPFDocument xwpfDocument) {
        DocRedhead docRedhead = null;
        List<IBodyElement> elements = xwpfDocument.getBodyElements();
        $1:
        for (int i = 0; i < elements.size() && i < 10; i++) {
            IBodyElement element = elements.get(i);
            if (element instanceof XWPFParagraph) {
                XWPFParagraph paragraph = (XWPFParagraph) element;
                if (!paragraph.runsIsEmpty()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String color = run.getColor();
                        if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                            docRedhead = new DocRedhead();
                            docRedhead.setInTable(false);
                            docRedhead.setEnd(i);
                            break $1;
                        }
                    }
                }
            }
            // 查询红头是否在表格内
            if (element instanceof XWPFTable) {
                XWPFTable table = (XWPFTable) element;
                List<XWPFRun> runs = FormatTools.getRun(table);
                if (runs != null && runs.size() > 0) {
                    for (XWPFRun run : runs) {
                        String color = run.getColor();
                        if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                            docRedhead = new DocRedhead();
                            docRedhead.setInTable(true);
                            docRedhead.setEnd(i);
                            break $1;
                        }
                    }
                }
                break;
            }
        }
        return docRedhead;
    }

//    /**
//     * 查找主标题
//     */
//    private static DocMainTitle findMainTitle(XWPFDocument xwpfDocument, int pos) {
//        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
//        DocMainTitle docMainTitle = null;
//        int startPos = pos == 0 ? 0 : pos + 1;
//        int maxPos = startPos + 10;
//        // 先查找包含关键字的段落
//        for (int i = startPos; i < paragraphs.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphs.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (FormatTools.isMainTitle(paragraphText)) { // 定位标题结束位置
//                docMainTitle = new DocMainTitle();
//                docMainTitle.setText(paragraphText);
//                docMainTitle.setStart(i);
//                docMainTitle.setEnd(i);
//                docMainTitle.getTexts().add(paragraphText);
//                break;
//            }
//        }
//        // 没有包含关键字的段落就查找居中的段落
//        if (docMainTitle != null) {
//            int n = docMainTitle.getEnd();
//            while (n-- > 0) {
//                XWPFParagraph paragraph = paragraphs.get(n);
//                String paragraphText = paragraph.getText().trim();
//                // 字符中包含标点符号或者是括号开头
//                if (StringUtils.isBlank(paragraphText) || ReUtil.contains("[。，：；！？★]", paragraphText) || ReUtil.isMatch("^[(（]", paragraphText)) {
//                    break;
//                }
//                // 把标题填充到对象里面
//                docMainTitle.setStart(n);
//                docMainTitle.setText(paragraphText + docMainTitle.getText());
//                docMainTitle.getTexts().add(0, paragraphText);
//            }
//        } else {
//            for (int i = startPos; i < paragraphs.size() && i < maxPos; i++) {
//                XWPFParagraph paragraph = paragraphs.get(i);
//                String paragraphText = paragraph.getText().trim();
//                if (StringUtils.isBlank(paragraphText) || paragraphText.length() < 3 || ReUtil.contains("[〔〕]", paragraphText) || ReUtil.contains("^[ ]*[(（].+[）)][ ]*$", paragraphText)) {
//                    if (docMainTitle == null) {
//                        continue;
//                    } else {
//                        break;
//                    }
//                }
//                if (paragraph.getAlignment() == ParagraphAlignment.CENTER || paragraphText.contains("印发《关于")) {
//                    if (docMainTitle == null) {
//                        docMainTitle = new DocMainTitle();
//                        docMainTitle.setText(paragraphText);
//                        docMainTitle.setStart(i);
//                    } else {
//                        docMainTitle.setText(docMainTitle.getText() + paragraphText);
//                    }
//                    docMainTitle.setEnd(i);
//                    docMainTitle.getTexts().add(paragraphText);
//                }
//            }
//        }
//        // 提取印发或通知内容标题
//        if (docMainTitle != null) { //的通知
//            List<String> bodyTitles = ReUtil.findAll(PatternPool.get("^[\\u4e00-\\u9fa5]+《([^》《]+)》?[\\u4e00-\\u9fa5]*"), docMainTitle.getText(), 1);
//            if (bodyTitles != null && bodyTitles.size() > 0) {
//                docMainTitle.setBodyTitle(bodyTitles.get(0));
//            }
//        }
//        return docMainTitle;
//    }

    /**
     * 查找标题
     */
    private static List<DocTitle> findTitle(XWPFDocument xwpfDocument) {
        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
        List<DocTitle> titles = new ArrayList<>();
        int startPos = 0;
        // 先查找包含关键字的段落
        for (int i = startPos; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String paragraphText = paragraph.getText().trim();
            if (paragraphText.length() > 5 &&
                    !ReUtil.contains("[ ]{4,}", paragraphText) &&
                    !ReUtil.contains("[〔〕]", paragraphText) &&
                    !ReUtil.contains("^[(（]", paragraphText) &&
                    !ReUtil.contains("[）)]$", paragraphText) &&
                    !ReUtil.contains("[!?,:;！？，。：；]", paragraphText) &&
                    !ReUtil.contains("^[一二三四五六七八九十]+[,.．，。、]", paragraphText) &&
                    !ReUtil.contains("^第[一二三四五六七八九零十百]+章", paragraphText) &&
                    !ReUtil.contains("^[   ]*\\d+[.]", paragraphText) &&
                    !ReUtil.contains("^[\u4e00-\u9fa5]{2,4}[ ]{1,4}", paragraphText)) {
                try {
                    DateUtil.parseDate(paragraphText);
                    continue;
                } catch (Exception ignored) {
                }
                boolean noRed = true;
                for (XWPFRun run : paragraph.getRuns()) {
                    String color = run.getColor();
                    if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                        noRed = false;
                        break;
                    }
                }
                if (noRed) {
                    DocTitle docTitle = new DocTitle();
                    docTitle.setText(paragraphText);
                    docTitle.setPos(i);
                    titles.add(docTitle);
                }
            }
        }
        return titles;
    }

//    // 提取主标题下附注
//    DocMainTitleAnnotation docMainTitleAnnotation = findMainTitleAnnotation(xwpfDocument, pos);
//        if (docMainTitleAnnotation != null) {
//        pos = docMainTitleAnnotation.getEnd();
//        docContent.setMainTitleAnnotation(docMainTitleAnnotation);
//        docContent.addInPos(docMainTitleAnnotation);
//    }

    /**
     * 提取主标题下附注
     */
//    private static DocMainTitleAnnotation findMainTitleAnnotation(XWPFDocument xwpfDocument, Integer pos) {
//        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
//        int startPos = pos + 1;
//        int maxPos = startPos + 5;
//        DocMainTitleAnnotation docMainTitleAnnotation = null;
//        for (int i = startPos; i < paragraphs.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphs.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (StringUtils.isBlank(paragraphText)) {
//                if (docMainTitleAnnotation == null) {
//                    continue;
//                } else {
//                    break;
//                }
//            }
//            // 括号开头
//            if (docMainTitleAnnotation == null && ReUtil.contains("^[（(]", paragraphText)) {
//                docMainTitleAnnotation = new DocMainTitleAnnotation();
//                docMainTitleAnnotation.setText(paragraphText);
//                docMainTitleAnnotation.setStart(i);
//                docMainTitleAnnotation.setEnd(i);
//                docMainTitleAnnotation.getTexts().add(paragraphText);
//            } else if (docMainTitleAnnotation != null) {
//                docMainTitleAnnotation.setEnd(i);
//                docMainTitleAnnotation.setText(docMainTitleAnnotation.getText() + paragraphText);
//                docMainTitleAnnotation.getTexts().add(paragraphText);
//            }
//            if (docMainTitleAnnotation != null && ReUtil.contains("[）)]$", paragraphText)) {
//                break;
//            }
//        }
//        return docMainTitleAnnotation;
//    }

    /**
     * 提取主送机构
     */
//    private static DocMainDelivery findMainDelivery(XWPFDocument xwpfDocument, int pos) {
//        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
//        int startPos = pos + 1;
//        int maxPos = startPos + 5;
//        DocMainDelivery docMainDelivery = null;
//        for (int i = startPos; i < paragraphs.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphs.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (StringUtils.isBlank(paragraphText)) {
//                maxPos += 1; // 出先多个空白段落无法查找到问题
//                continue;
//            }
//            if (ReUtil.contains("[\\u4e00-\\u9fa5]+[省市区县镇村组府局厅部委校位体构][:：]$", paragraphText)) {
//                docMainDelivery = new DocMainDelivery();
//                docMainDelivery.setText(paragraphText);
//                docMainDelivery.setStart(i);
//                docMainDelivery.setEnd(i);
//                break;
//            }
//        }
//        return docMainDelivery;
//    }

    /**
     * 提取贯彻目标
     */
//    private static DocImplement findImplement(XWPFDocument xwpfDocument, Integer pos) {
//        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
//        int startPos = pos + 1;
//        int maxPos = startPos + 3;
//        DocImplement docImplement = null;
//        for (int i = 0; i < paragraphs.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphs.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (StringUtils.isBlank(paragraphText)) {
//                continue;
//            }
//            if (ReUtil.contains("给你们.+贯彻[\\u4e00-\\u9fa5]+[.。]?", paragraphText)) {
//                docImplement = new DocImplement();
//                docImplement.setText(paragraphText);
//                docImplement.setStart(i);
//                docImplement.setEnd(i);
//                break;
//            }
//        }
//        return docImplement;
//    }

    /**
     * 提取公文目的
     * @param xwpfDocument Doc
     * @param pos          查找启示位置
     */
//    private static DocPurpose findPurpose(XWPFDocument xwpfDocument, Integer pos) {
//        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
//        int startPos = pos + 1;
//        int maxPos = startPos + 3;
//        DocPurpose docPurpose = null;
//        for (int i = startPos; i < paragraphs.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphs.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (StringUtils.isBlank(paragraphText)) {
//                continue;
//            }
//            if (ReUtil.isMatch("^为.+[,，]现.+(如下[：:.。]?)$", paragraphText)) {
//                docPurpose = new DocPurpose();
//                docPurpose.setText(paragraphText);
//                docPurpose.setStart(i);
//                docPurpose.setEnd(i);
//                break;
//            }
//        }
//        return docPurpose;
//    }

    /**
     * 提取公文标题
     */
//    private static DocBodyTitle findBodyTitle(XWPFDocument xwpfDocument, DocMainTitle docMainTitle, Integer pos) {
//        if (docMainTitle == null || StringUtils.isBlank(docMainTitle.getBodyTitle())) {
//            return null;
//        }
//        List<XWPFParagraph> paragraphList = xwpfDocument.getParagraphs();
//        int startPos = pos + 1;
//        int maxPos = startPos + 3;
//        DocBodyTitle docBodyTitle = null;
//        for (int i = startPos; i < paragraphList.size() && i < maxPos; i++) {
//            XWPFParagraph paragraph = paragraphList.get(i);
//            String paragraphText = paragraph.getText().trim();
//            if (docBodyTitle == null) {
//                if (StringUtils.isNotBlank(paragraphText) && paragraphText.length() > 4 && paragraphText.length() < 28) {
//                    String st = paragraphText.substring(0, 4);
//                    if (docMainTitle.getText().contains(st)) {
//                        docBodyTitle = new DocBodyTitle();
//                        docBodyTitle.setText(paragraphText);
//                        docBodyTitle.setStart(i);
//                        docBodyTitle.setEnd(i);
//                        docBodyTitle.getTexts().add(paragraphText);
//                    }
//                }
//                continue;
//            }
//            if (StringUtils.isBlank(paragraphText) || paragraphText.length() >= 28) {
//                break;
//            }
//            if (ReUtil.contains("[省市区县镇村组府局厅部委校位体构院]$", paragraphText)) {
//                docBodyTitle.setOrgName(paragraphText);
//                docBodyTitle.setEnd(i);
//                break;
//            }
//            docBodyTitle.setText(docBodyTitle.getText() + paragraphText);
//            docBodyTitle.setEnd(i);
//            docBodyTitle.getTexts().add(paragraphText);
//        }
//        return docBodyTitle;
//    }

    /**
     * 获取成文时间
     */
    private static DocCompleteDate findCompleteDate(XWPFDocument xwpfDocument) {
        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
        DocCompleteDate docCompleteDate = null;
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String paragraphText = paragraph.getText().trim().replaceAll("\\p{Zs}", "");
            if (StringUtils.isBlank(paragraphText) || paragraphText.length() > 26) {
                continue;
            }
            if (ReUtil.contains("^[\\dxX]+年[\\dxX]+月[\\dxX]+日$", paragraphText)) {
                docCompleteDate = new DocCompleteDate();
                docCompleteDate.setText(paragraphText);
                docCompleteDate.setStart(i);
                docCompleteDate.setEnd(i);
                docCompleteDate.setSection(paragraph);
                break;
            }
        }
        return docCompleteDate;
    }

    /**
     * 提取组织名称
     */
    private static DocOrgName findOrgName(XWPFDocument xwpfDocument, DocCompleteDate docCompleteDate) {
        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
        DocOrgName docOrgName = null;
        if (docCompleteDate != null && docCompleteDate.getStart() > 0) {
            int startPos = docCompleteDate.getStart() - 1;
            int maxPos = Math.max((startPos - 5), 0);
            for (int i = startPos; i > maxPos; i--) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String paragraphText = paragraph.getText().trim().replaceAll("\\p{Zs}", "");
                paragraphText = paragraphText.replaceAll("^[  　]*", "");
                paragraphText = paragraphText.replaceAll("[  　]*$", "");
                if (StringUtils.isBlank(paragraphText)) {
                    if (docOrgName == null) {
                        continue;
                    } else {
                        break;
                    }
                }
                if (ReUtil.contains("[\\d!?,.:;！？，。：；（）【】]", paragraphText)) {
                    break;
                }
                if ((paragraphText.length() > 2 && paragraphText.length() <= 25 && !ReUtil.contains("[!?,.:;！？，。：；（）【】]$", paragraphText))
                        || ReUtil.contains("[省市区县镇村组府局厅部委校位体构院业门司长理总站位][　 ]*$", paragraphText)) {
                    if (docOrgName == null) {
                        docOrgName = new DocOrgName();
                        docOrgName.setStart(i);
                        docOrgName.setEnd(i);
                        docOrgName.setText(paragraphText);
                        docOrgName.getTexts().add(paragraphText);
                    } else {
                        docOrgName.setStart(i);
                        docOrgName.setText(docOrgName.getText() + paragraphText);
                        docOrgName.getTexts().add(paragraphText);
                    }
                    docOrgName.setSection(paragraph);
                }
            }
        }
        return docOrgName;
    }
}
