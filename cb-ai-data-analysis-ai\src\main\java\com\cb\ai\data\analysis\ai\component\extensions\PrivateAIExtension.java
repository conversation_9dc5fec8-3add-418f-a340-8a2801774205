package com.cb.ai.data.analysis.ai.component.extensions;

import com.cb.ai.data.analysis.ai.component.choreography.extension.BaseExtension;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionConfig;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionType;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:04
 * @Copyright (c) 2025
 * @Description 私有化底座扩展
 */
@ExtensionConfig(desc = "私有化底座扩展", type = ExtensionType.PRIVATE_BASE)
public interface PrivateAIExtension<Context> extends BaseExtension<Context> {

}
