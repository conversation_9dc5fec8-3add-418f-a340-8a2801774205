package com.cb.ai.data.analysis.petition.mapper;

import com.cb.ai.data.analysis.petition.annotation.DataSource;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionOriginEntity;
import com.cb.ai.data.analysis.petition.enums.DataSourceType;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DataSource(DataSourceType.SLAVE)
public interface SsPetitionOriginMapper extends BaseMapper<SsPetitionOriginEntity> {


    List<SsPetitionOriginEntity> findByIdsAndStatus(@Param("ids") List<Long> ids, @Param("statusList") List<Integer> statusList);

}
