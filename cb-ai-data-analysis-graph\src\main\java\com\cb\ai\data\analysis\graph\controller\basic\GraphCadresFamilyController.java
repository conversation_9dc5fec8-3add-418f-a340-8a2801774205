package com.cb.ai.data.analysis.graph.controller.basic;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresFamily;
import com.cb.ai.data.analysis.graph.service.basic.GraphCadresFamilyService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.easyexcel.EasyExcelService;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 知识图谱-干部亲属表(GraphCadresFamily)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:12
 */
@Validated
@RestController
@RequestMapping(Constants.API_GRAPH_PATH + "/cadres/family")
public class GraphCadresFamilyController extends BaseController<GraphCadresFamilyService, GraphCadresFamily> {
    @Resource
    private EasyExcelService easyExcelService;

    /**
     * 分页获取知识图谱-干部亲属表
     *
     * @param graphCadresFamily 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(GraphCadresFamily graphCadresFamily) {
        LambdaQueryWrapper<GraphCadresFamily> queryWrapper = XQueryWrapper.newInstance(graphCadresFamily)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(GraphCadresFamily::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 获取知识图谱-干部亲属表
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "知识图谱-干部亲属表ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增知识图谱-干部亲属表
     *
     * @param graphCadresFamily 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增知识图谱-干部亲属表", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody GraphCadresFamily graphCadresFamily) {
        baseService.save(graphCadresFamily);
        return Result.success("新增知识图谱-干部亲属表成功");
    }

    /**
     * 修改知识图谱-干部亲属表
     *
     * @param graphCadresFamily 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改知识图谱-干部亲属表", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody GraphCadresFamily graphCadresFamily) {
        baseService.updateById(graphCadresFamily);
        return Result.success("知识图谱-干部亲属表修改成功");
    }

    /**
     * 删除知识图谱-干部亲属表
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除知识图谱-干部亲属表", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "知识图谱-干部亲属表ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        baseService.deleteByIds(list);
        return Result.success("知识图谱-干部亲属表删除成功");
    }


    @PostMapping("/import")
    @XLog(title = "导入知识图谱-干部亲属表", execType = ExecType.IMPORT)
    public Result importData(MultipartFile file) {
        AtomicInteger count = new AtomicInteger();
        List<String> errMsgList = new ArrayList<>();
        try {
            easyExcelService.importExcel(
                    file.getInputStream(),
                    GraphCadresFamily.class,
                    saveFunction -> {
                        count.addAndGet(saveFunction.size());
                        return baseService.importExcel(saveFunction);
                    },
                    errorMessage -> errMsgList.add(errorMessage)
            );
        } catch (Exception e) {
            // 记录异常信息
            return Result.fail("导入知识图谱-干部亲属表失败: " + e.getMessage());
        }
        StringBuilder msg = new StringBuilder();
        if (!errMsgList.isEmpty()) {
            msg.append("</br>导入失败！部分记录中的内容不正确：</br>" + String.join("</br>", errMsgList));
        }
        if (count.get() > 0) {
            msg.insert(0, "成功导入<span style=\"color:red;\">" + count + "</span>条数据!");
        }
        return Result.success(msg.toString());
    }

    @GetMapping("/importTemplate")
    @XLog(title = "下载知识图谱-干部亲属表导入模板", execType = ExecType.DOWNLOAD)
    public void importTemplate(HttpServletResponse response) {
        easyExcelService.downloadExcelTemplate(GraphCadresFamily.class,
                "知识图谱-干部亲属表导入模板", "知识图谱-干部亲属表", response);
    }


    @GetMapping("/export")
    @XLog(title = "导出知识图谱-干部亲属表", execType = ExecType.EXPORT)
    public void exportData(GraphCadresFamily family, HttpServletResponse response) {
        LambdaQueryWrapper<GraphCadresFamily> queryWrapper = XQueryWrapper.newInstance(family)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(GraphCadresFamily::getCreateTime);
        List<GraphCadresFamily> list = baseService.list(queryWrapper);
        easyExcelService.exportExcel(GraphCadresFamily.class, "知识图谱-干部亲属表", "知识图谱-干部亲属表", list, response);
    }

}

