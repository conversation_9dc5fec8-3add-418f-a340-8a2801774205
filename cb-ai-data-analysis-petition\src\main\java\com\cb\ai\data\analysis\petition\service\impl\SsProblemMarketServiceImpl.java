package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.dto.QuestionDTO;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketClassifyEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsProblemMarketVo;
import com.cb.ai.data.analysis.petition.mapper.SsProblemMarketClassifyMapper;
import com.cb.ai.data.analysis.petition.mapper.SsProblemMarketDetailsMapper;
import com.cb.ai.data.analysis.petition.service.SsProblemMarketService;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/***
 * <AUTHOR>
 * 问题矩阵
 */
@Service
public class SsProblemMarketServiceImpl implements SsProblemMarketService {

    @Autowired
    private SsProblemMarketClassifyMapper problemMarketClassifyMapper;

    @Autowired
    private SsProblemMarketDetailsMapper problemMarketDetailsMapper;

    @Override
    public Page<SsProblemMarketClassifyEntity> classifyPage(SsProblemMarketVo problemMarket) {
        Page<SsProblemMarketClassifyEntity> page = new Page<>(problemMarket.getPageNo(), problemMarket.getPageSize());
        LambdaQueryWrapper<SsProblemMarketClassifyEntity> queryWrapper = new QueryWrapper<SsProblemMarketClassifyEntity>().lambda();
        if(StringUtils.isNotBlank(problemMarket.getProblemName())){
            queryWrapper.like(SsProblemMarketClassifyEntity::getProblemTitle,problemMarket.getProblemName());
        }
        if(StringUtils.isNotBlank(problemMarket.getProblemClassify())){
            queryWrapper.like(SsProblemMarketClassifyEntity::getProblemClassify,problemMarket.getProblemClassify());
        }
        if(!ObjectUtils.isEmpty(problemMarket.getFileName())){
            queryWrapper.eq(SsProblemMarketClassifyEntity::getFileName,problemMarket.getFileName());
        }
        queryWrapper.orderByDesc(SsProblemMarketClassifyEntity::getCreateTime);
        return problemMarketClassifyMapper.selectPage(page, queryWrapper);
    }

    @Override
    public IPage<SsProblemMarketDetailsEntity> detailsPage(SsProblemMarketVo problemMarket) {
        Page<SsProblemMarketVo> page = new Page<>(problemMarket.getPageNo(), problemMarket.getPageSize());
        QueryWrapper<SsProblemMarketVo> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(problemMarket.getProblemName())){
            queryWrapper.like("t1.problem_name",problemMarket.getProblemName());
        }
        if(ArrayUtils.isNotEmpty(problemMarket.getProblemClassifys())){
            queryWrapper.in("t2.problem_classify",problemMarket.getProblemClassifys());
        }
        if(!ObjectUtils.isEmpty(problemMarket.getFileName())){
            queryWrapper.like("t2.file_name",problemMarket.getFileName());
        }
        if(!ObjectUtils.isEmpty(problemMarket.getProblemClassifyId())&&problemMarket.getProblemClassifyId()>0){
            queryWrapper.eq("t1.problem_classify_id",problemMarket.getProblemClassifyId());
        }
        queryWrapper.orderByDesc("t1.create_time");
        return problemMarketDetailsMapper.detailsPage(page,queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUpload(MultipartFile file, String domain) throws Exception {
        try{
            if(ObjectUtils.isEmpty(file)){
                throw new XServiceException("文件列表不能为空！");
            }
            if(StringUtils.isBlank(domain)){
                throw new XServiceException("所属领域不能为空！");
            }
            String fileName=file.getOriginalFilename();
            String problemTitle = fileName.substring(0, fileName.lastIndexOf('.'));
            List<QuestionDTO> readQuestionList= EasyExcel.read(file.getInputStream())
                    .head(QuestionDTO.class)
                    .headRowNumber(2)
                    .sheet()
                    .doReadSync();
            if(CollectionUtils.isEmpty(readQuestionList)){
                throw new XServiceException("读取文件内容为空！");
            }
            Long classifyId=IdUtil.getSnowflakeNextId();
            Integer problemDataCount=readQuestionList.size();
            SsProblemMarketClassifyEntity problemMarketClassifyEntity=new SsProblemMarketClassifyEntity();
            problemMarketClassifyEntity.setId(classifyId);
            problemMarketClassifyEntity.setFileName(fileName);
            problemMarketClassifyEntity.setProblemTitle(problemTitle);
            problemMarketClassifyEntity.setProblemDataCount(problemDataCount);
            problemMarketClassifyEntity.setProblemClassify(domain);
            problemMarketClassifyEntity.setCreateTime(LocalDateTime.now());
            String userName=SecurityUtils.getUsername();
            problemMarketClassifyEntity.setCreateBy(userName);
            problemMarketClassifyMapper.insert(problemMarketClassifyEntity);
            List<SsProblemMarketDetailsEntity> detailsEntityList=new ArrayList<>();
            for(QuestionDTO question:readQuestionList){
                SsProblemMarketDetailsEntity detailsEntity=new SsProblemMarketDetailsEntity();
                detailsEntity.setId(IdUtil.getSnowflakeNextId());
                detailsEntity.setProblemClassifyId(classifyId);
                detailsEntity.setProblemName(question.getQuestion());
                detailsEntity.setCreateTime(LocalDateTime.now());
                detailsEntity.setCreateBy(userName);
                detailsEntityList.add(detailsEntity);
            }
            problemMarketDetailsMapper.insert(detailsEntityList);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delProblemMarket(String id) throws Exception {
        try{
            if(StringUtils.isBlank(id)){
                throw new XServiceException("ID不能为空！");
            }
            int i=problemMarketClassifyMapper.deleteById(id);
            if(i<=0){
                throw new XServiceException("删除提问矩阵信息失败！");
            }
            LambdaQueryWrapper<SsProblemMarketDetailsEntity> delWrapper = new LambdaQueryWrapper<>();
            delWrapper.eq(SsProblemMarketDetailsEntity::getProblemClassifyId, id);
            problemMarketDetailsMapper.delete(delWrapper);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public int delProblemDetails(String id) {
        return problemMarketDetailsMapper.deleteById(id);
    }
}
