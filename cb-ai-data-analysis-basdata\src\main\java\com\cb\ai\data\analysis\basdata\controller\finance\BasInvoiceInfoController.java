package com.cb.ai.data.analysis.basdata.controller.finance;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasInvoiceInfo;
import com.cb.ai.data.analysis.basdata.service.finance.BasInvoiceInfoService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.easyexcel.EasyExcelService;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 发票信息表(BasInvoiceInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-14 21:59:36
 */
@RestController
@RequestMapping(Constants.API_BASIC_DATA_ROOT_PATH + "/finance/invoiceInfo")
public class BasInvoiceInfoController extends BaseController<BasInvoiceInfoService, BasInvoiceInfo> {

    @Resource
    private EasyExcelService easyExcelService;

    /**
     * 分页获取发票信息表
     *
     * @param basInvoiceInfo 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(BasInvoiceInfo basInvoiceInfo) {
        LambdaQueryWrapper<BasInvoiceInfo> queryWrapper = XQueryWrapper.newInstance(basInvoiceInfo)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(BasInvoiceInfo::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 获取发票信息表
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "发票信息表ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增发票信息表
     *
     * @param basInvoiceInfo 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增发票信息表", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody BasInvoiceInfo basInvoiceInfo) {
        if (baseService.save(basInvoiceInfo)) {
            return Result.success("发票信息表新增成功！");
        }
        return Result.fail("发票信息表新增失败！");
    }

    /**
     * 修改发票信息表
     *
     * @param basInvoiceInfo 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改发票信息表", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody BasInvoiceInfo basInvoiceInfo) {
        if (baseService.updateById(basInvoiceInfo)) {
            return Result.success("发票信息表修改成功！");
        }
        return Result.fail("发票信息表修改失败！");
    }

    /**
     * 删除发票信息表
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除发票信息表", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "发票信息表ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.deleteByIds(list)) {
            return Result.success("发票信息表删除成功！");
        }
        return Result.fail("发票信息表删除失败！");
    }


    @PostMapping("/import")
    @XLog(title = "导入发票信息表", execType = ExecType.IMPORT)
    public Result importData(MultipartFile file) {
        AtomicInteger count = new AtomicInteger();
        List<String> errMsgList = new ArrayList<>();
        try {
            easyExcelService.importExcel(
                    file.getInputStream(),
                    BasInvoiceInfo.class,
                    saveFunction -> {
                        count.addAndGet(saveFunction.size());
                        return baseService.importExcel(saveFunction);
                    },
                    errorMessage -> errMsgList.add(errorMessage)
            );
        } catch (Exception e) {
            return Result.fail("导入发票信息表失败: " + e.getMessage());
        }
        StringBuilder msg = new StringBuilder();
        if (!errMsgList.isEmpty()) {
            msg.append("</br>导入失败！部分记录中的内容不正确：</br>" + String.join("</br>", errMsgList));
        }
        if (count.get() > 0) {
            msg.insert(0, "成功导入<span style=\"color:red;\">" + count + "</span>条数据!");
        }
        return Result.success(msg.toString());
    }

    @GetMapping("/importTemplate")
    @XLog(title = "下载发票信息导入模板", execType = ExecType.DOWNLOAD)
    public void importTemplate(HttpServletResponse response) {
        easyExcelService.downloadExcelTemplate(
                BasInvoiceInfo.class,
                "发票信息导入模板",
                "发票信息",
                response);
    }

    @GetMapping("/export")
    @XLog(title = "导出发票信息", execType = ExecType.EXPORT)
    public void exportData(BasInvoiceInfo basInvoiceInfo, HttpServletResponse response) {
        LambdaQueryWrapper<BasInvoiceInfo> queryWrapper = XQueryWrapper.newInstance(basInvoiceInfo)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(BasInvoiceInfo::getCreateTime);
        List<BasInvoiceInfo> list = baseService.list(queryWrapper);
        easyExcelService.exportExcel(BasInvoiceInfo.class,
                "发票信息",
                "发票信息",
                list,
                response);
    }
}

