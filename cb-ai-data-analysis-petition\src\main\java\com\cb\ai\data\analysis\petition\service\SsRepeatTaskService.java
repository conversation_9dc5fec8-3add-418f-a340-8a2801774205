package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.cb.ai.data.analysis.petition.domain.SsRepeatTask;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.xong.boot.common.service.BaseService;

public interface SsRepeatTaskService extends BaseService<SsRepeatTask> {

    void createTask(SsRepeatTaskVo.CreateReq req);

    void startAsyncTask(String taskId, double threshold, boolean force);

    Page<SsRepeatTask> pageByWrapper(Page<SsRepeatTask> page, Wrapper<SsRepeatTask> wrapper);

    Page<SsRepeatTask> pageBySelf(Page<SsRepeatTask> page, SsRepeatTask query);

    int delTask(String taskId);

    Page<SsRepeatResultGroup> getRepeatTaskResult(String taskId, Page<SsRepeatResultGroup> page, SsRepeatResultGroup entity);

}
