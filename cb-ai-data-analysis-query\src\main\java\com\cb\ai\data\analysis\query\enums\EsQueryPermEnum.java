package com.cb.ai.data.analysis.query.enums;

import java.util.Arrays;

/**
 * ElasticSearch数据权限枚举
 */
public enum EsQueryPermEnum {
    SCOPE_ALL("all", null, "所有权限"),
    SCOPE_DEPT("dept", "deptId", "部门数据权限"),
    SCOPE_DEPT_AND_CHILD("dept_and_child", "deptId", "部门及以下数据权限"),
    SCOPE_DISTRICT("district", "districtId", "地区数据权限"),
    SCOPE_DISTRICT_AND_CHILD("district_and_child", "districtId", "地区及以下数据权限"),
    SCOPE_SELF("self", "createBy", "仅本人数据权限");
    // 类型
    private String perm;
    // 字段
    private String field;
    // 描述
    private String desc;

    EsQueryPermEnum(String perm, String field, String desc) {
        this.perm = perm;
        this.field = field;
        this.desc = desc;
    }

    public String getPerm() {
        return perm;
    }

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }

    public static EsQueryPermEnum of(String perm) {
        return Arrays.stream(EsQueryPermEnum.values())
                .filter(value -> perm.equals(value.getPerm()))
                .findFirst()
                .orElse(null);
    }
}
