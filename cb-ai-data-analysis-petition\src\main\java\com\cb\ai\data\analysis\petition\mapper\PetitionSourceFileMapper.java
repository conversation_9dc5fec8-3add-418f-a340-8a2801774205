package com.cb.ai.data.analysis.petition.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionSourceFileEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile;
import com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/***
 * 信访源文件
 */
@Mapper
public interface PetitionSourceFileMapper  extends BaseMapper<PetitionSourceFileEntity> {

    IPage<SourceFileVo> querySourceFileList(IPage<PetitionSourceFile> page, @Param(Constants.WRAPPER) Wrapper<PetitionSourceFile> wrapper);


}
