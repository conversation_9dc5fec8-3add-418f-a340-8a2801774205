package com.cb.ai.data.analysis.ai.component.choreography.model;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:08
 * @Copyright (c) 2025
 * @Description 扩展业务路由场景
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Route {
    /**
     * 标签
     */
    String tag() default "";
    /**
     * 业务类型
     */
    BusinessTypeEnum business();
    /**
     * 场景
     */
    SceneEnum scene() default SceneEnum.DEFAULT_SCENE;

}
