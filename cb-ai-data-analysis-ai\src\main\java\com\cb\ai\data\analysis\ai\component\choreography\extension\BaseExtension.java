package com.cb.ai.data.analysis.ai.component.choreography.extension;

import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:00
 * @Copyright (c) 2025
 * @Description 基础扩展接口
 */
public interface BaseExtension<Context> {
    /**
     * @description 扩展点执行
     * @param args 扩展点入参数
     * @return Object 扩展点返回值
     * @createtime 2025/7/4 下午12:14
     * <AUTHOR>
     * @version 1.0
     */
    Flux<?> invoke(Context context);

    /**
     * @description 扩展点执行
     * @param args 扩展点入参数
     * @return Object 扩展点返回值
     * @createtime 2025/7/4 下午12:14
     * <AUTHOR>
     * @version 1.0
     */
    default Object syncInvoke(Context context) { return null; }
}
