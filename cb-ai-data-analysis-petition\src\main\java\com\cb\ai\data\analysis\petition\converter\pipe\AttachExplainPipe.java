package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 公文目录
 * <AUTHOR>
 */
public class AttachExplainPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.isMatch("^[  　]*附[  　]{0,2}件[:：].+", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化附件目录
        List<XWPFRun> runs = paragraph.getRuns();
        // 修改序号后标点
        for (XWPFRun run : runs) {
            String runText = run.text();
            if (StringUtils.isBlank(runText)) {
                continue;
            }
            if (ReUtil.contains("[0-9][.。．]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "([0-9])([.。．])", "$1.");
                run.setText(replaced, 0);
                break;
            }
            if (ReUtil.contains("^[.。．]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "^([.。．])", ".");
                run.setText(replaced, 0);
                break;
            }
        }
        // 格式化段落
        FormatTools.formatAttachExplain(paragraph, config, true);
        int l = 1;
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = pos + 1; i < paragraphs.size(); i++) {
            XWPFParagraph xwpfParagraph = paragraphs.get(i);
            String paragraphText = xwpfParagraph.getText().trim();
            l++;
            if (StringUtils.isNotBlank(paragraphText) && ReUtil.contains("^[  　]*[" + l + "]", paragraphText)) {
                FormatTools.formatAttachExplain(xwpfParagraph, config, false);
                continue;
            }
            break;
        }
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
