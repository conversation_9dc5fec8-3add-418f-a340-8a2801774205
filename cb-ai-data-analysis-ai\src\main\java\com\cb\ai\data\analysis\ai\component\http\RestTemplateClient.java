package com.cb.ai.data.analysis.ai.component.http;

import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/3 17:23
 * @Copyright (c) 2025
 * @Description RestTemplate客户端
 */
public class RestTemplateClient extends BaseClient {
    /**
     * 客户端
     */
    private final RestTemplate restTemplate;

    public RestTemplateClient() {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        this.restTemplate = builder.setConnectTimeout(Duration.ofSeconds(300))
            .setReadTimeout(Duration.ofSeconds(300))
            .additionalMessageConverters(
                // 确保有JSON转换器
                new MappingJackson2HttpMessageConverter(),
                // 添加文本转换器
                new StringHttpMessageConverter()
            )
            .build();
    }

    @Override
    public <V> Flux<V> executeStream() {
        try {
            String responseBody = doExecute();
            // 提取SSE流数据
            List<V> result = processSseStream(responseBody);
            if (result.isEmpty()) {
                throw new RuntimeException("请求接口成功，但是未能从原始返回数据中提取到流式数据, 原始返回数据：" + responseBody);
            }
            return Flux.fromIterable(result);
        } finally {
            clear();
        }
    }

    @Override
    public <V> V executeBlock() {
        try {
            return processResultData(doExecute());
        } finally {
            clear();
        }
    }

    private String doExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonLog.info("RestTemplateClient -> 请求开始 ------>");
        CommonLog.info("RestTemplateClient -> 请求接口地址：{}", getUrl());
        CommonLog.info("RestTemplateClient -> 请求头：{}", JsonUtil.toStr(getHeaders().toSingleValueMap()));
        CommonLog.info("RestTemplateClient -> 请求体：{}", JsonUtil.toStr(getBody()));
        try {
            HttpEntity<?> requestEntity = new HttpEntity<>(getBody(), getHeaders());
            // 对于SSE请求，使用String作为响应类型
            ResponseEntity<String> response = restTemplate.exchange(getUrl(), getMethod(), requestEntity, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                return OptionalUtil.ofBlankable(response.getBody()).orElse("");
            }
            throw new RuntimeException("请求失败，状态码: " + response.getStatusCode() + ", 消息：" + response.getBody());
        } catch (Exception e) {
            throw getOnError().apply(e);
        } finally {
            stopWatch.stop();
            CommonLog.info("RestTemplateClient -> 请求结束，耗时: {} ms", stopWatch.getTotalTimeMillis());
        }
    }
}
