package com.cb.ai.data.analysis.ai.provider;

import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;

/**
 * AI Provider Service接口
 * <AUTHOR>
 */
public interface AiProviderService {
    /**
     * 创建用户会话
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param title     会话标题
     */
    String createSession(String sessionId, String userId, String title);

    /**
     * 保存AI历史会话消息
     * @param sessionMessage 历史会话消息
     */
    void saveSessionMessage(AiChatHistorySessionMessage sessionMessage);
}
