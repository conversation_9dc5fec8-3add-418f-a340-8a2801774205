package com.cb.ai.data.analysis.ai.exception;

/**
 * AI 异常
 * <AUTHOR>
 */
public class AiException extends RuntimeException {
    public AiException() {
    }

    public AiException(String message) {
        super(message);
    }

    public AiException(String message, Throwable cause) {
        super(message, cause);
    }

    public AiException(Throwable cause) {
        super(cause);
    }

    public AiException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
