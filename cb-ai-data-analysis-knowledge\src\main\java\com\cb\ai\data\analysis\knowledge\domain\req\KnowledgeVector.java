package com.cb.ai.data.analysis.knowledge.domain.req;

import com.xong.boot.common.domain.BasePage;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 向量库查询实体
 */

@Data
public class KnowledgeVector extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     *  知识库id
     */
    private List<String> baseIds;
    /**
     * 相似度过滤
     */
    private Double similarityHolds;
    /***
     * 首次从向量库召回的数据量
     */
    private Integer topK;
    /***
     * 文件ids
     */
    private List<String> fileIds;
    /***
     * 提示词
     */
    private String promote;


    /***
     * 知识库ID
     */
    private String baseId;

    /***
     * 文件ID
     */
    private String fileId;
}
