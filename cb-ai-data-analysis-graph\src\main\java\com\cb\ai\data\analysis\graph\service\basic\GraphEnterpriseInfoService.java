package com.cb.ai.data.analysis.graph.service.basic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphEnterpriseInfo;

import java.util.List;

/**
 * 知识图谱-企业信息(GraphEnterpriseInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04 11:06:12
 */
public interface GraphEnterpriseInfoService extends IService<GraphEnterpriseInfo> {

    public boolean save(GraphEnterpriseInfo graphEnterpriseInfo);

    public boolean updateById(GraphEnterpriseInfo graphEnterpriseInfo);

    public boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<GraphEnterpriseInfo> list);
}

