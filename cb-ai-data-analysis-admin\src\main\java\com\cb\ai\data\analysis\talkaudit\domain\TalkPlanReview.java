package com.cb.ai.data.analysis.talkaudit.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 谈话方案审查(TalkPlanReview)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-09 14:36:37
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class TalkPlanReview extends BaseDomain {

    private static final long serialVersionUID = 1L;


    //主键id
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    //部门id
    private String deptId;

    //文件名称
    @NotBlank(message = "文件名称不存在")
    private String fileName;

    //文件路径
    private String filePath;

    //文件类型
    private String fileType;

    //文件大小
    private Long fileSize;

    //标签
    private String tag;

    //分析结果
    @NotBlank(message = "分析结果不存在", groups = UpdateGroup.class)
    private String analyseResult;

    //审查报告
    private String auditReport;

    //审查报告路径
    private String auditReportPath;

}

