package com.cb.ai.data.analysis.dbtable.converts;

import com.cb.ai.data.analysis.dbtable.enums.ConvertMode;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 固定值
 * <AUTHOR>
 */
@Component
@Order(0)
public class FixedValueConvert extends Convert<String> {
    public FixedValueConvert() {
        super("FIXED_VALUE", "固定值", ConvertMode.USER_FILL);
    }
}
