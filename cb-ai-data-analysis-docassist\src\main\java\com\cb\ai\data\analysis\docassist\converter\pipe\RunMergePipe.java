package com.cb.ai.data.analysis.docassist.converter.pipe;

import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.List;

/**
 * 将段落中的RUN标签进行拼接
 *
 * <AUTHOR>
 */
public class RunMergePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        return true;
    }

    @Override
    public boolean isBreak() {
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        if (paragraph.runsIsEmpty()) {
            return;
        }
        // 拼接字符串
        NodeList nodeList = paragraph.getCTP().getDomNode().getChildNodes();
        StringBuilder sb = null;
        Node firstNode = null;
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            String nodeName = node.getNodeName();
            boolean isGoBack = false;
            if (nodeName.startsWith("w:bookmark")) {
                Node attrNode = node.getAttributes().getNamedItem("w:name");
                if (attrNode != null) {
                    String name = attrNode.getNodeValue();
                    isGoBack = name.equals("_GoBack");
                }
            }
            if (nodeName.equals("w:r")) {
                NodeList nodeList1 = node.getChildNodes();
                boolean hasWt = false;
                for (int j = 0; j < nodeList1.getLength(); j++) {
                    Node node1 = nodeList1.item(j);
                    String nodeName1 = node1.getNodeName();
                    if (nodeName1.equals("w:t")) {
                        hasWt = true;
                        NodeList nodeList2 = node1.getChildNodes();
                        Node node2 = nodeList2.item(0);
                        if (node2 == null) {
                            break;
                        }
                        String nodeText = node2.getNodeValue();
                        if (sb == null) {
                            sb = new StringBuilder(nodeText);
                            firstNode = node2;
                        } else {
                            sb.append(nodeText);
                        }
                        node2.setNodeValue("");
                        break;
                    }
                }
                if (!hasWt && sb != null) {
                    firstNode.setNodeValue(sb.toString());
                    sb = null;
                    firstNode = null;
                }
            } else if (!isGoBack) {
                if (sb != null) {
                    firstNode.setNodeValue(sb.toString());
                    sb = null;
                    firstNode = null;
                }
            }
        }
        if (sb != null) {
            firstNode.setNodeValue(sb.toString());
        }
        // 删除空白run
        List<XWPFRun> runs = paragraph.getRuns();
        for (int i = runs.size() - 1; i >= 0; i--) {
            XWPFRun run = runs.get(i);
            CTR runCTR = run.getCTR();
            if (runCTR.sizeOfTArray() > 0 && run.text().length() == 0) {
                paragraph.removeRun(i);
                i -= 1;
            }
        }
    }
}
