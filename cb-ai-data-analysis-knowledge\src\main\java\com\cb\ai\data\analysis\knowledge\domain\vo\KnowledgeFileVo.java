package com.cb.ai.data.analysis.knowledge.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 * <AUTHOR>
 * 知识库文件返回实体
 */
@Data
public class KnowledgeFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     * 文件ID
     */
    private String id;


    /***
     * 文件名称
     */
    private String fileName;

    /***
     * 文件类型
     */
    private Integer fileType;

    /**
     * 处理状态
     */
    private Integer processStatus;

    /***
     * 处理备注
     */
    private String processRemark;


    /***
     * 文件url地址
     */
    private String fileUrl;

    /***
     * 文件大小
     */
    private Integer fileSize;

    /***
     * 创建时间
     */
    private String createTime;

    /***
     * 删除标识
     */
    private Integer isDel;

    /***
     * 文件ID，文件管理的
     */
    private String fileId;

    /**
     * 更新时间
     */
    @ExcelIgnore
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;
}
