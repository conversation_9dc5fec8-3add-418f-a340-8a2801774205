//package com.cb.ai.data.analysis.file.config;
//
//import com.alibaba.fastjson2.JSON;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import io.minio.GetObjectArgs;
//import io.minio.GetPresignedObjectUrlArgs;
//import io.minio.MinioClient;
//import io.minio.credentials.AssumeRoleBaseProvider;
//import io.minio.credentials.AssumeRoleProvider;
//import io.minio.credentials.Credentials;
//import io.minio.credentials.Provider;
//import io.minio.errors.*;
//import io.minio.http.Method;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.stereotype.Component;
//import org.springframework.util.Assert;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.security.NoSuchAlgorithmException;
//import java.util.*;
//
///**
// * Minio 基础操作类 (暂时没使用)
// *
// * <AUTHOR>
// */
//@Component
//@Configuration
//public class MinioTemplate implements InitializingBean {
//
//    private MinioClient minioClient;
//    @Value("${minio.url}")
//    private String url;
//    @Value("${minio.accessKey}")
//    private String accessKey;
//    @Value("${minio.secretKey}")
//    private String secretKey;
//    @Value("${minio.bucket}")
//    private String bucket;
//
//    @Override
//    public void afterPropertiesSet() {
//        Assert.hasText(url, "Minio url 为空");
//        Assert.hasText(accessKey, "Minio accessKey为空");
//        Assert.hasText(secretKey, "Minio secretKey为空");
//        this.minioClient = MinioClient
//                .builder()
//                .endpoint(url)
//                .credentials(accessKey, secretKey)
//                .build();
//    }
//
//    public MinioClient getMinioClient() {
//        if (minioClient == null) {
//            try {
//                 this.minioClient = MinioClient
//                        .builder()
//                        .endpoint(url)
//                        .credentials(accessKey, secretKey)
//                        .build();
//                return this.minioClient;
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return minioClient;
//    }
//
////    /**
////     * 上传
////     */
////    public String putObject(MultipartFile multipartFile) throws Exception {
////        // bucket 不存在，创建
////        if (!minioClient.bucketExists(this.bucket)) {
////            minioClient.makeBucket(this.bucket);
////        }
////        try (InputStream inputStream = multipartFile.getInputStream()) {
////            // 上传文件的名称
////            String fileName = multipartFile.getOriginalFilename();
////            // PutObjectOptions，上传配置(文件大小，内存中文件分片大小)
////            PutObjectOptions putObjectOptions = new PutObjectOptions(multipartFile.getSize(), PutObjectOptions.MIN_MULTIPART_SIZE);
////            // 文件的ContentType
////            putObjectOptions.setContentType(multipartFile.getContentType());
////            minioClient.putObject(this.bucket, fileName, inputStream, putObjectOptions);
////            // 返回访问路径
////            return this.url + UriUtils.encode(fileName, StandardCharsets.UTF_8);
////        }
////    }
//    /**
//     * 获取外部下载链接
//     * @param bucketName
//     * @param objectName
//     * @return
//     */
//    public String getPreSignedObjectUrl(String bucketName,String objectName) {
//        GetPresignedObjectUrlArgs build = GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(objectName).method(Method.GET).expiry(60*60*24).build();
//        String presignedObjectUrl = "";
//        try {
//            presignedObjectUrl = getMinioClient().getPresignedObjectUrl(build);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return presignedObjectUrl;
//    }
//    public String getPreSignedObjectUrl(String objectName) {
//       return getPreSignedObjectUrl(bucket,objectName);
//    }
//    /**
//     * 文件下载
//     */
//    public void download(String fileName) {
//        // 从链接中得到文件名
//        InputStream inputStream = null;
//        try {
//            GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucket).object(fileName).build();
//            inputStream = minioClient.getObject(getObjectArgs);
//            inputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                if (inputStream != null) {
//                    inputStream.close();
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    /**
//     * 获取外部上传链接
//     * @param filename
//     * @return
//     * @throws Exception
//     */
//    public Map<String, String> generateUploadUrl(
//            String filename) throws Exception {
//
//        try {
//            // 2. 生成唯一对象名称 (防止覆盖)
//            String objectName = generateUniqueObjectName(filename);
//
//            // 3. 设置 URL 有效期 (单位：秒)
//            int expirySeconds = 60 * 60; // 1小时
//
//            // 4. 生成预签名 PUT URL
//            String url = minioClient.getPresignedObjectUrl(
//                    io.minio.GetPresignedObjectUrlArgs.builder()
//                            .method(Method.PUT)
//                            .bucket(bucket)
//                            .object(objectName)
//                            .expiry(expirySeconds)
//                            .build());
//
//            // 5. 返回结果
//            Map<String, String> response = new HashMap<>();
//
//            response.put("url", url);
//            response.put("objectName", objectName);
//            response.put("expiry", new Date(System.currentTimeMillis() + expirySeconds * 1000).toString());
//
//            return response;
//
//        } catch (MinioException e) {
//            throw new Exception("MinIO 错误: " + e.getMessage());
//        }
//    }
//
//    // 生成唯一对象名 (日期 + UUID + 原始文件名)
//    private String generateUniqueObjectName(String originalFilename) {
//        String timestamp = String.valueOf(System.currentTimeMillis());
//        String uuid = UUID.randomUUID().toString().substring(0, 8);
//        String safeFilename = originalFilename.replaceAll("[^a-zA-Z0-9.\\-]", "_");
//        return "uploads/" + timestamp + "-" + uuid + "-" + safeFilename;
//    }
//
//  public Credentials getTempCredentials(){
//      String policyJson = null;
//      try {
//          policyJson = buildUploadDownloadPolicy(bucket);
//          AssumeRoleProvider provider = new AssumeRoleProvider(
//                  url, accessKey, secretKey, 3600, policyJson
//                  , null, null, "roleSessionName", null, null
//          );
//          Credentials credentials = provider.fetch();
//          System.out.println("accessKey:" + credentials.accessKey());
//          System.out.println("secretKey:" + credentials.secretKey());
//          System.out.println("sessionToken:" + credentials.sessionToken());
//          System.out.println(credentials.isExpired());
//          System.out.println("Credentials ok");
//          return credentials;
//      } catch (JsonProcessingException e) {
//          throw new RuntimeException(e);
//      } catch (NoSuchAlgorithmException e) {
//          throw new RuntimeException(e);
//      }
//  }
//    /**
//     * 构建上传下载策略
//     * @param bucketName 存储桶名称
//     * @return JSON格式的策略字符串
//     */
//    private static String buildUploadDownloadPolicy(String bucketName)
//            throws  JsonProcessingException {
//
//        // 创建主策略 Map
//        Map<String, Object> policy = new HashMap<>();
//        policy.put("Version", "2012-10-17");
//
//        // 创建 Statement 列表
//        List<Map<String, Object>> statements = new ArrayList<>();
//
//        // 第一条声明
//        Map<String, Object> statement1 = new HashMap<>();
//        statement1.put("Effect", "Allow");
//
//        Map<String, List<String>> principal1 = new HashMap<>();
//        principal1.put("AWS", List.of("*"));
//        statement1.put("Principal", principal1);
//
//        statement1.put("Action", List.of(
//                "s3:GetBucketLocation",
//                "s3:ListBucketMultipartUploads"
//        ));
//
//        statement1.put("Resource", List.of(
//                "arn:aws:s3:::" + bucketName
//        ));
//
//        // 第二条声明
//        Map<String, Object> statement2 = new HashMap<>();
//        statement2.put("Effect", "Allow");
//
//        Map<String, List<String>> principal2 = new HashMap<>();
//        principal2.put("AWS", List.of("*"));
//        statement2.put("Principal", principal2);
//
//        statement2.put("Action", List.of(
//                "s3:AbortMultipartUpload",
//                "s3:DeleteObject",
//                "s3:ListMultipartUploadParts",
//                "s3:PutObject"
//        ));
//
//        statement2.put("Resource", List.of(
//                "arn:aws:s3:::" + bucketName + "/**"
//        ));
//
//        // 添加所有声明到列表
//        statements.add(statement1);
//        statements.add(statement2);
//
//        // 将声明列表添加到策略
//        policy.put("Statement", statements);
//
//        return new ObjectMapper().writeValueAsString(policy);
//
//    }
//
//
////    /**
////     * 列出所有存储桶名称
////     * @return
////     * @throws Exception
////     */
////    public List<String> listBucketNames()
////            throws Exception {
////        List<Bucket> bucketList = listBuckets();
////        List<String> bucketListName = new ArrayList<>();
////        for (Bucket bucket : bucketList) {
////            bucketListName.add(bucket.name());
////        }
////        return bucketListName;
////    }
////
////    /**
////     * 查看所有桶
////     * @return
////     * @throws Exception
////     */
////    public List<Bucket> listBuckets()
////            throws Exception {
////        return minioClient.listBuckets();
////    }
////
////    /**
////     * 检查存储桶是否存在
////     * @param bucketName
////     */
////    public boolean bucketExists(String bucketName) throws Exception {
////        boolean flag = minioClient.bucketExists(bucketName);
////        if (flag) {
////            return true;
////        }
////        return false;
////    }
////
////    /**
////     * 创建存储桶
////     * @param bucketName
////     */
////    public boolean makeBucket(String bucketName)
////            throws Exception {
////        boolean flag = bucketExists(bucketName);
////        if (!flag) {
////            minioClient.makeBucket(bucketName);
////            return true;
////        } else {
////            return false;
////        }
////    }
////
////    /**
////     * 删除桶
////     * @param bucketName
////     */
////    public boolean removeBucket(String bucketName)
////            throws Exception {
////        boolean flag = bucketExists(bucketName);
////        if (flag) {
////            Iterable<Result<Item>> myObjects = listObjects(bucketName);
////            for (Result<Item> result : myObjects) {
////                Item item = result.get();
////                // 有对象文件，则删除失败
////                if (item.size() > 0) {
////                    return false;
////                }
////            }
////            // 删除存储桶，注意，只有存储桶为空时才能删除成功。
////            minioClient.removeBucket(bucketName);
////            flag = bucketExists(bucketName);
////            if (!flag) {
////                return true;
////            }
////
////        }
////        return false;
////    }
////
////    /**
////     * 列出存储桶中的所有对象
////     * @param bucketName 存储桶名称
////     */
////    public Iterable<Result<Item>> listObjects(String bucketName) throws Exception {
////        boolean flag = bucketExists(bucketName);
////        if (flag) {
////            return minioClient.listObjects(bucketName);
////        }
////        return null;
////    }
////
////    /**
////     * 列出存储桶中的所有对象名称
////     * @param bucketName 存储桶名称
////     */
////    public List<String> listObjectNames(String bucketName) throws Exception {
////        List<String> listObjectNames = new ArrayList<>();
////        boolean flag = bucketExists(bucketName);
////        if (flag) {
////            Iterable<Result<Item>> myObjects = listObjects(bucketName);
////            for (Result<Item> result : myObjects) {
////                Item item = result.get();
////                listObjectNames.add(item.objectName());
////            }
////        }
////        return listObjectNames;
////    }
////
////    /**
////     * 删除一个对象
////     * @param bucketName 存储桶名称
////     * @param objectName 存储桶里的对象名称
////     */
////    public boolean removeObject(String bucketName, String objectName) throws Exception {
////        boolean flag = bucketExists(bucketName);
////        if (flag) {
////            List<String> objectList = listObjectNames(bucketName);
////            for (String s : objectList) {
////                if (s.equals(objectName)) {
////                    minioClient.removeObject(bucketName, objectName);
////                    return true;
////                }
////            }
////        }
////        return false;
////    }
////
////    /**
////     * 文件访问路径
////     * @param bucketName 存储桶名称
////     * @param objectName 存储桶里的对象名称
////     */
////    public String getObjectUrl(String bucketName, String objectName) throws Exception {
////        boolean flag = bucketExists(bucketName);
////        String url = "";
////        if (flag) {
////            url = minioClient.getObjectUrl(bucketName, objectName);
////        }
////        return url;
////    }
//}
