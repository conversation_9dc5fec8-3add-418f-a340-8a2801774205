package com.cb.ai.data.analysis.ai.common.event;

import cn.hutool.core.collection.CollectionUtil;
import com.cb.ai.data.analysis.ai.common.event.model.HistoryChatEvent;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail;
import com.cb.ai.data.analysis.ai.service.IAiChatHistoryDetailService;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.lmax.disruptor.EventHandler;
import com.xong.boot.framework.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:31
 * @Copyright (c) 2025
 * @Description 历史会话事件处理器
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class HistoryEventHandler implements EventHandler<HistoryChatEvent> {

    private final IAiChatHistoryDetailService detailService;

    @Override
    public void onEvent(HistoryChatEvent historyChatEvent, long l, boolean b) throws Exception {
        List<NodeContext> nodeContextList = historyChatEvent.getNodeContextList();
        if (CollectionUtil.isEmpty(nodeContextList)) {
            return;
        }
        NodeContext collect = new NodeContext();
        historyChatEvent.getNodeContextList().forEach(nodeContext -> {
            if (nodeContext.isMergeThinking()) {
                collect.collectThinking(nodeContext.getFullThinking());
            }
            if (nodeContext.isMergeContent()) {
                collect.collectContent(nodeContext.getFullContent());
            }
            collect.collectData(nodeContext.getFullDataList());
        });

        AiChatHistoryDetail detail = new AiChatHistoryDetail();
        detail.setSessionId(historyChatEvent.getSessionId());
        detail.setUserId(SecurityUtils.getUserId());
        detail.setEvent(historyChatEvent.getEvent());
        detail.setRole(historyChatEvent.getRoleEnum().name());
        detail.setReasoningContent(collect.getFullThinking());
        detail.setContent(collect.getFullContent());
        detail.setDataList(JsonUtil.toStr(collect.getFullDataList()));

        NodeContext first = nodeContextList.get(0);
        LocalDateTime responseTime = first.getResponseTime();
        detail.setCreateTime(responseTime);

        if (detailService.saveHistoryDetail(detail)) {
            CommonLog.info("历史会话消息存储成功");
        } else {
            CommonLog.error("历史会话消息存储失败，消息内容：{}", detail);
        }
    }
}

