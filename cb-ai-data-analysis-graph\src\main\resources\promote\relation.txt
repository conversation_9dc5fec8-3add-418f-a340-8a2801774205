你将收到两部分输入：
1. 一个 JSON 模板 #{template}，定义了目标字段名称和层级结构。该模板是动态提供的，请原样读取，但不输出与模板不符的任何字段。
2. 一段原始数据，可能是 JSON 数组、单条 JSON 或任意长文本，其中包含待提取的信息。

请严格按照以下规则，从原始数据中提取信息并填充到模板中，最终只输出一个合法的 JSON：

—— 提取与填充规则（静态部分） ——

1. 仅输出模板中声明的字段：
   • “机构组织企业政府部门信息”
   • “人员信息”
   • “人员关系信息”
   不要新增、删除或更改字段名称与层级。

2. 人员信息中必须包含：
   - 人员ID：为每位人员生成唯一标识（字母+数字，不超过5字符），同一调用内不可重复。
   - name、工作地点、身份证号、职务：若在原文中无法明确获取，则填空字符串 ""。
   - 如果人员有明确的工作地点，请确保這个工作地点被解析到了机构组织企业政府部门信息的分类中

3. 人员关系信息中每条须包含：
   - 关系源人员ID、关系目标人员ID：必须对应已生成的人员ID；
   - 关系描述：尽量精确为两个字（如“上下”、“夫妻”），无法确定时填 ""。

4. 若某个分类下无可提取内容，返回空数组 `[]`。

5. 输出前务必校验并修复 JSON 结构，确保：
   - 所有键和值均以双引号包裹；
   - 无多余或缺失的逗号；
   - 数组和对象都正确闭合；
   - 能通过标准 JSON 校验器。

6. 禁止输出：
   - 任何非 JSON 文本（如解释、注释、Markdown）；
   - 模板以外的字段；
   - 模糊或不明确的内容。

—— 输入示例 ——
#{template}

—— 你的输出 ——
请直接输出填充后、完整合法的 JSON 字符串，没有其他任何多余内容。
