package com.cb.ai.data.analysis.query.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: elasticsearch 查询参数
 * @date 2025/07/03
 */
public interface ElasticReqBo {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Query implements Serializable {
        private static final long serialVersionUID = 1L;
        // elasticsearch 索引名称
        private String indexName;
        // elasticsearch 查询条件
        private String keyword;
        //查询匹配字段 列如："fileName^3", "fileContent^2", "title", "content", "*"；可以增加权重 fieldName^2
        private String[] matchQueryFields;
        // 排序字段
        private String sortField;
        //是否升序排序
        private boolean isSortAsc;
    }

    // CRUD 操作请求实体
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Req implements Serializable {
        private static final long serialVersionUID = 1L;
        // 文档id
        private String documentId;
        // 数据
        private Map<String, Object> data;
    }
}
