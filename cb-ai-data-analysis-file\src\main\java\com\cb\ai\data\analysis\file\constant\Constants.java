package com.cb.ai.data.analysis.file.constant;

import org.springframework.util.unit.DataSize;

/**
 * 监督一体化资源配置常量
 * <AUTHOR>
 */
public class Constants {
    public static final String API_UNI_SUPERVISE_ROOT_PATH = com.xong.boot.common.constant.Constants.API_ROOT_PATH + "/supervise";
    /**
     * 资源文件存放路径
     */
    public final static String FILE_PATH = "/supervise/resources";
    /**
     * 允许上传的资源文件类型
     */
    public final static String[] ALLOW_SUFFIX = {"ALL"};
    /**
     * 单文件允许上传最大大小（单位字节bytes）
     */
    public final static DataSize SINGLE_MAX_SIZE = DataSize.ofMegabytes(2050);
}
