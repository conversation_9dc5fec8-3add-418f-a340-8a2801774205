package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySession;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;
import com.cb.ai.data.analysis.ai.exception.AiException;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistorySessionMapper;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistorySessionMessageMapper;
import com.cb.ai.data.analysis.ai.provider.AiProviderService;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.security.token.UserDetailsAuthenticationToken;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * AI Provider Service
 * <AUTHOR>
 */
@Service
public class AiProviderServiceImpl implements AiProviderService {
    private final AiChatHistorySessionMapper aiChatHistorySessionMapper;
    private final AiChatHistorySessionMessageMapper aiChatHistorySessionMessageMapper;
    private final TaskExecutor taskExecutor;

    public AiProviderServiceImpl(AiChatHistorySessionMapper aiChatHistorySessionMapper, AiChatHistorySessionMessageMapper aiChatHistorySessionMessageMapper, TaskExecutor taskExecutor) {
        this.aiChatHistorySessionMapper = aiChatHistorySessionMapper;
        this.aiChatHistorySessionMessageMapper = aiChatHistorySessionMessageMapper;
        this.taskExecutor = taskExecutor;
    }

    /**
     * 创建用户会话
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param title     会话标题
     */
    @Override
    public String createSession(String sessionId, String userId, String title) {
        if (StringUtils.isBlank(userId)) {
            throw new AiException("用户ID不存在");
        }
        if (StringUtils.isBlank(title)) {
            throw new AiException("会话标题不存在");
        }
        if (StringUtils.isBlank(sessionId)) {
            AiChatHistorySession chatHistorySession = new AiChatHistorySession();
            chatHistorySession.setUserId(userId);
            chatHistorySession.setTitle(title);
            if (SqlHelper.retBool(aiChatHistorySessionMapper.insert(chatHistorySession))) {
                return chatHistorySession.getSessionId();
            }
            throw new AiException("创建会话失败");
        }
        AiChatHistorySession chatHistorySession = aiChatHistorySessionMapper.selectById(sessionId);
        if (chatHistorySession == null) {
            chatHistorySession = new AiChatHistorySession();
            chatHistorySession.setSessionId(sessionId);
            chatHistorySession.setUserId(userId);
            chatHistorySession.setTitle(title);
            if (SqlHelper.retBool(aiChatHistorySessionMapper.insert(chatHistorySession))) {
                return chatHistorySession.getSessionId();
            }
            throw new AiException("创建会话失败");
        }
        return chatHistorySession.getSessionId();
    }

    /**
     * 保存AI历史会话消息
     * @param sessionMessage 历史会话消息
     */
    @Override
    public void saveSessionMessage(AiChatHistorySessionMessage sessionMessage) {
        UserDetailsAuthenticationToken authentication = SecurityUtils.getAuthentication();
        // 把用户消息插入到会话历史内
        taskExecutor.execute(() -> {
            SecurityContextHolder.getContext().setAuthentication(authentication);
            aiChatHistorySessionMessageMapper.insert(sessionMessage);
        });
    }
}
