package com.cb.ai.data.analysis.ai.component.http;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.config.WebClientConfig;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpRequest;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:10
 * @Copyright (c) 2025
 * @Description WebClient客户端
 */
public class WebFluxClient extends BaseClient {
    /** webClient **/
    private final WebClient webClient;

    public WebFluxClient() {
        this.webClient = WebClientConfig.getWebClient();
    }

    @Override
    public <V> Flux<V> executeStream() {
        StopWatch stopWatch = new StopWatch();
        return responseSpec(stopWatch)
            .bodyToFlux(String.class)
            .map(data -> (V) getOnSuccess().apply(data))
            .onErrorResume(e -> Flux.error(getOnError().apply(e)))
            .filter(ObjectUtil::isNotEmpty)
            .subscribeOn(Schedulers.boundedElastic())
            .doFinally(signalType -> {
                stopWatch.stop();
                CommonLog.info("WebFluxClient -> 请求结束，耗时: {} ms", stopWatch.getTotalTimeMillis());
                clear();
            });
    }

    @Override
    public <V> V executeBlock() {
        StopWatch stopWatch = new StopWatch();
        return responseSpec(stopWatch)
            .bodyToMono(String.class)
            .map(data -> (V) getOnSuccess().apply(data))
            .onErrorResume(e -> Mono.error(getOnError().apply(e)))
            .filter(ObjectUtil::isNotEmpty)
            .subscribeOn(Schedulers.boundedElastic())
            .doFinally(signalType -> {
                stopWatch.stop();
                CommonLog.info("WebFluxClient -> 请求结束，耗时: {} ms", stopWatch.getTotalTimeMillis());
                clear();
            })
            .block();
    }

    private WebClient.ResponseSpec responseSpec(StopWatch stopWatch) {
        stopWatch.start();
        CommonLog.info("WebFluxClient -> 请求开始 ------>");
        CommonLog.info("WebFluxClient -> 请求接口地址：{}", getUrl());
        CommonLog.info("WebFluxClient -> 请求头：{}", JsonUtil.toStr(getHeaders().toSingleValueMap()));
        CommonLog.info("WebFluxClient -> 请求体：{}", JsonUtil.toStr(getBody()));
        return webClient
            .method(getMethod())
            .uri(getUrl())
            .headers(headers -> headers.setAll(getHeaders().toSingleValueMap()))
            .body(bodyInserter())
            .accept(MediaType.TEXT_EVENT_STREAM, MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN)
            .retrieve();
    }

    private BodyInserter<?, ? super ClientHttpRequest> bodyInserter() {
        if (isMultiFormData()) {
            Assert.notNull(getFileBody(), "上传文件请求体(fileBody)不能为空");
            return BodyInserters.fromMultipartData(CommonUtil.convertMultiDataToResourceData(getFileBody()));
        } else {
            if (isFormData()) {
                MultiValueMap<String, String> formData = JsonUtil.toBean(getBody(), new TypeReference<LinkedMultiValueMap<String, String>>() {});
                return BodyInserters.fromFormData(formData);
            }
            return BodyInserters.fromValue(getBody());
        }
    }

}
