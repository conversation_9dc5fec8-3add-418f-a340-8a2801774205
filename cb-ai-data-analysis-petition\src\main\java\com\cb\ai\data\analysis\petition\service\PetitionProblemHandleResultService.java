package com.cb.ai.data.analysis.petition.service;

import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.xong.boot.common.service.BaseService;
import jakarta.servlet.http.HttpServletResponse;

/***
 * <AUTHOR>
 * 问题批处理问答结果
 */
public interface PetitionProblemHandleResultService extends BaseService<PetitionProblemHandleResultEntity> {


    void exportQaDetailWord(String problemId, HttpServletResponse response);

    void exportQaDetailExcel(String problemId, HttpServletResponse response);

}
