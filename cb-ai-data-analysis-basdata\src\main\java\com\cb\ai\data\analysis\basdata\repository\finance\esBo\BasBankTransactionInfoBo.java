package com.cb.ai.data.analysis.basdata.repository.finance.esBo;

import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 银行交易信息
 *
 * <AUTHOR>
 * @since 2025-07-14 21:57:31
 */
@Data
@Document(indexName = Constant.ES_FINANCE_DATA_INDEX + Constant.SLICING + "bas_bank_transaction_info")
@Setting(shards = 1, replicas = 0)
public class BasBankTransactionInfoBo extends EsPermBo {
    private static final long serialVersionUID = 1L;
    //主键id
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    //客户名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String customerName;

    //客户编号
    @Field(type = FieldType.Keyword)
    private String customerId;

    //客户账号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String customerAccount;

    //交易卡号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String transactionNumber;

    //交易币种
    @Field(type = FieldType.Keyword)
    private String transactionCurrency;

    //交易日期
    @Field(type = FieldType.Long)
    private LocalDateTime transactionTime;

    //交易类型
    @Field(type = FieldType.Keyword)
    private String transactionType;

    //摘要
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String abstractInfo;

    //借方标志
    @Field(type = FieldType.Keyword)
    private String borrowersSign;

    //交易金额
    @Field(type = FieldType.Double)
    private BigDecimal transactionAmount;

    //交易余额
    @Field(type = FieldType.Double)
    private BigDecimal transactionsBalances;

    //交易机构号
    @Field(type = FieldType.Keyword)
    private String transDeptId;

    //交易机构名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String transDeptName;

    //对方账号
    @Field(type = FieldType.Text)
    private String reciprocalAccount;

    //对方姓名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String reciprocalName;

    //对方户名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String reciprocalAccountName;

    //对方证件号
    @Field(type = FieldType.Keyword)
    private String reciprocalIdNumber;

    //对方行号
    @Field(type = FieldType.Keyword)
    private String reciprocalBankNumber;

    //对方行名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String reciprocalBankName;

    //柜员号
    @Field(type = FieldType.Keyword)
    private String tellerNumber;

    //交易流水号
    @Field(type = FieldType.Keyword)
    private String transactionSerialNo;

    //交易渠道
    @Field(type = FieldType.Keyword)
    private String transactionChannel;

    //交易备注
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String transactionRemark;

    //交易网点代码
    @Field(type = FieldType.Keyword)
    private String tradingOutletCode;

    //交易网点名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String tradingOutletName;

    //现金标志
    @Field(type = FieldType.Keyword)
    private String isCash;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;
}

