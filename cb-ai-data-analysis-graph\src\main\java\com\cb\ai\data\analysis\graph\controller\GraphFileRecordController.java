package com.cb.ai.data.analysis.graph.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.domain.vo.ExtractLabelVo;
import com.cb.ai.data.analysis.graph.domain.vo.RgFileRecordVo;
import com.cb.ai.data.analysis.graph.service.business.GraphFileRecordService;
import com.cb.ai.data.analysis.graph.utils.FileProcessCache;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Validated
@RestController
@RequestMapping(Constants.API_GRAPH_PATH + "/fileRecord")
@Slf4j
public class GraphFileRecordController {

    @Autowired
    private GraphFileRecordService fileRecordService;

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;

    private static ExecutorService executor = Executors.newFixedThreadPool(10);
    @Autowired
    private FileProcessCache fileProcessCache;

    @PostMapping("/uploadFile")
//    public AjaxResult uploadFile(HttpServletRequest request, HttpServletResponse response) {
    public Result uploadFile(HttpServletRequest request, HttpServletResponse response, @RequestParam Boolean precise) {
        Map<String,String> fileNameIdMap = new LinkedHashMap<>();
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            List<MultipartFile> multipartFileList = multipartRequest.getFiles("file");
            try {
                for (int i = 0; i < multipartFileList.size(); i++) {
                    MultipartFile multipartFile = multipartFileList.get(i);
                    if (null != multipartFile && !multipartFile.isEmpty()) {
                        String fileName = multipartFile.getOriginalFilename();
                        SuperviseResourceFolder folder = new SuperviseResourceFolder();
                        folder.setId("0");
                        SuperviseResourceFile superviseResourceFile = superviseResourceFileService.uploadFile(multipartFile, folder, null, null);
                        fileNameIdMap.put(fileName, superviseResourceFile.getId());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("文件存储失败！" + e.getMessage());
            }

        } else {
            throw new RuntimeException("未检测到文件，请选择文件！");
        }
        if (fileNameIdMap.isEmpty()) {
            throw new RuntimeException("未检测到文件，请选择文件！");
        }

        List<GraphFileRecord> recordList = fileNameIdMap.entrySet().stream().map(entry -> {
            GraphFileRecord fileRecord = new GraphFileRecord();
            fileRecord.setId(IdUtil.getSnowflakeNextIdStr());
            fileRecord.setFileName(entry.getKey());
            fileRecord.setFileId(entry.getValue());
            fileRecord.setStatus(0);
            fileRecord.setCreateBy(SecurityUtils.getUsername());
            fileRecord.setCreateTime(LocalDateTime.now());
            return fileRecord;
        }).collect(Collectors.toList());
        //入库
        fileRecordService.saveBatch(recordList);

        String promote = request.getParameter("promote");
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            for (GraphFileRecord fileRecord : recordList) {
                String recordId = fileRecord.getId();
                String fileId = fileRecord.getFileId();
                try {
                    Integer status = fileRecord.getStatus();
                    if (Integer.valueOf(0).equals(status)) {
                        fileRecord.setStatus(1);
                        fileRecordService.updateById(fileRecord);
                        fileRecordService.fileSyncNeo4j(fileRecord, precise, promote);
                    }
                    fileRecord.setStatus(2);
                    log.info("文件同步到neo4j成功！文件id：{}", fileId);
                } catch (Exception e) {
                    log.error("文件同步到neo4j失败！文件id：" + fileId, e);
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    e.printStackTrace(pw);
                    String errLog = sw.toString();
                    if (StringUtils.isNotBlank(errLog) && errLog.length() > 5000) {
                        errLog = errLog.substring(0, 5000);
                    }
                    fileRecord.setStatus(3);
                    fileRecord.setErrLog(errLog);
                } finally {
                    //更新，包括状态和(非实时)计数
                    FileProcessCache.Progress progress = fileProcessCache.getProgress(recordId);
                    if (progress != null) {
                        fileRecord.setTotalCount(progress.getTotalCount());
                        fileRecord.setProcessedCount(progress.getProcessedCount());
                        fileProcessCache.clearProgress(recordId);
                    }
                    fileRecordService.updateById(fileRecord);
                }
            }

        }, executor);

        return Result.success("上传成功，后台解析中...");
    }

    /**
     * 分页查询
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    public Result page(@RequestBody RgFileRecordVo.RgFileRecordPageQueryReq req) {
        Date endTime = req.getEndTime();
        if (null != endTime) {
            Date end = DateUtil.endOfDay(endTime).toJdkDate();
            req.setEndTime(end);
        }
        return Result.successData(fileRecordService.page(req));
    }

    /**
     * 获取关系图谱-上传文件记录信息
     */
    @GetMapping("/detail/{id}")
    public Result detail(@PathVariable("id") Long id) {
        GraphFileRecord record = fileRecordService.getById(id);
        return Result.successData(record);
    }

    /**
     * 下载文件
     *
     * @param request
     * @param response
     * @param id
     */
    @GetMapping("/downloadFile")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response,
                             @RequestParam(value = "id", required = true) String id) throws UnsupportedEncodingException {
        GraphFileRecord record = fileRecordService.getById(id);
        if(null == record || StringUtils.isBlank(record.getFileId())){
            throw new RuntimeException("文件不存在");
        }
        InputStream is = superviseResourceFileService.getFileStream(record.getFileId());

        SuperviseResourceFile file = superviseResourceFileService.getById(record.getFileId());

        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setContentLength(file.getFileSize().intValue());

        String filename = record.getFileName();
        String encodedFilename = URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
        String contentDisposition = "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename;

        response.setHeader("Content-Disposition", contentDisposition);

        // 写入响应流
        try (InputStream inputStream = new BufferedInputStream(is); OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
            os.flush();
        } catch (IOException e) {
            throw new RuntimeException("文件读取或输出异常", e);
        }

    }

    @GetMapping("/extract/graph/labels")
    public Result extractGraphLabels() {
        return Result.successData(fileRecordService.extractGraphLabels());
    }


    @PostMapping("/promote")
    public Result promote(@RequestBody List<ExtractLabelVo> extractLabelVos) {
        try {
            return Result.success("生成成功", fileRecordService.promote(extractLabelVos));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成提示词失败：{}", e.getMessage());
            return Result.fail("生成提示词失败：" + e.getMessage());
        }
    }

}
