package com.cb.ai.data.analysis.ai.component.choreography.engine;


import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.func.TriConsumer;

import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:25
 * @Copyright (c) 2025
 * @Description 节点属性操作
 */
public interface INodeAttributeOperation<E, R> extends INode {
    /**
     * 设置节点名称
     */
    INodeAttributeOperation<E, R> nodeId(String nodeId);
    /**
     * 设置节点名称
     */
    INodeAttributeOperation<E, R> nodeName(String nodeName);
    /**
     * 设置节点名称
     */
    INodeAttributeOperation<E, R> nodeDesc(String nodeDesc);
    /**
     * 设置父节点Id
     */
    INodeAttributeOperation<E, R> parentNodeId(String parentNodeId);
    /**
     * 设置节点请求上下文
     */
    default INodeAttributeOperation<E, R> context(E context) { return context(context, false); }

    default INodeAttributeOperation<E, R> context(Supplier<E> contextFun) { return context(contextFun, false); }

    INodeAttributeOperation<E, R> context(Function<FlowContext, E> requestContextFun);

    INodeAttributeOperation<E, R> context(E context, boolean override);

    INodeAttributeOperation<E, R> context(Supplier<E> contextFun, boolean override);

    /**
     * 设置节点流式数据特殊处理操作
     */
    INodeAttributeOperation<E, R> dataDispose(Function<R, R> disposeFun);

    INodeAttributeOperation<E, R> dataDispose(Consumer<R> disposeFun);

    /**
     * 设置节点数据处理后的处理
     */
    INodeAttributeOperation<E, R> dispose(TriConsumer<String, R, FlowContext> processDataFun);;
    /**
     * 设置节点前置插入数据(按插入顺序执行)
     */
    INodeAttributeOperation<E, R> addBeforeData(BiFunction<NodeContext, E, R> beforeFun);
    /**
     * 设置节点前置插入数据(按插入顺序执行)
     */
    INodeAttributeOperation<E, R> addBeforeData(ResultDataStatusEnum statusEnum, BiFunction<NodeContext, E, R> beforeFun);
    /**
     * 设置节点后置插入数据(按插入顺序执行)
     */
    INodeAttributeOperation<E, R> addAfterData(BiFunction<NodeContext, E, R> afterFun);
    /**
     * 设置节点后置插入数据(按插入顺序执行)
     */
    INodeAttributeOperation<E, R> addAfterData(ResultDataStatusEnum statusEnum, BiFunction<NodeContext, E, R> afterFun);
    /**
     * 设置添加节点不输出（隐藏）思考过程
     */
    INodeAttributeOperation<E, R> hideThinking();
    /**
     * 设置添加节点不输出（隐藏）内容
     */
    INodeAttributeOperation<E, R> hideContent();
    /**
     * 自定义最大token数
     */
    INodeAttributeOperation<E, R> maxTokens(Function<Integer, Integer> maxTokensFun);
    /**
     * 自定义topK数
     */
    INodeAttributeOperation<E, R> topK(Function<Integer, Integer> topKFun);
    /**
     * 自定义topP数
     */
    INodeAttributeOperation<E, R> topP(Function<Float, Float> topPFun);
    /**
     * 自定义温度（采样率）数
     */
    INodeAttributeOperation<E, R> temperature(Function<Float, Float> temperatureFun);

}
