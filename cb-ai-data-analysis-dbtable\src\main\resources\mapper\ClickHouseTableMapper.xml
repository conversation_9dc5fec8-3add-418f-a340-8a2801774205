<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.dbtable.mapper.ClickHouseTableMapper">
    <select id="existSqlTable" resultType="boolean">
        SELECT COUNT(1)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = #{dbName} AND TABLE_NAME = #{tableName}
    </select>

    <select id="existTableData" resultType="boolean">
        SELECT COUNT(1)
        FROM `${tableName}` LIMIT 0,1
    </select>

    <insert id="createTable">
        CREATE TABLE `${tableName}` (
        <foreach collection="columns" item="item" separator=",">${item}</foreach>
        ) ENGINE = MergeTree
        PARTITION BY toYYYYMM(create_time)
        <if test="primaryColumns != null and primaryColumns.size() > 0">
        <foreach collection="primaryColumns" item="item" open="PRIMARY KEY (" separator="," close=")">
            ${item}
        </foreach>
        <foreach collection="primaryColumns" item="item" open="ORDER BY (" separator="," close=")">
            ${item}
        </foreach>
        </if>
        COMMENT #{tableComment}
    </insert>

    <delete id="deleteTable">
        DROP TABLE IF EXISTS `${tableName}`
    </delete>

    <update id="renameTableName">
        RENAME TABLE `${tableName}` TO `${nowTableName}`
    </update>

    <insert id="copyTableData">
        INSERT INTO `${targetTableName}`<foreach collection="names" item="name" open="(" separator="," close=")">${name}</foreach>
        SELECT<foreach collection="names" item="name" separator=",">${name}</foreach> FROM `${sourceTableName}`
    </insert>

    <select id="selectTableColumnName" resultType="string">
        SELECT column_name
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE table_schema = #{dbName} AND table_name = #{tableName}
    </select>

    <select id="selectTableRows" resultType="map">
        SELECT *
        FROM `${tableName}`
    </select>

    <select id="selectTableData" resultType="map">
        SELECT *
        FROM ${tableName}
        <if test="whereSql != null and whereSql != ''">
            WHERE ${whereSql}
        </if>
    </select>

    <select id="selectTableDataById" resultType="map">
        SELECT *
        FROM ${tableName}
        where id = #{id}
    </select>

    <insert id="insertTableData" useGeneratedKeys="false">
        INSERT INTO ${tableName}
        <foreach collection="et" item="item" open="(" separator="," close=")">
            <if test="item.columnValue != null">${item.columnName}</if>
        </foreach>
        <foreach collection="et" item="item" open="VALUES (" separator="," close=")">
            <if test="item.columnValue != null">#{item.columnValue}</if>
        </foreach>
    </insert>

    <delete id="deleteTableData" parameterType="String">
        DELETE FROM ${tableName} WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="addTableData" >
        INSERT INTO ${tableName}
        <foreach index="key" item="value" collection="et" open="(" separator="," close=")">
            ${key}
        </foreach>
        VALUES
        <foreach index="key" item="value" collection="et" open="(" separator="," close=")">
            #{value}
        </foreach>
    </insert>

    <update id="updateTableData" >
        UPDATE ${tableName}
        <set>
            <foreach index="key" item="value" collection="et" separator=",">
                ${key} = #{value}
            </foreach>
        </set>
        WHERE id = #{id}
    </update>

</mapper>
