package com.cb.ai.data.analysis.petition.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.core.task.TaskExecutor;

import java.util.concurrent.*;

/**
 * petition模块专用线程池配置
 */
@Configuration
public class RepeatTaskThreadPoolConfig {
    // 任务前缀
    private static final String THREAD_NAME_PREFIX = "repeatTask-executor-";
    // 核心线程池大小
    private static final int CORE_POOL_SIZE = 10;
    // 最大可创建的线程数
    private static final int MAX_POOL_SIZE = 50;
    // 队列最大长度
    private static final int QUEUE_CAPACITY = 200;
    // 线程池维护线程所允许的空闲时间
    private static final int KEEP_ALIVE_SECONDS = 120;

    @Bean(name = "repeatTaskExecutor")
    public TaskExecutor repeatTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        return executor;
    }

    @Bean(name = "problemFileThreadPoolExecutor")
    public ThreadPoolExecutor problemFileThreadPoolExecutor() {
        int corePoolSize = 1;
        int maximumPoolSize = 1;
        long keepAliveTime = 0L;
        // 使用无界队列，任务会一直等待，不会拒绝
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>();
        // 自定义线程工厂（可选）
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        // 拒绝策略不需要特别配置，因为使用的是无界队列，不会触发拒绝
        RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                workQueue,
                threadFactory,
                handler
        );
    }


    @Bean(name = "databaseThreadPoolExecutor")
    public ThreadPoolExecutor databaseThreadPoolExecutor() {
        int corePoolSize = 1;
        int maximumPoolSize = 1;
        long keepAliveTime = 0L;
        // 使用无界队列，任务会一直等待，不会拒绝
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>();
        // 自定义线程工厂（可选）
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        // 拒绝策略不需要特别配置，因为使用的是无界队列，不会触发拒绝
        RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                workQueue,
                threadFactory,
                handler
        );
    }


    @Bean(name = "workFlowThreadPoolExecutor")
    public ThreadPoolExecutor workFlowThreadPoolExecutor() {
        int corePoolSize = 1;
        int maximumPoolSize = 1;
        long keepAliveTime = 0L;
        // 使用无界队列，任务会一直等待，不会拒绝
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>();
        // 自定义线程工厂（可选）
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        // 拒绝策略不需要特别配置，因为使用的是无界队列，不会触发拒绝
        RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                workQueue,
                threadFactory,
                handler
        );
    }
} 