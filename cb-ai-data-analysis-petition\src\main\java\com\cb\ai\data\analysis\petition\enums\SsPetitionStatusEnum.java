package com.cb.ai.data.analysis.petition.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum SsPetitionStatusEnum {

    UN_ANALYZED(0, "未解析"),
    ANALYZING(1, "解析中"),
    ANALYZED(2, "已解析");


    private final Integer status;

    private final String description;

    public static String getDescription(Integer status) {
        return Arrays.stream(SsPetitionStatusEnum.values()).filter(e -> e.getStatus() == status).findFirst().get().getDescription();
    }
}
