package com.cb.ai.data.analysis.ai.component.choreography.flow;


import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:23
 * @Copyright (c) 2025
 * @Description 空的子流程节点
 */
public class EmptySubFlowNode extends SubFlowNode {

    @Override
    public Flux<ResultData<?>> execute(Object requestContext) {
        return null;
    }
}
