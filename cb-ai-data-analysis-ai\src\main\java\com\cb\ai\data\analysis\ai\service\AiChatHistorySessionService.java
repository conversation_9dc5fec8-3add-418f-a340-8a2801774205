package com.cb.ai.data.analysis.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySession;

import java.util.List;

/**
 * AI历史会话 Service
 * <AUTHOR>
 */
public interface AiChatHistorySessionService extends IService<AiChatHistorySession> {
    /**
     * 删除历史会话
     * @param sessionIds 会话ID
     */
    void removeBySessionId(List<String> sessionIds);
}

