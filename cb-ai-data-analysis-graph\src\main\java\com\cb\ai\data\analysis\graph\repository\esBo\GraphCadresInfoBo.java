package com.cb.ai.data.analysis.graph.repository.esBo;

import com.baomidou.mybatisplus.annotation.*;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.time.LocalDateTime;

/**
 * 知识图谱-干部信息(GraphCadresInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-03 13:50:42
 */
@Data
@Document(indexName = Constant.ES_GRAPH_DATA_INDEX + Constant.SLICING + "graph_cadres_info")
@Setting(shards = 1, replicas = 0)
public class GraphCadresInfoBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    //ID
    @Id
    private String id;

    //工作单位
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String workUnit;

    //姓名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String name;

    //性别
    @Field(type = FieldType.Keyword)
    private String sex;

    //身份证号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String idCard;

    //民族
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String nation;

    //籍贯
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String nativePlace;

    //出生地
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String birthPlace;

    //出生年月
    @Field(type = FieldType.Long)
    private LocalDateTime birthday;

    //政治面貌
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String politicalIdentity;

    //入党时间
    @Field(type = FieldType.Long)
    private LocalDateTime partyJoinTime;

    //参加工作时间
    @Field(type = FieldType.Long)
    private LocalDateTime startWorkTime;

    //现职务
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String workPost;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;

}

