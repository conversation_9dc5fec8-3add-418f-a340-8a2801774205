package com.cb.ai.data.analysis.knowledge.controller;


import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeSuperviseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFileReq;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeSuperviseFileService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件选择管理 因为要跟文件管理联动，故写在admin模块
 */
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/file/supervise")
public class KnowledgeSuperviseFileController {

    @Autowired
    private KnowledgeSuperviseFileService knowledgeSuperviseFileService;

    @GetMapping("/{folderId}")
    public Result getKnowledgeSelectFile(@PathVariable String folderId){
        try{
            return Result.successData(knowledgeSuperviseFileService.getKnowledgeSelectFile(folderId));
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("文件列表获取失败！");
        }
    }

    @PostMapping("/save")
    public Result superviseSaveUpload(@RequestBody KnowledgeFileReq KnowledgeFile){
        try{
            knowledgeSuperviseFileService.superviseSaveUpload(KnowledgeFile);
            return Result.success("提交文件解析任务成功！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("提交文件解析任务失败！");
        }
    }
}
