package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ss_work_flow_job
 * <AUTHOR>
@Data
@TableName("ss_work_flow_job")
public class SsWorkFlowJobEntity implements Serializable {
    private Long id;

    /**
     * 任务名称2
     */
    private String jobName;


    /**
     * 任务完成时间
     */
    private Date finishTime;

    /**
     * 任务类型 1:线索解析 2:报告生成 3:重复线索判定
     */
    private Integer jobType;

    /**
     * 0:进行中 1:任务成功 -1:任务失败
     */
    private Integer jobStatus;

    /**
     * 任务异常信息
     */
    private String errorMessage;

    /**
     * 流程引擎响应内容
     */
    private String workFlowResponse;

    private static final long serialVersionUID = 1L;
}
