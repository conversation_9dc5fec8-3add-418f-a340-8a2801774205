package com.cb.ai.data.json.tool.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.json.tool.domain.JsonAnalysisTask;
import com.cb.ai.data.json.tool.service.JsonAnalysisTaskService;
import com.cb.ai.data.json.tool.service.JsonToolService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping(Constants.API_ROOT_PATH + "/json/tool")
public class JsonAnalysisTaskController extends BaseController<JsonAnalysisTaskService, JsonAnalysisTask> {

    private final JsonToolService jsonToolService;

    @GetMapping("/page")
    public Result page(JsonAnalysisTask task) {
        LambdaQueryWrapper<JsonAnalysisTask> queryWrapper = XQueryWrapper.newInstance(task)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(JsonAnalysisTask::getCreateTime);
        queryWrapper.eq(JsonAnalysisTask::getCreateBy, SecurityUtils.getUsername());
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    @PostMapping("/analysis")
    @XLog(title = "上传Json解析文件", execType = ExecType.UPLOAD)
    public Result analysis(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.fail("请上传文件！");
        }
        // 判断文件是否是.zip格式
        if (!file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
            return Result.fail("请上传.zip格式的文件！");
        }
        try {
            jsonToolService.analysisZipJsonFile(file);
            return Result.success();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail();
        }
    }

    // 重新解析
    @GetMapping("/reAnalyze")
    @XLog(title = "重新解析Json文件", execType = ExecType.OTHER)
    public Result reAnalyze(String taskId) {
        try {
            jsonToolService.reAnalyzeJsonFile(taskId);
            return Result.success();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail();
        }
    }

    @GetMapping("/downloadCompressFile")
    @XLog(title = "下载Json解析文件", execType = ExecType.DOWNLOAD)
    public void downloadCompressFile(HttpServletResponse response, String taskId) {
        jsonToolService.downloadCompressFile(response, taskId);
    }

    /**
     * 删除银行交易信息表
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @DeleteMapping("/deleteAnalysisTask")
    @XLog(title = "删除Json解析任务表", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "解析任务ID不存在") String id) {
        baseService.deleteTask(id);
        return Result.success("Json解析任务删除成功！");
    }


}
