package com.cb.ai.data.analysis.ai.component.choreography.flow;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.cb.ai.data.analysis.ai.common.event.HistoryChatEventProducer;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import com.cb.ai.data.analysis.ai.utils.SecurityContextUtils;
import com.xong.boot.common.exception.CustomException;
import reactor.core.publisher.Flux;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 15:12
 * @Copyright (c) 2025
 * @Description 流程执行链服务
 */
public class FlowChainService {

    private final HistoryChatEventProducer eventProducer;

    public FlowChainService(HistoryChatEventProducer eventProducer) {
        this.eventProducer = eventProducer;
    }

    public Flux<ResultData<?>> execute(FlowChain flowChain) {
        return execute(flowChain, null);
    }

    public Flux<ResultData<?>> execute(FlowChain flowChain, Object requestContext) {
        FlowAttribute flowAttribute = new FlowAttribute();
        FlowContext flowContext = new FlowContext(flowChain.getNodes().size());
        flowAttribute.setFlowContext(flowContext);
        flowAttribute.setInFlowChain(true);
        return execute(flowChain, requestContext, flowAttribute);
    }

    @SuppressWarnings("unchecked")
    public <E> Flux<ResultData<?>> execute(FlowChain flowChain, E requestContext, FlowAttribute flowAttribute) {
        Assert.notNull(flowAttribute, "flowAttribute不能为空");
        String flowId = IdUtil.randomUUID();
        flowAttribute.setTraceId(flowChain.getRequestId());
        FlowContext flowContext = flowAttribute.getFlowContext();
        SecurityContextUtils contextUtils = new SecurityContextUtils();
        return Flux.fromIterable(flowChain.getNodes())
            .concatMap(flowNode ->  contextUtils.withSecurityContext(() -> {
                // 检查流程是否已停止
                if (flowContext.isStop()) {
                    return Flux.empty();
                }
                // 检查是否为并行节点
                if (flowNode instanceof ParallelFlowNode<?> parallelFlowNode) {
                    // 处理并行节点
                    return Flux.merge(
                        ((ParallelFlowNode<E>) parallelFlowNode).getNodes()
                            .parallelStream()
                            .map(node -> contextUtils.withSecurityContext(() -> node.processData(requestContext, flowAttribute)))
                            .toList()
                    );
                } else if (flowNode instanceof ProcessFlowNode processFlowNode) {
                    // 处理流程节点
                    Flux<?> flux = processFlowNode.getNode();
                    if (flux == null) {
                        NodeContext nodeContext = new NodeContext();
                        flux = processFlowNode.getNodeFun().apply(flowContext, nodeContext);
                        flowContext.add(nodeContext);
                    }
                    String nodeId = RandomUtil.randomString(32).toLowerCase();
                    return flux.map(data ->
                        new ResultData<>(data)
                            .setNodeId(nodeId)
                            .setNodeName(OptionalUtil.ofBlankable(processFlowNode.getNodeName()).orElse("自定义流式执行节点"))
                            .setNodeDesc("processFlowNode")
                            .setStatus(ResultDataStatusEnum.STREAMING)
                    )
                    .onErrorResume(e -> {
                        flowContext.stop();
                        return Flux.just(new ResultData<>(ResultDataStatusEnum.ERROR, e));
                    })
                    .concatWith(Flux.just(buildEndResultData(nodeId)));
                }
                else {
                    // 处理普通节点
                    if (flowNode instanceof IFlowProcessNode<?, ?> processNode) {
                        return ((IFlowProcessNode<E, ?>) processNode).processData(requestContext, flowAttribute);
                    } else {
                        return Flux.just(new ResultData<>(ResultDataStatusEnum.ERROR, new CustomException("普通节点必须实现IFlowProcessNode接口")));
                    }
                }
            }))
            .startWith(new ResultData<>().setStatus(flowChain.isSubFlow()? ResultDataStatusEnum.SUB_FLOW_START : ResultDataStatusEnum.FLOW_START).setNodeId(flowId).setRequestId(flowAttribute.getTraceId()))
            .concatWith(Flux.defer(() -> Flux.just(new ResultData<>().setStatus(flowChain.isSubFlow()? ResultDataStatusEnum.SUB_FLOW_END : ResultDataStatusEnum.FLOW_END).setNodeId(flowId).setRequestId(flowAttribute.getTraceId()))));
    }

    @SuppressWarnings("unchecked")
    private <E extends ResultData<?>> E buildEndResultData(String nodeId) {
        return (E) new ResultData<>()
            .setNodeId(nodeId)
            .setNodeName("自定义流式执行节点")
            .setNodeDesc("processFlowNode")
            .setStatus(ResultDataStatusEnum.END);
    }

}
