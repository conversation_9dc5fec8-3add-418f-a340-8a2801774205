package com.cb.ai.data.analysis.graph.service.basic.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphEnterpriseInfo;
import com.cb.ai.data.analysis.graph.mapper.basic.GraphEnterpriseInfoMapper;
import com.cb.ai.data.analysis.graph.repository.GraphEnterpriseInfoRepository;
import com.cb.ai.data.analysis.graph.repository.esBo.GraphEnterpriseInfoBo;
import com.cb.ai.data.analysis.graph.service.basic.GraphEnterpriseInfoService;
import com.cb.ai.data.analysis.graph.utils.GraphUtil;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识图谱-企业信息(GraphEnterpriseInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-04 11:06:10
 */
@Service("graphEnterpriseInfoService")
public class GraphEnterpriseInfoServiceImpl extends ServiceImpl<GraphEnterpriseInfoMapper, GraphEnterpriseInfo> implements GraphEnterpriseInfoService {

    @Autowired
    private GraphEnterpriseInfoRepository repository;

    @Override
    @Transactional
    public boolean save(GraphEnterpriseInfo graphEnterpriseInfo) {
        if (baseMapper.insert(graphEnterpriseInfo) > 0) {
            GraphUtil.handleEnterpriseExcel(List.of(graphEnterpriseInfo));
            GraphEnterpriseInfoBo bo = convert2Bo(graphEnterpriseInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean updateById(GraphEnterpriseInfo graphEnterpriseInfo) {
        if (baseMapper.updateById(graphEnterpriseInfo) > 0) {
            GraphUtil.handleEnterpriseExcel(List.of(graphEnterpriseInfo));
            GraphEnterpriseInfoBo bo = convert2Bo(graphEnterpriseInfo);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        // 从ES 中删除
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<GraphEnterpriseInfo> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            GraphUtil.handleEnterpriseExcel(list);
            List<GraphEnterpriseInfoBo> collect = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }


    private GraphEnterpriseInfoBo convert2Bo(GraphEnterpriseInfo info) {
        GraphEnterpriseInfoBo bo = new GraphEnterpriseInfoBo();
        BeanUtils.copyProperties(info, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }

}

