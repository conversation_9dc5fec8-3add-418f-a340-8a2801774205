package com.cb.ai.data.analysis.talkaudit.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.DocFormatConverter;
import com.cb.ai.data.analysis.talkaudit.domain.TalkPlanReview;
import com.cb.ai.data.analysis.talkaudit.service.TalkPlanReviewService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 谈话方案审查(TalkPlanReview)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-09 14:36:25
 */
@Validated
@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/talk/plan/review")
public class TalkPlanReviewController extends BaseController<TalkPlanReviewService, TalkPlanReview> {

    /**
     * 分页获取谈话方案审查
     *
     * @param talkPlanReview 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(TalkPlanReview talkPlanReview) {
        LambdaQueryWrapper<TalkPlanReview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.or(wrapper -> {
            wrapper.eq(TalkPlanReview::getCreateBy, SecurityUtils.getUsername());
        }).or(wrapper -> {
            wrapper.eq(TalkPlanReview::getDeptId, talkPlanReview.getDeptId());
        });
        ;
        queryWrapper.orderByDesc(TalkPlanReview::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 获取谈话方案审查
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "谈话方案审查ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增谈话方案审查
     *
     * @param talkPlanReview 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增谈话方案审查", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody TalkPlanReview talkPlanReview) {
        talkPlanReview.setDeptId(SecurityUtils.getDeptId());
        if (baseService.save(talkPlanReview)) {
            return Result.success("谈话方案审查新增成功！");
        }
        return Result.fail("谈话方案审查新增失败！");
    }

    /**
     * 批量新增谈话方案审查
     *
     * @param entityList 实体对象
     * @return 新增结果
     */
    @PostMapping("/batch/insert")
    @XLog(title = "批量新增谈话方案审查", execType = ExecType.INSERT)
    public Result batchAdd(@RequestBody @Valid List<@Valid TalkPlanReview> entityList) {
        for (TalkPlanReview talkPlanReview : entityList) {
            talkPlanReview.setDeptId(StrUtil.isNotBlank(SecurityUtils.getDeptId()) ? SecurityUtils.getDeptId() : "-1");
        }
        if (baseService.saveBatch(entityList)) {
            return Result.success("谈话方案审查批量新增成功！");
        }
        return Result.fail("谈话方案审查批量新增失败！");
    }

    /**
     * 修改谈话方案审查
     *
     * @param talkPlanReview 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改谈话方案审查", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody TalkPlanReview talkPlanReview) {
        if (ObjectUtil.isNotNull(talkPlanReview) && StrUtil.isNotBlank(talkPlanReview.getAnalyseResult())) {
            talkPlanReview.setAuditReport("审核报告_" + IdUtil.getSnowflakeNextIdStr() + ".docx");
        }
        if (baseService.updateById(talkPlanReview)) {
            return Result.success("谈话方案审查修改成功！");
        }
        return Result.fail("谈话方案审查修改失败！");
    }

    /**
     * 删除谈话方案审查
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除谈话方案审查", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "谈话方案审查ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.removeByIds(list)) {
            return Result.success("谈话方案审查删除成功！");
        }
        return Result.fail("谈话方案审查删除失败！");
    }


    /**
     * 地区资源文件下载
     */
    @GetMapping("/download")
    public void download(@NotBlank(message = "谈话方案审查ID不存在") String id, HttpServletResponse response) throws IOException {
        TalkPlanReview talkPlanReview = baseService.getById(id);
        if (ObjectUtil.isNull(talkPlanReview)) {
            throw new RuntimeException("对应的谈话方案审查数据不存在！");
        }
        String analyseResult = talkPlanReview.getAnalyseResult();
        if (StrUtil.isBlank(analyseResult)) {
            throw new RuntimeException("对应的谈话方案审查文件未解析完成！");
        }
        String fileName = talkPlanReview.getAuditReport();
        analyseResult = analyseResult.replaceAll("审查报告", "审核报告");
        XWPFDocument xwpfDocument = new XWPFDocument();
        // 分段处理
        analyseResult = analyseResult.replaceAll(" ", "").trim();
        String[] paragraphs = analyseResult.split("\\n");
        for (String line : paragraphs) {
            line = line.trim();
            XWPFParagraph para = xwpfDocument.createParagraph();
            XWPFRun run = para.createRun();
            run.setText(line);
            // 判断是否是第一行
            if (xwpfDocument.getParagraphs().size() == 1) {
                // 添加一个空行
                para = xwpfDocument.createParagraph();
                run = para.createRun();
                run.setText("");
            }
        }
        // 排版
        DocFormatConverter.formatDocument(xwpfDocument, new DocConfig());
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        xwpfDocument.write(response.getOutputStream());
    }

}

