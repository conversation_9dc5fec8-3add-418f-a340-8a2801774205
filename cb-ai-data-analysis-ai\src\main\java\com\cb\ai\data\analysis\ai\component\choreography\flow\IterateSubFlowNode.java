package com.cb.ai.data.analysis.ai.component.choreography.flow;


import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/1 10:15
 * @Copyright (c) 2025
 * @Description 迭代子流程节点
 */
public class IterateSubFlowNode extends SubFlowNode<List<JsonMap>> {

    @Override
    Flux<ResultData<?>> execute(List<JsonMap> requestContext) {
        return Flux.fromIterable(requestContext).flatMap(json -> getSubChain()
            .execute(json, getSubFlowAttribute())
            .doFinally(signalType -> {
                System.out.println(signalType);
            })
        );


    }

}
