package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChain;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateOcrAnalysis;
import com.cb.ai.data.analysis.ai.component.flows.opensource.codes.WordFileAnalysisNode;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.model.PcraGovernmentParam;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.request.context.ExtendCommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.MdPromoteExtractorUtil;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/30 10:59
 * @Copyright (c) 2025
 * @Description 市委办政策合规审查助手（Policy Compliance Review Assistant for the Kunming Municipal Government
 */
@ExtensionProvider(desc = "市委办政策合规审查助手", businessScenes = {
    @Route(tag = "PCRAG", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivatePCRAGovernmentExtension implements PrivateAIExtension<ExtendCommonAIRequestContext<PcraGovernmentParam>> {

    private static final Pattern REGEX = Pattern.compile("(第[一二三四五六七八九十百千万\\d]+条)([\\s\\S]*?)(?=第[一二三四五六七八九十百千万\\d]+条|$)");

    @Override
    public Flux<?> invoke(ExtendCommonAIRequestContext<PcraGovernmentParam> context) {
        FlowChain flowChain = FlowChain.newChain(context.getRequestId());
        // 并行读取文档内容
        AtomicInteger fileCount = new AtomicInteger();
        List<String> fileContents = new ArrayList<>();
        MultiFileData fileData = context.getFileData();
        Assert.notEmpty(fileData, "市委办政策合规审查 -> 上传文件为空");
        fileData.forEach((key, files) ->
            files.forEach(file -> {
                String filename = OptionalUtil.ofBlankable(file.getOriginalFilename()).orElse("");
                String fileType = filename.substring(filename.lastIndexOf(".") + 1);
                if (fileType.matches("^(pdf)$")) {
                    flowChain.addParallelNode(PrivateOcrAnalysis.class, node ->
                        node.context(() -> {
                            CommonAIRequestContext orcContext = new CommonAIRequestContext();
                            orcContext.setFileData(MultiFileData.of(key, file));
                            return orcContext;
                        })
                        .hideContent()
                        .hideThinking()
                        .dispose((nodeId, data, flowContext) -> {
                            fileCount.incrementAndGet();
                            fileContents.add(data.getContent());
                        })
                    );
                } else {
                    flowChain.addParallelNode(WordFileAnalysisNode.class, node -> {
                        fileCount.incrementAndGet();
                        node.context(file).dataDispose((Consumer<String>) fileContents::add);
                    });
                }
            })
        );

        // 条款拆分
        flowChain.addProcessNode("条款拆分", (flowContext, nodeContext) -> {
            flowContext.set(parseClauses(fileContents));
            flowContext.set("fileContents", fileContents);
            return Flux.just(new ResultData<>(JsonMap.of("data", JsonMap.of("process", 100)))
                .setStatus(ResultDataStatusEnum.WAIT)
            );
        })

        // 参考法规获取
        .addParallelNode(PrivateAIChat.class, node ->
            node.context(flowContext -> {
                String mdPath = "promote/swbzchgsc/市委办政策合规审查-参考法规获取.md";
                CommonAIRequestContext aiContext = context.copy();
                aiContext.setSystemPromote(MdPromoteExtractorUtil.replaceSysPromoteTags(mdPath, flowContext.getData()));
                aiContext.setPromote(MdPromoteExtractorUtil.getUserPromote(mdPath));
                return aiContext;
            })
            .hideThinking()
            .dispose((nodeId, data, flowContext) -> {

            })
        )
        // 政策类型/层级+政策简称
        .addParallelNode(PrivateAIChat.class, node ->
            node.context(flowContext -> {
                String mdPath = "promote/swbzchgsc/市委办政策合规审查-政策类型层级简称.md";
                CommonAIRequestContext aiContext = context.copy();
                aiContext.setSystemPromote(MdPromoteExtractorUtil.replaceSysPromoteTags(mdPath, flowContext.getData()));
                aiContext.setPromote(MdPromoteExtractorUtil.getUserPromote(mdPath));
                return aiContext;
            })
            .dispose((nodeId, data, flowContext) -> {
                String json = CommonUtil.repairJson(data.getContent());
                flowContext.set(JsonUtil.toMap(json));
            })
        );

        // 迭代
        //.addProcessNode((flowContext, nodeContext) -> {
        //
        //})

        return null;
    }

    // 条款拆分
    private static Map<String, Object> parseClauses(List<String> text) {
        // 将列表合并为一个整体字符串
        String fullText = String.join("\n", text);
        // 正则表达式模式
        Matcher matcher = REGEX.matcher(fullText);
        List<Map<String, String>> clauses = new ArrayList<>();
        while (matcher.find()) {
            Map<String, String> clause = new HashMap<>();
            clause.put("clause_id", matcher.group(1));
            clause.put("clause_text", matcher.group(2).trim().replace("\n", " "));
            clauses.add(clause);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("clauses", clauses);
        return result;
    }

}
