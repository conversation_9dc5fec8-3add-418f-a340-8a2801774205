package com.cb.ai.data.analysis.dbtable.mapper;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据导入任务Mapper接口
 * <AUTHOR>
 */
public interface AnalysisDbTableTaskMapper extends BaseMapper<AnalysisDbTableTask> {
    /**
     * 查询数据导入工作
     * @param id 数据导入工作主键
     * @return 数据导入工作
     */
    AnalysisDbTableTask selectTableTaskById(String id);

    /**
     * 分页查询数据导入工作
     * @param page
     * @param bigdataPoolJob
     * @return
     */
    Page<AnalysisDbTableTask> pageTableTaskList(Page<?> page, @Param(Constants.ENTITY) AnalysisDbTableTask bigdataPoolJob);

    /**
     * 存在工作日志
     */
    boolean existJobLog(String[] ids);
}
