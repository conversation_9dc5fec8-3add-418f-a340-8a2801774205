package com.cb.ai.data.analysis.graph.utils;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class FileProcessCache {

    // Map<fileRecordId, Progress>
    private final ConcurrentHashMap<String, Progress> progressMap = new ConcurrentHashMap<>();

    public void initProgress(String fileRecordId, int totalCount) {
        progressMap.put(fileRecordId, new Progress(totalCount));
    }

    public void incrementProgress(String fileRecordId, int count) {
        Progress progress = progressMap.get(fileRecordId);
        if (progress != null) {
            progress.increment(count);
        }
    }

    public Progress getProgress(String fileRecordId) {
        return progressMap.get(fileRecordId);
    }

    public void clearProgress(String fileRecordId) {
        progressMap.remove(fileRecordId);
    }

    @Data
    public static class Progress {
        private final int totalCount;
        private final AtomicInteger processedCount = new AtomicInteger(0);

        public Progress(int totalCount) {
            this.totalCount = totalCount;
        }

        public void increment(int count) {
            this.processedCount.addAndGet(count);
        }

        public int getRemaining() {
            return totalCount - processedCount.get();
        }

        public int getProcessedCount() {
            return processedCount.get();
        }
    }
}
