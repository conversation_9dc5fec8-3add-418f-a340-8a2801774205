package com.cb.ai.data.analysis.knowledge.controller;


import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeService;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/***
 * <AUTHOR>
 * 知识库文件管理 因为要跟文件管理联动，故写在admin模块
 */
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/file/upload")
public class KnowledgeController {

    @Autowired
    private KnowledgeService knowledgeService;


    @PostMapping("/save")
    public Result saveupload(@RequestBody KnowledgeBaseFile knowledgeBaseFile){
        try{
            String respMsg=knowledgeService.saveupload(knowledgeBaseFile);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库保存失败！");
        }
    }

    /***
     * 删除知识库文件
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable String id){
        try{
            knowledgeService.delKnowledgeFile(id);
            return Result.success("知识库文件删除成功！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库文件删除失败！");
        }
    }


//    @PostMapping
//    public Result upload(MultipartFile file, String baseId){
//        try {
//            SuperviseResourceFile superviseResourceFile= knowledgeService.upload(file,baseId);
//            return Result.successData(superviseResourceFile);
//        }catch (XServiceException e){
//            e.printStackTrace();
//            return Result.fail(e.getMessage());
//        }catch (Exception e){
//            e.printStackTrace();
//            return Result.fail("文件上传失败");
//        }
//    }


    @GetMapping("/folder/root")
    public Result getResourceFolderRootInfo(){
        try{
            SuperviseResourceFolder superviseResourceFolder=knowledgeService.getResourceFolderRootInfo();
            return Result.successData(superviseResourceFolder);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库删除失败！");
        }
    }

}
