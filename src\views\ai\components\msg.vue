<script lang="ts" setup>
import { DownloadOutlined, FundProjectionScreenOutlined } from '@ant-design/icons-vue'
import {
  type ComponentCustomProperties,
  computed,
  defineModel,
  getCurrentInstance,
  reactive,
  useTemplateRef
} from 'vue'
import J<PERSON>Zip from 'jszip'
import { saveAs } from 'file-saver'
import markdownIt from '@/utils/markdown-it.js'
import { getFileExtension } from '@/utils/fileUtil'
import SqlParseView from './SqlParseView.vue'
import { resourceFile } from '@/api/file/index'
import { chatHistory, report } from '@/api/ai/'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import tabs from './tabs.vue'
import { downloadFile } from '@/api/file/file'
import errorLog from '@/utils/errorLog'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const router = useRouter()
const htmlIframe = useTemplateRef('htmlIframe')
const props = defineProps({
  role: {
    type: Number,
    default: 1
  }
})
const modelContent = defineModel('content', {
  type: Object,
  default: () => ({})
})
const thinkStatusTextArray = computed(() => {
  const statusText =
    (modelContent.value.thinkStatusText ? '【' + modelContent.value.thinkStatusText + '】' : '') +
    '深度研究涉及多轮计划执行，请耐心等待。。。'
  return statusText.split('')
})
const drawer = reactive({
  visible: false,
  key: 0,
  fileName: '',
  fileUrl: '',
  page: undefined,
  traceProperties: undefined
})

const exportInfo = reactive({
  isExporting: false,
  percent: 0,
  status: 'normal'
})

function buildPdfUrl(fileUrl, page) {
  if (!fileUrl) return ''
  let fragment = 'zoom=1'
  if (page) {
    fragment = `page=${page}`
  }
  return `${fileUrl}#${fragment}`
}

window.aonmouseenter = function (el: HTMLElement) {
  const index = Number(el.getAttribute('data-index'))
  const source = (modelContent.value.files || []).find((s) => s.index === index)
  if (source) {
    const position = el.getBoundingClientRect()
    const sourceReferences: HTMLElement = document.getElementById('sourceReferences')
    sourceReferences.onmouseenter = () => {
      sourceReferences.style.display = 'block'
    }
    sourceReferences.onmouseleave = () => {
      sourceReferences.style.display = 'none'
    }
    sourceReferences.textContent = source?.references
    sourceReferences.style.left = `${position.x + position.width / 2}px`
    sourceReferences.style.top = `${position.y + position.height / 2}px`
    sourceReferences.style.display = 'block'
  }
}
window.aonmouseleave = function () {
  const sourceReferences = document.getElementById('sourceReferences')
  sourceReferences.style.display = 'none'
}
window.aClick = async function (el: HTMLElement) {
  const index = Number(el.getAttribute('data-index'))
  const source = (modelContent.value.files || []).find((s) => s.index === index)
  if (source) {
    drawer.key = Date.now().toString()
    if (isPDF(source.sourceFileName)) {
      await openFileByReUrl(source)
      drawer.page = source.page
      drawer.fileName = source.sourceFileName
      drawer.fileUrl = source.fileUrl
    } else if (isHtmlFile(source.sourceFileName)) {
      const res = await openHTMLByUrl(source.sourceFileUrl)
      const blob = new Blob([res], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      drawer.fileUrl = url
      drawer.fileName = source.sourceFileName
      drawer.traceProperties = source.traceProperties
    } else if (isImage(source.sourceFileName)) {
      drawer.fileName = source.sourceFileName
      drawer.fileUrl = await openFile(source, false)
    } else {
      message.info('该文件暂不支持预览，已为您下载')
      return openFile(source, true)
    }

    drawer.visible = true
  }
}

function toMarkdown(str = '') {
  try {
    let replaced = str.replace(/\[\^(\d+)(?:\])?(?=\D|$)/g, (match, index) => {
      const source = modelContent.value.files.find((s) => s.index === Number(index))
      return source
        ? `<a href="javascript:void(0)" onmouseout="aonmouseleave(this)" onmouseenter="aonmouseenter(this)" onclick="aClick(this)"  class="ref-link" data-index="${index}">[^${index}]</a>`
        : ''
    })
    // 单独处理深度研究中的引用文件
    // 按行分割
    const lines = replaced.split('\n') // 单行正则：匹配 [text](url) 或 [text](url（无右括号）
    const resultLines = lines.map((line) => {
      // 在每行中查找并替换所有匹配项
      return line.replace(
        /\[([^\]]+)\]\(([^()]*(?:\([^()]*\)[^()]*)*)\)|\[([^\]]+)\]\(([^()]*(?:\([^()]*\)[^()]*)*)/g,
        (
          match: string,
          fullText: string,
          fullUrl: string,
          partialText: string,
          partialUrl: string
        ) => {
          const textContent = fullText || partialText
          const urlContent = fullUrl || partialUrl
          const escapedPath = urlContent.replace(/"/g, '&quot;').replace(/&/g, '&amp;')
          return `<a href="javascript:void(0)" data-path="${escapedPath}" onclick="downloadFileByReUrl(this)" class="ref-link">【${textContent}】</a>`
        }
      )
    })
    replaced = resultLines.join('\n')
    return markdownIt.render(replaced)
  } catch (err) {
    errorLog.push({ msg: err.message, stack: err.stack, title: 'md转换错误', data: str })
    message.error('md转换错误')
  }
}

// 去除连续重复的角标
function deduplicateConsecutiveSup(html) {
  // 匹配所有连续的 <sup>...<\/sup> 序列
  return html.replace(
    /(?:<sup\s+data-footnote="[^"]+"[^>]*>[\s\S]*?<\/sup>\s*)+/g,
    function (supGroup) {
      const seen = new Set()
      let result = ''
      const supRegex = /<sup\s+data-footnote="([^"]+)"[^>]*>[\s\S]*?<\/sup>/g
      let match

      while ((match = supRegex.exec(supGroup)) !== null) {
        const [fullMatch, footnoteValue] = match
        if (!seen.has(footnoteValue)) {
          seen.add(footnoteValue)
          result += fullMatch
        }
      }

      return result
    }
  )
}

// 导出文件
async function exportFile() {
  try {
    // const converted = await asBlob(toMarkdown(modelContent.value.content))
    // const zip = new JSZip()
    // zip.file(exportMessageFileName, converted)
    // const content = await zip.generateAsync({ type: 'blob' })
    // saveAs(content, `消息记录${Date.now()}.zip`)
    const fileArray = [...files.value]
    exportInfo.isExporting = true
    const exportMessageFileName = `消息记录${Date.now()}.docx`
    let mainContent = modelContent.value.content
    const supIndexArray: string[] = []
    mainContent = mainContent.replace(
      /\[\^(\d+)(?:\])?(?=\D|$)/g,
      (match: string, index: string) => {
        // 拿原文件信息
        const source = modelContent.value.files.find(
          (s: Record<string, any>) => s.index === Number(index)
        )
        if (source) {
          if (!supIndexArray.includes(source.fileId)) {
            supIndexArray.push(source.fileId)
          }
          return `<sup data-footnote="${source.fileName}">${supIndexArray.indexOf(source.fileId) + 1}</sup>`
        } else {
          // 没有的情况要删除掉对应角标
          return ''
        }
      }
    )
    // 单独处理深度研究中的引用文件
    // 按行分割
    const lines = mainContent.split('\n') // 单行正则：匹配 [text](url) 或 [text](url（无右括号）
    const resultLines = lines.map((line: string) => {
      // 在每行中查找并替换所有匹配项
      return line.replace(
        /\[([^\]]+)\]\(([^()]*(?:\([^()]*\)[^()]*)*)\)|\[([^\]]+)\]\(([^()]*(?:\([^()]*\)[^()]*)*)/g,
        (
          match: string,
          fullText: string,
          fullUrl: string,
          partialText: string,
          partialUrl: string
        ) => {
          const textContent = fullText || partialText
          const urlContent = fullUrl || partialUrl
          const escapedPath = urlContent.replace(/"/g, '&quot;').replace(/&/g, '&amp;')
          const suffix = getFileExtension(escapedPath)
          if (suffix) {
            fileArray.push({
              fileName: `${textContent}.${suffix}`,
              fileUrl: urlContent
            })
          }
          return `<a href="javascript:void(0)" data-path="${escapedPath}" onclick="downloadFileByReUrl(this)" class="ref-link">【${textContent}】</a>`
        }
      )
    })
    mainContent = resultLines.join('\n')
    mainContent = deduplicateConsecutiveSup(mainContent)
    mainContent = markdownIt.render(mainContent)
    if (modelContent.value.deepThink && !mainContent) {
      mainContent = toMarkdown(modelContent.value.deepThinkData.originalContent)
    }
    const res = await report.common({
      html: `<div>${mainContent}</div>`,
      fileName: exportMessageFileName,
      whetherFormat: true,
      whetherMarkdown: true
    })
    if (res.code === 200 && res.data) {
      const zip = new JSZip()
      zip.file(exportMessageFileName, res.data)
      exportInfo.percent = 10
      const precent = 90 / fileArray.length
      let relateFolder
      for (const file of fileArray) {
        if (!relateFolder) {
          // 需和后端的引用文件路径一致
          relateFolder = zip.folder('相关引用文件')
        }
        // 跳过出错问题处理
        try {
          let fileUrlRes
          if (file.fileId) {
            if (props.role === 1 || file.fileFrom === 'sys') {
              fileUrlRes = await resourceFile.getDownloadUrlByFilePath({ filePath: file.fileUrl })
            } else {
              fileUrlRes = await chatHistory.getFileAbsUrl(file.fileId)
            }
          } else {
            fileUrlRes = await resourceFile.getDownloadUrlByFilePath({ filePath: file.fileUrl })
          }
          const response = await fetch(fileUrlRes.data)
          const blob = await response.blob()
          relateFolder.file(file.fileName, blob)
          exportInfo.percent += precent
        } catch (err) {
          errorLog.push({ error: err.message, stack: err.stack, title: '导出消息失败', data: file })
        }
      }
      exportInfo.percent = 100
      exportInfo.status = 'success'
      if (fileArray.length > 0) {
        const content = await zip.generateAsync({ type: 'blob' })
        saveAs(content, `消息记录${Date.now()}.zip`)
      } else {
        saveAs(res.data, exportMessageFileName)
      }
    } else {
      _this.$message.error(res.message)
      exportInfo.status = 'exception'
    }
  } catch (err: any) {
    errorLog.push({
      error: err.message,
      stack: err.stack,
      title: '导出消息失败',
      data: modelContent.value
    })
    _this.$message.error(err.message)
    exportInfo.status = 'exception'
  } finally {
    setTimeout(() => {
      exportInfo.isExporting = false
      exportInfo.status = 'normal'
      exportInfo.percent = 0
    }, 2000)
  }
}

function onDrawerClose() {
  drawer.visible = false
  setTimeout(() => {
    drawer.page = undefined
    drawer.traceProperties = undefined
    drawer.fileName = ''
    drawer.fileUrl = ''
    drawer.key = 0
  }, 200)
}

function onHtmlIframeLoad() {
  if (!drawer.traceProperties) return

  const iframe = htmlIframe.value
  if (!iframe?.contentWindow?.document) return

  const { startRow, endRow } = drawer.traceProperties
  const doc = iframe.contentWindow.document
  const rows = doc.querySelectorAll('tr[data-row]')

  if (!rows.length) {
    console.warn('未找到含有 data-row 的 <tr>，无法高亮')
    return
  }

  let firstMatch = null
  rows.forEach((tr) => {
    const rowNum = parseInt(tr.dataset.row)
    if (rowNum >= startRow && rowNum <= endRow) {
      tr.style.backgroundColor = '#fff6c1'
      if (!firstMatch) firstMatch = tr
    }
  })

  if (firstMatch) {
    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

function isImage(fileName) {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName)
}

function isPDF(fileName) {
  return /\.pdf$/i.test(fileName)
}

function isHtmlFile(fileName) {
  return /\.html$/i.test(fileName)
}

const files = computed(() => {
  return (modelContent.value.files || []).reduce((acc, item) => {
    if (acc.some((v) => v.fileId === item.fileId)) {
    } else {
      acc.push(item)
    }
    return acc
  }, [])
})

function openHTML() {
  // 打开新标签页
  const route = router.resolve({
    name: 'dynamicWebPage',
    params: { msg: modelContent.value.content }
  })
  localStorage.setItem('dynamicWebPageMsg', modelContent.value.content)
  window.open(route.href, '_blank', 'noopener,noreferrer')
}

async function openFileByReUrl(file) {
  try {
    const res = await chatHistory.getFileByReUrl(file.sourceFileUrl)
    if (res.code === 200) {
      file.fileUrl = res.data
    } else {
      throw new Error(res.message)
    }
  } catch (err) {
    errorLog.push({
      error: err.message,
      stack: err.stack,
      title: 'openFileByReUrl失败',
      data: file
    })
    message.error(err.message)
  }
}

async function openHTMLByUrl(url) {
  try {
    const res = await chatHistory.getHTMLByUrl(url)
    if (res.code === 200) {
      return res.data
    } else {
      throw new Error(res.message)
    }
  } catch (err) {
    errorLog.push({ error: err.message, stack: err.stack, title: 'openHTMLByUrl失败', data: url })
    message.error(err.message)
  }
}

window.downloadFileByReUrl = async function (e) {
  const path = e.getAttribute('data-path')
  if (path) {
    if (/^http(s?):\/\//.test(path)) {
      window.open(path, '_blank')
    } else {
      const res = await resourceFile.getDownloadUrlByFilePath({ filePath: path })
      downloadFile(res.data, path.split('/').pop())
    }
  }
}

async function openFile(file, isDownload = true) {
  try {
    if (props.role === 1 || file.fileFrom === 'sys') {
      const res = await resourceFile.getDownloadUrlByFilePath({ filePath: file.fileUrl })
      // const res = await resourceFile.getFileRePath(file.fileId)
      if (res.code === 200 && res.data) {
        if (isDownload) {
          window.open(res.data, '_blank')
        }
        return res.data
      } else {
        throw new Error(res.message)
      }
    } else if (props.role === 2) {
      //   AI的文件
      const res = await chatHistory.getFileAbsUrl(file.fileId)
      if (res.code === 200 && res.data) {
        if (isDownload) {
          window.open(res.data, '_blank')
        }
        return res.data
      } else {
        throw new Error(res.message)
      }
    }
  } catch (err) {
    errorLog.push({ error: err.message, stack: err.stack, title: 'openFile失败', data: file })
    message.error(err.message)
  }
}

const getPopupContainer = (trigger: HTMLElement) => {
  return trigger.parentElement
}
</script>

<template>
  <div class="msgContent">
    <template v-if="props.role === 1 || (props.role === 2 && !modelContent.deepThink)">
      <tabs v-if="modelContent.thinking" v-model:hidden="modelContent.unexpand">
        <template v-slot:header>
          <a-spin v-if="modelContent.thinkStatus" />
          {{ modelContent.thinkStatusText }}
        </template>
        <template v-slot:default>
          <div
            v-if="modelContent.thinkText"
            class="thinkText"
            v-html="modelContent.thinkText"
          ></div>
          <a-progress v-if="modelContent.progress" :percent="modelContent.progress" />
        </template>
      </tabs>
      <div class="textMsg" v-html="toMarkdown(modelContent.content)"></div>
    </template>

    <!--  多段思考过程内容展示  -->
    <template v-if="props.role === 2 && modelContent.deepThink">
      <div v-for="(item, index) in modelContent.multipartThink" :key="index" class="multipartThink">
        <tabs v-if="item.thinking" v-model:hidden="item.unexpand">
          <template v-slot:header>
            <a-spin v-if="item.thinkStatus" />
            {{ item.thinkStatusText }}
          </template>
          <template v-slot:default>
            <div v-if="item.thinkText" class="thinkText" v-html="item.thinkText"></div>
          </template>
        </tabs>
      </div>
      <div
        class="textMsg"
        v-html="
          toMarkdown(
            modelContent.deepThinkData.originalContent +
              (modelContent.content ? '\n\n---\n\n' + modelContent.content : '')
          )
        "
      ></div>
    </template>

    <div v-if="files.length > 0" class="fileMsg">
      <div class="fileTxt">文件列表：</div>
      <div v-for="(item, index) in files" :key="item.fileId" class="file" @click="openFile(item)">
        <span class="fileIndex">{{ index + 1 }}</span
        >{{ item.fileName }}
      </div>
    </div>
    <div v-if="modelContent.dbTableInfo?.sql" class="data-show">
      <p>✅查看解析相关数据</p>
      <SqlParseView
        :columnComments="modelContent.dbTableInfo.columnComments"
        :sql="modelContent.dbTableInfo.sql"
      ></SqlParseView>
    </div>
  </div>
  <div
    v-if="
      role === 2 &&
      !modelContent.thinkStatus &&
      (modelContent.content?.trim() || modelContent.deepThinkData?.originalContent?.trim())
    "
    class="operate-gather"
  >
    <a-tooltip
      :get-popup-container="getPopupContainer"
      placement="top"
      title="导出消息内容和相关文件"
    >
      <a-button
        v-if="!exportInfo.isExporting"
        class="download-button"
        type="text"
        @click="exportFile"
        ><DownloadOutlined
      /></a-button>
      <a-progress
        v-else
        :percent="exportInfo.percent"
        :size="30"
        :status="exportInfo.status"
        type="circle"
      />
    </a-tooltip>
    <a-tooltip :get-popup-container="getPopupContainer" placement="top" title="正纪互融">
      <a-button class="operate-button" type="text" @click="openHTML">
        <FundProjectionScreenOutlined />
      </a-button>
    </a-tooltip>
  </div>
  <div v-if="modelContent.deepThink && modelContent.thinkStatus" class="deep-think-loading">
    <a-spin />
    <div class="load-text">
      <span
        v-for="(char, index) in thinkStatusTextArray"
        :key="index"
        :style="{
          animationDelay: `${(index + 1) * 0.4}s`
        }"
        class="load-span"
      >
        {{ char }}
      </span>
    </div>
  </div>
  <div v-if="modelContent.quote?.length || modelContent.error?.length" class="quotes">
    <span v-for="item in modelContent.quote" :key="item" class="quote">{{ item }}</span>
    <span v-for="item in modelContent.error" :key="item" class="quote errorMsg">
      {{ item }}
    </span>
  </div>

  <a-drawer
    v-model:open="drawer.visible"
    destroyOnClose
    title="参考文件预览"
    width="50%"
    @close="onDrawerClose"
  >
    <div class="preview-container">
      <iframe
        v-if="isPDF(drawer.fileName)"
        :key="drawer.key"
        :src="buildPdfUrl(drawer.fileUrl, drawer.page)"
        class="preview-embed"
      />
      <iframe
        v-else-if="isHtmlFile(drawer.fileName)"
        :key="drawer.key"
        ref="htmlIframe"
        :src="drawer.fileUrl"
        class="preview-embed"
        @load="onHtmlIframeLoad"
      />
      <img
        v-else-if="isImage(drawer.fileName)"
        :key="drawer.key"
        :src="drawer.fileUrl"
        class="preview-image"
      />
      <span v-else>暂不支持的文件类型</span>
    </div>
  </a-drawer>
</template>

<style lang="less">
.msgContent {
  width: fit-content;
  max-width: 99%;
  box-sizing: border-box;
  padding: 1em;
  background-color: var(--layout-light-bgcolor);
  box-shadow: 0 2px 10px rgba(16, 171, 175, 0.15);

  .ant-collapse-content-box,
  .ant-collapse-header {
    padding: 0 !important;
  }

  .thinking {
    margin-bottom: 1vh;
  }

  .thinkText {
    width: 100%;
    color: #303030;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    padding: 5px;
    box-sizing: border-box;
    text-align: justify;
    word-break: break-all;
    white-space: break-spaces;
  }

  .textMsg {
    width: 100%;
    text-align: justify;
    word-break: break-all;

    .ref-link {
      color: var(--primary-color);
    }

    :last-of-type {
      margin-bottom: 0;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
      overflow: auto;
    }

    th,
    td {
      border: 1px solid #ccc;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f1f1f1;
      font-weight: bold;
    }

    /* 斑马纹（可选） */
    //tbody tr:nth-child(even) {
    //  background-color: #f9f9f9;
    //}

    pre.hljs {
      padding: 12px 2px 12px 40px !important;
      border-radius: 5px !important;
      position: relative;
      font-size: 14px !important;
      line-height: 22px !important;
      overflow: hidden !important;

      code {
        display: block !important;
        margin: 0 10px !important;
        overflow-x: auto !important;
      }

      .line-numbers-rows {
        position: absolute;
        pointer-events: none;
        top: 12px;
        bottom: 12px;
        left: 0;
        font-size: 100%;
        width: 40px;
        text-align: center;
        letter-spacing: -1px;
        border-right: 1px solid rgba(0, 0, 0, 0.66);
        user-select: none;
        counter-reset: linenumber;

        span {
          pointer-events: none;
          display: block;
          counter-increment: linenumber;

          &:before {
            content: counter(linenumber);
            color: #999;
            display: block;
            text-align: center;
          }
        }
      }

      b.name {
        position: absolute;
        top: 2px;
        right: 50px;
        z-index: 10;
        color: #999;
        pointer-events: none;
      }

      .copy-btn {
        position: absolute;
        top: 2px;
        right: 4px;
        z-index: 10;
        color: #333;
        cursor: pointer;
        background-color: #fff;
        border: 0;
        border-radius: 2px;
      }
    }
  }

  .fileMsg {
    width: 100%;

    .fileTxt {
      border-top: 1px solid #b6b6b6;
      padding-top: 1vh;
      width: 100%;
      font-weight: bold;
      color: var(--layout-light-color);
      margin-bottom: 1vh;
    }

    .file {
      display: block;
      width: 100%;
      margin-bottom: 1vh;
      color: var(--primary-color);
      cursor: pointer;

      &:hover {
        font-weight: bolder;
      }

      .fileIndex {
        margin-right: 10px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .data-show {
    width: 100%;
    margin: 1vh 0;
  }

  .multipartThink {
    margin-bottom: 10px;
  }
}

.quotes {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 5px;
  gap: 5px;

  .quote {
    width: fit-content;
    word-break: break-all;
    white-space: break-spaces;
    text-align: justify;
    font-size: 0.9rem;
    padding: 2px 5px;
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--layout-light-color);
    border-radius: 5px;
  }

  .errorMsg {
    color: var(--error-color);
  }
}

.preview-container {
  height: 100%;
  width: 100%;

  .preview-embed {
    width: 100%;
    height: 100%;
    border: none;
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .preview-download {
    padding: 2rem;
    font-size: 1rem;
  }
}

.operate-gather {
  margin: 10px 0;
  display: flex;
  gap: 5px;

  .download-button {
    font-size: 20px;
    color: rgba(0, 191, 165, 0.8);
    background-color: rgba(255, 255, 255, 0.8);

    &:hover {
      color: rgba(0, 191, 165, 1);
      background-color: rgba(255, 255, 255, 1);
    }
  }

  .operate-button {
    font-size: 20px;
    color: rgba(41, 121, 255, 0.8);
    background-color: rgba(255, 255, 255, 0.8);

    &:hover {
      color: rgba(41, 121, 255, 1);
      background-color: rgba(255, 255, 255, 1);
    }
  }
}

.deep-think-loading {
  margin: 10px 0;
  width: 100%;
  position: relative;
  display: flex;
  gap: 10px;
  align-items: center;

  .load-text {
    height: 50px;
    font-weight: 700;
    letter-spacing: 5px;
    display: flex;
    justify-content: center;
    align-items: center;

    .load-span {
      animation: load-span 2.8s linear infinite;
    }

    @keyframes load-span {
      0% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-5px);
      }
      100% {
        transform: translateY(0);
      }
    }
  }
}
</style>
