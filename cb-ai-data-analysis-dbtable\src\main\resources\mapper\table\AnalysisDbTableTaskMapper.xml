<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableTaskMapper">
    <resultMap type="com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask" id="DynamicTableTaskResult">
        <id property="id" column="id"/>
        <result property="tableId" column="table_id"/>
        <result property="tableName" column="table_name"/>
        <result property="tableComment" column="table_comment"/>
        <result property="sourceFileId" column="source_file_id"/>
        <result property="filename" column="filename"/>
        <result property="sheetNo" column="sheet_no"/>
        <result property="headStart" column="head_start"/>
        <result property="headEnd" column="head_end"/>
        <result property="startRow" column="start_row"/>
        <result property="endRow" column="end_row"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="total" column="total"/>
        <result property="successCount" column="success_count"/>
        <result property="errorCount" column="error_count"/>
        <result property="waitConfirmCount" column="wait_confirm_count"/>
        <result property="notRemarkCount" column="not_remark_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="pageTableTaskList" parameterType="com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask" resultMap="DynamicTableTaskResult">
        SELECT t1.id,
            t1.table_id,
            t2.table_name,
            t2.table_comment,
            t1.source_file_id,
            t3.filename,
            t1.sheet_no,
            t1.head_start,
            t1.head_end,
            t1.start_row,
            t1.end_row,
            t1.start_time,
            t1.end_time,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
            COUNT(t4.id) total,
            SUM(IF(t4.`status`=0, 1, 0)) success_count,
            SUM(IF(t4.`status`=1, 1, 0)) error_count,
            SUM(IF(t4.`status`=2, 1, 0)) wait_confirm_count,
            SUM(IF(t4.`status`=3, 1, 0)) not_remark_count
        FROM analysis_db_table_task t1
        LEFT JOIN analysis_db_table t2 ON t1.table_id = t2.id
        LEFT JOIN supervise_resource_file t3 ON t3.id = t1.source_file_id
        LEFT JOIN analysis_db_table_task_item t4 ON t4.job_id = t1.id
        <where>
            <if test="et.tableName != null and et.tableName != ''">AND t2.table_name LIKE CONCAT('%', #{et.tableName}, '%')</if>
            <if test="et.tableComment != null and et.tableComment != ''">AND t2.table_comment LIKE CONCAT('%', #{et.tableComment}, '%')</if>
            <if test="et.filename != null and et.filename != ''">AND t3.filename LIKE CONCAT('%', #{et.filename}, '%')</if>
            <if test="et.sourceFileId != null and et.sourceFileId != ''">AND t1.source_file_id = #{et.sourceFileId}</if>
            <if test="et.startTime != null">AND t1.start_time = #{et.startTime}</if>
            <if test="et.endTime != null">AND t1.end_time = #{et.endTime}</if>
        </where>
        GROUP BY t1.id
        ORDER BY t1.id DESC
    </select>

    <select id="selectTableTaskById" parameterType="String" resultMap="DynamicTableTaskResult">
        SELECT t1.id,
               t1.table_id,
               t2.table_name,
               t2.table_comment,
               t1.source_file_id,
               t3.filename,
               t1.sheet_no,
               t1.head_start,
               t1.head_end,
               t1.start_row,
               t1.end_row,
               t1.start_time,
               t1.end_time,
               t1.create_by,
               t1.create_time,
               t1.update_by,
               t1.update_time,
               COUNT(t4.id) total,
               SUM(IF(t4.`status`=0, 1, 0)) success_count,
               SUM(IF(t4.`status`=1, 1, 0)) error_count,
               SUM(IF(t4.`status`=2, 1, 0)) wait_confirm_count,
               SUM(IF(t4.`status`=3, 1, 0)) not_remark_count
        FROM analysis_db_table_task t1
        LEFT JOIN analysis_db_table t2 ON t1.table_id = t2.id
        LEFT JOIN supervise_resource_file t3 ON t3.id = t1.source_file_id
        LEFT JOIN analysis_db_table_task_item t4 ON t4.job_id = t1.id
        WHERE t1.id = #{id}
    </select>

    <select id="existJobLog" parameterType="String" resultType="boolean">
        SELECT COUNT(1)
        FROM analysis_db_table_task_item WHERE job_id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
