package com.cb.ai.data.analysis.ai.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryDatasDto {

    @NotEmpty(message = "sql不能为空")
    private String sql;

    /**
     * 请求参数
     */
    private Map<String, Object> params;

    @JsonProperty(QueryHelper.PARAM_NAME_PAGE_CURRENT)
    private Long pageCurrent = 0L;

    @JsonProperty(QueryHelper.PARAM_NAME_PAGE_SIZE)
    private Long pageSize = 10L;
}
