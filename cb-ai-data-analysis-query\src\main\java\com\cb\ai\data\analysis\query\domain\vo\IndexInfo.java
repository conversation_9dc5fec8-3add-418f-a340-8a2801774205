package com.cb.ai.data.analysis.query.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 索引信息类，用于存储和管理Elasticsearch索引的相关元数据。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndexInfo {
    /**
     * 索引的唯一标识符
     */
    private String uuid;

    /**
     * 索引名称
     */
    private String index;

    /**
     * 索引的健康状态（如 green, yellow, red）
     */
    private String health;

    /**
     * 索引的当前状态（如 open, close）
     */
    private String status;

    /**
     * 主分片数量
     */
    private Integer pri;

    /**
     * 副本分片数量
     */
    private Integer rep;

    /**
     * 索引中文档的总数
     */
    private Long docCount;

    /**
     * 已删除的文档数量
     */
    private Long docDeleted;

    /**
     * 索引的总存储大小（包含主分片和副本分片）
     */
    private String storeSize;

    /**
     * 主分片的存储大小
     */
    private String priStoreSize;
}
