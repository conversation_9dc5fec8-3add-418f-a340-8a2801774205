package com.cb.ai.data.analysis.ai.utils;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ClassLoaderUtil;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.json.JsonSanitizer;
import com.xong.boot.common.exception.CustomException;
import io.github.haibiiin.json.repair.JSONRepair;
import io.github.haibiiin.json.repair.JSONRepairConfig;
import io.github.haibiiin.json.repair.RepairFailureException;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/2 12:36
 * @Copyright (c) 2025
 * @Description 通用工具类（不好分类的）
 */
public class CommonUtil {
    private static final Map<Class<?>, SerializedLambda> LAMBDAMAP = new ConcurrentHashMap<>();

    /** 角标正则 **/
    private static final Pattern MARKS_REG = Pattern.compile("(\\[\\^\\d+\\])*");

    /**
     * @description 替换溯源角标
     * @param content 数据
     * @return 替换后的数据
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static String repairJson(String badJson) {
        // 预处理：去除多余空白字符和特殊符号
        badJson = badJson.replaceAll("[\r\n\t]*", "")  // 去除换行符、回车符和制表符
                .replaceAll("\\s+", "")     // 移除多余的空格
                .replaceAll("```(json)?", "") // 去除特殊字符
                .replaceAll("^['“]|['”]$", "")  // 去除首尾可能的多余的'和中文双引号
                .replaceAll("^[（(]|[)）]$", "") // 去除可能的多余()
                .trim();                        // 去除首尾空格

        String correctJSON = null;
        try {
            // 先使用JsonSanitizer进行常规修复
            correctJSON = JsonSanitizer.sanitize(badJson);
        } catch (Exception e) {
            CommonLog.error("Sanitizer工具修复失败，原json：{}；失败原因：{}", badJson, e.getMessage());
        }
        if (correctJSON == null) {
            // 修复失败，尝试使用针对LLM的修复工具修复
            try {
                // 使用JSONRepair修复错误格式的json
                JSONRepairConfig config = new JSONRepairConfig();
                config.enableExtractJSON();
                JSONRepair repair = new JSONRepair(config);
                correctJSON = repair.handle(badJson);
            } catch (RepairFailureException e) {
                CommonLog.error("LLM工具修复失败，原json：{}；失败原因：{}", badJson, e.getMessage());
            }
        }
        // 最后统一使用jackson修复输出
        try {
            // 对修复正常的使用jackson美化为标准json
            ObjectMapper mapper = JsonUtil.getInstance();
            JsonNode node = mapper.readTree(OptionalUtil.ofBlankable(correctJSON).orElse(badJson));
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(node);
        } catch (JsonProcessingException e) {
            // 处理错误
            throw new IllegalArgumentException("jackson格式化失败；失败原因：" + e.getMessage());
        }
    }

    /**
     * @description 替换溯源角标
     * @param content 数据
     * @return 替换后的数据
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceMarks(String content) {
        if (StrUtil.isBlank(content)) {
            return content;
        }
        Matcher matcher = MARKS_REG.matcher(content);
        return matcher.replaceAll("");
    }

    /**
     * @description 转换MultiFileData为Resource的MultiValueMap
     * @param fileData 文件数据
     * @return MultiValueMap 资源
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static MultiValueMap<String, Object> convertMultiDataToResourceData(String key, MultipartFile file) {
        Assert.notNull(file, "CommonUtil.convertMultiDataToResourceData(), file不能为空");
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        formData.add(key, convertMultiFileToResource(file));
        return formData;
    }

    /**
     * @description 转换MultiFileData为Resource的MultiValueMap
     * @param fileData 文件数据
     * @return MultiValueMap 资源
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static MultiValueMap<String, Object> convertMultiDataToResourceData(MultiFileData fileData) {
        Assert.notNull(fileData, "CommonUtil.convertMultiDataToResourceData(MultiFileData fileData), fileData不能为空");
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        fileData.forEach((key, files) ->
            formData.addAll(key, convertMultiFileToResource(files))
        );
        return formData;
    }

    /**
     * @description 转换MultipartFile为Resource
     * @param file 文件数据
     * @return Resource 资源
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static Resource convertMultiFileToResource(MultipartFile file) {
        Assert.notNull(file, "CommonUtil.convertMultiFileToResource(MultipartFile file), file文件不能为空");
        try {
            return new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return StrUtil.removeSuffix(file.getOriginalFilename(), ".dec"); // 保持原始文件名
                }
            };
        } catch (Exception e) {
            throw new CustomException("文件(" + file.getOriginalFilename() + ")-MultipartFile转ByteArrayResource失败", e);
        }
    }

    /**
     * @description 转换List<MultipartFile> 为 List<Resource>
     * @param files 文件数据
     * @return Resource 资源
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static List<Resource> convertMultiFileToResource(List<MultipartFile> files) {
        return files.stream().map(CommonUtil::convertMultiFileToResource).toList();
    }

    /**
     * @description 读取文件流
     * @param filePath 文件路径
     * @return InputStream 文件流
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static InputStream getFileInputStream(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!path.isAbsolute()) {
            ResourceLoader resourceLoader = new DefaultResourceLoader(CommonUtil.class.getClassLoader());
            Resource resource = resourceLoader.getResource("classpath:" + filePath);
            if (!resource.exists()) {
                throw new IllegalArgumentException("Resource not found: " + filePath);
            }
            return resource.getInputStream();
        } else {
            return new FileInputStream(path.toFile());
        }
    }

    /**
     * @description 判断Resources下文件是否存在
     * @param filePath 文件路径
     * @return boolean
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static boolean existsResourcesFile(String filePath) {
        Path path = Paths.get(filePath);
        if (!path.isAbsolute()) {
            ResourceLoader resourceLoader = new DefaultResourceLoader(CommonUtil.class.getClassLoader());
            Resource resource = resourceLoader.getResource("classpath:" + filePath);
            return resource.exists();
        } else {
            return Files.exists(path);
        }
    }

    /**
     * 提取字符串列表的最长公共前缀
     */
    public static String extractCommonPrefix(String... strings) {
        return extractCommonPrefix(ListUtil.toList(strings));
    }

    /**
     * @description 提取字符串列表的最长公共前缀
     * @param strings 字符串列表
     * @return 最长公共前缀
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static String extractCommonPrefix(Collection<String> strings) {
        if (strings == null || strings.isEmpty()) {
            return "";
        }
        // 取第一个字符串作为初始前缀
        String prefix = strings.iterator().next();
        for (String str : strings) {
            // 逐步缩短前缀，直到匹配当前字符串
            while (!str.startsWith(prefix)) {
                prefix = prefix.substring(0, prefix.length() - 1);
                if (prefix.isEmpty()) {
                    return "";
                }
            }
        }
        return prefix;
    }

    /**
     * @description 组装URL
     * @param urls URL数组
     * @return 组装后的URL
     * @createtime 2025/7/2 下午12:37
     * <AUTHOR>
     * @version 1.0
     */
    public static String assemblyUrl(String... urls) {
        if (urls.length > 0) {
            StringBuilder fullUrl = new StringBuilder();
            for (String url : urls) {
                if (StrUtil.isBlank(url)) {
                    continue;
                }
                int len = fullUrl.length();
                if (len > 0) {
                    int index = 0;
                    if (fullUrl.charAt(len - 1) == '/') {
                        index  = 1;
                    }
                    if (url.startsWith("/")) {
                        fullUrl.append(url.substring(index));
                    } else {
                        fullUrl.append(index == 0?  "/" : "").append(url);
                    }
                } else {
                    fullUrl.append(url);
                }
            }
            return fullUrl.toString();
        }
        return null;
    }

    /**
     * @description 判断是否是Lambda接口类
     * @param clazz
     * @return boolean
     * @createtime 2024/12/27 14:30
     * <AUTHOR>
     * @version 1.0
     */
    public static boolean isLambdaClass(Class<?> clazz) {
        return clazz.isInterface() && clazz.getAnnotation(FunctionalInterface.class) != null;
    }

    /**
     * @description 判断Lambda接口类是否继承Serializable接口
     * @param clazz
     * @return boolean
     * @createtime 2024/12/27 14:30
     * <AUTHOR>
     * @version 1.0
     */
    public static boolean isSerLambdaClass(Class<?> clazz) {
        return isLambdaClass(clazz) && Serializable.class.isAssignableFrom(clazz);
    }

    /**
     * @description 获取Lambda接口指定方法名名称
     * @param getter 继承了Serializable接口的Lambda接口对象
     * @return string 方法名称
     * @createtime 2024/12/27 14:30
     * <AUTHOR>
     * @version 1.0
     */
    public static String getLambdaName(Serializable getter) {
        String methodName = getSerializedLambda(getter).getImplMethodName();
        if (methodName.startsWith("is")) {
            methodName = methodName.substring(2);
        } else {
            if (!methodName.startsWith("get") && !methodName.startsWith("set")) {
                throw new IllegalCallerException("Error parsing property name '" + methodName + "'.  Didn't start with 'is', 'get' or 'set'.");
            }
            methodName = methodName.substring(3);
        }
        if (methodName.length() == 1 || methodName.length() > 1 && !Character.isUpperCase(methodName.charAt(1))) {
            methodName = methodName.substring(0, 1).toLowerCase(Locale.ENGLISH) + methodName.substring(1);
        }
        return methodName;
    }

    /**
     * @description 获取实现了Lambda接口的子类
     * @param getter 继承了Serializable接口的Lambda接口对象
     * @return Class
     * @createtime 2024/12/27 14:30
     * <AUTHOR>
     * @version 1.0
     */
    public static Class<?> getLambdaClass(Serializable getter) {
        String className = getSerializedLambda(getter).getImplClass();
        return ClassLoaderUtil.loadClass(className, true);
    }

    /**
     * @description 获取Lambda接口对象的SerializedLambda对象
     * @param getter 继承了Serializable接口的Lambda接口对象
     * @return SerializedLambda
     * @createtime 2024/12/27 14:30
     * <AUTHOR>
     * @version 1.0
     */
    public static SerializedLambda getSerializedLambda(Serializable getter) {
        return MapUtil.computeIfAbsent(LAMBDAMAP, getter.getClass(), (aClass) -> {
            try {
                Method method = getter.getClass().getDeclaredMethod("writeReplace");
                method.setAccessible(Boolean.TRUE);
                return (SerializedLambda) method.invoke(getter);
            } catch (Exception var3) {
                throw new RuntimeException(var3);
            }
        });
    }
}
