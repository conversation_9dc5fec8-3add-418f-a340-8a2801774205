import { computed } from 'vue'
import { defineStore } from 'pinia'
import { lGet, lSet } from '@/utils/storage'
import { type AiTool, AI_TOOLS_KEY, defaultTools } from '@/config/ai.config'

/** AI配置 */
export const useAiAppStore = defineStore('ai-app', () => {
  return {
    /** AI会话工具集 */
    chatTools: computed(() => {
      return lGet(AI_TOOLS_KEY, defaultTools) as AiTool[]
    }),
    /** 设置AI会话工具 */
    setChatTools(data: AiTool[]) {
      if (!data || data.length === 0) {
        return
      }
      lSet(
        AI_TOOLS_KEY,
        data.map(({ id, icon, name, description, hide, role, permission ,fixed,picture}) => {
          return {
            id,
            icon,
            name,
            description,
            hide,
            role,
            fixed,
            picture,
            permission
          }
        })
      )
    },
    /** 重置AI会话工具 */
    resetChatTools() {
      lSet(AI_TOOLS_KEY, defaultTools)
    }
  }
})
