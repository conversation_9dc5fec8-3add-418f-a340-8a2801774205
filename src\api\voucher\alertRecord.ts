import { http } from '@/utils/http'

const prefix = '/api/voucher/alertRecord'

/**
 * 分页获取告警记录列表
 * @param params 查询条件
 */
export function page(params?: Record<string, any>) {
  return http.get(`${prefix}/page`, { params })
}

/**
 * 获取告警记录详情
 * @param id 告警记录ID
 */
export function detail(id: string) {
  return http.get(`${prefix}/detail/${id}`)
}

/**
 * 根据任务ID分页获取告警记录
 * @param taskId 任务ID
 * @param params 查询条件
 */
export function pageByTaskId(taskId: string, params?: Record<string, any>) {
  return http.get(`${prefix}/pageByTaskId/${taskId}`, { params })
}

/**
 * 根据凭证ID获取告警详情
 * @param voucherId 凭证ID
 */
export function detailByVoucherId(voucherId: string) {
  return http.post(`${prefix}/detailByVoucherId/${voucherId}`)
}
