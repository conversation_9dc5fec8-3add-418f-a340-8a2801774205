package com.cb.ai.data.analysis.file.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SuperviseResourceFolder  extends BaseDomain {
    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 父级ID
     */
    private String parentId;

    /**
     * 全路径
     */
    private String fullPath;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 排序
     */
    private Integer sortOn;

    /**
     * 状态（0正常 1禁用）
     */
    private Integer status;

    /**
     * 删除标志（0正常 1删除）
     */
    @JsonIgnore
    @TableLogic
    private Boolean delFlag;

    /**
     * 原路径
     */
    private String originalPath;
}
