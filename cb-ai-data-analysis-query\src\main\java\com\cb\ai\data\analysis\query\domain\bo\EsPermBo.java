package com.cb.ai.data.analysis.query.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsPermBo implements Serializable {
    private static final long serialVersionUID = 1L;
    @Field(type = FieldType.Keyword)
    private String deptId;

    @Field(type = FieldType.Keyword)
    private String districtId;
}
