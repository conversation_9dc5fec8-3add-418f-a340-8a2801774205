package com.cb.ai.data.analysis.ai.config;

import org.apache.commons.pool2.impl.BaseObjectPoolConfig;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/1/7 15:36
 * @Copyright (c) 2025
 * @Description 对象池配置
 */
public class ObjectPoolConfig {
    public static <T> GenericObjectPoolConfig<T> genericObjectPoolConfig() {
        // 池对象参数配置
        GenericObjectPoolConfig<T> poolConfig = new GenericObjectPoolConfig<T>();
        // 最大空闲数
        poolConfig.setMaxIdle(5);
        // 最小空闲数, 池中只有一个空闲对象的时候，池会在创建一个对象，并借出一个对象，从而保证池中最小空闲数为0
        poolConfig.setMinIdle(0);
        // 最大池对象总数
        poolConfig.setMaxTotal(100);
        // 设置基础配置
        buildPoolConfig(poolConfig);
        return poolConfig;
    }

    public static <T> GenericKeyedObjectPoolConfig<T> keyedObjectPoolConfig() {
        GenericKeyedObjectPoolConfig<T> poolConfig = new GenericKeyedObjectPoolConfig<T>();
        // 最大空闲数
        poolConfig.setMaxIdlePerKey(5);
        // 最小空闲数, 池中只有一个空闲对象的时候，池会在创建一个对象，并借出一个对象，从而保证池中最小空闲数为0
        poolConfig.setMinIdlePerKey(0);
        // 最大池对象总数
        poolConfig.setMaxTotal(33);
        // 每个key最大实例化数量
        poolConfig.setMaxTotalPerKey(10);
        // 设置基础配置
        buildPoolConfig(poolConfig);
        return poolConfig;
    }

    private static <T> void buildPoolConfig(BaseObjectPoolConfig<T> poolConfig) {
        // 在获取对象的时候检查有效性, 默认false
        poolConfig.setTestOnBorrow(true);
        // 在归还对象的时候检查有效性, 默认false
        poolConfig.setTestOnReturn(true);
        // 在空闲时检查有效性, 默认false
        poolConfig.setTestWhileIdle(true);
        // 最大等待时间， 默认的值为-1，表示无限等待。
        poolConfig.setMaxWait(Duration.ofMillis(5000L));
        // 连接耗尽时是否阻塞, false报异常, true阻塞直到超时, 默认true
        poolConfig.setBlockWhenExhausted(true);
        // 每次逐出检查时 逐出的最大数目 默认3
        poolConfig.setNumTestsPerEvictionRun(3);
        // 取消jmx监控
        poolConfig.setJmxEnabled(false);
    }
}
