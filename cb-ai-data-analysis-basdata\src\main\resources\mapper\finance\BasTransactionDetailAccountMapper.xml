<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.basdata.mapper.finance.BasTransactionDetailAccountMapper">
    <resultMap type="com.cb.ai.data.analysis.basdata.domain.entity.finance.BasTransactionDetailAccount"
               id="BasTransactionDetailAccountMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="accountsCode" column="accounts_code" jdbcType="VARCHAR"/>
        <result property="accountsName" column="accounts_name" jdbcType="VARCHAR"/>
        <result property="transactionTime" column="transaction_time" jdbcType="TIMESTAMP"/>
        <result property="businessTime" column="business_time" jdbcType="TIMESTAMP"/>
        <result property="voucherNumber" column="voucher_number" jdbcType="VARCHAR"/>
        <result property="abstractInfo" column="abstract_info" jdbcType="VARCHAR"/>
        <result property="businessNumber" column="business_number" jdbcType="VARCHAR"/>
        <result property="paymentsWay" column="payments_way" jdbcType="VARCHAR"/>
        <result property="paymentsNumber" column="payments_number" jdbcType="VARCHAR"/>
        <result property="oppositeAccounts" column="opposite_accounts" jdbcType="VARCHAR"/>
        <result property="joinVoucherNumber" column="join_voucher_number" jdbcType="VARCHAR"/>
        <result property="debit" column="debit" jdbcType="NUMERIC"/>
        <result property="creditMount" column="credit_mount" jdbcType="NUMERIC"/>
        <result property="direction" column="direction" jdbcType="VARCHAR"/>
        <result property="balance" column="balance" jdbcType="NUMERIC"/>
        <result property="referenceInfo" column="reference_info" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

