package com.cb.ai.data.analysis.ai.component.choreography.flow;


import cn.hutool.core.collection.CollectionUtil;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:25
 * @Copyright (c) 2025
 * @Description 流程执行节点接口
 */
public interface IFlowProcessNode<E, R> extends IFlowNode {
    /**
     * 流程节点的处理方法（流式）
     * @return 处理结果
     */
    default Flux<ResultData<R>> processData(E requestContext) {return processData(requestContext, null); }

    /**
     * 流程节点的处理方法（流式）
     * @return 处理结果
     */
    default Flux<ResultData<R>> processData(FlowAttribute flowAttribute) {return processData(null, flowAttribute); }

    /**
     * 流程节点的处理方法（流式）
     * @param context 请求上下文
     * @return 处理结果
     */
    Flux<ResultData<R>> processData(E context, FlowAttribute flowAttribute);

    /**
     * 流程节点的处理方法（非流式）
     * @return 处理结果
     */
    default ResultData<R> syncProcessData(E requestContext) {return syncProcessData(requestContext, null); }

    /**
     * 流程节点的处理方法（非流式）
     * @return 处理结果
     */
    default ResultData<R> syncProcessData(FlowAttribute flowAttribute) {return syncProcessData(null, flowAttribute); }

    /**
     * 流程节点的处理方法（非流式）
     * @param context 请求上下文
     * @return 处理结果
     */
    default ResultData<R> syncProcessData(E context, FlowAttribute flowAttribute) {
        List<ResultData<R>> resultDataList = processData(context, flowAttribute).collectList().publishOn(Schedulers.boundedElastic()).block();
        if (CollectionUtil.isNotEmpty(resultDataList)) {
            return resultDataList.get(0);
        }
        return new ResultData<>();
    };
}
