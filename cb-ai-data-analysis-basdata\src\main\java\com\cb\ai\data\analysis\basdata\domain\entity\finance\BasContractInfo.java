package com.cb.ai.data.analysis.basdata.domain.entity.finance;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同信息表(BasContractInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:55:36
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class BasContractInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //主键
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //部门id
    @ExcelIgnore
    private String deptId;

    //区域编码
    @ExcelIgnore
    private String areaCode;

    //合同编号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "合同编号")
    private String contractNumber;

    //合同名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "合同名称")
    private String contractName;

    //合同类型
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "合同类型")
    private String contractType;

    //签订日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "签订日期")
    private LocalDateTime signingDate;

    //合同开始日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "合同开始日期")
    private LocalDateTime startTime;

    //合同结束日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "合同结束日期")
    private LocalDateTime endTime;

    //合同状态
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "合同状态")
    private String status;

    //甲方名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "甲方名称")
    private String partA;

    //乙方名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "乙方名称")
    private String partB;

    //联系人
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "联系人")
    private String telName;

    //电话
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "电话")
    private String telPhone;

    //合同标的
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "合同标的")
    private String contractObject;

    //合同金额
    @ExcelProperty(value = "合同金额")
    private BigDecimal contractAmount;

    //付款条款
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "付款条款")
    private String payItem;

}

