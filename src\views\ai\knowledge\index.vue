<script lang="ts" name="AiKnowledge" setup>
import { useKnowledgeStore } from '@/stores'
import edit from './edit.vue'
import { createVNode, reactive, useTemplateRef } from 'vue'
import XIcon from '@/components/XIcon/XIcon'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import * as base from '@/api/knowledge/base'
import BaseFileComp from '@/views/knowledge/file/BaseFile.vue'
import searchInput from '../components/search.vue'
import { useRouter } from 'vue-router'
import errorLog from '@/utils/errorLog'

const router = useRouter()
const knowledgeStore = useKnowledgeStore()
const editRef = useTemplateRef('editRef')
const fileAttrs = reactive<Record<string, any>>({
  visible: false,
  knowledgeId: undefined,
  permission: undefined
})
const listConfig = reactive({
  list: [],
  done: false,
  total: 0,
  loading: false,
  query: {
    pageCurrent: 0,
    pageSize: 50
  }
})

function open(data: any) {
  editRef.value?.init({ id: data.id })
}

function del(data: any, index) {
  Modal.confirm({
    title: '警告',
    icon: createVNode(ExclamationCircleOutlined),
    content: '是否删除该数据',
    onOk() {
      return base.del([data.id]).then((res) => {
        if (res.code === 200) {
          listConfig.list.splice(index, 1)
          knowledgeStore.getKnowledge({ force: true } as object)
        }
        return res
      })
    }
  })
}

function toFiles(item: any) {
  fileAttrs.knowledgeId = item.id
  fileAttrs.visible = true
  fileAttrs.permission = item.permissionMark
  // router.push({ name: 'aiKnowledgeFile', params: { id: item.id } })
}

function onsearch() {
  router.push({ name: 'AiSearch' })
}

function successTotal(item) {
  return item.countResult?.reduce((total, item) => {
    if (item.processStatus === -1) {
      total += item.sumCount
    }
    return total
  }, 0)
}

function refreshPage1() {
  listConfig.done = false
  loadMore({ pageCurrent: 0 })
}
async function loadMore(query) {
  if (listConfig.loading || listConfig.done) {
    return
  }
  const newQuery = {
    ...listConfig.query,
    ...query,
    isShowFileStatus: true
  }
  newQuery.pageCurrent++
  listConfig.loading = true
  const res = await base.page(newQuery)
  listConfig.loading = false
  listConfig.query = newQuery
  if (res.code === 200) {
    listConfig.total = res.data.total
    if (newQuery.pageCurrent === 1) {
      listConfig.list = res.data.records || []
    } else {
      listConfig.list.push(...res.data.records)
    }
    if (listConfig.list.length >= res.data.total) {
      listConfig.done = true
    }
  } else {
    errorLog.push({ msg: res.message, stack: 'loadMore103', title: '知识库列表失败', data: res })
    message.error(res.message)
  }
}

loadMore()
knowledgeStore.getKnowledge()
</script>
<template>
  <div class="aiKnowledge">
    <searchInput title="全文检索" @onsearch="onsearch" />
    <div v-loadMore="loadMore" class="scrollView scrollBeauty">
      <div class="item create">
        <div class="titleHeader">知识库</div>
        <div class="subTitle">主题知识：上传、分类、分级，助您构建清晰体系…</div>
        <div class="btn">
          <a-button class="title" type="primary" @click="open({})">创建知识库</a-button>
        </div>
      </div>
      <div
        v-for="(item, index) in listConfig.list"
        :key="item.id"
        class="item knowledge"
        @click="toFiles(item)"
      >
        <img class="icon" src="/images/icon-color-upload-document.png" />
        <div class="content">
          <div class="titleHeader">
            <span v-if="item.tag" class="tag">{{ item.tag }}</span>
            {{ item.name }}
            <a-dropdown>
              <x-icon class="more" type="#icon-more" @click.prevent></x-icon>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="open(item)">编辑</a-menu-item>
                  <a-menu-item @click="del(item, index)">删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="count">
            文件数量:{{ item.fileCount }}&nbsp;解析失败:{{ successTotal(item) }}
          </div>
          <div class="desc">{{ item.remark }}</div>
        </div>
      </div>
    </div>
    <edit ref="editRef" @refresh="refreshPage1" />
    <BaseFileComp
      v-model:visible="fileAttrs.visible"
      :knowledgeId="fileAttrs.knowledgeId"
      :permission="fileAttrs.permission"
      @success="refreshPage1"
    />
  </div>
</template>

<style lang="less">
@import '@/assets/styles/utils';

.aiKnowledge {
  width: .px2vw(1460) [ @vw];
  min-width: 960px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  padding-top: var(--headerSize);
  flex-direction: column;
  box-sizing: border-box;

  .scrollView {
    width: 100%;
    height: 0;
    overflow: auto;
    flex-grow: 1;
    padding-right: 5px;
    @spacing: .px2vh(30) [ @vh];
    margin: @spacing 0;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: stretch;
    align-content: flex-start;

    .item {
      width: 340px;
      box-sizing: border-box;
      padding-inline: .px2vw(20) [ @vw];
      padding-block: .px2vh(20) [ @vh];
      border-radius: 10px;
      backdrop-filter: blur(10px);
      background-color: #ffffffaa;
    }

    .knowledge {
      position: relative;
      display: flex;
      align-items: flex-start;
      cursor: pointer;

      &:hover {
        background-color: var(--layout-light-bgcolor);
      }

      .icon {
        display: inline-block;
        width: 60px;
        flex-shrink: 0;
      }

      .content {
        height: 100%;
        flex-grow: 1;
        display: inline-flex;
        width: 0;
        margin-left: 20px;
        box-sizing: border-box;
        flex-direction: column;

        .titleHeader {
          width: 100%;
          font-weight: bold;
          font-size: 1.2rem;
          color: var(--layout-light-color);
          display: flex;
          flex-wrap: nowrap;
          align-items: flex-start;

          .tag {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 1rem;
            color: var(--layout-dark-color);
            border-radius: 10px 10px 10px 2px;
            padding: 4px 18px;
            background: linear-gradient(149.09deg, #07fefe 0%, #102baf 100%);
          }
        }

        .count {
          margin-top: .px2vh(10) [ @vh];
        }

        .desc {
          margin-top: .px2vh(10) [ @vh];
          width: 100%;
          color: var(--layout-light-color);
          font-size: 0.8rem;
        }
      }

      .more {
        margin: 0 0 0 auto;
      }
    }

    .create {
      background: linear-gradient(58.84deg, #f1f4fd 0%, #f3f9ff00 100%);
      border: 1px solid #c9dbfd;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      row-gap: .px2vh(10) [ @vh];

      .titleHeader {
        font-weight: bold;
        font-size: 1.2rem;
        color: var(--layout-light-color);
      }

      .subTitle {
        color: var(--layout-light-color);
        font-size: 0.8rem;
      }

      .btn {
        width: fit-content;
      }
    }
  }
}
</style>
