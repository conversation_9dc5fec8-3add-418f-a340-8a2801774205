package com.cb.ai.data.analysis.file.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SuperviseResourcePermission  extends BaseDomain {
    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 权限主体类型（1用户 2部门 3角色 4职务）
     */
    private String subjectType = "1";

    /**
     * 权限主体ID
     */
    private String subjectId;

    /**
     * 权限对象类型（1文件 2文件夹）
     */
    private Integer objectType = 1;

    /**
     * 权限对象ID（文件ID或文件夹ID）
     */
    private String objectId;

    /**
     * 权限类型（r读 w写 d删 s分享）
     */
    private String permissionType;

    /**
     * 状态（0正常 1禁用）
     */
    private Integer status = 0;
}
