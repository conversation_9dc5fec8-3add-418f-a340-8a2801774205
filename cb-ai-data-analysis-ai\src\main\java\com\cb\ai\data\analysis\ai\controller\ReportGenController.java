package com.cb.ai.data.analysis.ai.controller;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.cb.ai.data.analysis.ai.domain.request.HtmlToWordRequest;
import com.cb.ai.data.analysis.ai.service.IReportService;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.exception.CustomException;
import com.xong.boot.common.utils.FileUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.io.OutputStream;

@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/report")
public class ReportGenController {

    @Resource
    private IReportService reportService;

    /**
     * 获取模板列表
     * @return
     */
    @GetMapping("/listTpl")
    public Result listTpl(){
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            org.springframework.core.io.Resource[] resources = resolver.getResources("classpath*:templates/report/纪委/*.docx");
            JSONArray result = new JSONArray();
            for (org.springframework.core.io.Resource resource : resources) {
                String fileName = resource.getFilename();
                if (fileName != null) {
                    //String templateName = fileName.substring(0, fileName.lastIndexOf('.'));
                    result.add(fileName);
                }
            }
            return Result.successData(result);
        } catch (Exception e) {
            throw new CustomException("获取模板列表失败", e);
        }
    }

    @PostMapping("/common")
    public void common(HttpServletRequest request, HttpServletResponse response, @RequestBody HtmlToWordRequest htmlToWordRequest) {
        try {
            HtmlToWordRequest wordRequest = new HtmlToWordRequest(CommonUtil.replaceMarks(htmlToWordRequest.html()), htmlToWordRequest.fileName(), htmlToWordRequest.whetherFormat(), htmlToWordRequest.whetherMarkdown());
            String filePath = reportService.htmlToDocx(request, wordRequest);
            try (
                InputStream inputStream = FileUtils.getInputStream(filePath);
                OutputStream outputStream = response.getOutputStream();
            ) {
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + StrUtil.subAfter(filePath, "/", true));
                response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
                response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
                response.setHeader(HttpHeaders.PRAGMA, "no-cache");
                response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache");
                inputStream.transferTo(outputStream);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
