package com.cb.ai.data.analysis.voucher.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTask;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherInfoVo;
import com.xong.boot.common.service.BaseService;

import java.time.LocalDateTime;
import java.util.Set;

public interface VoucherInfoService extends BaseService<VoucherInfo> {

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    Page<VoucherInfo> pageByEntity(Page<VoucherInfo> page, VoucherInfoVo.PageReq req);

    /**
     * 异步对凭证文件进行ocr解析
     * @param ids
     * @param force 是否不管是否已经解析过了，强制解析
     */
    void asyncOcrByIds(Set<String> ids, boolean force);

    /**
     * 异步对凭证文件进行ocr解析
     * @param id
     */
    void asyncAnalysisById(String id);

    /**
     * 根据任务和凭证ID进行解析
     * @param rules 分析规则
     * @param task
     * @param id
     * @return 正常返回 true,有预警信息返回false
     */
     boolean analysisByTaskAndId(String rules, VoucherTask task, String id);

    /**
     * 根据标签和日期统计凭证数量
     * @param tags
     * @param now
     * @return
     */
    long countByTagsAndDate(String[] tags, LocalDateTime now);

    /**
     * 根据标签和日期分页查询凭证
     * @param page
     * @param tags
     * @param now
     * @return
     */
    Page<VoucherInfo> pageByTagsAndDate(Page<VoucherInfo> page, String[] tags, LocalDateTime now);

}
