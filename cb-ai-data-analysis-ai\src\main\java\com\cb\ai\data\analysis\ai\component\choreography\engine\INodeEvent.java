package com.cb.ai.data.analysis.ai.component.choreography.engine;

import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 23:39
 * @Copyright (c) 2025
 * @Description 节点事件
 */
public interface INodeEvent<R> extends INode {
    /**
     * 是否为思考过程
     * @param  rawData 每次发射的数据
     * @createtime 2025/7/19 下午11:44
     * <AUTHOR>
     * @version 1.0
     */
    boolean isThinking(R rawData);

    /**
     * 是否为内容
     * @param  rawData 每次发射的数据
     * @createtime 2025/7/19 下午11:44
     * <AUTHOR>
     * @version 1.0
     */
    boolean isContent(R rawData);

    /**
     * 数据收集事件
     * @param rawData 每次发射的数据
     * @param nodeContext 收集的节点上下文
     * @createtime 2025/7/19 下午11:44
     * <AUTHOR>
     * @version 1.0
     */
    default void collectNodeData(R rawData, NodeContext nodeContext) {}

    /**
     * 节点上下文转数据
     * @param nodeContext 收集的节点上下文
     * @createtime 2025/7/19 下午11:44
     * <AUTHOR>
     * @version 1.0
     */
    default R nodeContextToData(NodeContext nodeContext) { return null; }

}
