package com.cb.ai.data.analysis.knowledge.domain.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/***
 * <AUTHOR>
 * 聊天问答请求实体
 */
@Data
public class KnowledgeChat extends ChatAiConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     * 提示词
     */
    private String promote;

    /***
     * 知识库ID
     */
    private List<String> baseIds;

    /***
     * 
     */
    private Boolean stream;
}
