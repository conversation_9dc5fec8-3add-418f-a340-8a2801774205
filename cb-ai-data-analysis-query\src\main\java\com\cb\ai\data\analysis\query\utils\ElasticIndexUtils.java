package com.cb.ai.data.analysis.query.utils;

import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch.indices.PutMappingRequest;
import com.cb.ai.data.analysis.query.domain.bo.DynamicFieldMapping;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import com.xong.boot.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 索引工具类
 */
@Slf4j
public class ElasticIndexUtils {

    // 默认分词器
    public static final String[] PARTICIPLE = {"ik_smart", "ik_max_word"};
    // 默认字段类型
    public static final String[] DEFAULT_FIELD_TYPE = {"Text", "Keyword", "Integer", "Long", "Float", "Double", "Date", "Boolean", "Binary", "Nested", "Object"};

    /**
     * 构建索引字段映射
     *
     * @param builder
     * @param mappings
     */
    public static void buildFieldMapping(TypeMapping.Builder builder, List<DynamicFieldMapping> mappings) {
        // 添加权限字段
        addPermFieldMapping(mappings);
        mappings.forEach(mapping -> {
            String fieldName = mapping.getFieldName();
            if (StringUtils.isNotBlank(fieldName)) {
                builder.properties(fieldName, buildProperty(mapping));
            }
        });
    }

    /**
     * 新增索引字段映射
     *
     * @param builder
     * @param mappings
     */
    public static void addFieldMapping(PutMappingRequest.Builder builder, List<DynamicFieldMapping> mappings) {
        mappings.forEach(mapping -> {
            String fieldName = mapping.getFieldName();
            if (StringUtils.isNotBlank(fieldName)) {
                builder.properties(fieldName, buildProperty(mapping));
            }
        });
    }

    /**
     * 构建索引字段属性
     *
     * @param mapping
     * @return
     */
    public static Property buildProperty(DynamicFieldMapping mapping) {
        // 获取字段类型，若为空则直接返回 null
        FieldType fieldType = mapping.getFieldType();
        if (Objects.isNull(fieldType)) return null;

        // 根据字段类型构建对应的属性配置
        return switch (fieldType) {
            case Text -> Property.of(p -> p.text(t -> {
                // 配置文本类型字段的分词器和搜索分词器
                String analyzer = mapping.getAnalyzer();
                String searchAnalyzer = mapping.getSearchAnalyzer();
                // 如果分词器存在且为指定分词器之一，设置分词器
                if (StringUtils.isNotBlank(analyzer) && Arrays.asList(PARTICIPLE).contains(analyzer)) {
                    t.analyzer(analyzer);
                }
                // 如果搜索分词器存在且为指定分词器之一，设置搜索分词器
                if (StringUtils.isNotBlank(searchAnalyzer) && Arrays.asList(PARTICIPLE).contains(searchAnalyzer)) {
                    t.searchAnalyzer(searchAnalyzer);
                }

                return t;
            }));

            // 关键字类型字段
            case Keyword -> Property.of(p -> p.keyword(k -> k));

            // 整型字段
            case Integer -> Property.of(p -> p.integer(i -> i));

            // 长整型字段
            case Long -> Property.of(p -> p.long_(l -> l));

            // 单精度浮点型字段
            case Float -> Property.of(p -> p.float_(f -> f));

            // 双精度浮点型字段
            case Double -> Property.of(p -> p.double_(f -> f));

            // 日期类型字段
            case Date -> Property.of(p -> p.date(d -> d));

            // 布尔类型字段
            case Boolean -> Property.of(p -> p.boolean_(b -> b));

            // 二进制类型字段
            case Binary -> Property.of(p -> p.binary(b -> b));

            // 嵌套类型字段
            case Nested -> Property.of(p -> p.nested(n -> n));

            // 对象类型字段
            case Object -> Property.of(p -> p.object(o -> o));

            // 默认情况，返回 null
            default -> null;
        };
    }

    /**
     * 添加权限字段
     *
     * @param mappings
     */
    private static void addPermFieldMapping(List<DynamicFieldMapping> mappings) {
        Field[] declaredFields = EsPermBo.class.getDeclaredFields();
        for (Field f : declaredFields) {
            org.springframework.data.elasticsearch.annotations.Field annotation = f.getAnnotation(
                    org.springframework.data.elasticsearch.annotations.Field.class);
            if (annotation == null || mappings.contains(f.getName())) {
                continue;
            }
            mappings.add(new DynamicFieldMapping(
                    f.getName(),
                    annotation.type(),
                    annotation.analyzer(),
                    annotation.searchAnalyzer()
            ));
        }
    }
}
