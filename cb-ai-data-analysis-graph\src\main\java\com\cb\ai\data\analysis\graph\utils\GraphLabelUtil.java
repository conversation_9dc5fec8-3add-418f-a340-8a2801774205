package com.cb.ai.data.analysis.graph.utils;


import com.cb.ai.data.analysis.graph.annotation.GraphLabel;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class GraphLabelUtil {

    public static <T> List<Map<String, String>> convertToLabeledMap(List<T> list) {
        List<Map<String, String>> result = new ArrayList<>();

        for (T obj : list) {
            Map<String, String> map = new LinkedHashMap<>();
            for (Field field : obj.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    // 获取注解值作为 key，没有注解则用字段名
                    GraphLabel label = field.getAnnotation(GraphLabel.class);
                    String key = label != null ? label.name() : field.getName();
                    Object value = field.get(obj);
                    map.put(key, value != null ? value.toString() : "");
                } catch (IllegalAccessException e) {
                    e.printStackTrace(); // 可替换为日志
                }
            }
            result.add(map);
        }
        return result;
    }

}
