package com.cb.ai.data.analysis.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;
import com.cb.ai.data.analysis.ai.service.AiChatHistorySessionMessageService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * AI历史会话消息 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(AiConstants.API_AI_ROOT_PATH + "/chat/history/session/message")
public class AiChatHistorySessionMessageController extends BaseController<AiChatHistorySessionMessageService, AiChatHistorySessionMessage> {
    /**
     * 删除历史会话消息
     * @param ids 历史会话ID
     */
    @DeleteMapping("/message")
    public Result deleteMessage(@NotEmpty(message = "ID不存在") String[] ids) {
        baseService.removeByIds(Arrays.asList(ids));
        return Result.success("删除历史会话消息成功");
    }

    /**
     * 历史会话消息列表
     * @param params 会话历史消息
     */
    @GetMapping("/page")
    public Result page(AiChatHistorySessionMessage params) {
        LambdaQueryWrapper<AiChatHistorySessionMessage> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda()
                .orderByDesc(AiChatHistorySessionMessage::getSendTime)
                .orderByDesc(AiChatHistorySessionMessage::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
