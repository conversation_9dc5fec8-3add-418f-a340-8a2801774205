package com.xong.boot.common.crypto.file;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * SM4文件加解密器
 * <AUTHOR>
 */
public class Sm4FileEncoder implements FileEncoder {
    /**
     * 加密算法
     */
    private final SM4 sm4;

    public Sm4FileEncoder(String secretKey) {
        sm4 = SmUtil.sm4(HexUtil.decodeHex(secretKey));
    }

    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        sm4.encrypt(is, out, isClose);
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        sm4.decrypt(is, out, isClose);
    }
}
