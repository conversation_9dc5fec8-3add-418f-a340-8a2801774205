package com.cb.ai.data.analysis.petition.domain;

import lombok.Data;

import java.util.Objects;

@Data
public class FootnoteData {

    String refText;
    String footnoteText;
    String bookmarkId;
    boolean isFirstOccurrence;

    public FootnoteData(String refText, String footnoteText) {
        this.refText = refText;
        this.footnoteText = footnoteText;
        this.bookmarkId = "footnote" + Objects.hash(footnoteText);
        this.isFirstOccurrence = false;
    }
}
