package com.cb.ai.data.analysis.voucher.utils.pdf;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.voucher.utils.ocr.AiUtil;
import com.cb.ai.data.analysis.voucher.utils.ocr.TableMarkdownConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * PDF转图片工具类
 * 采用Builder模式和链式调用，提供简洁易用的API
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-08-01
 */
@Slf4j
public class PdfToImageUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private PdfToImageUtil() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }

    /**
     * 创建PDF转换器的入口方法
     *
     * @param pdfFile PDF文件
     * @return PDF转换器Builder
     */
    public static PdfConverter from(File pdfFile) {
        return new PdfConverter(pdfFile);
    }

    /**
     * 创建PDF转换器的入口方法
     *
     * @param inputStream PDF输入流
     * @return PDF转换器Builder
     */
    public static PdfConverter from(InputStream inputStream) {
        return new PdfConverter(inputStream);
    }

    /**
     * 快速获取PDF页数的静态方法
     *
     * @param pdfFile PDF文件
     * @return 总页数
     */
    public static int getPageCount(File pdfFile) {
        return from(pdfFile).getPageCount();
    }

    /**
     * 快速获取PDF页数的静态方法
     *
     * @param inputStream PDF输入流
     * @return 总页数
     */
    public static int getPageCount(InputStream inputStream) {
        return from(inputStream).getPageCount();
    }

    /**
     * PDF转换器Builder类
     * 提供链式调用API，支持各种转换需求
     */
    public static class PdfConverter {
        private final File pdfFile;
        private final InputStream inputStream;
        private byte[] pdfBytes; // 缓存PDF字节数据，支持流的多次使用

        // 配置参数
        private float dpi = PdfToImageConfig.DEFAULT_DPI;
        private String format = PdfToImageConfig.DEFAULT_FORMAT;
        private float quality = 0.9f;
        private boolean antiAliasing = true;
        private int pageSize = 10;
        private boolean autoGc = true;

        // 页面选择参数
        private Integer singlePage = null;
        private Integer startPage = null;
        private Integer endPage = null;

        /**
         * 构造函数 - 使用文件
         */
        private PdfConverter(File pdfFile) {
            this.pdfFile = pdfFile;
            this.inputStream = null;
            this.pdfBytes = null;
        }

        /**
         * 构造函数 - 使用输入流
         */
        private PdfConverter(InputStream inputStream) {
            this.pdfFile = null;
            this.inputStream = inputStream;
            this.pdfBytes = null;
        }

        /**
         * 设置DPI
         *
         * @param dpi DPI值
         * @return this
         */
        public PdfConverter dpi(float dpi) {
            this.dpi = dpi;
            return this;
        }

        /**
         * 设置图片格式
         *
         * @param format 图片格式（PNG、JPEG、BMP、GIF）
         * @return this
         */
        public PdfConverter format(String format) {
            this.format = format;
            return this;
        }

        /**
         * 设置JPEG质量
         *
         * @param quality 质量值（0.0-1.0）
         * @return this
         */
        public PdfConverter quality(float quality) {
            this.quality = quality;
            return this;
        }

        /**
         * 设置是否启用抗锯齿
         *
         * @param antiAliasing 是否启用抗锯齿
         * @return this
         */
        public PdfConverter antiAliasing(boolean antiAliasing) {
            this.antiAliasing = antiAliasing;
            return this;
        }

        /**
         * 设置分页大小（用于分页处理）
         *
         * @param pageSize 每批处理的页数
         * @return this
         */
        public PdfConverter pageSize(int pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        /**
         * 设置是否自动垃圾回收
         *
         * @param autoGc 是否自动垃圾回收
         * @return this
         */
        public PdfConverter autoGc(boolean autoGc) {
            this.autoGc = autoGc;
            return this;
        }

        /**
         * 选择单个页面
         *
         * @param pageIndex 页面索引（从0开始）
         * @return this
         */
        public PdfConverter page(int pageIndex) {
            this.singlePage = pageIndex;
            this.startPage = null;
            this.endPage = null;
            return this;
        }

        /**
         * 选择页面范围
         *
         * @param startPage 起始页面（从0开始，包含）
         * @param endPage   结束页面（从0开始，包含）
         * @return this
         */
        public PdfConverter pages(int startPage, int endPage) {
            this.startPage = startPage;
            this.endPage = endPage;
            this.singlePage = null;
            return this;
        }

        /**
         * 使用高质量预设
         *
         * @return this
         */
        public PdfConverter highQuality() {
            this.dpi = PdfToImageConfig.HIGH_QUALITY_DPI;
            this.format = "PNG";
            this.antiAliasing = true;
            return this;
        }

        /**
         * 使用低质量预设
         *
         * @return this
         */
        public PdfConverter lowQuality() {
            this.dpi = PdfToImageConfig.LOW_QUALITY_DPI;
            this.format = "PNG";
            this.quality = 0.7f;
            return this;
        }

        /**
         * 使用内存优化预设
         *
         * @return this
         */
        public PdfConverter memoryOptimized() {
            this.dpi = PdfToImageConfig.DEFAULT_DPI;
            this.format = "PNG";
            this.quality = 0.8f;
            this.pageSize = 5;
            this.autoGc = true;
            this.antiAliasing = false;
            return this;
        }

        // ==================== 转换方法 ====================

        /**
         * 获取PDF总页数
         * 对于InputStream，会自动缓存PDF数据以支持后续操作
         *
         * @return 总页数
         */
        public int getPageCount() {
            try {
                if (pdfFile != null) {
                    return getPageCountFromFile();
                } else {
                    // 确保PDF字节数据已缓存
                    ensurePdfBytesLoaded();
                    return getPageCountFromBytes();
                }
            } catch (Exception e) {
                throw new PdfConvertException("获取PDF页数失败", e);
            }
        }

        /**
         * 确保PDF字节数据已加载到内存中
         * 这样可以支持InputStream的多次使用
         */
        private void ensurePdfBytesLoaded() throws IOException {
            if (pdfBytes == null && inputStream != null) {
                log.debug("缓存PDF字节数据以支持多次操作");
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }
                    pdfBytes = baos.toByteArray();
                    log.debug("PDF字节数据缓存完成，大小: {} bytes", pdfBytes.length);
                }
            }
        }

        /**
         * 创建新的ByteArrayInputStream用于PDF操作
         */
        private InputStream createPdfInputStream() throws IOException {
            if (pdfFile != null) {
                return new FileInputStream(pdfFile);
            } else {
                ensurePdfBytesLoaded();
                return new ByteArrayInputStream(pdfBytes);
            }
        }

        /**
         * 转换为单个BufferedImage（仅用于单页转换）
         *
         * @return BufferedImage
         */
        public BufferedImage toImage() {
            if (singlePage == null) {
                throw new PdfConvertException("请先使用page()方法指定页面");
            }

            try {
                return convertSinglePage();
            } catch (Exception e) {
                throw new PdfConvertException("转换PDF页面失败", e);
            }
        }

        /**
         * 转换为BufferedImage列表
         *
         * @return BufferedImage列表
         */
        public List<BufferedImage> toImages() {
            try {
                if (singlePage != null) {
                    // 单页转换
                    List<BufferedImage> result = new ArrayList<>();
                    result.add(convertSinglePage());
                    return result;
                } else if (startPage != null && endPage != null) {
                    // 范围转换
                    return convertPageRange();
                } else {
                    // 全文档转换
                    return convertAllPages();
                }
            } catch (Exception e) {
                throw new PdfConvertException("转换PDF为图片失败", e);
            }
        }

        /**
         * 转换并保存为图片文件
         *
         * @param outputDir 输出目录
         * @return 生成的文件列表
         */
        public List<File> toFiles(String outputDir) {
            if (StrUtil.isBlank(outputDir)) {
                throw new PdfConvertException("输出目录不能为空");
            }
            try {
                List<BufferedImage> images = toImages();
                return saveImagesToFiles(images, outputDir);
            } catch (Exception e) {
                throw new PdfConvertException("保存图片文件失败", e);
            }
        }

        /**
         * 转换为字节数组列表
         *
         * @return 字节数组列表
         */
        public List<byte[]> toBytes() {
            try {
                List<BufferedImage> images = toImages();
                return convertImagesToBytes(images);
            } catch (Exception e) {
                throw new PdfConvertException("转换为字节数组失败", e);
            }
        }

        /**
         * 转换为Base64字符串列表
         *
         * @return Base64字符串列表
         */
        public List<String> toBase64() {
            try {
                List<byte[]> bytesArray = toBytes();
                List<String> base64List = new ArrayList<>();
                for (byte[] bytes : bytesArray) {
                    base64List.add(Base64.encode(bytes));
                }
                return base64List;
            } catch (Exception e) {
                throw new PdfConvertException("转换为Base64失败", e);
            }
        }

        /**
         * 转换为数据URI列表
         *
         * @return 数据URI列表
         */
        public List<String> toDataUri() {
            try {
                List<String> base64List = toBase64();
                List<String> dataUriList = new ArrayList<>();
                String mimeType = getMimeType(format);

                for (String base64 : base64List) {
                    dataUriList.add(String.format("data:%s;base64,%s", mimeType, base64));
                }
                return dataUriList;
            } catch (Exception e) {
                throw new PdfConvertException("转换为数据URI失败", e);
            }
        }

        /**
         * 分页转换为文件（内存友好）
         *
         * @param outputDir 输出目录
         * @return 生成的文件列表
         */
        public List<File> toFilesPaged(String outputDir) {
            if (StrUtil.isBlank(outputDir)) {
                throw new PdfConvertException("输出目录不能为空");
            }

            try {
                return convertToFilesPaged(outputDir);
            } catch (Exception e) {
                throw new PdfConvertException("分页转换为文件失败", e);
            }
        }

        /**
         * 分页转换为Base64（内存友好）
         *
         * @return Base64字符串列表
         */
        public List<String> toBase64Paged() {
            try {
                return convertToBase64Paged();
            } catch (Exception e) {
                throw new PdfConvertException("分页转换为Base64失败", e);
            }
        }

        // ==================== 内部实现方法 ====================

        private PdfToImageConfig buildConfig() {
            PdfToImageConfig config = new PdfToImageConfig();
            config.setDpi(dpi);
            config.setFormat(format);
            config.setQuality(quality);
            config.setAntiAliasing(antiAliasing);
            config.setPageSize(pageSize);
            config.setAutoGc(autoGc);
            config.validate();
            return config;
        }

        private int getPageCountFromFile() throws IOException {
            if (pdfFile == null || !pdfFile.exists()) {
                throw new PdfConvertException("PDF文件不存在: " + pdfFile);
            }

            try (FileInputStream fis = new FileInputStream(pdfFile);
                 PDDocument document = PDDocument.load(fis)) {
                return document.getNumberOfPages();
            }
        }

        private int getPageCountFromBytes() throws IOException {
            if (pdfBytes == null) {
                throw new PdfConvertException("PDF字节数据未加载");
            }

            try (ByteArrayInputStream bais = new ByteArrayInputStream(pdfBytes);
                 PDDocument document = PDDocument.load(bais)) {
                return document.getNumberOfPages();
            }
        }

        private BufferedImage convertSinglePage() throws IOException {
            PdfToImageConfig config = buildConfig();

            try (InputStream stream = createPdfInputStream()) {
                return convertPageFromStream(stream, singlePage, config);
            }
        }

        private List<BufferedImage> convertPageRange() throws IOException {
            PdfToImageConfig config = buildConfig();

            try (InputStream stream = createPdfInputStream()) {
                return convertPagesFromStream(stream, startPage, endPage, config);
            }
        }

        private List<BufferedImage> convertAllPages() throws IOException {
            PdfToImageConfig config = buildConfig();

            try (InputStream stream = createPdfInputStream()) {
                return convertAllPagesFromStream(stream, config);
            }
        }

        private BufferedImage convertPageFromStream(InputStream stream, int pageIndex, PdfToImageConfig config) throws IOException {
            try (PDDocument document = PDDocument.load(stream)) {
                int pageCount = document.getNumberOfPages();

                if (pageIndex < 0 || pageIndex >= pageCount) {
                    throw new PdfConvertException("页面索引超出范围: " + pageIndex + "，总页数: " + pageCount);
                }

                PDFRenderer renderer = new PDFRenderer(document);
                BufferedImage image = renderer.renderImageWithDPI(pageIndex, config.getDpi(), ImageType.RGB);

                if (config.isAntiAliasing()) {
                    image = applyAntiAliasing(image);
                }

                log.debug("成功转换第{}页", pageIndex + 1);
                return image;
            }
        }

        private List<BufferedImage> convertPagesFromStream(InputStream stream, int startPage, int endPage, PdfToImageConfig config) throws IOException {
            List<BufferedImage> images = new ArrayList<>();

            try (PDDocument document = PDDocument.load(stream)) {
                int pageCount = document.getNumberOfPages();

                if (startPage < 0 || startPage >= pageCount) {
                    throw new PdfConvertException("起始页面索引超出范围: " + startPage + "，总页数: " + pageCount);
                }

                int actualEndPage = Math.min(endPage, pageCount - 1);
                PDFRenderer renderer = new PDFRenderer(document);

                log.info("开始转换PDF页面范围 {}-{}，共{}页", startPage + 1, actualEndPage + 1, actualEndPage - startPage + 1);

                for (int pageIndex = startPage; pageIndex <= actualEndPage; pageIndex++) {
                    BufferedImage image = renderer.renderImageWithDPI(pageIndex, config.getDpi(), ImageType.RGB);

                    if (config.isAntiAliasing()) {
                        image = applyAntiAliasing(image);
                    }

                    images.add(image);
                    log.debug("成功转换第{}页", pageIndex + 1);
                }

                log.info("PDF页面范围转换完成，共转换{}页", images.size());
                return images;
            }
        }

        private List<BufferedImage> convertAllPagesFromStream(InputStream stream, PdfToImageConfig config) throws IOException {
            List<BufferedImage> images = new ArrayList<>();

            try (PDDocument document = PDDocument.load(stream)) {
                PDFRenderer renderer = new PDFRenderer(document);
                int pageCount = document.getNumberOfPages();

                log.info("开始转换PDF，共{}页，DPI: {}", pageCount, config.getDpi());

                for (int pageIndex = 0; pageIndex < pageCount; pageIndex++) {
                    BufferedImage image = renderer.renderImageWithDPI(pageIndex, config.getDpi(), ImageType.RGB);

                    if (config.isAntiAliasing()) {
                        image = applyAntiAliasing(image);
                    }

                    images.add(image);
                    log.debug("成功转换第{}页", pageIndex + 1);
                }

                log.info("PDF转换完成，共转换{}页", images.size());
                return images;
            }
        }

        private List<File> saveImagesToFiles(List<BufferedImage> images, String outputDir) throws IOException {
            File outputDirFile = new File(outputDir);
            if (!outputDirFile.exists()) {
                boolean created = outputDirFile.mkdirs();
                if (!created) {
                    throw new PdfConvertException("创建输出目录失败: " + outputDir);
                }
            }

            List<File> imageFiles = new ArrayList<>();
            String baseName = pdfFile != null ? FileUtil.getPrefix(pdfFile) : "pdf";
            String formatLower = format.toLowerCase();
            PdfToImageConfig config = buildConfig();

            // 根据转换配置确定实际的页码范围
            List<Integer> actualPageNumbers = getActualPageNumbers(images.size());

            for (int i = 0; i < images.size(); i++) {
                int actualPageNumber = actualPageNumbers.get(i);
                String fileName = String.format("%s_page_%03d.%s", baseName, actualPageNumber, formatLower);
                File imageFile = new File(outputDirFile, fileName);

                saveImageToFile(images.get(i), imageFile, config);
                imageFiles.add(imageFile);
                log.debug("保存图片文件: {} (PDF第{}页)", imageFile.getAbsolutePath(), actualPageNumber);
            }

            log.info("图片文件保存完成，共{}个文件", imageFiles.size());
            return imageFiles;
        }

        /**
         * 根据转换配置获取实际的PDF页码列表
         *
         * @param imageCount 转换的图片数量
         * @return 实际的PDF页码列表（从1开始）
         */
        private List<Integer> getActualPageNumbers(int imageCount) {
            List<Integer> pageNumbers = new ArrayList<>();

            if (singlePage != null) {
                // 单页转换
                pageNumbers.add(singlePage + 1); // 转换为从1开始的页码
            } else if (startPage != null && endPage != null) {
                // 页面范围转换
                for (int i = startPage; i <= startPage + imageCount - 1; i++) {
                    pageNumbers.add(i + 1); // 转换为从1开始的页码
                }
            } else {
                // 全文档转换
                for (int i = 0; i < imageCount; i++) {
                    pageNumbers.add(i + 1); // 转换为从1开始的页码
                }
            }

            return pageNumbers;
        }

        private List<byte[]> convertImagesToBytes(List<BufferedImage> images) throws IOException {
            List<byte[]> bytesArray = new ArrayList<>();
            PdfToImageConfig config = buildConfig();

            for (BufferedImage image : images) {
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    saveImageToStream(image, baos, config);
                    bytesArray.add(baos.toByteArray());
                }
            }

            return bytesArray;
        }

        private List<File> convertToFilesPaged(String outputDir) throws IOException {
            File outputDirFile = new File(outputDir);
            if (!outputDirFile.exists()) {
                boolean created = outputDirFile.mkdirs();
                if (!created) {
                    throw new PdfConvertException("创建输出目录失败: " + outputDir);
                }
            }

            int totalPages = getPageCount();
            List<File> allImageFiles = new ArrayList<>();
            String baseName = pdfFile != null ? FileUtil.getPrefix(pdfFile) : "pdf";
            String formatLower = format.toLowerCase();
            PdfToImageConfig config = buildConfig();

            log.info("开始分页转换PDF，总页数: {}，每批处理: {}页", totalPages, pageSize);

            for (int startPage = 0; startPage < totalPages; startPage += pageSize) {
                int endPage = Math.min(startPage + pageSize - 1, totalPages - 1);

                log.info("处理页面范围: {} - {}", startPage + 1, endPage + 1);

                // 使用新的流创建方式，支持InputStream
                try (InputStream stream = createPdfInputStream()) {
                    List<BufferedImage> batchImages = convertPagesFromStream(stream, startPage, endPage, config);

                    for (int i = 0; i < batchImages.size(); i++) {
                        int actualPageIndex = startPage + i;
                        int actualPageNumber = actualPageIndex + 1; // 转换为从1开始的页码
                        String fileName = String.format("%s_page_%03d.%s", baseName, actualPageNumber, formatLower);
                        File imageFile = new File(outputDirFile, fileName);

                        saveImageToFile(batchImages.get(i), imageFile, config);
                        allImageFiles.add(imageFile);
                        log.debug("保存图片文件: {} (PDF第{}页)", imageFile.getAbsolutePath(), actualPageNumber);
                    }

                    // 清理当前批次的图片，释放内存
                    batchImages.clear();
                }

                // 建议垃圾回收
                if (autoGc && startPage + pageSize < totalPages) {
                    System.gc();
                }
            }

            log.info("分页转换完成，生成{}个图片文件", allImageFiles.size());
            return allImageFiles;
        }

        private List<String> convertToBase64Paged() throws IOException {
            int totalPages = getPageCount();
            List<String> allBase64Strings = new ArrayList<>();
            PdfToImageConfig config = buildConfig();

            log.info("开始分页转换PDF为Base64，总页数: {}，每批处理: {}页", totalPages, pageSize);

            for (int startPage = 0; startPage < totalPages; startPage += pageSize) {
                int endPage = Math.min(startPage + pageSize - 1, totalPages - 1);

                log.debug("处理页面范围: {} - {}", startPage + 1, endPage + 1);

                // 使用新的流创建方式，支持InputStream
                try (InputStream stream = createPdfInputStream()) {
                    List<BufferedImage> batchImages = convertPagesFromStream(stream, startPage, endPage, config);

                    for (BufferedImage image : batchImages) {
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            saveImageToStream(image, baos, config);
                            byte[] bytes = baos.toByteArray();
                            String base64 = Base64.encode(bytes);
                            allBase64Strings.add(base64);
                        }
                    }

                    // 清理当前批次的图片，释放内存
                    batchImages.clear();
                }

                // 建议垃圾回收
                if (autoGc && startPage + pageSize < totalPages) {
                    System.gc();
                }
            }

            log.info("分页转换为Base64完成，共转换{}页", allBase64Strings.size());
            return allBase64Strings;
        }
    }

    // ==================== 静态工具方法 ====================

    /**
     * 获取图片格式对应的MIME类型
     */
    private static String getMimeType(String format) {
        String upperFormat = format.toUpperCase();
        switch (upperFormat) {
            case "PNG":
                return "image/png";
            case "JPG":
            case "JPEG":
                return "image/jpeg";
            case "BMP":
                return "image/bmp";
            case "GIF":
                return "image/gif";
            default:
                return "image/png";
        }
    }

    /**
     * 应用抗锯齿处理
     */
    private static BufferedImage applyAntiAliasing(BufferedImage originalImage) {
        BufferedImage antiAliasedImage = new BufferedImage(
            originalImage.getWidth(),
            originalImage.getHeight(),
            BufferedImage.TYPE_INT_RGB
        );

        Graphics2D g2d = antiAliasedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        return antiAliasedImage;
    }

    /**
     * 保存图片到文件
     */
    private static void saveImageToFile(BufferedImage image, File file, PdfToImageConfig config) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            saveImageToStream(image, fos, config);
        }
    }

    /**
     * 保存图片到输出流
     */
    private static void saveImageToStream(BufferedImage image, OutputStream output, PdfToImageConfig config) throws IOException {
        String format = config.getFormat().toUpperCase();

        if ("JPEG".equals(format) || "JPG".equals(format)) {
            saveJpegWithQuality(image, output, config.getQuality());
        } else {
            ImageIO.write(image, format, output);
        }
    }

    /**
     * 保存JPEG格式图片并设置质量
     */
    private static void saveJpegWithQuality(BufferedImage image, OutputStream output, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("JPEG");
        if (!writers.hasNext()) {
            throw new IOException("没有找到JPEG图片写入器");
        }

        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(quality);

        try (ImageOutputStream ios = ImageIO.createImageOutputStream(output)) {
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(image, null, null), param);
        } finally {
            writer.dispose();
        }
    }

    /**
     * 测试方法 - 演示正确的文件命名和流的多次使用
     */
//    public static void main(String[] args) {
//        // 测试文件路径 - 请修改为实际存在的PDF文件路径
//        String pdfPath = "C:\\Users\\<USER>\\Desktop\\pdf2img\\202402-29付李忠,柏劼随市委主要领导赴北京拜访商务部差旅费.pdf";
//        String outputDir = "C:\\Users\\<USER>\\Desktop\\pdf2img\\output";
//        StringBuilder sb = new StringBuilder();
//        try(FileInputStream fis = new FileInputStream(pdfPath)) {
//            PdfConverter converter = PdfToImageUtil.from(fis);
//            converter.lowQuality();
//            int pageCount = converter.getPageCount();
//            AiUtil aiUtil = new AiUtil();
//            for (int i = 0; i < pageCount; i++) {
//                List<String> base64List = converter.page(i).toBase64();
//                if(!base64List.isEmpty()){
//                    String base64 = base64List.get(0);
//                    String text = aiUtil.ocrBase64(base64, true);
//                    String rst = TableMarkdownConverter.convertTableToMarkdown(text);
//                    rst = rst.replaceAll("\r\n\r\n","\r\n").replaceAll("\n\n","\n");
//                    sb.append(rst).append(System.lineSeparator());
//                }
//            }
//            System.out.println( sb.toString());
//        } catch (Exception e) {
//            System.err.println("测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }

}
