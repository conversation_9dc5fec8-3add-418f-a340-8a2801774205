package com.cb.ai.data.analysis.file.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.mapper.SuperviseResourceFolderMapper;

import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.xong.boot.common.enums.DropPosition;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class SuperviseResourceFolderServiceImpl extends BaseServiceImpl<SuperviseResourceFolderMapper, SuperviseResourceFolder> implements SuperviseResourceFolderService {

//    private final SuperviseResourceFileService superviseResourceFileService;
//
//    public SuperviseResourceFolderServiceImpl(SuperviseResourceFileService superviseResourceFileService) {
//        this.superviseResourceFileService = superviseResourceFileService;
//    }

    @Transactional
    @Override
    public void dropFolder(DropParams params) {
        //获取当前节点
//        SuperviseResourceFolder source = getById(params.getDragId());
        //获取目标节点
        SuperviseResourceFolder target = getById(params.getDropId());
        //更新当前
        List<SuperviseResourceFolder> updateFolders = new ArrayList<>();
        //放置节点同级数据
        List<SuperviseResourceFolder> peerFolders = null;
        if (params.getPosition() == DropPosition.LOWER) { // 放置节点下
            peerFolders = list(Wrappers.lambdaQuery(SuperviseResourceFolder.class)
                    .eq(SuperviseResourceFolder::getParentId, target.getId())
                    .ne(SuperviseResourceFolder::getId, params.getDragId())
                    .orderByAsc(SuperviseResourceFolder::getSortOn));
        } else {
            peerFolders = list(Wrappers.lambdaQuery(SuperviseResourceFolder.class)
                    .eq(SuperviseResourceFolder::getParentId, target.getParentId())
                    .ne(SuperviseResourceFolder::getId, params.getDragId())
                    .orderByAsc(SuperviseResourceFolder::getSortOn));
        }

        if (params.getPosition() == DropPosition.LOWER) { // 放置节下
            //原来的节点下是正常排序的，即使少了一个排序也还是对的，只需要对目标列表重新排序
            AtomicInteger i = new AtomicInteger();
            SuperviseResourceFolder firstFolder = new SuperviseResourceFolder();
            firstFolder.setId(params.getDragId());
            firstFolder.setParentId(params.getDropId());
            firstFolder.setSortOn(i.incrementAndGet());
            //更新自己以及下级全路径
            updateFolderAndFullPath(firstFolder);
            if (peerFolders != null && peerFolders.size() > 0) {
                peerFolders.forEach(m -> {
                    SuperviseResourceFolder folder = new SuperviseResourceFolder();
                    folder.setParentId(m.getParentId());
                    folder.setId(m.getId());
                    folder.setSortOn(i.incrementAndGet());
                    updateFolders.add(folder);
                });
            }
        } else if (params.getPosition() == DropPosition.AFTER) { // 放置节点后
            AtomicInteger i = new AtomicInteger();
            peerFolders.forEach(m -> {
                SuperviseResourceFolder dept = new SuperviseResourceFolder();
                dept.setParentId(m.getParentId());
                dept.setId(m.getId());
                dept.setSortOn(i.incrementAndGet());
                updateFolders.add(dept);
                // 如果是当前是放置节点，就在后面添加拖拽节点
                if (params.getDropId().equals(m.getId())) {
                    SuperviseResourceFolder dragFolder = new SuperviseResourceFolder();
                    dragFolder.setId(params.getDragId());
                    dragFolder.setParentId(m.getParentId());
                    dragFolder.setSortOn(i.incrementAndGet());
                    updateFolderAndFullPath(dragFolder);
                }
            });
        } else { // 放置节点前
            AtomicInteger i = new AtomicInteger();
            SuperviseResourceFolder firstFolder = new SuperviseResourceFolder();
            firstFolder.setId(params.getDragId());
            firstFolder.setParentId(target.getParentId());
            firstFolder.setSortOn(i.incrementAndGet());
            updateFolderAndFullPath(firstFolder);
            peerFolders.forEach(m -> {
                SuperviseResourceFolder dept = new SuperviseResourceFolder();
                dept.setParentId(m.getParentId());
                dept.setId(m.getId());
                dept.setSortOn(i.incrementAndGet());
                updateFolders.add(dept);
            });
        }
        if (updateFolders.size() > 0) {
            updateBatchById(updateFolders);
        }
    }

    /**
     * 这个是放置节点完成以后，计算当前节点和子节点的全路径
     *
     * @param folder
     */
    @Transactional
    public void updateFolderAndFullPath(SuperviseResourceFolder folder) {
        SuperviseResourceFolder oldFolder = getById(folder.getId());
        if (folder.getParentId() == null || folder.getParentId().equals(oldFolder.getParentId())) { // 判断是否进行了级别调整
            if (!updateById(folder)) {
                throw new XServerException("文件夹更新失败");
            }
            return;
        }
        // 如修改了父级组织，则修改关联的所有下级组织
        List<String> fullPath = new ArrayList<>(); // 构建当前组织新的全路径
        if (StringUtils.isBlank(folder.getParentId()) || "0".equals(folder.getParentId())) {
            fullPath.add("0");
        } else {
            SuperviseResourceFolder superFolder = getById(folder.getParentId());
            String[] split = superFolder.getFullPath().split(",");
            fullPath.addAll(List.of(split));
        }
        fullPath.add(folder.getId());
        folder.setFullPath(StringUtils.join(",", fullPath));
        if (updateById(folder)) {
            String rootPath = StringUtils.join(",", fullPath);
            String oldPath = oldFolder.getFullPath();
            LambdaQueryWrapper<SuperviseResourceFolder> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(SuperviseResourceFolder::getId, SuperviseResourceFolder::getFullPath);
            queryWrapper.likeRight(SuperviseResourceFolder::getFullPath, oldPath + ",");
            List<SuperviseResourceFolder> folderList = list(queryWrapper); // 获取包含旧节点的所有文件夹
            if (folderList == null || folderList.size() == 0) {
                return;
            }
            folderList.forEach(d -> {
                String nowFullPath = StringUtils.join(",", d.getFullPath()).replace(oldPath, rootPath);
                d.setFullPath(nowFullPath); // 替换父级路径
            });
            if (!updateBatchById(folderList)) { // 更新所有下级全路径
                throw new XServerException("更新下级全路径失败");
            }
        } else {
            throw new XServerException("部门更新失败");
        }
    }

    @Override
    @Transactional
    public boolean removeFolders(String[] ids) {
//        LambdaQueryWrapper<SuperviseResourceFolder> query =Wrappers.lambdaQuery();
//        query.in(SuperviseResourceFolder::getId,Arrays.asList(ids));
//        List<SuperviseResourceFolder> list = list(query);
//        for (SuperviseResourceFolder folder : list) {
//            //删除所有文件夹
//            LambdaQueryWrapper<SuperviseResourceFolder> queryWrapper = Wrappers.lambdaQuery();
//            queryWrapper.select(SuperviseResourceFolder::getId, SuperviseResourceFolder::getFullPath);
//            queryWrapper.likeRight(SuperviseResourceFolder::getFullPath, folder.getFullPath() + ",");
//            List<SuperviseResourceFolder> list1 = list(queryWrapper);
//            List<String> folderIds = list1.stream().map(SuperviseResourceFolder::getId).toList();
//            removeByIds(folderIds);
//            //删除所有文件
//            LambdaQueryWrapper<SuperviseResourceFile> queryFile =Wrappers.lambdaQuery();
//            LambdaQueryWrapper<SuperviseResourceFile> in = queryFile.in(SuperviseResourceFile::getFolderId, folderIds);
//            List<SuperviseResourceFile> files = superviseResourceFileService.list(in);
//            List<String> fileIds = files.stream().map(SuperviseResourceFile::getId).toList();
//            superviseResourceFileService.removeByIds(fileIds);
//        }
        updateFoldersOriginalPath(Arrays.asList(ids));
        //应该先更新源文件夹路径
        return removeByIds(Arrays.asList(ids));
    }

    /**
     * 根据文件夹id更新文件的原路径
     *
     * @param ids
     * @return
     */
    @Override
    public Boolean updateFoldersOriginalPath(List<String> ids) {
        /**
         * 获取删除文件的所有文件夹
         */
        LambdaQueryWrapper<SuperviseResourceFolder> superviseResourceFolderQueryWrapper = new QueryWrapper<SuperviseResourceFolder>().lambda();
        superviseResourceFolderQueryWrapper.in(SuperviseResourceFolder::getId, ids);
        List<SuperviseResourceFolder> folderList = list(superviseResourceFolderQueryWrapper);
//        Map<String, SuperviseResourceFolder> folderIdMap = folderList.stream().collect(Collectors.toMap(SuperviseResourceFolder::getId, e -> e, (k1, k2) -> k1));
        /**
         * 获取文件夹映射
         */
        Map<String, SuperviseResourceFolder> allFolderIdMap = folderIdMap(ids);
        /**
         * 前置数据准备完成，准备删除文件
         */
        Map<String, SuperviseResourceFolder> folderIdMap = new HashMap<>();
        for (SuperviseResourceFolder superviseResourceFolder : folderList) {
            //更新他的前置原文件路径
            SuperviseResourceFolder resourceFolder = folderIdMap.get(superviseResourceFolder.getId());
            ArrayList<String> strings = new ArrayList<>();

            if (resourceFolder == null) {
                strings.add("根目录");
            } else {
                String fullPath = resourceFolder.getFullPath();
                String[] split = fullPath.split(",");
                for (String s : split) {
                    SuperviseResourceFolder superviseResourceFolder1 = allFolderIdMap.get(s);
                    if (superviseResourceFolder1 == null) {
                        strings.add(superviseResourceFolder1.getFolderName());
                    }
                }
            }
            strings.add(superviseResourceFolder.getFolderName());
            superviseResourceFolder.setOriginalPath(String.join("/", strings));
        }

        return updateBatchById(folderList);
    }

    /**
     * 根据当前id列表，获取他的全路径文件夹map映射
     *
     * @param fullPaths
     */
    @Override
    public Map<String, SuperviseResourceFolder> folderIdMap(List<String> fullPaths) {
        /**
         * 获取全路径的文件夹
         */
        List<String> fullIds = new ArrayList<>();
        for (String s : fullPaths) {
            String[] split = s.split(",");
            fullIds.addAll(List.of(split));
        }
        fullIds = fullIds.stream().distinct().collect(Collectors.toList());
        if (fullIds.isEmpty()) {
            fullIds.add("0");
        }
        LambdaQueryWrapper<SuperviseResourceFolder> queryWrapper = new QueryWrapper<SuperviseResourceFolder>().lambda();
        queryWrapper.in(SuperviseResourceFolder::getId, fullIds);
        List<SuperviseResourceFolder> allFolderList = list(queryWrapper);
        SuperviseResourceFolder superviseResourceFolder2 = new SuperviseResourceFolder();
        superviseResourceFolder2.setId("0");
        superviseResourceFolder2.setFolderName("根目录");
        allFolderList.add(superviseResourceFolder2);
        Map<String, SuperviseResourceFolder> allFolderIdMap = allFolderList.stream().collect(Collectors.toMap(SuperviseResourceFolder::getId, e -> e, (k1, k2) -> k1));
        return allFolderIdMap;
    }

    @Override
    public Page<SuperviseResourceFolder> pageDeletedFolders(Page<SuperviseResourceFolder> page, SuperviseResourceFolder superviseResourceFile) {
        LambdaQueryWrapper<SuperviseResourceFolder> lambda = new QueryWrapper<SuperviseResourceFolder>().lambda();
        lambda.eq(SuperviseResourceFolder::getDelFlag, true);
        if (StringUtils.isNotBlank(superviseResourceFile.getFolderName())) {
            lambda.like(SuperviseResourceFolder::getFolderName, superviseResourceFile.getFolderName());
        }
        // 使用自定义方法查询已删除文件
        return baseMapper.getFolderList(page, lambda);
    }

    @Override
    public void restoreFolder(List<String> ids, String folderId) {
        baseMapper.restoreFolders(ids, folderId);
    }

    @Override
    public List<SuperviseResourceFolder> permanentlyDelete(List<String> ids) {
        //注意，删除文件夹时，要删除自己及其子文件夹的所有数据
        LambdaQueryWrapper<SuperviseResourceFolder> lambda = new QueryWrapper<SuperviseResourceFolder>().lambda();
        lambda.in(SuperviseResourceFolder::getId,ids);
        List<SuperviseResourceFolder> folderList = baseMapper.getFolderList(lambda);
       List<SuperviseResourceFolder> superviseResourceFolders = new ArrayList<>();
        superviseResourceFolders.addAll(folderList);
        for (SuperviseResourceFolder superviseResourceFolder : folderList) {
            LambdaQueryWrapper<SuperviseResourceFolder> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(SuperviseResourceFolder::getId, SuperviseResourceFolder::getFullPath);
            queryWrapper.likeRight(SuperviseResourceFolder::getFullPath, superviseResourceFolder.getFullPath() + ",");
            List<SuperviseResourceFolder> folderChildrenList = list(queryWrapper); // 获取所有节点文件夹
            superviseResourceFolders.addAll(folderChildrenList);
        }
        return superviseResourceFolders;
    }

    @Override
    public Integer deleteByCustom(Wrapper<SuperviseResourceFolder> wrapper) {

        return baseMapper.deleteByCustom(wrapper);
    }
}
