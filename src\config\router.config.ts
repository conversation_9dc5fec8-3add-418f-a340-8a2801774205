// import type { Breadcrumb } from '#/router'
import type { RouteRecordRaw } from 'vue-router'
import { BasicLayout, BlankLayout, ChatLayout, UserLayout, XRouteView } from '@/layouts'

/**
 * 首页
 */
const HOME_ROUTE = {
  path: '/',
  name: 'Home',
  title: '首页',
  icon: 'HomeOutlined',
  redirect: '/ai/welcome'
}

/**
 * 登录页
 */
const LOGIN_PAGE = '/login'

/**
 * 登录拦截排除
 * 根据路由fullPath匹配
 */
const LOGIN_IGNORE_PATHS = [LOGIN_PAGE, '/400', '/403', '/404', '/500', '/changepassword']

/**
 * 菜单
 * 会生在菜单中出现
 */
const MENU_ITEMS: MenuItem[] = []

/**
 * 基础路由
 * 不会出现在菜单中
 */
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/user',
    name: 'UserLayout',
    component: UserLayout,
    redirect: LOGIN_PAGE,
    children: [
      {
        path: LOGIN_PAGE,
        name: 'UserLogin',
        component: () => import('@/views/user/Login.vue')
      },
      {
        path: '/changepassword',
        name: 'UserChangePassword',
        component: () => import('@/views/user/ChangePassword.vue')
      }
    ]
  },
  {
    path: HOME_ROUTE.path,
    name: HOME_ROUTE.name,
    redirect: HOME_ROUTE.redirect,
    component: BasicLayout,
    children: [
      {
        path: '/home',
        name: 'HomePage',
        component: () => import('@/views/Home.vue'),
        meta: {
          affix: true,
          title: HOME_ROUTE.title,
          icon: HOME_ROUTE.icon
        }
      },
      {
        path: '/user/settings',
        name: 'UserSettings',
        component: () => import('@/views/user/settings/index.vue'),
        meta: {
          affix: false,
          title: '个人设置',
          icon: 'ControlOutlined'
        }
      },
      {
        path: '/user/center',
        name: 'UserCenter',
        component: () => import('@/views/user/center/index.vue'),
        meta: {
          affix: false,
          title: '个人中心',
          icon: 'ContactsOutlined'
        }
      }
    ]
  },
  {
    path: '/ai',
    name: 'Ai',
    redirect: '/ai/welcome',
    component: () => import('@/views/ai/index.vue'),
    children: [
      {
        path: '/ai/welcome',
        name: 'AiWelcome',
        component: () => import('@/views/ai/welcome.vue'),
        meta: {
          title: 'AI首页'
        }
      },
      {
        path: '/ai/chat',
        name: 'AiChat',
        component: () => import('@/views/ai/chat.vue'),
        meta: {
          title: 'AI会话'
        }
      },
      {
        path: '/ai/knowledge',
        name: 'AiKnowledge',
        component: () => import('@/views/ai/knowledge/index.vue'),
        meta: {
          title: '知识库'
        }
      },
      {
        path: '/ai/search',
        name: 'AiSearch',
        component: () => import('@/views/ai/search.vue'),
        meta: {
          title: '全文检索'
        }
      }
    ]
  },
  {
    path: '/ai/dynamicWebPage',
    name: 'dynamicWebPage',
    component: () => import('@/views/ai/dynamicWebPage.vue'),
    meta: {
      title: '正纪互融'
    }
  },
  {
    path: '/file/detail/:id',
    name: 'FileDetail',
    component: () => import('@/views/file/components/FileDetail.vue'),
    meta: { title: '文件详情' }
  },
  {
    path: '/v2/ai',
    name: 'AIV2',
    redirect: '/v2/ai/home',
    component: ChatLayout,
    children: [
      {
        path: 'home',
        name: 'AIV2ChatHome',
        component: () => import('@/views/chat/Home.vue'),
        meta: { title: 'AI会话' }
      },
      {
        path: 'chat',
        name: 'AIV2Chat',
        component: () => import('@/views/chat/Message.vue'),
        meta: { title: 'AI会话' }
      }
    ]
  },
  {
    path: '/voucher',
    redirect: '/voucher/info',
    component: BasicLayout,
    meta: { title: '凭证管理' },
    children: [
      {
        path: '/voucher/info',
        name: 'VoucherInfo',
        component: () => import('@/views/voucher/info/index.vue'),
        meta: { title: '凭证信息' }
      },
      {
        path: '/voucher/checkRule',
        name: 'VoucherCheckRule',
        component: () => import('@/views/voucher/checkRule/index.vue'),
        meta: { title: '检测规则' }
      },
      {
        path: '/voucher/task',
        name: 'VoucherTask',
        component: () => import('@/views/voucher/task/index.vue'),
        meta: { title: '分析任务' }
      },
      {
        path: '/voucher/alertRecord',
        name: 'alertRecord',
        component: () => import('@/views/voucher/alertRecord/index.vue'),
        meta: { title: '预警信息' }
      }
    ]
  },
  {
    path: '/400',
    name: 'Error400',
    component: () => import('@/views/error/400.vue'),
    meta: {
      title: '400'
    }
  },
  {
    path: '/403',
    name: 'Error403',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '403'
    }
  },
  {
    path: '/404',
    name: 'Error404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '404'
    }
  },
  {
    path: '/500',
    name: 'Error500',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: '500'
    }
  }
]

/**
 * 结束路由
 */
const endRoutes = [
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    redirect: '/404'
  }
]

const compMap: { [key: string]: any } = {
  BlankLayout,
  XRouteView,
  SystemConfig: () => import('@/views/system/config/index.vue'),
  SystemDict: () => import('@/views/system/dict/index.vue'),
  SystemDistrict: () => import('@/views/system/district/index.vue'),
  SystemPosition: () => import('@/views/system/position/index.vue'),
  SystemMenu: () => import('@/views/system/menu/index.vue'),
  SystemRole: () => import('@/views/system/role/index.vue'),
  SystemDept: () => import('@/views/system/dept/index.vue'),
  SystemUser: () => import('@/views/system/user/index.vue'),
  LogLogin: () => import('@/views/log/login/index.vue'),
  LogSystem: () => import('@/views/log/system/index.vue'),
  MonitorServer: () => import('@/views/monitor/server/index.vue'),
  MonitorRedis: () => import('@/views/monitor/redis/index.vue'),
  MonitorOnline: () => import('@/views/monitor/online/index.vue'),
  Resource: () => import('@/views/file/index.vue'), //
  ResourceRecycleBin: () => import('@/views/file/recyclebin/index.vue'), //回收站
  KnowledgeBase: () => import('@/views/knowledge/base/index.vue'), // 知识库
  // 关系图谱模块
  GraphCadresInfo: () => import('@/views/graph/basic/cadresInfo/index.vue'),
  GraphCadresFamily: () => import('@/views/graph/basic/cadresFamily/index.vue'),
  GraphEnterpriseInfo: () => import('@/views/graph/basic/enterpriseInfo/index.vue'),
  GraphView: () => import('@/views/graph/view/index.vue'),
  GraphFile: () => import('@/views/graph/file/index.vue'),
  DbtableTable: () => import('@/views/dbtable/table/index.vue'),
  DbtableView: () => import('@/views/dbtable/view/index.vue'),
  DbtableViewDesktop: () => import('@/views/dbtable/view/desktop.vue'),
  DbtableTask: () => import('@/views/dbtable/task/index.vue'),

  // 信访模块
  PetitionRepeatTask: () => import('@/views/petition/repeatTask/index.vue'),
  BatchIndex: () => import('@/views/petition/batch/index.vue'),
  PetitionXfkReport: () => import('@/views/petition/xfk/report/index.vue'),
  PetitionXfkReportMap: () => import('@/views/petition/xfk/yunnanMap/index.vue'),
  PetitionXfkAnalyzed: () => import('@/views/petition/xfk/analyzed/index.vue'),
  PetitionXfkFile: () => import('@/views/petition/xfk/file/index.vue'),
  PetitionTalkIndex: () => import('@/views/petition/talk/index.vue'),

  // 基础数据模块
  BankTransactionInfo: () => import('@/views/basdata/finance/bankTransactionInfo/index.vue'),
  ContractInfo: () => import('@/views/basdata/finance/contractInfo/index.vue'),
  InvoiceInfo: () => import('@/views/basdata/finance/invoiceInfo/index.vue'),
  TransactionDetailAccount: () =>
    import('@/views/basdata/finance/transactionDetailAccount/index.vue'),

  //凭证模块
  VoucherInfo: () => import('@/views/voucher/info/index.vue'),
  VoucherCheckRule: () => import('@/views/voucher/checkRule/index.vue'),
  VoucherTask: () => import('@/views/voucher/task/index.vue'),
  VoucherAlertRecord: () => import('@/views/voucher/alertRecord/index.vue'),

  // word转换器
  WordConverter: () => import('@/views/tool/wordconverter/index.vue'),
  // Es
  ElasticQuery: () => import('@/views/query/search/index.vue'), //全文检索
  IndexManage: () => import('@/views/query/indexManage/index.vue'), //索引管理

  // NLSQL知识库
  NLSQL: () => import('@/views/knowledge/nlsql/index.vue'),
  // 大数据MCP配置
  AIBigDataAnalyseConfig: () => import('@/views/ai/bigdata/analyseConfig/index.vue'),
  // JSON解析任务
  JSONAnalysisTask: () => import('@/views/jsontool/analysisTask/index.vue'),
  // 前端日志
  LogFront: () => import('@/views/log/front/index.vue')
}

export {
  HOME_ROUTE,
  LOGIN_PAGE,
  MENU_ITEMS,
  LOGIN_IGNORE_PATHS,
  constantRoutes,
  endRoutes,
  compMap
}
