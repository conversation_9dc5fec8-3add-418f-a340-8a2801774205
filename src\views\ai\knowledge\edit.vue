<script lang="ts" setup>
import { computed, defineExpose, reactive, ref, useTemplateRef } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { add, detail, edit } from '@/api/knowledge/base'
import { useKnowledgeStore } from '@/stores'
import errorLog from '@/utils/errorLog'

const knowledgeStore = useKnowledgeStore()
const visible = ref(false)
const formRef = useTemplateRef<FormInstance>('formRef')
const emits = defineEmits(['refresh'])
const formModule = reactive<{
  form: Form
  rules?: object
}>({
  form: {
    id: undefined,
    name: undefined,
    tag: undefined,
    purviewMark: 1,
    deptIds: undefined,
    remark: undefined
  },
  rules: {
    name: [{ required: true, trigger: 'blur' }]
  }
})
const formTitle = computed(() => {
  return formModule.form.id ? '编辑知识库' : '创建知识库'
})
const loading = ref(false)

interface Form {
  id?: string | undefined
  name: string | undefined
  tag: string | undefined
  purviewMark: number | undefined
  deptIds: string[] | undefined
  remark: string | undefined
}

async function init(item: any) {
  visible.value = true
  if (item.id) {
    const { code, data, message } = await detail(item.id)
    if (code === 200) {
      formModule.form = data
      if (!data.purviewMark) {
        formModule.form.purviewMark = 1
      }
    } else {
      errorLog.push({ msg: message, stack: 'init42', title: '知识库表单初始化失败', data: item })
    }
  } else {
    formModule.form = item
    formModule.form.purviewMark = 1
  }
}

function ok() {
  formRef.value?.validate().then(async () => {
    loading.value = false
    try {
      const api = formModule.form.id ? edit : add
      const res = await api(formModule.form)
      if (res.code === 200) {
        knowledgeStore.getKnowledge({ force: true } as object)
        emits('refresh')
        visible.value = false
      } else {
        errorLog.push({ msg: res.message, stack: 'ok71', title: '知识库表单保存失败', data: res })
      }
    } catch (err) {
      errorLog.push({
        msg: err.message,
        stack: err.stack,
        title: '知识库表单保存失败',
        data: formModule.form
      })
    } finally {
      loading.value = false
    }
  })
}

function cancel() {
  visible.value = false
}

function purviewChange() {
  formModule.form.deptIds = undefined
}

defineExpose({ init })
</script>

<template>
  <a-modal
    v-model:open="visible"
    :maskClosable="false"
    :title="formTitle"
    @cancel="cancel"
    @ok="ok"
  >
    <a-form
      ref="formRef"
      :disabled="loading"
      :label-col="{ style: { width: '90px' } }"
      :model="formModule.form"
      :rules="formModule.rules"
    >
      <a-form-item ref="name" label="知识库名称" name="name">
        <a-input v-model:value="formModule.form.name" />
      </a-form-item>
      <a-form-item label="知识库标签">
        <a-input v-model:value="formModule.form.tag" />
      </a-form-item>
      <a-form-item label="知识库描述">
        <a-textarea v-model:value="formModule.form.remark" />
      </a-form-item>
      <a-form-item
        :rules="{ required: true, trigger: 'change', message: '请选择是否公开' }"
        label="是否公开"
        name="purviewMark"
      >
        <a-radio-group v-model:value="formModule.form.purviewMark" @change="purviewChange">
          <a-radio :value="1">公开</a-radio>
          <a-radio :value="2">部门</a-radio>
          <a-radio :value="3">部门及以下</a-radio>
          <a-radio :value="4">私有</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="[2, 3].includes(formModule.form.purviewMark)"
        :rules="{
          required: [2, 3].includes(formModule.form.purviewMark),
          trigger: 'change',
          message: '请选择部门'
        }"
        label="可见部门"
        name="deptIds"
      >
        <x-dept-picker
          v-model:value="formModule.form.deptIds"
          allow-clear
          :multiple="true"
          ignore-status
          placeholder="请选择部门"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button :disabled="loading" :loading="loading" @click="cancel">取消</a-button>
      <a-button :disabled="loading" :loading="loading" type="primary" @click="ok">保存</a-button>
    </template>
  </a-modal>
</template>
