<template>
  <a-modal :open="visible" :title="`凭证告警详情 - ${voucherName}`" width="60%" :footer="null" @cancel="onCancel">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="alertData" class="alert-detail-content">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="凭证名称">
          {{ alertData.voucherName }}
        </a-descriptions-item>
        <a-descriptions-item label="分析类型">
          {{ alertData.taskName || '单文件分析' }}
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ alertData.createBy }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ $date.formatDateTime(alertData.createTime) }}
        </a-descriptions-item>
      </a-descriptions>

      <div class="alert-content-section">
        <h4>告警内容：</h4>
        <ContentTable :content="alertData.content" />
      </div>
    </div>

    <div v-else class="no-data">
      <a-empty description="该凭证暂无告警记录" />
    </div>
  </a-modal>
</template>

<script setup lang="ts" name="InfoAlertModal">
import { getCurrentInstance, ref, watch, type ComponentCustomProperties } from 'vue'
import { alertRecord } from '@/api/voucher'
import ContentTable from '../components/ContentTable.vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean
  voucherId?: string
  voucherName?: string
}>()

const loading = ref(false)
const alertData = ref(null)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.voucherId) {
      loadAlertData()
    }
  }
)

function onCancel() {
  alertData.value = null
  emits('update:visible', false)
}

/**
 * 加载告警数据
 */
async function loadAlertData() {
  if (!props.voucherId) return

  try {
    loading.value = true
    const { data } = await alertRecord.detailByVoucherId(props.voucherId)
    alertData.value = data
  } catch (error) {
    _this.$message.error('获取告警详情失败')
    alertData.value = null
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="less">
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.alert-detail-content {
  .alert-content-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 10px;
      color: #333;
    }

    .alert-content {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      border-left: 4px solid #ff4d4f;
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.6;
      min-height: 100px;
    }
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
