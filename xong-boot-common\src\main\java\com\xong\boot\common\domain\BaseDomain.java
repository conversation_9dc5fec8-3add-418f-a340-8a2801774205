package com.xong.boot.common.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DAO基类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseDomain extends SimpleBaseDomain {
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @Size(max = 500, message = "备注必须在500字内", groups = {AddGroup.class, UpdateGroup.class})
    @TableField(whereStrategy = FieldStrategy.NEVER)
    private String remark;
}
