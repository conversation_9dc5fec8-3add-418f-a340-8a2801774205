import Qs from 'qs'
import { FetchHttp, Http, type HttpConfig } from './helpers'
import { useUserStore } from '@/stores'
import { Modal } from 'ant-design-vue'
import { LOGIN_PAGE } from '@/config/router.config'
import errorLog from '@/utils/errorLog'

/**
 * 创建http请求实例
 * Qs.stringify({ids: [1, 2, 3]}, { indices: false }) //形式： ids=1&ids=2&ids=3
 * Qs.stringify({ids: [1, 2, 3]}, {arrayFormat: 'indices'}) //形ids[0]=1&ids[1]=2&ids[2]=3
 * Qs.stringify({ids: [1, 2, 3]}, {arrayFormat: 'brackets'}) //形式：ids[]=1&ids[]=2&ids[]=3
 * Qs.stringify({ids: [1, 2, 3]}, {arrayFormat: 'repeat'}) //形式： ids=1&ids=2&ids=3
 */
function createAxios(config: HttpConfig) {
  return new Http({
    timeout: 3000,
    paramsSerializer: (params: any) => {
      return Qs.stringify(params, { arrayFormat: 'repeat' })
    },
    ...config
  })
}

/**
 * 创建fetch请求实例
 */
function createFetch(config: RequestInit & { baseURL: string }) {
  return new FetchHttp(config)
}

const http = createAxios({
  baseURL: import.meta.env.VITE_HTTP_BASE_URL,
  timeout: import.meta.env.VITE_HTTP_TIMEOUT
})

const fetchHttp = createFetch({
  baseURL: import.meta.env.VITE_HTTP_BASE_URL
})

const aiHttpt = createAxios({
  baseURL: import.meta.env.VITE_AI_BASE_URL,
  timeout: import.meta.env.VITE_HTTP_TIMEOUT,
  withToken: false,
  headers: {
    Authorization: import.meta.env.VITE_AI_TOKEN
  },
  ignoreCancelToken: true
})

interface steamArgs {
  url: string
  method: string
  body: any
  headers?: object
  signal?: any
  success: Function
  error?: Function
  complete?: Function
}

async function fetchStream({
  url,
  method = 'post',
  headers = { 'Content-Type': 'application/json' },
  body,
  signal,
  success = () => {},
  error = () => {},
  complete = () => {}
}: steamArgs) {
  try {
    const res = await fetch(url, {
      method,
      headers,
      body,
      signal
    })
    // 处理http报错
    res
      .clone()
      .json()
      .then((data) => {
        if (data.code === 14002) {
          const userStore = useUserStore()
          Modal.confirm({
            title: '提示！',
            content: data.message,
            onOk(destroy) {
              userStore.resetToken()
              window.location.href = LOGIN_PAGE
              destroy()
            }
          })
        } else {
          errorLog.push({
            msg: data.message,
            stack: `fetchStream`,
            title: '流式请求失败',
            data: { body, data }
          })
          error(data.message)
        }
      })
      .catch(() => {
        //   不需要处理
      })

    const response = res.clone()
    if (!response.ok || !response.body) {
      errorLog.push({
        msg: '爆炸了',
        stack: `fetchStream`,
        title: '网络错误或不支持流式响应',
        data: response
      })
      throw new Error(`网络错误或不支持流式响应: ${response.status} ${response.statusText}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let done = false
    let buffer = '' // 缓存未处理完的文本
    while (!done) {
      const { value, done: streamDone } = await reader.read()
      done = streamDone
      if (value) {
        const chunk = decoder.decode(value, { stream: !done })
        buffer += chunk

        // 拆分成行进行处理
        const lines = buffer.split('\n')
        buffer = lines.pop() // 保留最后一行（可能不完整）

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.slice(5) // 去掉 'data:' 和前后空格
            if (data) {
              success(data) // 直接返回原始字符串
            }
          }
        }
      }
    }
  } catch (err) {
    if (err.name === 'AbortError') {
      //   手动中断
    } else {
      errorLog.push({ msg: err.message, stack: err.stack, title: '流式请求报错了', data: body })
      error(err.message)
    }
  } finally {
    complete()
  }
}

export { http, fetchHttp, fetchStream, aiHttpt }
