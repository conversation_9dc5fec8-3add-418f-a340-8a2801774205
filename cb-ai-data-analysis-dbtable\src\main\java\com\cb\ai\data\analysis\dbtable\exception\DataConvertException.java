package com.cb.ai.data.analysis.dbtable.exception;

/**
 * 数据转换异常
 * <AUTHOR>
 */
public class DataConvertException extends Exception {
    public DataConvertException(String message) {
        super(message);
    }

    public DataConvertException(String message, Throwable cause) {
        super(message, cause);
    }

    public DataConvertException(Throwable cause) {
        super(cause);
    }

    public DataConvertException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
