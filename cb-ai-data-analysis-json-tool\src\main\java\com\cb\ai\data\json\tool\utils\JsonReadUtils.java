package com.cb.ai.data.json.tool.utils;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cb.ai.data.json.tool.constants.Constant;
import com.cb.ai.data.json.tool.domain.ReportContextData;
import com.cb.ai.data.json.tool.reg.JsonRegExp;
import com.xong.boot.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * JsonReadUtils
 *
 * <AUTHOR>
 * 2025/7/31
 */
@Slf4j
public class JsonReadUtils {


    /**
     * 读取JSON数组并转换为List
     *
     * @param jsonContent JSON内容
     * @return
     */
    /**
     * 读取配置文件JSON内容并转换为ReportContextData对象
     *
     * @param jsonContent 配置文件的JSON字符串内容
     * @return ReportContextData对象，包含报表配置信息
     */
    public static ReportContextData readConfig(String jsonContent) {
        String content = jsonContent;
        // 检查输入内容是否为空
        if (StringUtils.isBlank(jsonContent)) {
            return null;
        }
        // 移除报表上下文前缀，获取纯净的JSON内容
        jsonContent = removeReportContextPrefix(jsonContent, JsonRegExp.REPORT_CONTEXT_CONFIG);
        // 使用 Hutool 解析 JSON
        JSONObject jsonObject = JSONUtil.parseObj(jsonContent);
        // 获取是否为纵向报表的标识
        Boolean isVertical = jsonObject.getBool("isVertical");
        // 获取列配置信息
        JSONArray columns = jsonObject.getJSONArray("columns");
        // 将列配置信息转换为ReportConfig列表
        LinkedList<ReportContextData.ReportConfig> configList = IntStream.range(0, columns.size())
                .mapToObj(i -> {
                    JSONObject column = columns.getJSONObject(i);
                    String title = column.getStr("title");
                    String dataIndex = column.getStr("dataIndex");
                    JSONObject scopedSlots = (JSONObject) column.get("scopedSlots");
                    return new ReportContextData.ReportConfig(i, title, dataIndex, scopedSlots);
                })
                .collect(Collectors.toCollection(LinkedList::new));
        // 构造并返回ReportContextData对象
        ReportContextData data = new ReportContextData(isVertical, configList);
        Map<String, String> headMap = configList.stream().collect(Collectors.toMap(ReportContextData.ReportConfig::getDataIndex, ReportContextData.ReportConfig::getTitle));
        data.setHeaderMap(headMap);
        return data;
    }

    /**
     * 读取JSON数组并转换为List
     *
     * @param jsonContent
     * @return
     */
    /**
     * 读取数据文件JSON内容并转换为List<LinkedHashMap<Integer, Object>>对象
     *
     * @param jsonContent 数据文件的JSON字符串内容
     * @param contextData 报表上下文数据对象，包含列配置信息
     * @return List<LinkedHashMap < Integer, Object>>对象，表示报表数据列表
     */
    public static List<LinkedHashMap<Integer, Object>> readJSONObjectList(
            String jsonContent,
            ReportContextData contextData) {
        try {
            // 检查输入内容是否为空
            if (StringUtils.isBlank(jsonContent)) {
                return List.of();
            }
            // 移除报表上下文前缀，获取纯净的JSON内容
            jsonContent = removeReportContextPrefix(jsonContent, JsonRegExp.REPORT_CONTEXT_DATA);
            // 使用 Hutool 解析 JSON 数组
            // 创建JSON配置对象，并设置不忽略空值，以确保空值也能被正确解析和处理
            JSONArray jsonArray = JSONUtil.parseArray(jsonContent, false);
            // 获取列配置信息
            LinkedList<ReportContextData.ReportConfig> columns = contextData.getColumns();

            // 如果列配置为空且JSON数组不为空，则需要动态生成列配置
            if (!jsonArray.isEmpty()) {
                JSONObject column = (JSONObject) jsonArray.get(0);
//                List<String> headers = flattenJSONObjectKey(!columns.isEmpty() ? column : column.getJSONObject("Obj")); // 特殊处理，获取JSON对象中的所有键
                List<String> headers = flattenJSONObjectKey(column);// 不进行特殊处理，直接获取JSON对象，使程序兼容性更好
                // 根据提取的表头动态生成列配置
                LinkedList<ReportContextData.ReportConfig> headConfig = IntStream.range(0, headers.size())
                        .mapToObj(i -> {
                            String head = headers.get(i);
                            return new ReportContextData.ReportConfig(i, head, head, null);
                        })
                        .collect(Collectors.toCollection(LinkedList::new));
                // 更新上下文数据中的列配置
                contextData.setColumns(headConfig);
                // 构建表头映射，用于后续数据映射
                Map<String, Integer> headMap = buildHeadMap(headConfig); // 构建表头映射

                // 处理JSON数组中的每个对象，将其转换为LinkedHashMap<Integer, Object>格式
                return jsonArray.stream()
                        .map(json -> {
//                            JSONObject entries = !columns.isEmpty() ? (JSONObject) json : ((JSONObject) json).getJSONObject("Obj");
                            JSONObject entries = (JSONObject) json; // 不进行特殊处理，直接获取JSON对象，使程序兼容性更好
                            return flattenJSONObject(entries);
                        })
                        .map(m -> {
                            LinkedHashMap<Integer, Object> linkedMap = new LinkedHashMap<>();
                            for (Map.Entry<String, Object> entry : m.entrySet()) {
                                String key = entry.getKey();
                                Object value = entry.getValue();
                                Integer index = headMap.get(key);
                                if (index != null) {
                                    linkedMap.put(index, value);
                                }
                            }
                            return linkedMap;
                        }).collect(Collectors.toList());
            }
            /*else {
                // 列配置已存在，直接构建表头映射
                Map<String, Integer> headMap = buildHeadMap(columns);
                // 处理JSON数组中的每个对象，将其转换为LinkedHashMap<Integer, Object>格式
                List<LinkedHashMap<Integer, Object>> collect = jsonArray.stream()
                        .map(jsonObject -> (Map<String, Object>) jsonObject)
                        .map(m -> {
                            LinkedHashMap<Integer, Object> linkedMap = new LinkedHashMap<>();
                            for (Map.Entry<String, Object> entry : m.entrySet()) {
                                String key = entry.getKey();
                                Object value = entry.getValue();
                                Integer index = headMap.get(key);
                                if (index != null) {
                                    linkedMap.put(index, value);
                                }
                            }
                            return linkedMap;
                        }).collect(Collectors.toList());
                return collect;
            }*/
            return List.of();
        } catch (Exception e) {
            // 记录异常信息并返回空列表
            e.printStackTrace();
            return List.of();
        }
    }

    /**
     * 构建表头映射
     * 将报表配置列表转换为以dataIndex为键、index为值的映射关系，用于后续数据映射
     *
     * @param headConfig 报表配置列表，包含列的索引和数据字段信息
     * @return Map<String, Integer> 表头映射，键为数据字段名，值为列索引
     */
    private static Map<String, Integer> buildHeadMap(LinkedList<ReportContextData.ReportConfig> headConfig) {
        return headConfig
                .stream()
                .collect(Collectors.toMap(ReportContextData.ReportConfig::getDataIndex, ReportContextData.ReportConfig::getIndex));
    }

    /**
     * 递归处理JSON对象，提取所有层级的键（key）
     * 对于嵌套的JSONObject，会将父级key与子级key通过 {@link Constant#DELIMITER} 连接
     * 对于JSONArray，目前暂不展开处理，直接返回当前key
     *
     * @param jsonObject 需要处理的JSONObject
     * @return 包含所有层级键的字符串列表
     */
    public static List<String> flattenJSONObjectKey(JSONObject jsonObject) {
        Set<String> strings = jsonObject.keySet();
        return strings.stream().map(key -> {
            Object value = jsonObject.get(key);
            if (value instanceof JSONObject) {
                // 如果值是JSONObject，递归调用并拼接键名
                List<String> nestedKeys = flattenJSONObjectKey((JSONObject) value);
                return nestedKeys.stream()
                        .map(nestedKey -> key + Constant.DELIMITER + nestedKey)
                        .collect(Collectors.toList());
            } else if (value instanceof JSONArray) {
                /**
                 * TODO 尝试处理 JSONArray，但是一旦出现多条，会出现异常Duplicate key；
                 * TODO 所以暂时无法进行JSONArray展开，还不如直接返回，导出时进行toString处理
                 */
               /* JSONArray array = (JSONArray) value;
                return array.stream()
                        .map(item -> {
                            if (item instanceof JSONObject) {
                                return flattenJSONObjectKey((JSONObject) item).stream()
                                        .map(nestedKey -> key + Constant.FIELD_DELIMITER + nestedKey)
                                        .collect(Collectors.toList());
                            } else {
                                return List.of(key);
                            }
                        })
                        .flatMap(List::stream)
                        .collect(Collectors.toList());*/
                // 暂时不对JSONArray做展开处理，直接返回当前key
                return List.of(key);
            } else {
                // 基本类型值，直接返回当前key
                return List.of(key);
            }
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 递归处理JSON对象，将嵌套的JSONObject扁平化为键值对形式
     * 对于嵌套的JSONObject，会将父级key与子级key通过{@link Constant#DELIMITER}连接
     * 对于JSONArray，目前暂不展开处理，直接保留原值
     *
     * @param jsonObject 需要处理的JSONObject
     * @return 扁平化后的键值对映射
     */
    public static Map<String, Object> flattenJSONObject(JSONObject jsonObject) {
        return jsonObject.entrySet().stream()
                .flatMap(entry -> {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if (value == null || "null".equals(value.toString())) {
                        return Stream.empty(); // 返回空流而不是null，避免NPE
                    } else if (value instanceof String) {
                        // 对字符串进行处理并返回单个键值对
                        String processedValue = UnicodeUtil.toString(removeControlCharacters((String) value));
                        return Stream.of(Map.entry(key, processedValue));
                    } else if (value instanceof JSONObject) {
                        // 递归处理嵌套的 JSONObject
                        return flattenJSONObject((JSONObject) value).entrySet().stream()
                                .map(nestedEntry -> Map.entry(key + Constant.DELIMITER + nestedEntry.getKey(), nestedEntry.getValue()));
                    } else if (value instanceof JSONArray) {
                        if ("Contents".equals(key)) { // 特殊处理Contents 字段
                            return Stream.of(Map.entry(key, fomartJSONArray2String((JSONArray) value)));
                        }
                        return Stream.of(Map.entry(key, value.toString()));
                    } else if (value instanceof Boolean) {
                        // 如果是布尔型，转换为小写字符串输出
                        return Stream.of(Map.entry(key, value.toString().toLowerCase()));
                    } else {
                        // 直接返回当前键值对
                        return Stream.of(Map.entry(key, value));
                    }
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 移除内容上下文前缀
     * 该方法用于去除JSON内容中可能存在的上下文前缀信息，例如报表配置或数据的标识性前缀。
     * 通过正则表达式匹配并移除这些前缀，从而获取纯净的JSON字符串。
     *
     * @param content 原始JSON内容字符串，可能包含上下文前缀
     * @param regex   用于匹配上下文前缀的正则表达式
     * @return 移除前缀后的纯净JSON字符串；如果输入为空或正则表达式无效，则返回空字符串
     */
    private static String removeReportContextPrefix(String content, String regex) {
        // 检查输入内容和正则表达式是否为空
        if (StringUtils.isBlank(content) || StringUtils.isBlank(regex)) {
            return "";
        }
        // 编译传入的正则表达式
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        // 使用正则表达式替换匹配到的内容为空字符串，实现前缀移除
        return matcher.replaceAll("");
    }

    /**
     * 读取JSON文件
     *
     * @param inputStream 输入流
     * @return JSON文件内容字符串
     * @throws IOException IO异常
     */
    public static String processJsonFile(FileInputStream inputStream) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
            String jsonString = jsonBuilder.toString();
            // 继续处理 jsonString
            return jsonString;
        } catch (IOException e) {
            // 打印IO异常堆栈跟踪信息
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用正则表达式匹配文件名
     *
     * @param fileName 要匹配的文件名
     * @param regex    正则表达式
     * @return 如果文件名匹配正则表达式则返回true，否则返回false
     */
    public static boolean regexMatchFileName(String fileName, String regex) {
        // 检查文件名和正则表达式是否为空
        if (StringUtils.isBlank(fileName) || StringUtils.isBlank(regex)) {
            return false;
        }
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建匹配器
        Matcher matcher = pattern.matcher(fileName);
        // 执行匹配并返回结果
        return matcher.matches();
    }

    public static String fomartJSONArray2String(JSONArray jsonArray) {
        List<String> collect = jsonArray.stream().map(item -> {
            if (item instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) item;
                String type = jsonObject.getStr("Type");
                String value = jsonObject.getStr("Value");
                if (StringUtils.isNotBlank(type)) {
                    // TODO 处理Text类型 可接着扩展其他类型
                    return switch (type) {
                        case "Text" -> UnicodeUtil.toString(removeControlCharacters(value));
                        default -> value;
                    };
                }
            }
            return item.toString();
        }).collect(Collectors.toList());
        return String.join(";", collect);
    }

    /**
     * 去除字符串中的制表符、换行符、回车符等不可见字符
     */
    public static String removeControlCharacters(String input) {
        if (input == null) {
            return null; // 如果输入为 null，直接返回 null
        }
        // 使用正则表达式匹配并移除控制字符
        return input.replaceAll("[\\t\\n\\r\\p{C}]", "");
    }

}
