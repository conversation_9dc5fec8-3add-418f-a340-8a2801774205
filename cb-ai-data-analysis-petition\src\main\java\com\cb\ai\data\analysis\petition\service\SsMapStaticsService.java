package com.cb.ai.data.analysis.petition.service;



import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionPurposeDateQueryConditionVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.RegisterDateQueryCondition;
import com.cb.ai.data.analysis.petition.domain.vo.request.SelfDeptSendOrgNameQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsStaticsQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo;
import com.xong.boot.common.api.Result;
import java.util.List;

/***
 * <AUTHOR>
 * 信访报告图表相关
 */
public interface SsMapStaticsService {

    List<MapStaticsVo> countByRegion(SsStaticsQueryVo ssStaticsQueryVo);

    List<MapStaticsVo> countByDomain(SsStaticsQueryVo ssStaticsQueryVo);

    Result selectOptions(Integer type);

    Result countBySelfDeptSendOrgName(SelfDeptSendOrgNameQueryVo selfDeptSendOrgNameQueryVo);

    Result registerDate(RegisterDateQueryCondition registerDateQueryCondition);

    Result petitionPurpose(PetitionPurposeDateQueryConditionVo petitionPurposeDateQueryConditionVo);

    Result analyzedStatus();

 }
