package com.cb.ai.data.analysis.graph.repository.esBo;


import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.time.LocalDateTime;

/**
 * 知识图谱-企业信息(GraphEnterpriseInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-04 11:06:14
 */
@Data
@Document(indexName = Constant.ES_GRAPH_DATA_INDEX + Constant.SLICING + "graph_enterprise_info")
@Setting(shards = 1, replicas = 0)
public class GraphEnterpriseInfoBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    //ID
    @Id
    private String id;

    //企业名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String enterpriseName;

    //法定代表人
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String legalRepresentative;

    //注册资本
    @Field(type = FieldType.Text)
    private String registerCapital;

    //登记状态
    @Field(type = FieldType.Keyword)
    private String registerStatus;

    //统一社会信用代码
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String unifySocialCreditCode;

    //成立日期
    @Field(type = FieldType.Long)
    private LocalDateTime foundDate;

    //工商注册号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String industryCommerceRegisterNo;

    //纳税人识别号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String taxpayerIdentifyNo;

    //企业类型
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String enterpriseType;

    //核准日期
    @Field(type = FieldType.Long)
    private LocalDateTime approveDate;

    //曾用名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String formerName;

    //实缴资本
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String contributedCapital;

    //所属省份
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String province;

    //所属城市
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String city;

    //所属区县
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String county;

    //股东人员
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String shareholder;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;
}

