package com.cb.ai.data.analysis.ai.controller;

import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.cb.ai.data.analysis.ai.model.AIContext;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.provider.AiProviderManager;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.xong.boot.common.api.Result;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * AI会话 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(AiConstants.API_AI_ROOT_PATH)
public class AiChatController {
    private final AiProviderManager providerManager;
    private final AiConfigService aiConfigService;

    public AiChatController(AiProviderManager providerManager, AiConfigService aiConfigService) {
        this.providerManager = providerManager;
        this.aiConfigService = aiConfigService;
    }

    /**
     * 获取配置
     */
    @GetMapping("/config")
    public Result globalConfig() {
        return Result.successData(aiConfigService.getAiConfig());
    }

    /**
     * 修改配置
     * @param config 系统配置参数
     */
    @Validated
    @PutMapping("/config")
    public Result edit(@NotNull(message = "配置不存在") @RequestBody AiConfig config) {
        if (aiConfigService.updateAiConfig(config)) {
            return Result.success("修改成功");
        }
        return Result.fail("修改失败");
    }

    /**
     * 重置配置
     */
    @PutMapping("/config/reset")
    public Result reset() {
        aiConfigService.resetAiConfig();
        return Result.success("重置成功");
    }

    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<?> executeChat(@RequestBody AIContext context) {
        return providerManager.getFlux(context);
    }
}
