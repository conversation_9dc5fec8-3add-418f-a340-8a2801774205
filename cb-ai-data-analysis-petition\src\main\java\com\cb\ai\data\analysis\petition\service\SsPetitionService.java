//package com.cb.ai.data.analysis.petition.service;
//
//import com.cb.ai.data.analysis.petition.domain.vo.PageResultVo;
//import com.cb.ai.data.analysis.petition.domain.vo.request.DeleteRawRequestVo;
//import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionOriginQueryPageQueryVo;
//import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
//import com.cb.ai.data.analysis.petition.domain.vo.request.SsPetitionFileSearchRequestVo;
//import com.cb.ai.data.analysis.petition.domain.vo.response.SsPetitionVo;
//import com.xong.boot.common.api.Result;
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.web.multipart.MultipartFile;
//import java.io.IOException;
//import java.util.List;
//
//public interface SsPetitionService {
//
//   public Result aiAnalysis(String[] fileIds) throws IOException;
//}
