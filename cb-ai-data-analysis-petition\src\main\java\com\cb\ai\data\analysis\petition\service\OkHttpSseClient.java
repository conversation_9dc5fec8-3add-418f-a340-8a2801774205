package com.cb.ai.data.analysis.petition.service;

import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import okio.BufferedSink;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

public class OkHttpSseClient {
    private static final Logger logger = Logger.getLogger(OkHttpSseClient.class.getName());

    private final OkHttpClient client;
    private EventSource eventSource;
    private String lastEventId;

    public interface SseListener {
        void onOpen();
        void onEvent(String event, String data, String id);
        void onClosed();
        void onFailure(Throwable t);
    }

    public OkHttpSseClient() {
        this.client = new OkHttpClient.Builder()
                .readTimeout(0, TimeUnit.SECONDS) // 无读取超时
                .connectTimeout(100, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 使用POST方法连接到SSE服务器
     * @param url SSE服务地址
     * @param headers 自定义请求头
     * @param requestBody 要发送的请求体内容
     * @param listener 事件监听器
     */
    public void connectWithPost(String url, Map<String, String> headers, String requestBody, SseListener listener) {
        // 创建请求体
        MediaType mediaType = MediaType.get("application/json; charset=utf-8");
//        RequestBody body = okhttp3.RequestBody.create(requestBody,mediaType);
        RequestBody streamBody = createStreamBody(requestBody);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(streamBody)  // 设置为POST方法
                .header("Accept", "text/event-stream")
                .header("Cache-Control", "no-cache");

        // 添加自定义请求头
        if (headers != null) {
            headers.forEach(requestBuilder::header);
        }

        // 如果存在上次事件的ID，发送Last-Event-ID头
        if (lastEventId != null && !lastEventId.isEmpty()) {
            requestBuilder.header("Last-Event-ID", lastEventId);
        }

        EventSource.Factory factory = EventSources.createFactory(client);

        eventSource = factory.newEventSource(requestBuilder.build(), new EventSourceListener() {
            @Override
            public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                logger.info("SSE连接已建立");
                listener.onOpen();
            }

            @Override
            public void onEvent(@NotNull EventSource eventSource,
                                @Nullable String id,
                                @Nullable String type,
                                @NotNull String data) {
                // 记录最后接收到的事件ID
                if (id != null) {
                    lastEventId = id;
                }

                String eventType = type != null ? type : "message";
                logger.log(Level.INFO, "收到事件: {0}, 数据: {1}", new Object[]{eventType, data});
                listener.onEvent(eventType, data, id);
            }

            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                logger.info("SSE连接已关闭");
                listener.onClosed();
            }

            @Override
            public void onFailure(@NotNull EventSource eventSource,
                                  @Nullable Throwable t,
                                  @Nullable Response response) {
                String errorMsg = "SSE连接失败: ";
                if (t != null) {
                    errorMsg += t.getMessage();
                } else if (response != null) {
                    errorMsg += "HTTP " + response.code();
                }
                logger.log(Level.SEVERE, errorMsg);
                listener.onFailure(t != null ? t : new RuntimeException(errorMsg));

                // 自动重连逻辑可以在这里实现
            }
        });
    }

    /**
     * 关闭SSE连接
     */
    public void disconnect() {
        if (eventSource != null) {
            eventSource.cancel();
        }
        if (client != null) {
            client.dispatcher().executorService().shutdown();
        }
    }

    // 方案2：流式处理（适用于500MB+数据）
    public static RequestBody createStreamBody(String hugeJson) {
        return new RequestBody() {
            @Override
            public MediaType contentType() {
                return MediaType.get("application/json; charset=utf-8");
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                try (BufferedWriter writer = new BufferedWriter(
                        new OutputStreamWriter(sink.outputStream(), StandardCharsets.UTF_8))) {
                    // 分块写入（每次写入64KB）
                    int chunkSize = 65536;
                    for (int offset = 0; offset < hugeJson.length(); offset += chunkSize) {
                        int end = Math.min(offset + chunkSize, hugeJson.length());
                        writer.write(hugeJson, offset, end - offset);
                        writer.flush(); // 确保及时释放内存
                    }
                }
            }
        };
    }
}