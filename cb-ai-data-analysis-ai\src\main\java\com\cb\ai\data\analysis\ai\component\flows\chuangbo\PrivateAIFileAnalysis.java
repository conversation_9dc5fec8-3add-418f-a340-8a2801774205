package com.cb.ai.data.analysis.ai.component.flows.chuangbo;


import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.model.PrivateRequestContext;
import com.cb.ai.data.analysis.ai.domain.common.MinioFileData;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 09:42
 * @Copyright (c) 2025
 * @Description 私有化底座材料分析
 */
public class PrivateAIFileAnalysis extends BasePrivateAiPropertiesNode {

    @Override
    public String getNodeName() {
        return "私有化-材料分析-接口";
    }

    @Override
    public String setRequestUrl() {
        return MergeUtil.mergePath(aiProp.getBaseUrl(), aiProp.getFileAnalysis());
    }

    @Override
    public PrivateRequestContext setRequestBody() {
        List<MinioFileData> minioFileDataList = getRequestContext().getMinioFileDataList();
        Assert.notEmpty(minioFileDataList, "材料分析，minioFileDataList is empty");
        PrivateRequestContext context = (PrivateRequestContext) super.setRequestBody();
        // 目前接口只支持单文件分析
        context.setFileName(minioFileDataList.get(0).fileName());
        context.setFileUrl(minioFileDataList.get(0).fileUrl());
        return context;
    }

}
