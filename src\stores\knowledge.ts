import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as base from '@/api/knowledge/base'
import { message } from 'ant-design-vue'
import errorLog from '@/utils/errorLog'

export interface Knowledge {
  id: string
  name: string
  parentId:string,
  tag: string
  remark: string
  permissionMark: string
  fileCount: number
}

export const useKnowledgeStore = defineStore('knowledge', () => {
  let proms: Promise<void> | undefined = undefined
  const list = ref<Knowledge[]>([])
  const current = ref([]) //当前选择的知识库id
  function getKnowledge({ force = false } = { force: false }) {
    if (proms) {
      return proms
    } else if (force || list.value.length === 0) {
      proms = base
        .list()
        .then((res: any) => {
          if (res.code === 200) {
            list.value = res.data
          } else {
            errorLog.push({
              msg: res.message,
              stack: 'getKnowledge',
              title: '获取知识库失败',
              data: res
            })
            message.error(res.message)
          }
          return res
        })
        .finally(() => {
          proms = undefined
        })
    }
  }

  return { current, list, getKnowledge }
})
