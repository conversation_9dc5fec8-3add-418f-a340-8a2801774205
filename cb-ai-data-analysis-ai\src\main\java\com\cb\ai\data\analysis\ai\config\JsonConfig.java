package com.cb.ai.data.analysis.ai.config;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.json.PackageVersion;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/5/27 13:54
 * @Copyright (c) 2025
 * @Description jackson配置类
 */
public class JsonConfig {
    public static Jackson2ObjectMapperBuilder customizer(Jackson2ObjectMapperBuilder builder) {
        if (builder == null) {
            builder = new Jackson2ObjectMapperBuilder();
        }
        builder.locale(Locale.CHINA);
        builder.timeZone(TimeZone.getTimeZone(ZoneId.systemDefault()));
        // 设置日期格式
        builder.simpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        // 解决long类型损失精度
        builder.serializerByType(Long.class, ToStringSerializer.instance);
        // 日期格式自定义类
        builder.modules(new JavaTimeModule(), new CustomJavaTimeModule());
        return builder;
    }

    /**
     * @description: 此处定义了序列化规则，会覆盖原来的规则，现在的规则无法解析 yyyy-MM-dd'T'HH:mm:ss.SSS 格式
     */
    public static class CustomJavaTimeModule extends SimpleModule {
        public CustomJavaTimeModule() {
            super(PackageVersion.VERSION);
            // ======================= 时间序列化规则 ===============================
            // yyyy-MM-dd HH:mm:ss | yyyy-MM-dd HH:mm
            this.addSerializer(LocalDateTime.class, new CustomLocalDateTimeSerializer());
            // yyyy-MM-dd
            this.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            // HH:mm:ss
            this.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN)));

            // ======================= 时间反序列化规则 ==============================
            // yyyy-MM-dd HH:mm:ss | yyyy-MM-dd HH:mm
            this.addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer());
            // yyyy-MM-dd
            this.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            // HH:mm:ss
            this.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN)));
        }

        private class CustomLocalDateTimeSerializer extends LocalDateTimeSerializer {
            private final DateTimeFormatter minute_formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_MINUTE_PATTERN);

            private final DateTimeFormatter norm_formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);

            @Override
            public void serialize(LocalDateTime value, JsonGenerator g, SerializerProvider provider) throws IOException {
                if (this.useTimestamp(provider)) {
                    super.serialize(value, g, provider);
                } else {
                    if (value.toString().length() > 16) {
                        g.writeString(value.format(norm_formatter));
                    } else {
                        g.writeString(value.format(minute_formatter));
                    }
                }
            }
        }

        private static class CustomLocalDateTimeDeserializer extends LocalDateTimeDeserializer {
            private final DateTimeFormatter minute_formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_MINUTE_PATTERN);

            private final DateTimeFormatter norm_formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);

            @Override
            public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
                if (parser.hasTokenId(6)) {
                    return this._fromString(parser, context, parser.getText());
                } else if (parser.isExpectedStartObjectToken()) {
                    return this._fromString(parser, context, context.extractScalarFromObject(parser, this, this.handledType()));
                } else {
                    if (parser.isExpectedStartArrayToken()) {
                        JsonToken t = parser.nextToken();
                        if (t == JsonToken.END_ARRAY) {
                            return null;
                        }
                        LocalDateTime result;
                        if ((t == JsonToken.VALUE_STRING || t == JsonToken.VALUE_EMBEDDED_OBJECT) && context.isEnabled(DeserializationFeature.UNWRAP_SINGLE_VALUE_ARRAYS)) {
                            result = this.deserialize(parser, context);
                            if (parser.nextToken() != JsonToken.END_ARRAY) {
                                this.handleMissingEndArrayForSingle(parser, context);
                            }
                            return result;
                        }

                        if (t == JsonToken.VALUE_NUMBER_INT) {
                            int year = parser.getIntValue();
                            int month = parser.nextIntValue(-1);
                            int day = parser.nextIntValue(-1);
                            int hour = parser.nextIntValue(-1);
                            int minute = parser.nextIntValue(-1);
                            t = parser.nextToken();
                            if (t == JsonToken.END_ARRAY) {
                                result = LocalDateTime.of(year, month, day, hour, minute);
                            } else {
                                int second = parser.getIntValue();
                                t = parser.nextToken();
                                if (t == JsonToken.END_ARRAY) {
                                    result = LocalDateTime.of(year, month, day, hour, minute, second);
                                } else {
                                    int partialSecond = parser.getIntValue();
                                    if (partialSecond < 1000 && !context.isEnabled(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS)) {
                                        partialSecond *= 1000000;
                                    }

                                    if (parser.nextToken() != JsonToken.END_ARRAY) {
                                        throw context.wrongTokenException(parser, this.handledType(), JsonToken.END_ARRAY, "Expected array to end");
                                    }

                                    result = LocalDateTime.of(year, month, day, hour, minute, second, partialSecond);
                                }
                            }

                            return result;
                        }
                        context.reportInputMismatch(this.handledType(), "Unexpected token (%s) within Array, expected VALUE_NUMBER_INT", new Object[]{t});
                    }

                    if (parser.hasToken(JsonToken.VALUE_EMBEDDED_OBJECT)) {
                        return (LocalDateTime) parser.getEmbeddedObject();
                    } else {
                        if (parser.hasToken(JsonToken.VALUE_NUMBER_INT)) {
                            this._throwNoNumericTimestampNeedTimeZone(parser, context);
                        }

                        return this._handleUnexpectedToken(context, parser, "Expected array or string.", new Object[0]);
                    }
                }
            }

            @Override
            protected LocalDateTime _fromString(JsonParser p, DeserializationContext ctxt, String string0) throws IOException {
                String string = string0.trim();
                if (string.isEmpty()) {
                    return this._fromEmptyString(p, ctxt, string);
                } else {
                    try {
                        if (string.length() > 10 && string.charAt(10) == 'T' && string.endsWith("Z")) {
                            if (this.isLenient()) {
                                return LocalDateTime.parse(string.substring(0, string.length() - 1), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                            } else {
                                JavaType t = this.getValueType(ctxt);
                                return (LocalDateTime) ctxt.handleWeirdStringValue(t.getRawClass(), string, "Should not contain offset when 'strict' mode set for property or type (enable 'lenient' handling to allow)", new Object[0]);
                            }
                        } else {
                            if (string.length() > 16) {
                                return LocalDateTime.parse(string, this.norm_formatter);
                            } else {
                                return LocalDateTime.parse(string, this.minute_formatter);
                            }
                        }
                    } catch (DateTimeException var6) {
                        DateTimeException e = var6;
                        return (LocalDateTime) this._handleDateTimeException(ctxt, e, string);
                    }
                }
            }
        }

    }

}
