package com.cb.ai.data.analysis.ai.common.registry;

import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.common.properties.BasicProperties;
import com.cb.ai.data.analysis.ai.config.AIPropertiesConfigure;
import com.cb.ai.data.analysis.ai.domain.common.FieldHandle;
import com.cb.ai.data.analysis.ai.utils.RefUtil;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/6/6 15:07
 * @Copyright (c) 2025
 * @Description AI配置相关注册
 */
public class AIPropertiesRegistryBase extends BaseDynamicBeanRegistry {

    @Override
    void dynamicRegistry(BeanDefinitionRegistry beanDefinitionRegistry, ClassPathScanningCandidateComponentProvider scanner) {
        scanner.addIncludeFilter(new AssignableTypeFilter(AIPropertiesConfigure.class));
        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents(BASE_PACKAGE_NAME);
        if (!beanDefinitions.isEmpty()) {
            for (BeanDefinition beanDefinition : beanDefinitions) {
                String beanClassName = beanDefinition.getBeanClassName();
                if (beanClassName == null) {
                    continue;
                }
                try {
                    Class<?> clazz = Class.forName(beanClassName);
                    ConfigurationProperties configurationAnnotation = clazz.getAnnotation(ConfigurationProperties.class);
                    String prefix = configurationAnnotation.prefix();
                    BindResult<AIPropertiesConfigure> bindResult = Binder.get(environment).bind(prefix, AIPropertiesConfigure.class);
                    AIPropertiesConfigure properties = bindResult.orElseGet(null);
                    if (properties == null) {
                        return;
                    }
                    List<FieldHandle> normalFields = RefUtil.getNormalFields(clazz);
                    for (FieldHandle fieldHandle : normalFields) {
                        Object value = fieldHandle.get(properties);
                        if (value instanceof BasicProperties basicProperties) {
                            BeanDefinition propBean = BeanDefinitionBuilder
                                .genericBeanDefinition((Class) fieldHandle.getFieldType(), () -> basicProperties)
                                .setScope(BeanDefinition.SCOPE_SINGLETON)
                                .setPrimary(true)
                                .setLazyInit(true)
                                .getBeanDefinition();
                            beanDefinitionRegistry.registerBeanDefinition(value.getClass().getSimpleName(), propBean);
                        }
                    }

                } catch (ClassNotFoundException e) {
                    CommonLog.error("AI配置类加载失败, 类名:{}，原因：{}", beanClassName, e);
                }
                beanDefinitionRegistry.registerBeanDefinition(beanClassName, beanDefinition);
            }
        }
    }
}
