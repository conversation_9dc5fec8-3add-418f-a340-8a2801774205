package com.xong.boot.common.crypto.file;

import cn.hutool.core.io.IoUtil;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 不进行加解密
 * <AUTHOR>
 */
public class NoneFileEncoder implements FileEncoder {
    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            IoUtil.copy(is, out);
        } finally {
            if (isClose) {
                if (out != null) {
                    out.close();
                }
                if (is != null) {
                    is.close();
                }
            }
        }
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            IoUtil.copy(is, out);
        } finally {
            if (isClose) {
                if (out != null) {
                    out.close();
                }
                if (is != null) {
                    is.close();
                }
            }
        }
    }
}
