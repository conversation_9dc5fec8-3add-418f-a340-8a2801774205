package com.cb.ai.data.analysis.petition.handler;

import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.utils.EmbeddingUtils;
import com.xong.boot.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 向量写入批处理ResultHandler实现类
 * 用于处理信访数据的向量化和写入操作
 * <AUTHOR> Assistant
 */
@Slf4j
public class WriterVectorBatchResultHandler extends AbstractStreamBatchResultHandler<SsPetitionAnalyzedEntity> {
    
    private final SsRepeatTaskVo.RepeatTaskAnalyzeGroup group;
    private final String taskId;
    private final String taskDataDir;
    private final EmbeddingUtils embeddingUtils;
    
    /**
     * 构造函数
     * @param group 分析分组
     * @param taskId 任务ID
     * @param taskDataDir 任务数据目录
     * @param taskExecutor 任务执行器
     */
    public WriterVectorBatchResultHandler(SsRepeatTaskVo.RepeatTaskAnalyzeGroup group,
                                        String taskId,
                                        String taskDataDir,
                                        TaskExecutor taskExecutor) {
        super(500, 3, taskExecutor); // 500条一批，最多3个并发
        this.group = group;
        this.taskId = taskId;
        this.taskDataDir = taskDataDir;
        this.embeddingUtils = SpringUtils.getBean(EmbeddingUtils.class);
    }
    
    /**
     * 自定义配置的构造函数
     * @param group 分析分组
     * @param taskId 任务ID
     * @param taskDataDir 任务数据目录
     * @param batchSize 批次大小
     * @param maxConcurrentBatches 最大并发批次数
     * @param taskExecutor 任务执行器
     */
    public WriterVectorBatchResultHandler(SsRepeatTaskVo.RepeatTaskAnalyzeGroup group,
                                        String taskId,
                                        String taskDataDir,
                                        int batchSize,
                                        int maxConcurrentBatches,
                                        TaskExecutor taskExecutor) {
        super(batchSize, maxConcurrentBatches, taskExecutor);
        this.group = group;
        this.taskId = taskId;
        this.taskDataDir = taskDataDir;
        this.embeddingUtils = SpringUtils.getBean(EmbeddingUtils.class);
    }
    
    @Override
    protected void processBatch(List<SsPetitionAnalyzedEntity> batch, int batchNumber) throws Exception {
        long startTime = System.currentTimeMillis();
        try {
            // 执行具体的业务处理逻辑
            processPetitionBatch(batch, batchNumber);
            
            long endTime = System.currentTimeMillis();
            log.info("批次 #{} 处理完成，耗时: {}ms", batchNumber, endTime - startTime);
            
        } catch (Exception e) {
            log.error("批次 #{} 处理失败", batchNumber, e);
            throw e; // 重新抛出异常，触发错误处理
        }
    }
    
    @Override
    protected void onBatchError(List<SsPetitionAnalyzedEntity> batch, int batchNumber, Exception exception) {
        log.error("分组 {} 批次 #{} 处理失败，数据量: {}", group.genMD5(), batchNumber, batch.size(), exception);
        if(exception instanceof RuntimeException){
            throw (RuntimeException) exception;
        } else {
            throw new RuntimeException(exception);
        }
    }
    
    @Override
    protected void onAllBatchesCompleted() {
        log.info("分组 {} 所有批次处理完成，任务ID: {}", group.genMD5(), taskId);
    }
    
    /**
     * 处理单个批次的信访数据
     * @param batch 批次数据
     * @param batchNumber 批次号
     */
    private void processPetitionBatch(List<SsPetitionAnalyzedEntity> batch, int batchNumber) {
        log.debug("开始处理批次 #{}, 包含 {} 条信访数据", batchNumber, batch.size());

        // 这里实现具体的业务逻辑
//        for (SsPetitionAnalyzedEntity entity : batch) {
//            // 处理单条数据的业务逻辑
//            processSingleEntity(entity, batchNumber);
//        }

        // 批次级别的处理逻辑
        processBatchLevel(batch, batchNumber);
        
        log.debug("批次 #{} 处理完成，处理了 {} 条数据", batchNumber, batch.size());
    }
    
    /**
     * 处理单个信访实体
     * @param entity 信访实体
     * @param batchNumber 批次号
     */
    private void processSingleEntity(SsPetitionAnalyzedEntity entity, int batchNumber) {
        // 实现单个实体的处理逻辑
        // 例如：
        // 1. 提取文本内容
        // 2. 进行向量化
        // 3. 计算相似度
        // 4. 保存结果
    }
    
    /**
     * 批次级别的处理
     * @param batch 批次数据
     * @param batchNumber 批次号
     */
    private void processBatchLevel(List<SsPetitionAnalyzedEntity> batch, int batchNumber) {
        // 实现批次级别的处理逻辑
        // 例如：批量写入文件、批量调用API、批量数据库操作等
        
        log.debug("执行批次 #{} 级别处理，数据量: {}", batchNumber, batch.size());
        
        // 在这里添加批次级别的处理逻辑
        // 例如：
        // 1. 批量写入向量数据库
        // 2. 批量生成分析报告
        // 3. 批量更新处理状态
        File file = new File(taskDataDir, group.genVectorFileName());

        List<float[]> vectors = this.embeddingUtils.embedding2Vector(batch.stream().map(SsPetitionAnalyzedEntity::getBrief).toArray(String[]::new));

        // 提取ID列表
        List<Long> ids = batch.stream().map(SsPetitionAnalyzedEntity::getId).collect(java.util.stream.Collectors.toList());

        // 使用EmbeddingUtils写入向量文件
        try {
            EmbeddingUtils.writeVectorFile(file, ids, vectors);
            log.debug("批次 #{} 成功写入 {} 条向量数据到文件: {}", batchNumber, batch.size(), file.getAbsolutePath());
        } catch (IOException e) {
            log.error("批次 #{} 写入向量文件失败: {}", batchNumber, file.getAbsolutePath(), e);
            throw new RuntimeException("写入向量文件失败", e);
        }
    }

    // ========== Getter方法 ==========
    
    public SsRepeatTaskVo.RepeatTaskAnalyzeGroup getGroup() {
        return group;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public String getTaskDataDir() {
        return taskDataDir;
    }
}
