package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ss_report
 * <AUTHOR>
@Data
@TableName("ss_report")
public class SsReportEntity extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     * 主键ID
     */
    private String id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 下载路径
     */
    private String fileUrl;

    /**
     * 报告涉及数据集
     */

    private Integer status;

    /***
     * 完成时间
     */
    @TableField(updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime finishTime;

}
