package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 密级标题
 *
 * <AUTHOR>
 */
public class SecretPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return pos < 5 && ReUtil.contains("^(秘|机|绝)密", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatRedParagraph(paragraph, config);
        List<XWPFRun> runList = paragraph.getRuns();
        for (XWPFRun run : runList) {
            FormatTools.formatSerialAttachHead(run, config);
        }
    }
}
