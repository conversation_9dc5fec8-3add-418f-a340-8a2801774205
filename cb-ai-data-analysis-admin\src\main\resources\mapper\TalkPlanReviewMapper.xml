<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.talkaudit.mapper.TalkPlanReviewMapper">
    <resultMap type="com.cb.ai.data.analysis.talkaudit.domain.TalkPlanReview" id="TalkPlanReviewMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="INTEGER"/>
        <result property="tag" column="tag" jdbcType="VARCHAR"/>
        <result property="analyseResult" column="analyse_result" jdbcType="VARCHAR"/>
        <result property="auditReport" column="audit_report" jdbcType="VARCHAR"/>
        <result property="auditReportPath" column="audit_report_path" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

