package com.cb.ai.data.analysis.ai.common.properties;

import com.cb.ai.data.analysis.ai.domain.common.MultiUrlData;
import com.cb.ai.data.analysis.ai.domain.common.TaskUrlData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 09:51
 * @Copyright (c) 2025
 * @Description 私有化AI底座配置文件
 */
@Getter
@Setter
public class PrivateAIBaseProperties extends BasicProperties {
    /** 请求认证 **/
    private Map<String, String> headerMap;

    /** 私有底座基础问答接口 **/
    @NestedConfigurationProperty
    private MultiUrlData chat;

    /** 私有底座文件存储接口 **/
    private String fileStore;

    /** 私有底座知识库接口 **/
    private String knowledge;

    /** 私有底座orc图片解析接口 **/
    private String ocr;

    /** 私有底座大数据分析接口 **/
    private String finance;

    /** 私有底座文件（材料）分析接口 **/
    private String fileAnalysis;

    /** 私有底音频分析接口 **/
    @NestedConfigurationProperty
    private TaskUrlData audioAnalysis;

    /** 深度思考接口 **/
    private String deepThinkUrl;

    /** 向量查询接口 **/
    private String embeddingUrl;

}
