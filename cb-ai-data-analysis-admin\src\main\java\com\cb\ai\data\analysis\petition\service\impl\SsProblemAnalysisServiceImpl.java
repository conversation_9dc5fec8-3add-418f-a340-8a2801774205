package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeChat;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeChatService;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketClassifyEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity;
import com.cb.ai.data.analysis.petition.mapper.PetitionProblemHandleMapper;
import com.cb.ai.data.analysis.petition.mapper.PetitionProblemHandleResultMapper;
import com.cb.ai.data.analysis.petition.mapper.SsProblemMarketClassifyMapper;
import com.cb.ai.data.analysis.petition.mapper.SsProblemMarketDetailsMapper;
import com.cb.ai.data.analysis.petition.service.SsProblemAnalysisService;
import com.cb.ai.data.analysis.utils.AIResponseProcessor;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;


/***
 * 提问矩阵问题分析
 * 因为需要调用其他模块，故而分析这个放在了admin
 * <AUTHOR>
 */
@Service
public class SsProblemAnalysisServiceImpl implements SsProblemAnalysisService {

    @Autowired
    private SsProblemMarketClassifyMapper problemMarketClassifyMapper;

    @Autowired
    private SsProblemMarketDetailsMapper problemMarketDetailsMapper;

    @Autowired
    private PetitionProblemHandleMapper petitionProblemHandleMapper;

    @Autowired
    private PetitionProblemHandleResultMapper petitionProblemHandleResultMapper;

    @Resource(name = "problemFileThreadPoolExecutor")
    private ExecutorService problemFileThreadPoolExecutor;

    @Autowired
    private KnowledgeChatService knowledgeChatService;


    @Autowired
    private AiConfigService aiConfigService;

    /***
     * 问题数据保存，保存后调用分析人物
     * @param id
     * @param baseIds
     * @throws Exception
     */
    @Override
    public void batchAnalysis(String id,List<String> baseIds) throws Exception {
        try{
            if(StringUtils.isBlank(id)){
                throw new XServiceException("问题ID不能为空！");
            }
            if(CollectionUtils.isEmpty(baseIds)){
                throw new XServiceException("请选择知识库！");
            }
            SsProblemMarketClassifyEntity classifyEntity=problemMarketClassifyMapper.selectById(id);
            if(ObjectUtils.isEmpty(classifyEntity)){
                throw new XServiceException("获取问题信息为空！");
            }
            LambdaQueryWrapper<SsProblemMarketDetailsEntity> queryWrapper = new QueryWrapper<SsProblemMarketDetailsEntity>().lambda();
            queryWrapper.eq(SsProblemMarketDetailsEntity::getProblemClassifyId,id);
            List<SsProblemMarketDetailsEntity> detailsEntityList=problemMarketDetailsMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(detailsEntityList)){
                throw new XServiceException("获取待处理问题信息为空！");
            }
            PetitionProblemHandleEntity petitionProblemHandleEntity=new PetitionProblemHandleEntity();
            String problemId=IdUtil.getSnowflakeNextIdStr();
            petitionProblemHandleEntity.setId(problemId);
            petitionProblemHandleEntity.setBaseId(StringUtils.join(baseIds,","));
            petitionProblemHandleEntity.setFileId(null);
            petitionProblemHandleEntity.setFileName(classifyEntity.getFileName());
            petitionProblemHandleEntity.setFileReadStatus(1);
            petitionProblemHandleEntity.setQaCount(classifyEntity.getProblemDataCount());
            petitionProblemHandleEntity.setHandleStatus(1);
            petitionProblemHandleEntity.setCreateTime(LocalDateTime.now());
            petitionProblemHandleEntity.setCreateBy(SecurityUtils.getUsername());
            petitionProblemHandleMapper.insert(petitionProblemHandleEntity);
            AiConfig aiConfig=aiConfigService.getAiConfig();
            this.analysisProblemFile(detailsEntityList,problemId,baseIds,aiConfig);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e);
        }
    }

    /***
     * 问题批量分析
     * @param problemMarketDetailsList
     * @param problemId
     * @param baseIds
     */
    private void analysisProblemFile(List<SsProblemMarketDetailsEntity> problemMarketDetailsList,String problemId,List<String> baseIds,AiConfig aiConfig){
        problemFileThreadPoolExecutor.submit(() -> {
            try{
                List<PetitionProblemHandleResultEntity> resultEntityList=new ArrayList<>();
                for(SsProblemMarketDetailsEntity question:problemMarketDetailsList){
                    PetitionProblemHandleResultEntity resultEntity=new PetitionProblemHandleResultEntity();
                    resultEntity.setId(IdUtil.getSnowflakeNextIdStr());
                    resultEntity.setProblemId(problemId);
                    resultEntity.setProblemName(question.getProblemName());
                    resultEntity.setStatus(0);
                    resultEntityList.add(resultEntity);
                }
                Integer successCount=0;
                petitionProblemHandleResultMapper.insert(resultEntityList);
                for(PetitionProblemHandleResultEntity resultEntity:resultEntityList){
                    try{
                        KnowledgeChat knowledgeChat=new KnowledgeChat();
                        BeanUtils.copyProperties(aiConfig,knowledgeChat);
                        knowledgeChat.setMaxTokens(aiConfig.getMaxTokens()*0.8);
                        knowledgeChat.setStream(Boolean.FALSE);
                        knowledgeChat.setPromote(resultEntity.getProblemName());
                        knowledgeChat.setBaseIds(baseIds);
                        Result result=knowledgeChatService.chatKnowledge(knowledgeChat);
                        AIResponseProcessor aiResponseProcessor=new AIResponseProcessor();
                        successCount=successCount+1;
                        if(result.getCode() == ResultCode.SUCCESS.getCode()){
                            aiResponseProcessor.processChunk(result.getData());
                        }
                        resultEntity.setReasoningContent(aiResponseProcessor.getReasoningContent());
                        resultEntity.setContent(aiResponseProcessor.getContent());
                        resultEntity.setStatus(result.getCode());
                        resultEntity.setFileListJson(JSON.toJSONString(aiResponseProcessor.getFileList()));
                        petitionProblemHandleResultMapper.updateById(resultEntity);
                        PetitionProblemHandleEntity entity2=new PetitionProblemHandleEntity();
                        entity2.setId(problemId);
                        entity2.setFinishedQaCount(successCount);
                        petitionProblemHandleMapper.updateById(entity2);
                    }catch (Exception e){
                        e.printStackTrace();
                        error(resultEntity,problemId);
                    }
                }
                PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
                entity.setId(problemId);
                entity.setHandleStatus(2);
                entity.setFinishedQaCount(successCount);
                petitionProblemHandleMapper.updateById(entity);
            }catch (Exception e){
                e.printStackTrace();
                PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
                entity.setId(problemId);
                entity.setHandleStatus(-1);
                entity.setFinishedQaCount(0);
                petitionProblemHandleMapper.updateById(entity);
            }
        });
    }

    private void error(PetitionProblemHandleResultEntity resultEntity,String problemId){
        try {
            resultEntity.setStatus(500);
            petitionProblemHandleResultMapper.updateById(resultEntity);
            /*PetitionProblemHandleEntity entity=new PetitionProblemHandleEntity();
            entity.setId(problemId);
            entity.setHandleStatus(-1);
            petitionProblemHandleMapper.updateById(entity);*/
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
