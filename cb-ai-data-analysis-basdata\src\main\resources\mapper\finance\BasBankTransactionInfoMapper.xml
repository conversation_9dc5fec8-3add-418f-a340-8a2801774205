<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.basdata.mapper.finance.BasBankTransactionInfoMapper">

    <resultMap type="com.cb.ai.data.analysis.basdata.domain.entity.finance.BasBankTransactionInfo" id="BasBankTransactionInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
        <result property="customerAccount" column="customer_account" jdbcType="VARCHAR"/>
        <result property="transactionNumber" column="transaction_number" jdbcType="VARCHAR"/>
        <result property="transactionCurrency" column="transaction_currency" jdbcType="VARCHAR"/>
        <result property="transactionTime" column="transaction_time" jdbcType="TIMESTAMP"/>
        <result property="transactionType" column="transaction_type" jdbcType="VARCHAR"/>
        <result property="abstractInfo" column="abstract_info" jdbcType="VARCHAR"/>
        <result property="borrowersSign" column="borrowers_sign" jdbcType="VARCHAR"/>
        <result property="transactionAmount" column="transaction_amount" jdbcType="NUMERIC"/>
        <result property="transactionsBalances" column="transactions_balances" jdbcType="NUMERIC"/>
        <result property="transDeptId" column="trans_dept_id" jdbcType="VARCHAR"/>
        <result property="transDeptName" column="trans_dept_name" jdbcType="VARCHAR"/>
        <result property="reciprocalAccount" column="reciprocal_account" jdbcType="VARCHAR"/>
        <result property="reciprocalName" column="reciprocal_name" jdbcType="VARCHAR"/>
        <result property="reciprocalAccountName" column="reciprocal_account_name" jdbcType="VARCHAR"/>
        <result property="reciprocalIdNumber" column="reciprocal_id_number" jdbcType="VARCHAR"/>
        <result property="reciprocalBankNumber" column="reciprocal_bank_number" jdbcType="VARCHAR"/>
        <result property="reciprocalBankName" column="reciprocal_bank_name" jdbcType="VARCHAR"/>
        <result property="tellerNumber" column="teller_number" jdbcType="VARCHAR"/>
        <result property="transactionSerialNo" column="transaction_serial_No" jdbcType="VARCHAR"/>
        <result property="transactionChannel" column="transaction_channel" jdbcType="VARCHAR"/>
        <result property="transactionRemark" column="transaction_remark" jdbcType="VARCHAR"/>
        <result property="tradingOutletCode" column="trading_outlet_code" jdbcType="VARCHAR"/>
        <result property="tradingOutletName" column="trading_outlet_name" jdbcType="VARCHAR"/>
        <result property="isCash" column="is_cash" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    

</mapper>

