package com.cb.ai.data.analysis.petition.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class SsRepeatTask extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    private String title;

    /**
     * 起始日期 yyyy-MM-dd
     */
    private String beginDate;

    /**
     * 截止日期 yyyy-MM-dd
     */
    private String endDate;

    /**
     * 状态（0未开始 1分析中 2已完成 3分析报错）
     */
    private Integer status;

    /**
     * 重复件数量
     */
    private Long similar;

    /**
     * 分析的总数量
     */
    private Long total;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 删除标志（0正常 1删除）
     */
    @JsonIgnore
//    @TableLogic
    private Boolean delFlag;

}
