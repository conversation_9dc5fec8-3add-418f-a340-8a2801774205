package com.cb.ai.data.analysis.petition.converter.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 红头
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocRedhead extends BaseDoc {
    /**
     * 红头在表格内
     */
    private Boolean inTable;
    /**
     * 红头编号
     */
    private String bh;
    /**
     * 密级
     */
    private String mj;
    /**
     * 紧急程度
     */
    private String jjcd;
    /**
     * 发文机关
     */
    private String fwjg;
    /**
     * 机关代字
     */
    private String jgdz;
    /**
     * 签发人
     */
    private String pfr;
}
