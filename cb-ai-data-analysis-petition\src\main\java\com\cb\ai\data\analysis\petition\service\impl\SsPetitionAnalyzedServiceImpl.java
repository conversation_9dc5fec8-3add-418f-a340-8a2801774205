package com.cb.ai.data.analysis.petition.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.excel.MergeCellSheetWriteHandler;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.DomainStatisticExportDTO;
import com.cb.ai.data.analysis.petition.domain.vo.response.OrgExportDTO;
import com.cb.ai.data.analysis.petition.domain.vo.response.PurposeStatisticExportDTO;
import com.cb.ai.data.analysis.petition.mapper.SsPetitionAnalyzedMapper;
import com.cb.ai.data.analysis.petition.service.SsPetitionAnalyzedService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;


/***
 * <AUTHOR>
 * 信访件解析结果
 */
@Service
public class SsPetitionAnalyzedServiceImpl extends BaseServiceImpl<SsPetitionAnalyzedMapper, SsPetitionAnalyzedEntity> implements SsPetitionAnalyzedService {

    @Autowired
    private SsPetitionAnalyzedMapper ssPetitionAnalyzedMapper;

    @Override
    public List<SsPetitionAnalyzedEntity> selectByDateRange(String startDate, String endDate, List<String> types, String city, String district, Integer size) {
        return this.ssPetitionAnalyzedMapper.selectByDateRange(startDate,endDate, types,city,district,size);
    }

    @Override
    public Page<SsPetitionAnalyzedEntity> selectByPage(PetitionQueryPageQueryVo queryParams) {
        Page<SsPetitionAnalyzedEntity> page = new Page<>(queryParams.getPageNo(), queryParams.getPageSize());
        LambdaQueryWrapper<SsPetitionAnalyzedEntity> queryWrapper = new QueryWrapper<SsPetitionAnalyzedEntity>().lambda();
        if(StringUtils.isNotBlank(queryParams.getPetitionPurpose())){
            queryWrapper.like(SsPetitionAnalyzedEntity::getPetitionPurpose,queryParams.getPetitionPurpose());
        }
        queryWrapper.orderByDesc(SsPetitionAnalyzedEntity::getCreateTime);
        return this.ssPetitionAnalyzedMapper.selectPage(page, queryWrapper);
    }

    @Override
    public void export(PetitionQueryPageQueryVo queryParams, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        String encodedFileName = URLEncoder.encode("信访件AI解析数据.xlsx", "UTF-8").replaceAll("\\+", "%20"); // 处理空格编码兼容性
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
        LambdaQueryWrapper<SsPetitionAnalyzedEntity> queryWrapper = new QueryWrapper<SsPetitionAnalyzedEntity>().lambda();
        if(StringUtils.isNotBlank(queryParams.getPetitionPurpose())){
            queryWrapper.like(SsPetitionAnalyzedEntity::getPetitionPurpose,queryParams.getPetitionPurpose());
        }
        queryWrapper.orderByDesc(SsPetitionAnalyzedEntity::getCreateTime);
        List<SsPetitionAnalyzedEntity> dataList = ssPetitionAnalyzedMapper.selectList(queryWrapper);
        EasyExcel.write(response.getOutputStream(), SsPetitionAnalyzedEntity.class)
                .sheet("sheet1")
                .doWrite(dataList);
    }


    @Override
    public void exportStatics(HttpServletResponse response) {

        List<PurposeStatisticExportDTO> purposeStats = ssPetitionAnalyzedMapper.getPurposeStatistics();
        List<PurposeStatisticExportDTO> domainStats = ssPetitionAnalyzedMapper.getDomainStatistics();
        List<OrgExportDTO> orgStats = ssPetitionAnalyzedMapper.getOrgStatistics();


        int total1 = purposeStats.stream().mapToInt(PurposeStatisticExportDTO::getCount).sum();
        int total2 = domainStats.stream().mapToInt(PurposeStatisticExportDTO::getCount).sum();
        int total3 = orgStats.stream().mapToInt(OrgExportDTO::getCount).sum();

        processStatisticList(purposeStats, total1);
        processStatisticList(domainStats, total2);
        processOrgList(orgStats, total3);

        try {
            String fileName = URLEncoder.encode("信访统计.xlsx", StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            ExcelWriter writer = EasyExcel.write(response.getOutputStream()).build();

            writer.write(purposeStats, EasyExcel.writerSheet(0, "信访目的统计")
                    .head(PurposeStatisticExportDTO.class)
                    .registerWriteHandler(new MergeCellSheetWriteHandler(0, 1))
                    .build());

            writer.write(domainStats, EasyExcel.writerSheet(1, "所属领域统计")
                    .head(DomainStatisticExportDTO.class)
                    .registerWriteHandler(new MergeCellSheetWriteHandler(0, 1))
                    .build());

            writer.write(orgStats, EasyExcel.writerSheet(2, "去向机构统计")
                    .head(OrgExportDTO.class)
                    .build());

            response.flushBuffer();
            writer.finish();
        } catch (IOException e) {
            throw new RuntimeException("导出 Excel 失败", e);
        }
    }

    private void processStatisticList(List<PurposeStatisticExportDTO> list, int total) {
        list.forEach(item -> {
            if (item.getFirstCategory() == null) item.setFirstCategory("未填");
            if (item.getSecondCategory() == null) item.setSecondCategory("未填");
            item.setPercentage(Math.round(item.getCount() * 10000.0 / total) / 100.0);
        });

        PurposeStatisticExportDTO totalRow = new PurposeStatisticExportDTO();
        totalRow.setFirstCategory("总计");
        totalRow.setSecondCategory("");
        totalRow.setCount(total);
        totalRow.setPercentage(100.0);
        list.add(totalRow);
    }

    private void processOrgList(List<OrgExportDTO> list, int total) {
        list.forEach(item -> {
            if (item.getOrg() == null) item.setOrg("未填");
            item.setPercentage(Math.round(item.getCount() * 10000.0 / total) / 100.0);
        });

        OrgExportDTO totalRow = new OrgExportDTO();
        totalRow.setOrg("总计");
        totalRow.setCount(total);
        totalRow.setPercentage(100.0);
        list.add(totalRow);
    }

    @Override
    public List<SsRepeatTaskVo.RepeatTaskAnalyzeGroup> getAnalyzeGroup(Date beginDate, Date endDate) {
        return baseMapper.getAnalyzeGroup(beginDate, endDate);
    }

    @Override
    public void streamPetitionByAnalyzeGroup(SsRepeatTaskVo.RepeatTaskAnalyzeGroup g, ResultHandler<SsPetitionAnalyzedEntity> resultHandler) {
        baseMapper.streamPetitionByAnalyzeGroup(g, resultHandler);
    }
}
