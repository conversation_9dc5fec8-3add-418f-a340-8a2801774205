package com.cb.ai.data.analysis.ai.common.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 15:47
 * @Copyright (c) 2025
 * @Description 统一的日志输出类
 */
public class CommonLog {
    public static final Logger LOG = LoggerFactory.getLogger(CommonLog.class);

    public static void info(String format, Object... args) {
        LOG.info(format, args);
    }

    public static void warn(String format, Object... args) {
        LOG.warn(format, args);
    }

    public static void error(String format, Object... args) {
        LOG.error(format, args);
    }
}
