package com.cb.ai.data.analysis.ai.component.choreography.model;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 17:40
 * @Copyright (c) 2025
 * @Description 业务场景
 */
public record BusinessScene(String tag, String businessType, String scene) {

    public static BusinessScene of(String tag, String businessType) {
        return of(tag, businessType, SceneEnum.DEFAULT_SCENE.name());
    }

    public static BusinessScene of(String tag, BusinessTypeEnum businessTypeEnum) {
        return of(tag, businessTypeEnum, SceneEnum.DEFAULT_SCENE);
    }

    public static BusinessScene of(String tag, String businessType, String scene) {
        return new BusinessScene(tag, businessType, scene);
    }

    public static BusinessScene of(String tag, BusinessTypeEnum businessTypeEnum, SceneEnum sceneEnum) {
        return new BusinessScene(tag, businessTypeEnum.name(), sceneEnum.name());
    }

    public String getKey() {
        return String.format("%s_%s_%s", tag, businessType, scene);
    }
}
