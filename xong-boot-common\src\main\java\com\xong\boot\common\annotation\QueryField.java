package com.xong.boot.common.annotation;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.xong.boot.common.enums.QueryCondition;

import java.lang.annotation.*;

/**
 * 查询字段注解
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface QueryField {
    /**
     * 查询规则
     */
    QueryCondition value() default QueryCondition.EQ;
    /**
     * 字段名称
     * 字段名称前缀将失效
     */
    String name() default "";
    /**
     * 开启索引查询
     */
    boolean indexEnable() default false;
    /**
     * 字段前缀
     */
    String prefix() default "";
    /**
     * 字段验证策略之 where: 表示该字段在拼接where条件时的策略
     * IGNORED: 直接拼接 column=#{columnProperty}
     * NOT_NULL: <if test="columnProperty != null">column=#{columnProperty}</if>
     * NOT_EMPTY: <if test="columnProperty != null and columnProperty!=''">column=#{columnProperty}</if>
     * NOT_EMPTY 如果针对的是非 CharSequence 类型的字段则效果等于 NOT_NULL
     */
    FieldStrategy whereStrategy() default FieldStrategy.NOT_EMPTY;
    /**
     * 多重查询
     * eg.@QueryField(search = { 'username', 'userCode' }) 同时查询用户姓名与用户编码满足search
     */
    String[] search() default {};
    /**
     * 多重查询加密索引字段
     */
    String[] indexFieldSearch() default {};
}
