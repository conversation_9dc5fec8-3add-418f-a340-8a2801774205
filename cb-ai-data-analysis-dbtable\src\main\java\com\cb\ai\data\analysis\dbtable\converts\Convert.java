package com.cb.ai.data.analysis.dbtable.converts;

import com.cb.ai.data.analysis.dbtable.enums.ConvertMode;
import com.cb.ai.data.analysis.dbtable.exception.DataConvertException;
import com.cb.ai.data.analysis.dbtable.model.ExcelConvert;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据清洗抽象类
 * <AUTHOR>
 */
public abstract class Convert<T> {
    /**
     * 清洗标识
     */
    private final String key;
    /**
     * 清洗名称
     */
    private final String name;
    /**
     * 模式
     */
    private final ConvertMode mode;
    /**
     * 变量
     */
    private final List<Variable> variables;

    public Convert(String key, String name) {
        this(key, name, ConvertMode.COLUMN_NO);
    }

    public Convert(String key, String name, ConvertMode mode) {
        this(key, name, mode, null);
    }

    public Convert(String key, String name, List<Variable> variables) {
        this(key, name, ConvertMode.VARIABLE, variables);
    }

    public Convert(String key, String name, ConvertMode mode, List<Variable> variables) {
        if (mode == ConvertMode.VARIABLE && variables.size() < 2) {
            throw new IllegalArgumentException("多变量模式，变量必须大于2个");
        }
        this.key = key;
        this.name = name;
        this.mode = mode;
        this.variables = variables;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    public ConvertMode getMode() {
        return mode;
    }

    public List<Variable> getVariables() {
        return variables;
    }

    /**
     * 比较清洗key
     * @param etlKey etl清洗key
     */
    public boolean compare(String etlKey) {
        return etlKey.equals(getKey());
    }

    /**
     * 数据转换
     * @param value        原数据值
     * @param excelConvert 数据转换
     */
    protected T transform(Object value, ExcelConvert excelConvert, Map<Integer, String> source) {
        if (value == null) {
            return null;
        }
        return (T) value.toString();
    }

    /**
     * 执行转换
     * @param excelConvert 转换配置
     * @param source       源数据
     */
    public T execute(ExcelConvert excelConvert, Map<Integer, String> source) throws DataConvertException {
        if (mode == ConvertMode.USER_FILL) {
            return transform(excelConvert.getInValue(), excelConvert, source);
        }
        if (mode == ConvertMode.VARIABLE) {
            if (excelConvert.getVariables().size() != variables.size()) {
                throw new DataConvertException("Convert[variables]个数不一致");
            }
            Map<String, String> values = new HashMap<>(); // 提取参数值
            for (Variable variable : excelConvert.getVariables()) {
                values.put(variable.getName(), source.get(variable.getColumnNo()));
            }
            return transform(values, excelConvert, source);
        }
        return transform(source.get(excelConvert.getExcelColumnNo()), excelConvert, source);
    }

    /**
     * ETL变量
     */
    @Data
    public static class Variable {
        /**
         * 字段名称
         */
        private String name;
        /**
         * 字段注释
         */
        private String comment;
        /**
         * excel字段位置
         */
        private Integer columnNo;

        public Variable(String name, String comment) {
            this.name = name;
            this.comment = comment;
        }
    }
}
