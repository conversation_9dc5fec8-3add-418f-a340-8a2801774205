package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.cb.ai.data.analysis.docassist.utils.CharLenConstant;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 发文时间
 *
 * <AUTHOR>
 */
public class DatePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.isMatch("^[\\dxX]+年[\\dxX]+月[\\dxX]+日$", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
//        FormatTools.formatParagraphRightInd(paragraph, config, 400L);
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
//            if (documentInfo.isPrintSend()) {
//                FormatTools.formatSerialNumber2(run, config);
//            } else {
//                FormatTools.format(run, config);
//            }
            FormatTools.format(run, config);
        }
        FormatTools.formatParagraphRightInd(paragraph, config, 2 * CharLenConstant.CHINESE_CHAR);
    }
}
