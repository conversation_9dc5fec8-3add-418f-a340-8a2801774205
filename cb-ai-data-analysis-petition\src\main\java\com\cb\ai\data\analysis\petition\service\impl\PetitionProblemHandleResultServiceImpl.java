package com.cb.ai.data.analysis.petition.service.impl;


import cn.hutool.core.convert.NumberChineseFormatter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cb.ai.data.analysis.ai.service.impl.ReportServiceImpl;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.DocFormatConverter;
import com.cb.ai.data.analysis.petition.converter.QaExportRow;
import com.cb.ai.data.analysis.petition.converter.model.MergeCellSheetWriteHandler;
import com.cb.ai.data.analysis.petition.converter.model.QaSheetWriteHandler;
import com.cb.ai.data.analysis.petition.domain.FootnoteData;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.cb.ai.data.analysis.petition.mapper.PetitionProblemHandleResultMapper;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleResultService;
import com.cb.ai.data.analysis.petition.utils.MarkdownUtils;
import com.cb.ai.data.analysis.petition.utils.WordFootnoteUtils;
import com.xong.boot.common.properties.FileProperties;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/***
 * <AUTHOR>
 * 问题批处理结果
 */
@Service
public class PetitionProblemHandleResultServiceImpl extends BaseServiceImpl<PetitionProblemHandleResultMapper, PetitionProblemHandleResultEntity> implements PetitionProblemHandleResultService {


    @Autowired
    private FileProperties fileProperties;

    @Override
    public void exportQaDetailWord(String problemId, HttpServletResponse response) {
        try {
            // 1. 查询问题详情
            LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new QueryWrapper<PetitionProblemHandleResultEntity>().lambda();
            queryWrapper.eq(PetitionProblemHandleResultEntity::getProblemId,problemId);
            List<PetitionProblemHandleResultEntity> qaList = this.baseMapper.selectList(queryWrapper);
            if (qaList.isEmpty()) {
                throw new RuntimeException("暂无问答数据！");
            }
            // 3. 构建内容：每个问答附带序号
            String[] chineseNums = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                    "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"};
            Map<String,Integer> iMap=WordFootnoteUtils.getIndexMap();
            StringBuffer sbHtml=new StringBuffer();
            for (int i = 0; i < qaList.size(); i++) {
                PetitionProblemHandleResultEntity item = qaList.get(i);


                Map<String, String> subMap = new HashMap<>();
                Map<String, String> fileMap = new HashMap<>();
                if(StringUtils.isNotEmpty(item.getFileListJson())) {
                    JSONArray jsonArray = JSON.parseArray(item.getFileListJson());
                    if (!ObjectUtils.isEmpty(jsonArray) && jsonArray.size() > 0) {
                        for(int j=0; j<jsonArray.size(); j++){
                            JSONObject obj = jsonArray.getJSONObject(j);
                            subMap.put(obj.getString("index"),obj.getString("fileName"));

                            String key="index"+(obj.containsKey("index")?obj.getString("index"):"");
                            fileMap.put(key,"“"+obj.getString("fileName")+"”");
                        }
                    }
                }

                StringBuffer title=new StringBuffer();
                title.append("### ").append(NumberChineseFormatter.format(i + 1, false)).append("、").append(item.getProblemName()).append("\n");
                String content=StringUtils.isNotBlank(item.getContent())?item.getContent():"没有找到相关内容。";
                content=replaceByPattern(content,fileMap);
                String htmlContent = MarkdownUtils.convert(title+content);
                String html= WordFootnoteUtils.convertFootnotes(htmlContent,subMap,iMap);
                sbHtml.append(html).append("\n\n");
            }
            Document doc = new Document();

            Map<String,FootnoteData> footnotes = WordFootnoteUtils.extractFootnotes(sbHtml.toString());
            String cleanHtml = WordFootnoteUtils.removeDataAttributes(sbHtml.toString());
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.insertHtml(cleanHtml);
            WordFootnoteUtils.processSharedFootnotes(doc, footnotes);

            //WordFootnoteUtilsBak.clearHashMap();
            /*// 获取所有脚注信息
            List<FootnoteData> footnotes = WordFootnoteUtilsBak.extractFootnotes(sbHtml.toString());
            String cleanHtml = WordFootnoteUtilsBak.removeDataAttributes(sbHtml.toString());
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.insertHtml(cleanHtml);
            WordFootnoteUtilsBak.processSharedFootnotes(doc, footnotes);*/
            XWPFDocument template= convertToXWPFDocument(doc);
            DocConfig config=new DocConfig();
            config.setFontName("仿宋_GB2312");
            DocFormatConverter.formatMarkdown(template,config);
            template.write(response.getOutputStream());
            template.close();
            //String path=fileProperties.getRootDir()+"/问题清单_"+problemId+".docx";
            //doc.save(path, SaveFormat.DOCX);
           /* ByteArrayOutputStream baos = new ByteArrayOutputStream();
            doc.save(baos, SaveFormat.DOCX);
            response.getOutputStream().write(baos.toByteArray());
            response.flushBuffer();*/
            /*if (htmlToWordRequestVo.whetherFormat()) {
                WordFormatRequest wordFormatRequestVo = new WordFormatRequest();
                wordFormatRequestVo.setOldFileAbsolutePath(uploadPath + fileName);
                wordFormatRequestVo.setNewFileDir(uploadPath);
                wordFormatRequestVo.setNewFileName(formatFileName);

                finalFilePath = docAssistClient.formatDocx(request, wordFormatRequestVo);
            } else {
                finalFilePath = uploadPath + fileName;
            }*/

            // 2. 加载模板
            /* InputStream templateStream=this.getClass().getClassLoader().getResourceAsStream("templates/knowledge_batch_template.docx");
//            ClassPathResource resource = new ClassPathResource("templates/knowledge_batch_template.docx");
//            InputStream templateStream = resource.getStream();


           List<Map<String, Object>> renderList = new ArrayList<>();
            for (int i = 0; i < qaList.size(); i++) {
                PetitionProblemHandleResultEntity item = qaList.get(i);
                Map<String, Object> map = new HashMap<>();
                map.put("question", Optional.ofNullable(item.getProblemName()).orElse(""));
//                map.put("remark", Optional.ofNullable(item.getRemark()).orElse("无"));
                String answer = item.getContent();
                if(StringUtils.isNotEmpty(answer)){
                    answer = answer.replaceAll("(?<=\\d\\.)(\\s+)", "");
                }
                answer=MarkdownUtils.convertHeaders(formatAnswerWithLineBreaks(answer));
                StringBuffer sb=new StringBuffer();
                if(StringUtils.isNotEmpty(item.getFileListJson())){
                    JSONArray jsonArray= JSON.parseArray(item.getFileListJson());
                    if(!ObjectUtils.isEmpty(jsonArray)&&jsonArray.size()>0){
                        sb.append("\n\n参考文献：\n");
                        Map<String, String> subMap = new HashMap<>();
                        for(int j=0; j<jsonArray.size(); j++){
                            JSONObject obj = jsonArray.getJSONObject(j);
                            String fileName=obj.getString("fileName");
                            if(ObjectUtils.isEmpty(subMap.get(fileName))){
                                subMap.put(fileName,fileName);
                                sb.append(subMap.get(fileName)).append("\n");
                            }
                        }
                    }
                }
                map.put("answer", answer+sb);
                if (i > chineseNums.length - 1) {
                    map.put("chineseIndex", NumberChineseFormatter.format(i + 1, false));
                } else {
                    map.put("chineseIndex", chineseNums[i]);     // 中文编号：一、二、三……
                }
//                map.put("numberIndex", String.valueOf(i + 1)); // 数字编号：1、2、3……
                renderList.add(map);
            }

            // 4. 渲染内容
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(
                    new HashMap<String, Object>() {{
                        put("datas", renderList);
                    }}
            );
            // 排版
             DocFormatConverter.formatDocument(template.getXWPFDocument(), new DocConfig());

            template.write(response.getOutputStream());
            template.close();*/
        } catch (Exception e) {
            throw new RuntimeException("导出 Word 失败：" + e.getMessage(), e);
        }
    }
    public static XWPFDocument convertToXWPFDocument(Document asposeDoc) throws Exception {
        // 创建内存输出流
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 将Aspose文档保存为DOCX格式到内存流
            asposeDoc.save(outputStream, SaveFormat.DOCX);

            // 将内存流转换为输入流
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray())) {
                // 使用POI加载DOCX流，创建XWPFDocument
                return new XWPFDocument(inputStream);
            }
        }
    }
    @Override
    public void exportQaDetailExcel(String problemId, HttpServletResponse response) {
        LambdaQueryWrapper<PetitionProblemHandleResultEntity> queryWrapper = new QueryWrapper<PetitionProblemHandleResultEntity>().lambda();
        queryWrapper.eq(PetitionProblemHandleResultEntity::getProblemId,problemId);
        List<PetitionProblemHandleResultEntity> qaList = this.baseMapper.selectList(queryWrapper);
        if (qaList.isEmpty()) {
            throw new RuntimeException("暂无问答数据！");
        }

        List<QaExportRow> exportList = new ArrayList<>();
        for (int i = 0; i < qaList.size(); i++) {
            StringBuffer sb=new StringBuffer();
            PetitionProblemHandleResultEntity detail = qaList.get(i);
            QaExportRow dto = new QaExportRow();
            dto.setIndex(i + 1);
            dto.setQuestion(detail.getProblemName());

            Map<String,String> fileMap=new HashMap<>();
            if(StringUtils.isNotEmpty(detail.getFileListJson())){
                JSONArray jsonArray= JSON.parseArray(detail.getFileListJson());
                if(!ObjectUtils.isEmpty(jsonArray)&&jsonArray.size()>0){
                    Map<String, String> subMap = new HashMap<>();
                    for(int j=0; j<jsonArray.size(); j++){
                        JSONObject obj = jsonArray.getJSONObject(j);
                        String fileName=obj.getString("fileName");
                        if(ObjectUtils.isEmpty(subMap.get(fileName))){
                            subMap.put(fileName,fileName);
                            sb.append(subMap.get(fileName)).append("\n");
                        }
                        String key="index"+(obj.containsKey("index")?obj.getString("index"):"");
                        fileMap.put(key,"“"+fileName+"”");
                    }
                }
            }
            String answer =StringUtils.isNotBlank(detail.getContent())?detail.getContent():"没有找到相关内容。";
            answer=replaceByPattern(answer,fileMap);
            if(StringUtils.isNotEmpty(answer)){
                answer = MarkdownUtils.convertHeaders(contentFormat(answer));
            }
            dto.setAnswer(answer);
            dto.setFileName(sb.toString());
            exportList.add(dto);
        }

        String fileName = null;
        fileName = URLEncoder.encode(problemId + "_导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");

        ExcelWriterSheetBuilder writerBuilder = null;
        try {
            writerBuilder = EasyExcel.write(response.getOutputStream(), QaExportRow.class)
                    .autoCloseStream(Boolean.TRUE)
                    .registerWriteHandler(new QaSheetWriteHandler())
                    .registerWriteHandler(new MergeCellSheetWriteHandler(0, 1)) // 大表头从第0行开始合并
                    .sheet("问题清单");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        writerBuilder.doWrite(exportList);
    }

    private String formatAnswerWithLineBreaks(String answer) {
        if (answer == null) return "";

        // 给 1. 到 99. 前加换行符（前面不是数字、不是换行，避免干扰 11.、21. 这类）
        String formatted = answer.replaceAll("(?<!\\d)(?<!\\n)(\\b[1-9][0-9]?\\.)\\s*", "\n$1");

        // 可选：给 - 前也加换行
        formatted = formatted.replaceAll("(?<!\\n)-\\s+", "\n-");

        return formatted.trim();
    }

    private String contentFormat(String input) {
        if(org.apache.commons.lang3.StringUtils.isNotBlank(input)){
            return input.replaceAll("\\[\\^\\d+\\]", "");
        }else{
            return "";
        }
    }

    public static String replaceByPattern(String input, Map<String, String> replacementMap) {
        try{
            // 构建正则模式：匹配3个大写字母
            Pattern pattern = Pattern.compile("(index\\s+\\d+)");
            Matcher matcher = pattern.matcher(input);
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                String key = matcher.group(1);
                String k=key.replaceAll("\\s+", "");
                // 如果Map中存在该key则替换，否则保留原内容
                matcher.appendReplacement(sb,replacementMap.getOrDefault(k, "参考文本"));
            }
            matcher.appendTail(sb);
            return sb.toString();
        }catch (Exception e){
            return input;
        }
    }
}
