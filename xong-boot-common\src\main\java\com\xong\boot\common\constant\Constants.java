package com.xong.boot.common.constant;

import cn.hutool.crypto.KeyUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;

import javax.crypto.SecretKey;

/**
 * 通用常量
 * <AUTHOR>
 */
public class Constants {
    private Constants() {
    }

    /**
     * yml配置前缀
     */
    public static final String YML_PREFIX = "xong.boot";
    /**
     * 接口根路径
     */
    public static final String API_ROOT_PATH = "/api";
    /**
     * 管理平台接口根路径
     */
    public static final String API_ADMIN_ROOT_PATH = API_ROOT_PATH + "/admin";
    /**
     * 用户接口根路径
     */
    public static final String API_USER_ROOT_PATH = API_ROOT_PATH + "/user";
    /**
     * 系统管理接口路径
     */
    public static final String API_SYSTEM_ROOT_PATH = API_ADMIN_ROOT_PATH + "/system";

    /**
     * AI接口根路径
     */
    public static final String API_AI_ROOT_PATH = API_ROOT_PATH + "/ai";

    /**
     * 知识库接口根路径
     */
    public static final String API_KNOWLEDGE_ROOT_PATH = API_ROOT_PATH + "/knowledge";

    /**
     * 关系图谱接口根路径
     */
    public static final String API_GRAPH_PATH = API_ADMIN_ROOT_PATH + "/graph";

    /**
     * 信访接口根路径
     */
    public static final String API_PETITION_ROOT_PATH = API_ROOT_PATH + "/petition";

    /**
     * 基础数据接口根路径
     */
    public static final String API_BASIC_DATA_ROOT_PATH = API_ROOT_PATH + "/basdata";

    /**
     * 凭证分析接口根路径
     */
    public static final String API_VOUCHER_ROOT_PATH = API_ROOT_PATH + "/voucher";

    // -------------------- 自定义请求头 --------------------
    /**
     * 请求UUID
     */
    public static final String HEADER_X_SESSION_ID = "X-Session-Id";
    /**
     * 请求时间戳
     */
    public static final String HEADER_X_TIMESTAMP = "X-Timestamp";
    /**
     * 令牌
     */
    public static final String HEADER_AUTHORIZATION = "Authorization";
    /**
     * 授权类型‌
     */
    public static final String HEADER_AUTHORIZATION_PREFIX = "Bearer ";
    /**
     * GET请求 token参数名称
     */
    public static final String PARAMS_AUTH_TOKEN = "authToken";

    // -------------------- TOKEN --------------------
    /**
     * toekn令牌秘钥
     * 默认 md5("xong-boot")
     */
    public static final SecretKey TOKEN_SECRET_KEY = KeyUtil.generateKey(HmacAlgorithm.HmacSHA512.name(), SecureUtil.sha256("xong-boot").getBytes());

    /**
     * 自动识别json对象白名单配置（仅允许解析的包名，范围越小越安全）
     */
    public static final String[] JSON_WHITELIST_STR = {"org.springframework", "com.xong.boot"};
    /**
     * 英文特殊字符
     */
    public static final String RE_SPECIAL = "[~!@#$%^&*()_+{}|:\"<>?/.,';(\\\\)(\\])(\\[)=(\\-)`]";
}
