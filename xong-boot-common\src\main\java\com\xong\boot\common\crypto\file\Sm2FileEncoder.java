package com.xong.boot.common.crypto.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * SM2文件加解密器
 * <AUTHOR>
 */
public class Sm2FileEncoder implements FileEncoder {
    /**
     * 加密算法
     */
    private final SM2 sm2;

    public Sm2FileEncoder(String privateKey, String publicKey) {
        sm2 = new SM2(privateKey, publicKey);
    }

    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            byte[] bytes = sm2.encrypt(is, KeyType.PrivateKey);
            IoUtil.write(out, isClose, bytes);
        } finally {
            if (isClose && is != null) {
                is.close();
            }
        }
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            byte[] bytes = sm2.decrypt(is, KeyType.PublicKey);
            IoUtil.write(out, isClose, bytes);
        } finally {
            if (isClose && is != null) {
                is.close();
            }
        }
    }
}
