package com.cb.ai.data.analysis.petition.domain.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ss_report
 * <AUTHOR>
@Data
public class SsReportVo implements Serializable {
    private String id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 下载路径
     */
    private String fileUrl;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;
}
