package com.cb.ai.data.analysis.ai.component.choreography.extension;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessScene;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.choreography.model.SceneEnum;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.ClassUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:13
 * @Copyright (c) 2025
 * @Description 扩展管理类
 */
public class ExtensionManager {
    /**
     * 单例
     */
    private static ExtensionManager EXTENSION;
    /**
     * 扩展点实例缓存
     */
    private final Map<String, Object> extensionBeanMap;

    @Getter
    private Table<BusinessTypeEnum, String, List<Object>> bizExtensionMeta;

    private ExtensionManager() {
        this.extensionBeanMap = new ConcurrentHashMap<>(32);
        this.bizExtensionMeta = HashBasedTable.create();
    }

    @PostConstruct
    public void init() {
        String[] beanNames = SpringUtil.getApplicationContext().getBeanNamesForAnnotation(ExtensionProvider.class);
        for (String beanName : beanNames) {
            Object bean = SpringUtil.getBean(beanName);
            Set<Class<?>> interfaces = ClassUtils.getAllInterfacesForClassAsSet(bean.getClass());
            ExtensionProvider extension = AnnotationUtils.findAnnotation(bean.getClass(), ExtensionProvider.class);
            Route[] routes = extension.businessScenes();
            for (Class<?> anInterface : interfaces) {
                if (BaseExtension.class.isAssignableFrom(anInterface)) {
                    for (Route route : routes) {
                        String key = buildKey(anInterface, route.tag(), route.business().name(), route.scene().name());
                        Object value = extensionBeanMap.put(key, bean);
                        if (value != null) {
                            //CommonLog.error("注册 Extension key:{}冲突", key);
                            throw new RuntimeException("注册 Extension 冲突");
                        }
                        //CommonLog.info("注册 Extension key:{}, 接口:{}, 实现类:{}", key, anInterface.getSimpleName(), bean.getClass().getSimpleName());
                        //List<Object> extensions = bizExtensionMeta.get(route.business(), anInterface.getSimpleName());
                        //if (extensions == null) {
                        //    bizExtensionMeta.put(route.businessType(), anInterface.getSimpleName(), ListUtil.toList(bean));
                        //}
                    }
                }
            }
        }
    }

    public static <T> T getExtension(BusinessScene businessScene, Class<T> tClass) {
        if (EXTENSION == null) {
            synchronized (ExtensionManager.class) {
                if (EXTENSION == null) {
                    EXTENSION = SpringUtil.getBean(ExtensionManager.class);
                }
            }
        }
        return EXTENSION.extension(businessScene, tClass);
    }

    @SuppressWarnings("unchecked")
    private <T> T extension(BusinessScene businessScene, Class<T> tClass) {
        Assert.isTrue(tClass.isInterface(), String.format("%s 需要是一个接口", tClass.getSimpleName()));
        Assert.isTrue(BaseExtension.class.isAssignableFrom(tClass), String.format("%s 需要继承 BaseExtension 接口", tClass.getSimpleName()));

        // 定义查找顺序：1.具体场景 2.默认场景 3.默认业务类型 4.完全默认
        List<String> searchSequence = Arrays.asList(
            buildKey(tClass, businessScene.tag(), businessScene.businessType(), businessScene.scene()),
            buildKey(tClass, businessScene.tag(), businessScene.businessType(), SceneEnum.DEFAULT_SCENE.name()),
            buildKey(tClass, businessScene.tag(), BusinessTypeEnum.DEFAULT_BUSINESS.name(), businessScene.scene()),
            buildKey(tClass, businessScene.tag(), BusinessTypeEnum.DEFAULT_BUSINESS.name(), SceneEnum.DEFAULT_SCENE.name())
        );

        for (String key : searchSequence) {
            T extension = (T) extensionBeanMap.get(key);
            if (extension != null) {
                return extension;
            }
        }

        throw new NoSuchElementException(String.format(
            "%s 没有找到实现类%s",
            tClass.getSimpleName(),
            businessScene.getKey()
        ));
    }

    private String buildKey(Class<?> anInterface, String tag, String bizType, String scene) {
        return String.format("%s_%s_%s_%s", anInterface.getSimpleName(), tag, bizType, scene);
    }

}
