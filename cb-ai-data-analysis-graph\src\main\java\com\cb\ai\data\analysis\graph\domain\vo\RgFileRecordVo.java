package com.cb.ai.data.analysis.graph.domain.vo;


import com.cb.ai.data.analysis.petition.domain.vo.PageQueryVo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description:
 * @Author: ARPHS
 * @Date: 2025-04-30 16:17
 * @Version: 1.0
 **/
public interface RgFileRecordVo {

    @Data
    class RgFileRecordPageQueryReq {

        private String fileName;

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date beginTime;

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date endTime;

        /**
         * 处理状态 0-待解析 1-解析中 2-已入库 3-解析失败
         */
        private Integer status;

    }

}
