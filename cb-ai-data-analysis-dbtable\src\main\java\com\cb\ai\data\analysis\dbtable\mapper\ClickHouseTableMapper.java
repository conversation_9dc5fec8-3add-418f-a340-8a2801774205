package com.cb.ai.data.analysis.dbtable.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.model.ColumnData;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * clickhouse Mapper
 * <AUTHOR>
 */
@Mapper
@DS("clickhouse")
public interface ClickHouseTableMapper {
    /**
     * 判断是否存在表
     * @param dbName    数据库名称
     * @param tableName 表名称
     */
    boolean existSqlTable(@Param("dbName") String dbName, @Param("tableName") String tableName);

    /**
     * 判断是否存在表数据
     * @param tableName 表名称
     */
    boolean existTableData(@Param("tableName") String tableName);

    /**
     * 创建表 主键必须是排序键的前缀
     * @param tableName      表名
     * @param tableComment   表备注
     * @param primaryColumns 主键字段
     * @param columns        表字段
     */
    void createTable(@Param("tableName") String tableName,
                     @Param("tableComment") String tableComment,
                     @Param("primaryColumns") List<String> primaryColumns,
                     @Param("columns") List<String> columns);

    /**
     * 根据表名删除表
     * @param tableName 表名
     */
    void deleteTable(@Param("tableName") String tableName);

    /**
     * 修改表名
     * @param tableName 表名
     */
    boolean renameTableName(@Param("tableName") String tableName, @Param("nowTableName") String nowTableName);

    /**
     * 拷贝表数据
     * @param names           表字段名集
     * @param sourceTableName 源数据表名
     * @param targetTableName 目标数据表名
     */
    long copyTableData(@Param("names") List<String> names, @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName);

    /**
     * 获取表字段名
     * @param dbName    数据库名称
     * @param tableName 表名称
     */
    List<String> selectTableColumnName(@Param("dbName") String dbName, @Param("tableName") String tableName);

    /**
     * 查询表数据
     * @param page
     * @param tableName
     * @return
     */
    Page<Map<String, Object>> selectTableRows(Page<?> page, @Param("tableName") String tableName);

    /**
     * 查询表数据
     * @param tableName 表名称
     * @param whereSql  查询条件
     */
    Page<Map<String, Object>> selectTableData(Page<?> page, @Param("tableName") String tableName, @Param("whereSql") String whereSql);

    /**
     * 根据id查询表数据
     * @param tableName
     * @param id
     * @return
     */
    Map<String, Object> selectTableDataById(@Param("tableName") String tableName, String id);

    /**
     * 插入表数据
     * @param tableName   表名称
     * @param columnDatas 字段信息
     */
    int insertTableData(@Param("tableName") String tableName, @Param("et") List<ColumnData> columnDatas);

    /**
     * 批量删除表数据
     * @param tableName 表名
     * @param ids       需要删除的数据主键集合
     */
    int deleteTableData(@Param("tableName") String tableName, @Param("ids") List<String> ids);

    /**
     * 添加表数据
     * @param tableName
     * @param record
     * @return
     */
    int addTableData(@Param("tableName") String tableName, @Param("et") Map<String, Object> record);

    /**
     * 修改表数据
     * @param tableName
     * @param id
     * @param record
     * @return
     */
    int updateTableData(@Param("tableName") String tableName, @Param("id") String id, @Param("et") Map<String, Object> record);

}
