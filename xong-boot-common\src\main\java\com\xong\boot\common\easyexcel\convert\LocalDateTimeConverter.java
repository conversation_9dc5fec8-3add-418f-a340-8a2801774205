package com.xong.boot.common.easyexcel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Slf4j
public class LocalDateTimeConverter implements Converter<LocalDateTime> {

    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public Class<LocalDateTime> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    @Override
    public LocalDateTime convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String cellValue = cellData.getStringValue();
        if (cellValue == null || cellValue.trim().isEmpty()) {
            return null; // 如果单元格为空，返回 null
        }

        try {
            // 尝试使用默认日期格式解析
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT, Locale.getDefault());
            return LocalDateTime.parse(cellValue, formatter);
        } catch (Exception e) {
            // 如果解析失败，记录日志并返回 null 或抛出自定义异常
            log.error("无法解析日期：{}", cellValue);
            return null;
        }
    }
}
