package com.cb.ai.data.analysis.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/22 22:05
 * @Copyright (c) 2025
 * @Description AI会话历史记录详情服务层
 */
public interface IAiChatHistoryDetailService extends IService<AiChatHistoryDetail> {

    /**
     * 根据创建时间分页获取会话历史记录详情
     * @param aiChatHistoryDetail 查询实体
     * @param pageSize 每页数量
     *
     * @return
     */
    JsonMap pageByTime(AiChatHistoryDetail aiChatHistoryDetail);

    /**
     * 获取对应会话的历史会话详细记录
     * @param sessionId 会话ID
     *
     * @return
     */
    List<AiChatHistoryDetail> getHistoryDetailById(String sessionId, String userId);

    /**
     * 获取对应会话的历史会话详细记录
     * @param sessionId 会话ID
     *
     * @return
     */
    boolean saveHistoryDetail(AiChatHistoryDetail detail);

    /**
     * 删除对应会话历史详情记录
     * @param sessionId 会话ID
     *
     * @return
     */
    boolean removeById(String sessionId, String userId);

    /**
     * 删除对应会话详情Id的历史会话记录
     * @param sessionId 会话ID
     *
     * @return
     */
    boolean removeByIds(List<String> ids);

}

