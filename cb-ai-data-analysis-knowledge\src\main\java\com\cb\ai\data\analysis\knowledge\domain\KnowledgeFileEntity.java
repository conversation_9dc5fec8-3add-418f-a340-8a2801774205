package com.cb.ai.data.analysis.knowledge.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import java.io.Serializable;

@Data
@TableName("cb_knowledge_file")
public class KnowledgeFileEntity extends BaseDomain implements Serializable {

    /***
     * 主键ID
     */
    private String id;

    /***
     * 文件ID
     */
    private String fileId;

    /***
     * ai侧ID
     */
    private String aiFileId;

    /***
     * 知识库ID
     */
    private String baseId;

    /***
     * 文件名称
     */
    private String fileName;

    /***
     * 文件url
     */
    private String fileUrl;
}
