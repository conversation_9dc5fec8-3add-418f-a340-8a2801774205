package com.cb.ai.data.analysis.ai.component.choreography.engine;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:25
 * @Copyright (c) 2025
 * @Description 执行节点接口
 */
public interface IProcessNode<E, R> extends INode {
    /**
     * 单节点的处理方法
     * @return 执行结果
     */
    default R process() {
        return process(null);
    }
    /**
     * 单节点的处理方法
     * @param requestContext 请求上下文
     * @return 执行结果
     */
    R process(E requestContext);

}
