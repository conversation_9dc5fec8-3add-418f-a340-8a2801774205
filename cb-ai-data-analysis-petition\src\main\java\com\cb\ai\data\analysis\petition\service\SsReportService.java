package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsReportEntity;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsReportPageQueryQueryVo;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.service.BaseService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/***
 * <AUTHOR>
 * 信访报告
 */
public interface SsReportService extends BaseService<SsReportEntity> {

    /**
     * 分页查询报告列表信息
     * @param pageVo
     * @return
     */
    Page<SsReportEntity> selectByPage(SsReportPageQueryQueryVo pageVo);

    /***
     * 下载报告
     * @param reportId
     * @param response
     */
    void download(String reportId, HttpServletResponse response);

    /***
     * 生成报告
     * @param files 图片文件 地图和工单图表
     * @param startDate 开始时间
     * @param endDate  结束时间
     * @param filename 生成的文件名称
     * @param types 工单类型
     * @param previousReportData 省报告数据
     * @param city  地市
     * @param district
     * @param relateRepetitiveSize
     * @return
     * @throws IOException
     */
    Result submitGenerate(List<MultipartFile> files,
                          String startDate,
                          String endDate,
                          String filename, List<String> types, String previousReportData, String city, String district, Integer relateRepetitiveSize) throws IOException;

}
