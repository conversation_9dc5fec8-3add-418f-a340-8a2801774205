package com.cb.ai.data.analysis.ai.component.extensions;

import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.ai.utils.MdPromoteExtractorUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 向量知识库问答扩展
 */
@ExtensionProvider(desc = "向量知识库问答扩展", businessScenes = {
    @Route(tag = "向量知识库问答", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PrivateEmbeddingChatExtension implements PrivateAIExtension<CommonAIRequestContext> {

    private final EmbeddingExtension embeddingExtension;

    @Override
    public Flux<ResultData<PrivateAIBackData>> invoke(CommonAIRequestContext context) {
        AtomicReference<EmbeddingExtension.CollectData> collectDataRef = new AtomicReference<>();
        return embeddingExtension.invoke(context, collectDataRef::set)
            .mapNotNull(data -> new ResultData<>(ResultDataStatusEnum.STREAMING, data).setRequestId(context.getRequestId()))
            .concatWith(Flux.defer(() -> {
                EmbeddingExtension.CollectData collectData = collectDataRef.get();
                // 1.组装系统提示词
                String systemPromote = MdPromoteExtractorUtil.getSysPromote("promote/embedchat/default-source-footnote.md");
                // 2.组装用户提示词并加入请求的用户提示词
                String userPromote = MdPromoteExtractorUtil.replaceUserPromoteTags(
                        "promote/embedchat/reference.md",
                        Map.of("userPromote", context.getPromote(), "references", collectData.embeddingContext())
                );
                // 3.设置系统提示词和用户提示词
                context.setSystemPromote(systemPromote);
                context.setPromote(userPromote);
                PrivateAIChat privateAIChat = new PrivateAIChat();
                collectData.dataList().parallelStream().forEach(item ->
                    privateAIChat.addAfterData((nodeContext, requestContext) ->
                        new PrivateAIBackData()
                            .setRole(RoleEnum.assistant.name())
                            .setSessionId(requestContext.getSessionId())
                            .setData(item)
                    )
                );
                return privateAIChat.maxTokens(token -> (int) (token * 0.9)).processData(context);
            }
        ));
    }

}
