package com.cb.ai.data.analysis.dbtable.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableTaskService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import org.springframework.web.bind.annotation.*;

/**
 * 数据导入工作Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table/task")
public class AnalysisDbTableTaskController extends BaseController<AnalysisDbTableTaskService, AnalysisDbTableTask> {

    /**
     * 查询数据导入工作列表
     */
//    @PreAuthorize("@ss.hasPermi('datapool:job:list')")
    @GetMapping("/page")
    public Result list(AnalysisDbTableTask dynamicTableTask) {
        Page<AnalysisDbTableTask> list = baseService.pageTableTaskList(dynamicTableTask);
        return Result.successData(list);
    }

    /**
     * 删除数据导入工作
     */
//    @PreAuthorize("@ss.hasPermi('datapool:job:remove')")
    @DeleteMapping()
    public Result remove(@RequestParam(value = "ids") String[] ids) {
        baseService.deleteTableTaskByIds(ids);
        return Result.success();
    }
}
