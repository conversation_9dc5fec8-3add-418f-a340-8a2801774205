package com.cb.ai.data.analysis.ai.component.choreography.engine;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/8 16:00
 * @Copyright (c) 2025
 * @Description 同步执行节点接口
 */
public interface ISyncNode<E, R> extends INode {
    /**
     * 同步节点的处理方法
     * @return 执行结果
     */
    default R syncProcess() {
        return syncProcess(null);
    }
    /**
     * 同步节点的处理方法
     * @param requestContext 请求上下文
     * @return 执行结果
     */
    R syncProcess(E requestContext);

}
