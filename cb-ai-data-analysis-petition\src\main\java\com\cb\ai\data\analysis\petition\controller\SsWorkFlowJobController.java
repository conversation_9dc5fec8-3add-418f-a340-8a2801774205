//package com.cb.ai.data.analysis.petition.controller;
//
//
//import com.cb.ai.data.analysis.petition.constant.Constants;
//import com.cb.ai.data.analysis.petition.domain.vo.request.SsWorkJobPageQueryQueryVo;
//import com.cb.ai.data.analysis.petition.service.SsWorkFlowJobService;
//import com.xong.boot.common.api.Result;
//import jakarta.annotation.Resource;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//
//@RestController
//@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/workFlow/job")
//public class SsWorkFlowJobController {
//
//    @Resource
//    private SsWorkFlowJobService ssWorkFlowJobService;
//
//    @GetMapping("/page")
//    public Result page(SsWorkJobPageQueryQueryVo queryPageVo) {
//        return ssWorkFlowJobService.page(queryPageVo);
//    }
//
//
//    @GetMapping("/stop")
//    public Result stop() {
//        ssWorkFlowJobService.stop();
//        return Result.success();
//    }
//}
