package com.cb.ai.data.analysis.query.utils;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TextQueryType;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;
import com.cb.ai.data.analysis.query.enums.EsQueryPermEnum;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.utils.spring.SpringUtils;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.domain.items.RoleItem;
import com.xong.boot.framework.exception.XAuthenticationException;
import com.xong.boot.framework.utils.SecurityUtils;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.service.impl.SysDeptServiceImpl;
import lombok.extern.slf4j.Slf4j;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;

import java.text.Normalizer;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ElastiscSearch 查询工具类
 */
@Slf4j
public class ElasticQueryUtils {
    // 默认分页大小
    public static final int DEFAULT_PAGE_SIZE = 10;
    // 最大允许的 pageSize
    public static final int MAX_PAGE_SIZE = 100;
    // 最大允许的 from 值（防止深度分页）
    public static final int MAX_FROM = 10000;
    // 统计总数，设置太大影响搜索效率
    public final static Integer MAX_TRACK_TOTAL = 100;
    // 单次查询最大返回文档数
    public final static Integer MAX_BUILD_SIZE = 1000;
    /**
     * maxAnalyzedOffset 最大扫描字符数
     * 默认是 1,000,000（一百万）字符
     * 建议值范围:50 - 1,000,000 字符之间，根据业务需求调整
     * 若不配置，超出最大扫描字符数，ElasticSearch将抛出异常（The length [1499244] of field [fileContent] in doc[0]/index[biz_supervise_file] exceeds the [index.highlight.max_analyzed_offset] limit [1000000]. To avoid this error, set the query parameter [max_analyzed_offset] to a value less than index setting [1000000] and this will tolerate long field values by truncating them.）
     */
    public final static Integer MAX_ANALYZED_OFFSET = 500000;
    //默认查询匹配字段
    private static final String[] DEFAULT_MATCH_QUERY_FIELDS = {
            "name^4",
            "*Name^2",
            "*Title^2",
            "fileName", // 指定字段匹配
            "fileContent", //指定字段匹配
            "content",
            "*"
    };

    private final static SysDeptServiceImpl deptService = SpringUtils.getBean(SysDeptServiceImpl.class);

    /**
     * 构建查询条件
     *
     * @param builder
     * @param query
     * @return
     */
    public static void buildSearchBuilder(SearchRequest.Builder builder, ElasticReqBo.Query query) {
        // 设置Elasticsearch索引名称，如果未指定则使用默认前缀+通配符
        String index = query.getIndexName();
        if (StringUtils.isNotBlank(index)) {
            builder.index(index);
        } else {
            builder.index(Constant.ES_BASE_PREFIX + "*");
        }

        // 获取搜索关键词
        String keyword = query.getKeyword();
        builder.query(q -> q.bool(b -> {
            // 构建关键词搜索条件
            b.must(m -> {
                if (StringUtils.isNotBlank(keyword)) {
                    // 如果有关键词，执行multiMatch查询，搜索多个字段
                    m.multiMatch(multi -> multi
                            .type(TextQueryType.MostFields) // 改为MostFields以提升多字段匹配得分
                            .query(escapeLuceneQuery(keyword))
                            .fields(Arrays.asList(buildMatchQueryFields(query)))
                            // 设置模糊匹配程度为自动，允许Elasticsearch根据字段内容自动调整模糊匹配的编辑距离
                            // 可用参数： "AUTO"（自动）、"0"（精确匹配）、"1"（允许1个字符的编辑距离）、"2"（允许2个字符的编辑距离）
                            .fuzziness("AUTO")
                            .tieBreaker(0.7) // 提升tieBreaker参数，增强次优字段得分（ 确保 tieBreaker 不超过最大值 1.0）
                            .boost(1.5f)   //增加整体查询的权重（ 确保 boost 不超过最大值 10.0）
                    );
                } else {
                    // 如果没有关键词，则匹配所有文档
                    m.matchAll(matchAll -> matchAll);
                }
                return m;
            });
            // 添加权限控制条件到查询中
            buildPermQueryBuilder(b);

            return b;
        }));

        // 排序处理
        if (StringUtils.isNotBlank(query.getSortField())) {
            // 如果指定了排序字段，按照指定字段和顺序排序
            builder.sort(s -> s.field(f -> f
                    .field(query.getSortField())
                    .order(query.isSortAsc() ? SortOrder.Asc : SortOrder.Desc)));
        } else {
            // 默认按照文档相关性得分(_score)降序排序
            builder.sort(s -> s.field(f -> f.field("_score").order(SortOrder.Desc)));
        }
        // 设置单次查询最大返回文档数，防止内存溢出
        builder.size(MAX_BUILD_SIZE);
    }

    /**
     * 构建数据权限查询条件，用于 Elasticsearch 查询构建器。
     * 该方法根据当前用户的角色和权限信息，动态添加数据权限过滤条件，
     * 权限之间是 OR 关系，且所有条件使用 filter 上下文，不参与评分。
     *
     * @param builder Elasticsearch 查询构建器，用于构建布尔查询条件
     */
    private static void buildPermQueryBuilder(BoolQuery.Builder builder) {
        // 获取当前登录用户信息
        UserDetailsImpl userDetail = SecurityUtils.getUserDetails();
        if (Objects.isNull(userDetail)) {
            throw new XAuthenticationException("用户未登录");
        }
        // 获取用户部门ID和区域ID
        String deptId = userDetail.getDeptId();
        String districtId = SecurityUtils.getDistrictId();
        String username = userDetail.getUsername();

        // 获取用户角色
        List<RoleItem> roles = userDetail.getRoles();
        Set<String> queryPerms = roles.stream().map(RoleItem::getEsQueryPerm).collect(Collectors.toSet());
        // 测试使用
        // Set<String> queryPerms = Arrays.stream(EsQueryPermEnum.values()).map(EsQueryPermEnum::getPerm).collect(Collectors.toSet());
        //  如果查询权限空了，则降级为本人权限
        if (Objects.isNull(queryPerms) || queryPerms.isEmpty()) {
            filterMatchSelf(builder, EsQueryPermEnum.SCOPE_SELF.getField(), username);
        } else {
            if (queryPerms.contains(EsQueryPermEnum.SCOPE_ALL.getPerm())) {
                return;
            }
            List<Query> orFilters = new ArrayList<>();
            queryPerms.stream()
                    .filter(p -> !EsQueryPermEnum.SCOPE_ALL.getPerm().equals(p))
                    .forEach(perm -> {
                        // 将字符串类型的权限转换为枚举类型
                        EsQueryPermEnum permEnum = EsQueryPermEnum.of(perm);
                        String field = permEnum.getField();
                        Query query = null;
                        switch (permEnum) {
                            case SCOPE_SELF:
                                query = Query.of(q -> q
                                        .bool(b -> b
                                                .filter(f -> f.match(m -> m.field(field).query(username)))
                                        )
                                );
                                break;
                            case SCOPE_DEPT:
                                if (StringUtils.isNotBlank(deptId)) {
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(deptId)))
                                            )
                                    );
                                } else {
                                    // 降级处理：本人权限
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(username)))
                                            )
                                    );
                                }
                                break;
                            case SCOPE_DEPT_AND_CHILD:
                                if (StringUtils.isNotBlank(deptId)) {
                                    try {
                                        List<SysDept> deptList = deptService.selectDeptAndChildList(deptId);
                                        if (!deptList.isEmpty()) {
                                            query = Query.of(q -> q
                                                    .bool(b -> b
                                                            .filter(f -> f.terms(terms -> terms
                                                                    .field(field)
                                                                    .terms(t -> t.value(deptList.stream()
                                                                            .map(SysDept::getDeptId)
                                                                            .map(FieldValue::of)
                                                                            .collect(Collectors.toList())))
                                                            ))
                                                    )
                                            );
                                        }
                                    } catch (Exception e) {
                                        log.warn("获取部门及子部门失败，使用默认权限", e);
                                        // 降级处理：本人权限
                                        query = Query.of(q -> q
                                                .bool(b -> b
                                                        .filter(f -> f.match(m -> m.field(field).query(username)))
                                                )
                                        );
                                    }
                                } else {
                                    // 降级处理：本人权限
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(username)))
                                            )
                                    );
                                }
                                break;

                            case SCOPE_DISTRICT:
                                if (StringUtils.isNotBlank(districtId)) {
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(districtId)))
                                            )
                                    );
                                } else {
                                    // 降级处理：本人权限
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(username)))
                                            )
                                    );
                                }
                                break;

                            case SCOPE_DISTRICT_AND_CHILD:
                                if (StringUtils.isNotBlank(districtId)) {
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.prefix(p -> p.field(field).value(districtId)))
                                            )
                                    );
                                } else {
                                    // 降级处理：本人权限
                                    query = Query.of(q -> q
                                            .bool(b -> b
                                                    .filter(f -> f.match(m -> m.field(field).query(username)))
                                            )
                                    );
                                }
                                break;
                            default:
                                throw new XServerException("权限类型错误");
                        }
                        if (query != null) {
                            orFilters.add(query);
                        }
                    });
            if (orFilters.isEmpty()) {
                // 如果没有任何权限条件，则降级为本人权限
                filterMatchSelf(builder, EsQueryPermEnum.SCOPE_SELF.getField(), username);
            } else {
                // 使用 should 包裹多个 filter 查询，实现 OR 且不评分
                builder.should(orFilters);
            }
        }

    }

    /**
     * 构建仅匹配当前用户数据的过滤条件
     *
     * @param q        查询构建器
     * @param field    权限字段名称
     * @param username 当前用户用户名
     */
    private static void filterMatchSelf(BoolQuery.Builder q, String field, String username) {
        // 添加过滤条件，使用match查询匹配指定字段和用户名
        q.filter(m -> m.match(match -> match.field(field).query(username)));
    }

    /**
     * 构建分页查询条件
     *
     * @param builder
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    /**
     * 构建分页搜索请求
     *
     * @param builder  Elasticsearch搜索请求构建器
     * @param query    查询条件对象
     * @param pageNum  页码，从1开始
     * @param pageSize 每页记录数
     */
    public static void buildPageSearchBuilder(SearchRequest.Builder builder, ElasticReqBo.Query query, Integer pageNum, Integer pageSize) {
        // 首先构建基础查询条件
        buildSearchBuilder(builder, query);
        // 然后添加分页查询条件
        buildPageSearchBuilder(builder, pageNum, pageSize);
    }


    /**
     * 构建分页查询条件
     *
     * @param builder  Elasticsearch搜索请求构建器
     * @param pageNum  页码，从1开始
     * @param pageSize 每页记录数
     */
    public static void buildPageSearchBuilder(SearchRequest.Builder builder, Integer pageNum, Integer pageSize) {
        // 参数校验
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0 || pageSize > MAX_PAGE_SIZE) {
            pageSize = DEFAULT_PAGE_SIZE;
        }
        // 防止深度分页，限制最大 from 值
        int from = (pageNum - 1) * pageSize;
        if (from > MAX_FROM) {
            throw new IllegalArgumentException("分页超出最大限制，from 不能大于 " + MAX_FROM);
        }
        // 设置分页和总数统计
        builder.from(from).size(pageSize).trackTotalHits(t -> t.count(MAX_TRACK_TOTAL));
    }

    /**
     * 高亮显示匹配的搜索内容，默认使用红色标记
     *
     * @param builder Elasticsearch搜索请求构建器
     */
    public static void highlight(SearchRequest.Builder builder, ElasticReqBo.Query query) {
        highlight(builder, query, "<span style='color:red;'>", "</span>");
    }

    /**
     * 配置高亮显示设置
     *
     * @param builder  Elasticsearch搜索请求构建器
     * @param preTags  高亮前缀标签（HTML）
     * @param postTags 高亮结束标签（HTML）
     */
    public static void highlight(SearchRequest.Builder builder, ElasticReqBo.Query query, String preTags, String postTags) {
        // 校验字段是否存在
        String[] matchQueryFields = buildMatchQueryFields(query);
        // 处理高亮字段，去除字段权重标识 "^"
        String[] highlightFields = Arrays.stream(matchQueryFields)
                .map(f -> f.contains("^") ? f.split("\\^")[0] : f) // 提取基础字段名
                .toArray(String[]::new);
        // 配置高亮参数
        builder.highlight(h -> {
            h.maxAnalyzedOffset(MAX_ANALYZED_OFFSET); // 设置最大分析偏移量，防止过大文本导致异常
            h.type("unified"); // 显式指定高亮类型，提高性能和可控性

            // 遍历字段列表，配置每个字段的高亮显示
            for (String field : highlightFields) {
                h.fields(field, f -> f
                        .preTags(preTags)        // 设置高亮前缀（已转义）
                        .postTags(postTags)      // 设置高亮后缀（已转义）
                        .numberOfFragments(1)         // 每个字段返回一个高亮片段
                        .fragmentSize(50)             // 高亮片段长度
                );
            }
            return h;
        });
    }

    /**
     * 构建用于匹配查询的字段数组。
     * 该方法优先使用查询对象中指定的字段数组，若为空则使用默认字段数组。
     * 返回结果为原始数组的拷贝，以防止外部修改影响内部状态。
     *
     * @param query 查询请求对象，包含用户指定的匹配字段数组
     * @return 包含实际使用匹配字段的字符串数组
     */
    public static String[] buildMatchQueryFields(ElasticReqBo.Query query) {
        String[] matchQueryFields = query.getMatchQueryFields();
        if (matchQueryFields == null || matchQueryFields.length == 0) {
            matchQueryFields = DEFAULT_MATCH_QUERY_FIELDS;
        }
        return Arrays.copyOf(matchQueryFields, matchQueryFields.length);
    }

    /**
     * 转义Lucene查询中的特殊字符
     *
     * @param input 查询关键词
     * @return 转义后的字符串
     */
    public static String escapeLuceneQuery(String input) {
        if (input == null || input.isEmpty()) return input;
        // 标准化字符串，处理 Unicode 标准化问题
        String normalized = Normalizer.normalize(input, Normalizer.Form.NFC);
        // 转义 Lucene 查询语法中的特殊字符，包括布尔操作符和通配符
        return normalized.replaceAll("([+\\-!(){}\\[\\]^\"~:\\/]|\\s&&\\s|\\s\\|\\|\\s|\\*|\\?)", "\\\\$1");
    }
}


