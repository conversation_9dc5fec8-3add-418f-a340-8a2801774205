package com.cb.ai.data.analysis.ai.component.http;

import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import io.github.admin4j.http.ApiClient;
import io.github.admin4j.http.core.HttpConfig;
import io.github.admin4j.http.core.HttpDefaultConfig;
import io.github.admin4j.http.core.MediaTypeEnum;
import io.github.admin4j.http.core.Method;
import io.github.admin4j.http.util.HttpUtil;
import okhttp3.Call;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.http.HttpStatus;
import org.springframework.util.StopWatch;
import org.springframework.web.client.HttpClientErrorException;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/3 17:23
 * @Copyright (c) 2025
 * @Description OkHttps客户端
 */
public class OkHttpsClient extends BaseClient {
    /**
     * 客户端
     */
    private final ApiClient httpClient;

    public OkHttpsClient() {
        HttpConfig httpConfig = HttpDefaultConfig.get();
        httpConfig.setConnectTimeout(300);
        httpConfig.setReadTimeout(300);
        this.httpClient = HttpUtil.getClient();
    }

    @Override
    public <V> Flux<V> executeStream() {
        try {
            String responseBody = doExecute();
            // 提取SSE流数据
            List<V> result = processSseStream(responseBody);
            if (result.isEmpty()) {
                throw new RuntimeException("请求接口成功，但是未能从原始返回数据中提取到流式数据, 原始返回数据：" + responseBody);
            }
            return Flux.fromIterable(result);
        } finally {
            clear();
        }
    }

    @Override
    public <V> V executeBlock() {
        try {
            return processResultData(doExecute());
        } finally {
            clear();
        }
    }

    public String doExecute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonLog.info("OkHttpsClient -> 请求开始 ------>");
        CommonLog.info("OkHttpsClient -> 请求接口地址：{}", getUrl());
        CommonLog.info("OkHttpsClient -> 请求头：{}", JsonUtil.toStr(getHeaders().toSingleValueMap()));
        CommonLog.info("OkHttpsClient -> 请求体：{}", JsonUtil.toStr(getBody()));
        Call call;
        if (isMultiFormData()) {
            Assert.notNull(getFileBody(), "上传文件请求体(fileBody)不能为null");
            call = httpClient.buildCall(getUrl(), convertMethod(), MediaTypeEnum.FORM_DATA, null, null, null, getFileBody().toObjMap(), new JsonMap(getHeaders().toSingleValueMap()));
        } else {
            Assert.notNull(getBody(), "表单请求体(body)不能为null");
            if (isFormData()) {
                call = httpClient.buildCall(getUrl(), convertMethod(), MediaTypeEnum.FORM, null, null, null, JsonUtil.toMap(getBody()), new JsonMap(getHeaders().toSingleValueMap()));
            } else {
                call = httpClient.buildCall(getUrl(), convertMethod(), MediaTypeEnum.JSON, null, null, getBody(), null, new JsonMap(getHeaders().toSingleValueMap()));
            }
        }
        try (Response response = httpClient.execute(call)) {
            // 处理response, 暂不考虑重定向判断
            if (response.isSuccessful()) {
                // 获取body
                ResponseBody body = response.body();
                if (body != null) {
                    return body.string();
                } else {
                    return "";
                }
            }
            throw new HttpClientErrorException(HttpStatus.valueOf(response.code()), response.message());
        } catch (Exception e) {
            throw getOnError().apply(e);
        } finally {
            stopWatch.stop();
            CommonLog.info("OkHttpsClient -> 请求结束 , 耗时：{}ms", stopWatch.getTotalTimeMillis());
        }
    }

    private Method convertMethod() {
        return switch (getMethod().name()) {
            case "GET" -> Method.GET;
            case "PUT" -> Method.PUT;
            case "DELETE" -> Method.DELETE;
            case "PATCH" -> Method.PATCH;
            case "HEAD" -> Method.HEAD;
            case "OPTIONS" -> Method.OPTIONS;
            default -> Method.POST;
        };
    }
}
