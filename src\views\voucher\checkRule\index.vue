<template>
  <x-page-wrapper hiddenTitle class="voucher-check-rule">
    <x-table-search :model="searchFormData" :tableRef="tableRef">
      <a-form-item label="规则名称" name="name">
        <a-input v-model:value="searchFormData.name" :maxlength="100" allow-clear placeholder="请输入规则名称" />
      </a-form-item>
      <a-form-item label="状态" name="disable">
        <a-select v-model:value="searchFormData.disable" allow-clear placeholder="请选择状态">
          <a-select-option :value="0">启用</a-select-option>
          <a-select-option :value="1">禁用</a-select-option>
        </a-select>
      </a-form-item>
    </x-table-search>
    <x-table ref="tableRef" :columns="columns" :loadData="loadData" :rowSelection="true" title="凭证检测规则管理" row-key="id">
      <template #toolbar="{ selectedRowKeys }">
        <a-button type="primary" @click="onClickAdd">
          <template #icon>
            <x-icon type="PlusOutlined" />
          </template>
          新增规则
        </a-button>
        <a-button v-if="selectedRowKeys && selectedRowKeys.length > 0" danger
          @click="onClickBatchDelete(selectedRowKeys)">
          <template #icon>
            <x-icon type="DeleteOutlined" />
          </template>
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'disable'">
          <a-tag :color="text === 0 ? 'success' : 'default'">
            {{ text === 0 ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'promote'">
          <div class="promote-text">
            {{ text }}
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ $date.formatDateTime(text) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button type="link" size="small" @click="onClickEdit(record)">
              编辑
            </a-button>
            <a-button v-if="record.disable === 0" type="link" size="small" @click="onClickDisable(record)">
              禁用
            </a-button>
            <a-button v-else type="link" size="small" @click="onClickEnable(record)">
              启用
            </a-button>
            <a-button type="link" size="small" danger @click="onClickDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </x-table>

    <!-- 新增组件 -->
    <AddComp v-model:visible="addVisible" @success="onSuccess" />
    <!-- 编辑组件 -->
    <EditComp v-model:visible="editAttrs.visible" :rule-id="editAttrs.ruleId" @success="onSuccess" />
  </x-page-wrapper>
</template>

<script setup lang="ts" name="VoucherCheckRule">
import { computed, getCurrentInstance, reactive, ref, type ComponentCustomProperties } from 'vue'
import { checkRule } from '@/api/voucher'
import AddComp from './Add.vue'
import EditComp from './Edit.vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const tableRef = ref()
const addVisible = ref(false)
const editAttrs = reactive({
  visible: false,
  ruleId: undefined
})

const searchFormData = ref({
  name: '',
  disable: undefined
})

const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'name',
      title: '规则名称',
      width: 200,
      ellipsis: true
    },
    {
      dataIndex: 'description',
      title: '规则说明',
      width: 200,
      ellipsis: true
    },
    {
      dataIndex: 'promote',
      title: '提示词',
      width: 300,
      ellipsis: true
    },
    {
      dataIndex: 'disable',
      title: '状态',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
      align: 'center'
    }
  ] as TableColumn[]

  columnItems.push({
    key: 'actions',
    title: '操作',
    width: 150,
    align: 'center'
  })

  return columnItems
})

/**
 * 加载数据
 */
async function loadData(params: Record<string, any>) {
  const res = await checkRule.page(params)
  return res
}

/**
 * 新增规则
 */
function onClickAdd() {
  addVisible.value = true
}

/**
 * 编辑规则
 */
function onClickEdit(record: any) {
  console.log(record.id)
  editAttrs.visible = true
  editAttrs.ruleId = record.id
}

/**
 * 成功回调
 */
function onSuccess() {
  tableRef.value?.refresh()
}

/**
 * 禁用规则
 */
async function onClickDisable(record: any) {
  try {
    await checkRule.disableRule(record.id)
    _this.$message.success('禁用成功')
    tableRef.value?.refresh()
  } catch (error) {
    _this.$message.error('禁用失败')
  }
}

/**
 * 启用规则
 */
async function onClickEnable(record: any) {
  try {
    await checkRule.update({
      id: record.id,
      name: record.name,
      description: record.description,
      promote: record.promote,
      disable: 0
    })
    _this.$message.success('启用成功')
    tableRef.value?.refresh()
  } catch (error) {
    _this.$message.error('启用失败')
  }
}

/**
 * 删除规则
 */
function onClickDelete(record: any) {
  _this.$confirm({
    title: '确认删除',
    content: `确定要删除规则"${record.name}"吗？`,
    onOk: async () => {
      try {
        await checkRule.del([record.id])
        _this.$message.success('删除成功')
        tableRef.value?.refresh()
      } catch (error) {
        _this.$message.error('删除失败')
      }
    }
  })
}

/**
 * 批量删除
 */
function onClickBatchDelete(selectedRowKeys: string[]) {
  _this.$confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.length} 条规则吗？`,
    onOk: async () => {
      try {
        await checkRule.del(selectedRowKeys)
        _this.$message.success('批量删除成功')
        tableRef.value?.refresh()
      } catch (error) {
        _this.$message.error('批量删除失败')
      }
    }
  })
}
</script>

<style scoped lang="less">
.voucher-check-rule {
  .promote-text {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
