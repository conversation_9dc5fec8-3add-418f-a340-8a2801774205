package com.cb.ai.data.analysis.ai.domain.common;

import java.lang.invoke.VarHandle;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/3 15:53
 * @Copyright (c) 2025
 * @Description 字段反射记录器
 */
public record FieldHandle(VarHandle fieldHandle, Field field) {

    public Object get(Object obj) {
        return fieldHandle.get(obj);
    }

    public void set(Object obj, Object value) {
        fieldHandle.set(obj, value);
    }

    public String getFieldName() {
        return field.getName();
    }

    public Class<?> getFieldType() {
        return field.getType();
    }

}
