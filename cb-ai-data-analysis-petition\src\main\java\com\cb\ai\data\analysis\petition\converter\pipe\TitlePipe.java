package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocTitle;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;
import java.util.Objects;

/**
 * 标题管道
 * <AUTHOR>
 */
public class TitlePipe extends IPipe {

    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<DocTitle> titles = documentInfo.getTitles();
        if (titles == null || titles.size() == 0) {
            return false;
        }
        for (DocTitle docTitle : titles) {
            if (Objects.equals(pos, docTitle.getPos())) {
                return true;
            }
        }
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化段落
        FormatTools.formatParagraphTitle(paragraph, config);
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            FormatTools.formatTitle(run, config);
        }
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = pos - 1; i > 0; i--) {
            XWPFParagraph p = paragraphs.get(i);
            String pText = p.getText().trim();
            if (StringUtils.isNotBlank(pText)) {
                if (ReUtil.contains("[\\d×xX ]+年[\\d×xX ]+月[\\d×xX ]+日", pText) || ReUtil.contains("^[(（][^）)（(]+[）)]\\s*$", pText)) {
                    FormatTools.formatPageBreakBefore(paragraph, config);
                }
                break;
            }
        }
    }
}
