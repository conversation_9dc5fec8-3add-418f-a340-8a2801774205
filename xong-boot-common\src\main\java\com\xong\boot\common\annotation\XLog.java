package com.xong.boot.common.annotation;

import com.xong.boot.common.enums.ExecType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface XLog {
    /**
     * 操作说明
     */
    String title();

    /**
     * 执行类型
     */
    ExecType execType();

    /**
     * 保存请求参数
     */
    boolean saveParam() default false;

    /**
     * 保存结果详情
     */
    boolean saveResult() default false;
}
