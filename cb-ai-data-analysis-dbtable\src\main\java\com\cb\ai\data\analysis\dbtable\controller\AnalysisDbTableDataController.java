package com.cb.ai.data.analysis.dbtable.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.model.QueryItem;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableDataService;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTask;
import com.cb.ai.data.analysis.dbtable.model.req.ExcelHeadReq;
import com.cb.ai.data.analysis.dbtable.model.req.StartJobReq;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 数据Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table/data")
public class AnalysisDbTableDataController {
    private final AnalysisDbTableDataService dynamicDataService;

    public AnalysisDbTableDataController(AnalysisDbTableDataService dynamicDataService) {
        this.dynamicDataService = dynamicDataService;
    }

    /**
     * 获取Excel工作薄名称
     */
//    @PreAuthorize("@ss.hasPermi('datapool:data:list')")
    @PostMapping("/excel/sheet/names")
    public Result getExcelSheetNames(MultipartFile file) {
        return Result.successData(dynamicDataService.readExcelSheetNames(file));
    }

    /**
     * 获取Excel表头
     */
//    @PreAuthorize("@ss.hasPermi('datapool:data:list')")
    @PostMapping("/excel/head")
    public Result getExcelHead(ExcelHeadReq excelHeadReq) {
        return Result.successData(dynamicDataService.readExcelHead(excelHeadReq.getFile(), excelHeadReq.getSheetNo(), excelHeadReq.getStartHead(), excelHeadReq.getEndHead()));
    }

    /**
     * 获取ETL类型列表
     */
//    @PreAuthorize("@ss.hasPermi('datapool:data:list')")
    @GetMapping("/options/convert")
    public Result getConvertOptions() {
        return Result.successData(dynamicDataService.getConvertOptions());
    }

    /**
     * 启动导入任务
     */
    @PostMapping("/job/start")
    public Result startJob(StartJobReq startJobReq) throws IOException {
        // 开启数据导入任务
        AnalysisDbTableTask dynamicTableTask = dynamicDataService.startJob(startJobReq.getFile(),
                startJobReq.getTableId(),
                startJobReq.getSheetNo(),
                startJobReq.getStartHead(),
                startJobReq.getEndHead(),
                startJobReq.getStartRow(),
                startJobReq.getEndRow(),
                startJobReq.getExcelConvertArray());
        return Result.successData(dynamicTableTask);
    }

    /**
     * 获取数据列表
     */
//    @PreAuthorize("@ss.hasPermi('datapool:data:list')")
    @GetMapping("/page/{tableName:\\w{1,32}}")
    public Result pageTableData(@PathVariable("tableName") String tableName) {
        // 构建querys的条件
        QueryHelper.startAdvancedQuery();
        Page<Map<String, Object>> list = dynamicDataService.pageTableData(tableName);
        return Result.successData(list);
    }

    /**
     * 获取指定id的表数据
     * @param tableName
     * @param id
     * @return
     */
    @GetMapping("/{tableName:\\w{1,32}}")
    public Result getTableDataById(@PathVariable("tableName") String tableName, @RequestParam("id") String id){
        return Result.successData(dynamicDataService.getTableDataById(tableName, id));
    }

    /**
     * 编辑指定id的表数据
     * @param tableName
     * @param record
     * @return
     */
    @PostMapping("/{tableName:\\w{1,32}}")
    @XLog(title = "添加指定表的数据", execType = ExecType.INSERT)
    public Result addTableData(@PathVariable("tableName") String tableName, @RequestBody Map<String,Object> record){
        Assert.isTrue(!record.isEmpty(), "数据不能为空");
        return Result.successData(dynamicDataService.addTableData(tableName, record));
    }

    /**
     * 编辑指定id的表数据
     * @param tableName
     * @param record
     * @return
     */
    @PutMapping("/{tableName:\\w{1,32}}")
    @XLog(title = "修改指定表的指定数据", execType = ExecType.UPDATE)
    public Result editTableDataById(@PathVariable("tableName") String tableName, @RequestBody Map<String,Object> record){
        String id = MapUtil.getStr(record, "id");
        Assert.isTrue(StringUtils.isNotBlank(id), "id不能为空");
        return Result.successData(dynamicDataService.editTableDataById(tableName, id, record));
    }

    /**
     * 删除表数据
     */
//    @PreAuthorize("@ss.hasPermi('datapool:data:remove')")
    @DeleteMapping("/{tableName:\\w{1,32}}")
    @XLog(title = "删除指定表的指定数据", execType = ExecType.DELETE)
    public Result delTableData(@PathVariable(name = "tableName") String tableName, @RequestParam(name = "ids") List<String> ids) {
        dynamicDataService.deleteTableData(tableName, ids);
        return Result.success();
    }
}
