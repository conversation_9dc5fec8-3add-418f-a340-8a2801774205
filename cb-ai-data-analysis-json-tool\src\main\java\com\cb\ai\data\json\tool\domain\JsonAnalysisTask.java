package com.cb.ai.data.json.tool.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/***
 * json解析任务
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class JsonAnalysisTask extends SimpleBaseDomain {
    //主键id
    @TableId
    private String taskId;
    @TableField
    private String basePath;
    // 原始文件名
    @TableField(condition = SqlCondition.LIKE)
    private String originalFileName;
    // 上传文件名
    private String sourceFileName;
    //解压文件名
    private String extractFileName;
    // 0:未开始 1:进行中 2:已完成 3:失败
    private String status;
    // 错误信息
    private String errorMsg;
    // 开始时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;
    // 结束时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    public JsonAnalysisTask(String basePath, String originalFileName, String sourceFileName, String extractFileName, String status, LocalDateTime startTime) {
        this.basePath = basePath;
        this.originalFileName = originalFileName;
        this.sourceFileName = sourceFileName;
        this.extractFileName = extractFileName;
        this.status = status;
        this.startTime = startTime;
    }
}
