package com.cb.ai.data.analysis.graph.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.listener.PageReadListener;
import org.apache.commons.lang3.StringUtils;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @Description:
 * @Author: ARPHS
 * @Date: 2025-04-30 09:37
 * @Version: 1.0
 **/
public class ExcelReaderUtil {

    public static boolean containsHeaders(String filePath, String... headers) {
        return containsHeaders(filePath, 0, 1, headers);
    }

    public static boolean containsHeaders(String filePath, int headRowNum, String... headers) {
        return containsHeaders(filePath, 0, headRowNum, headers);
    }

    /**
     * @param filePath
     * @param headRowNum
     * @param headers
     * @return
     */
    public static boolean containsHeaders(String filePath, int sheetNum, int headRowNum, String... headers) {
        if (null == headers || headers.length == 0) {
            return true;
        }
        Map<Integer, String> integerStringMap = readHeader(filePath, sheetNum, headRowNum);
        return integerStringMap.values().containsAll(Arrays.asList(headers));
    }

    /**
     * 只读取指定表头行的表头信息，不读取其他数据
     *
     * @param filePath   Excel文件路径
     * @param headRowNum 表头行号（从1开始）
     * @return 表头Map，key为列索引，value为表头内容
     */
    public static Map<Integer, String> readHeader(String filePath, int sheetNum, int headRowNum) {
        try{
            FileInputStream is = new FileInputStream(filePath);
            return readHeader(is, sheetNum, headRowNum);
        } catch (FileNotFoundException e){
            throw new RuntimeException(e);
        }
    }

    public static Map<Integer, String> readHeader(InputStream is, int sheetNum, int headRowNum) {
        final Map<Integer, String>[] headerMapHolder = new Map[]{null};
        AnalysisEventListener<Map<Integer, String>> listener = new AnalysisEventListener<Map<Integer, String>>() {
            private boolean headerRead = false;

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // ... existing code ...
            }

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                if (!headerRead) {
                    headerMapHolder[0] = new HashMap<>(headMap);
                    headerRead = true;
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // ... existing code ...
            }
        };
        ExcelReaderSheetBuilder builder = EasyExcel.read(is, listener).headRowNumber(headRowNum).sheet(sheetNum);
        builder.doRead();
        return headerMapHolder[0];
    }


    /**
     * 读取指定sheet、指定表头行数，返回List<Map<String, Object>>
     *
     * @param inputStream excel文件流
     * @param sheetNo     sheet编号（从0开始）
     * @param headRowNum  表头行数
     * @return List<Map < String, Object>>
     */
    public static List<Map<String, String>> readToMap(InputStream inputStream, int sheetNo, int headRowNum) {
        List<Map<String, String>> result = new ArrayList<>();
        List<String> headerList = new ArrayList<>();

        EasyExcel.read(inputStream)
                .sheet(sheetNo)
                .headRowNumber(headRowNum)
                .registerReadListener(new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> data, AnalysisContext context) {
                        // 将列索引转为表头名
                        Map<String, String> row = new LinkedHashMap<>();
                        for (Map.Entry<Integer, String> entry : data.entrySet()) {
                            Integer columnIndex = entry.getKey();
                            String key = columnIndex < headerList.size() ? headerList.get(columnIndex) : "column_" + columnIndex;
                            row.put(key, entry.getValue());
                        }
                        result.add(row);
                    }

                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        headerList.clear();
                        for (int i = 0; i < headMap.size(); i++) {
                            String value = headMap.get(i);
                            // 表头列名去除空白字符
                            if (StringUtils.isNotBlank(value)) {
                                value = value.replaceAll("\\s", "");
                            }
                            headerList.add(value);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                })
                .doRead();
        return result;
    }

    /**
     * 读取指定sheet、指定表头行数，返回List<指定实体>
     *
     * @param inputStream excel文件流
     * @param sheetNo     sheet编号（从0开始）
     * @param headRowNum  表头行数
     * @param clazz       实体类Class
     * @param <T>         泛型
     * @return List<T>
     */
    public static <T> List<T> readToEntity(InputStream inputStream, int sheetNo, int headRowNum, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        EasyExcel.read(inputStream, clazz, new PageReadListener<T>(result::addAll))
                .sheet(sheetNo)
                .headRowNumber(headRowNum)
                .doRead();
        return result;
    }

    public static void main(String[] args) {
        String dir = "C:\\Users\\<USER>\\Desktop\\";
        String fileName = "数据标签.xlsx";
        try (FileInputStream fis = new FileInputStream(dir + fileName)) {
            List<Map<String, String>> mapList = readToMap(fis, 0, 1);
            for (Map<String, String> map : mapList) {
                System.out.println(map);
            }
//            List<RgCompany> rgCompanyVos = readToEntity(fis, 0, 1, RgCompany.class);
//            for (RgCompany rgCompanyVo : rgCompanyVos) {
//                System.out.println(rgCompanyVo.toMap());
//            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
