package com.cb.ai.data.analysis.ai.component.choreography.model;

import cn.hutool.core.date.DatePattern;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 15:37
 * @Copyright (c) 2025
 * @Description 节点上下文
 */
@Accessors(chain = true)
public class NodeContext {
    /**
     * 流程链中是否合并思考过程
     */
    @Setter
    @Getter
    private boolean mergeThinking;
    /**
     * 流程链中是否合并内容
     */
    @Setter
    @Getter
    private boolean mergeContent;
    /**
     * 节点开始时间
     */
    @Setter
    @Getter
    private LocalDateTime startTime;
    /**
     * 节点响应时间
     */
    @Setter
    @Getter
    private LocalDateTime responseTime;
    /**
     * 节点结束时间
     */
    @Setter
    @Getter
    private LocalDateTime endTime;
    /**
     * 节点最终收集的完整思考过程
     */
    private final StringBuilder fullThinking;
    /**
     * 节点最终收集的完整正文
     */
    private final StringBuilder fullContent;
    /**
     * 节点最终收集的完整额外数据
     */
    @Getter
    private final List<Object> fullDataList;

    public NodeContext() {
        this.mergeThinking = true;
        this.mergeContent = true;
        this.fullThinking = new StringBuilder();
        this.fullContent = new StringBuilder();
        this.fullDataList = new ArrayList<>();
    }

    //public String getStartTime() {
    //    return toTimeStr(startTime);
    //}

    //public String getResponseTime() {
    //    return toTimeStr(responseTime);
    //}

    //public String getEndTime() {
    //    return toTimeStr(endTime);
    //}

    public String getFullThinking() {
        return fullThinking.toString();
    }

    public String getFullContent() {
        return fullContent.toString();
    }

    public NodeContext collectThinking(String thinking) {
        this.fullThinking.append(OptionalUtil.ofBlankable(thinking).orElse(""));
        return this;
    }

    public NodeContext collectContent(String content) {
        this.fullContent.append(OptionalUtil.ofBlankable(content).orElse(""));
        return this;
    }

    public NodeContext collectData(Object data) {
        if (!ObjectUtils.isEmpty(data)) {
            if (data instanceof Collection<?>) {
                ((Collection<?>) data).forEach(item -> {
                    if (!ObjectUtils.isEmpty(item)) {
                        this.fullDataList.add(item);
                    }
                });
            } else if (data.getClass().isArray()) {
                Arrays.stream((Object[]) data)
                    .filter(item -> !ObjectUtils.isEmpty(item))
                    .forEach(this.fullDataList::add);
            } else {
                this.fullDataList.add(data);
            }
        }
        return this;
    }

    public void clear() {
        this.fullThinking.setLength(0);
        this.fullContent.setLength(0);
        this.fullDataList.clear();
    }

    private String toTimeStr(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.format(DatePattern.NORM_DATETIME_MS_FORMATTER);
    }
}
