package com.cb.ai.data.analysis.ai.component.choreography.model;

import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/8 15:50
 * @Copyright (c) 2025
 * @Description 引擎上下文
 */
public class FlowContext {
    /**
     * 存储工作流执行过程中需要传递的上下文数据
     */
    @Getter
    private final JsonMap data = new JsonMap();

    /**
     * 存储工作流执行过程中每个节点的完整收集数据
     */
    @Getter
    private final List<NodeContext> nodeList = new ArrayList<>();

    /**
     * 控制工作流是否继续执行
     */
    @Getter
    @Setter
    private volatile boolean continueExecution = true;
    /**
     * 内部节点下标
     */
    @Getter
    private int index;
    /**
     * 节点数量
     */
    private int nodesCount;

    public FlowContext(int nodesCount) {
        this.index = -1;
        this.nodesCount = nodesCount;
    }

    public void add(NodeContext data) {
        index++;
        this.nodeList.add(data);
    }

    public boolean containsKey(String key) {
        return this.data.containsKey(key);
    }

    public <T> T get(String key) {
        return (T) this.data.get(key);
    }

    public <T> void set(String key, T value) {
        this.data.put(key, value);
    }

    public void set(Map<String, Object> data) {
        this.data.putAll(data);
    }

    public void remove(String key) {
        this.data.remove(key);
    }

    public NodeContext getNodeData(int index) {
        Assert.isFalse(index < 0, "元素下标不得小于0！");
        Assert.isFalse(index > this.index, "元素下标大于当前节点下标，无法获取！");
        return this.nodeList.get(index);
    }

    public NodeContext getPreNodeData() {
        Assert.isTrue(index > -1 , "当前为第一个节点，无法获取前一个节点数据！");
        return this.nodeList.get(index);
    }

    public void setLength(int length) {
        this.nodesCount = length;
    }

    public boolean isStartNode() {
        return this.index == -1;
    }

    public boolean isEndNode() {
        return this.index == this.nodesCount - 1;
    }

    public void stop() {
        this.continueExecution = false;
    }

    public boolean isStop() {
        return !this.continueExecution;
    }

}
