package com.cb.ai.data.analysis.petition.domain.entity;

import com.xong.boot.common.domain.BaseDomain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RawFileEntity extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private String originFileName;

    private String fileDir;

    private String url;

    private String tag;

    private String contentType;

    private String fileType;

    private Integer analysisStatus;
}
