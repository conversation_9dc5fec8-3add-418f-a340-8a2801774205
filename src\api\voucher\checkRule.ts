import { http } from '@/utils/http'

const prefix = '/api/voucher/checkRule'

/**
 * 分页获取凭证检测规则列表
 * @param params 查询条件
 */
export function page(params?: Record<string, any>) {
  return http.get(`${prefix}/page`, { params })
}

/**
 * 新增凭证检测规则
 * @param data 规则数据
 */
export function add(data: Record<string, any>) {
  return http.post(prefix, data)
}

/**
 * 修改凭证检测规则
 * @param data 规则数据
 */
export function update(data: Record<string, any>) {
  return http.put(prefix, data)
}

/**
 * 删除凭证检测规则
 * @param ids 规则ID数组
 */
export function del(ids: string[]) {
  return http.delete(prefix, {
    params: { ids }
  })
}

/**
 * 禁用凭证检测规则
 * @param id 规则ID
 */
export function disableRule(id: string) {
  return http.put(`${prefix}/disableRule/${id}`)
}

/**
 * 获取凭证检测规则详情
 * @param id 规则ID
 */
export function detail(id: string) {
  console.log('api--->',id)
  return http.get(`${prefix}/${id}`)
}
