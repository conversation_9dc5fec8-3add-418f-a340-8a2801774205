package com.cb.ai.data.analysis.knowledge.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/***
 * <AUTHOR>
 * 向量查询返回实体
 */
@Data
public class KnowledgeVectorVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // id
    private String id;

    //原始文本 json格式
    private String text;

    // media
    private String media;

    //源数据
    private KnowledgeVectorMetadata metadata;

    //相似度
    private BigDecimal score;

    private String content;
}

