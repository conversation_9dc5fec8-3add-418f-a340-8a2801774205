package com.cb.ai.data.analysis.basdata.service.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasInvoiceInfo;

import java.util.List;

/**
 * 发票信息表(BasInvoiceInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14 21:59:36
 */
@DS("clickhouse")
public interface BasInvoiceInfoService extends IService<BasInvoiceInfo> {
    boolean save(BasInvoiceInfo info);

    boolean updateById(BasInvoiceInfo info);

    boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<BasInvoiceInfo> list);
}

