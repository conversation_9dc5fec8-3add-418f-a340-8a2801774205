package com.cb.ai.data.analysis.basdata.service.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasBankTransactionInfo;

import java.util.List;

/**
 * 银行交易信息表(BasBankTransactionInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14 21:57:31
 */
@DS("clickhouse")
public interface BasBankTransactionInfoService extends IService<BasBankTransactionInfo> {

    boolean save(BasBankTransactionInfo basBankTransactionInfo);

    boolean updateById(BasBankTransactionInfo basBankTransactionInfo);

    boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<BasBankTransactionInfo> list);
}

