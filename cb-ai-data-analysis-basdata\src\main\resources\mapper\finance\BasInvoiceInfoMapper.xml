<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.basdata.mapper.finance.BasInvoiceInfoMapper">

    <resultMap type="com.cb.ai.data.analysis.basdata.domain.entity.finance.BasInvoiceInfo" id="BasInvoiceInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="invoiceCodeNumber" column="invoice_code_number" jdbcType="VARCHAR"/>
        <result property="invoiceDate" column="invoice_date" jdbcType="TIMESTAMP"/>
        <result property="buyerName" column="buyer_name" jdbcType="VARCHAR"/>
        <result property="buyerTaxNumber" column="buyer_tax_number" jdbcType="VARCHAR"/>
        <result property="buyerAddress" column="buyer_address" jdbcType="VARCHAR"/>
        <result property="buyerPhone" column="buyer_phone" jdbcType="VARCHAR"/>
        <result property="buyerBankAccount" column="buyer_bank_account" jdbcType="VARCHAR"/>
        <result property="sellerName" column="seller_name" jdbcType="VARCHAR"/>
        <result property="sellerTaxNumber" column="seller_tax_number" jdbcType="VARCHAR"/>
        <result property="sellerAddress" column="seller_address" jdbcType="VARCHAR"/>
        <result property="sellerPhone" column="seller_phone" jdbcType="VARCHAR"/>
        <result property="sellerBankAccount" column="seller_bank_account" jdbcType="VARCHAR"/>
        <result property="priceTaxTotal" column="price_tax_total" jdbcType="NUMERIC"/>
        <result property="priceTotal" column="price_total" jdbcType="NUMERIC"/>
        <result property="taxTotal" column="tax_total" jdbcType="NUMERIC"/>
        <result property="drawer" column="drawer" jdbcType="VARCHAR"/>
        <result property="reviewer" column="reviewer" jdbcType="VARCHAR"/>
        <result property="payee" column="payee" jdbcType="VARCHAR"/>
        <result property="salesParty" column="sales_party" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    

</mapper>

