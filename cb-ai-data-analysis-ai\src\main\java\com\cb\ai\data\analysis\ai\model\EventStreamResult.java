package com.cb.ai.data.analysis.ai.model;

import com.cb.ai.data.analysis.ai.enums.EventStreamState;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Throwables;

import java.io.Serializable;

/**
 * EventStream 响应
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventStreamResult<T extends AiResultData> implements Serializable {
    /**
     * 会话Id
     */
    private String sessionId;
    /**
     * 节点名称
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 数据状态
     */
    private EventStreamState status;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 原始数据
     */
    private String rawData;
    /**
     * 异常信息
     */
    private Throwable error;

    public static <E extends AiResultData> EventStreamResult<E> newInstance(String sessionId) {
        EventStreamResult<E> result = new EventStreamResult<>();
        result.setSessionId(sessionId);
        return result;
    }

    public static <E extends AiResultData> EventStreamResult<E> newInstance(String sessionId, EventStreamState status) {
        EventStreamResult<E> result = new EventStreamResult<>();
        result.setSessionId(sessionId);
        result.setStatus(status);
        return result;
    }

    /**
     * 创建开始节点
     * @param sessionId 会话ID
     */
    public static <E extends AiResultData> EventStreamResult<E> generateStartResult(String sessionId) {
        return EventStreamResult.newInstance(sessionId, EventStreamState.start);
    }

    /**
     * 创建结束节点
     * @param sessionId 会话ID
     */
    public static <E extends AiResultData> EventStreamResult<E> generateEndResult(String sessionId) {
        return EventStreamResult.newInstance(sessionId, EventStreamState.end);
    }

    /**
     * 创建错误节点
     * @param sessionId 会话ID
     */
    public static <E extends AiResultData> EventStreamResult<E> generateErrorResult(String sessionId, Throwable throwable) {
        EventStreamResult<E> eventStreamResult = EventStreamResult.newInstance(sessionId, EventStreamState.error);
        eventStreamResult.setError(throwable);
        return eventStreamResult;
    }

    /**
     * 创建输出中错误节点
     * @param sessionId 会话ID
     */
    public static <E extends AiResultData> EventStreamResult<E> generateErrorStreamingResult(String sessionId, Throwable throwable) {
        EventStreamResult<E> eventStreamResult = EventStreamResult.newInstance(sessionId, EventStreamState.error_streaming);
        eventStreamResult.setError(throwable);
        return eventStreamResult;
    }

    public String getSessionId() {
        return sessionId;
    }

    public EventStreamResult<T> setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
    }

    public String getNodeId() {
        return nodeId;
    }

    public EventStreamResult<T> setNodeId(String nodeId) {
        this.nodeId = nodeId;
        return this;
    }

    public String getNodeName() {
        return nodeName;
    }

    public EventStreamResult<T> setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    public EventStreamState getStatus() {
        return status;
    }

    public EventStreamResult<T> setStatus(EventStreamState status) {
        this.status = status;
        return this;
    }

    public T getData() {
        return data;
    }

    public EventStreamResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public String getRawData() {
        return rawData;
    }

    public EventStreamResult<T> setRawData(String rawData) {
        this.rawData = rawData;
        return this;
    }

    public String getError() {
        return error != null ? Throwables.getRootCause(error).getMessage() : null;
    }

    public EventStreamResult<T> setError(Throwable error) {
        this.error = error;
        return this;
    }
}
