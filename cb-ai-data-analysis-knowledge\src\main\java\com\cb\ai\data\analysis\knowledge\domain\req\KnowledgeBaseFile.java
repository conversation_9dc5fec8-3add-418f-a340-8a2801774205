package com.cb.ai.data.analysis.knowledge.domain.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件请求实体
 */
@Data
public class KnowledgeBaseFile implements Serializable {

    private static final long serialVersionUID = 1L;
    /***
     * 文件列表
     */
    private List<KnowledgeFile> fileList;
    /***
     * 知识库ID
     */
    private String baseId;
    /***
     * 紧急程度 0紧急，1优先，2普通
     */
    private Integer topicType;
}
