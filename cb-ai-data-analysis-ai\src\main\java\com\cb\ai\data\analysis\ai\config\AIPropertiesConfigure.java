package com.cb.ai.data.analysis.ai.config;

import com.cb.ai.data.analysis.ai.common.properties.PrivateAIBaseProperties;
import com.cb.ai.data.analysis.ai.domain.common.DataConstants;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 14:00
 * @Copyright (c) 2025
 * @Description AI相关配置属性
 */
@Getter
@Setter
@EnableConfigurationProperties(AIPropertiesConfigure.class)
@ConfigurationProperties(prefix = DataConstants.CONFIG_PREFIX)
public class AIPropertiesConfigure {

    /** 自开发AI底座 **/
    @NestedConfigurationProperty
    private PrivateAIBaseProperties privateAiBase;


}
