package com.cb.ai.data.analysis.ai.service.impl;

import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.cb.ai.data.analysis.ai.exception.AiException;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.cb.ai.data.analysis.ai.utils.AiWebClient;
import com.cb.ai.data.analysis.ai.utils.PyAiWebClient;
import com.xong.boot.common.utils.BeanUtils;
import com.xong.boot.common.utils.RedisUtils;
import com.xong.boot.common.utils.spring.SpringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;

/**
 * AI配置 Service
 * <AUTHOR>
 */
@Service
public class AiConfigServiceImpl implements AiConfigService {
    private final RedisUtils redisUtils;
    private final PyAiWebClient pyAiWebClient;

    public AiConfigServiceImpl(RedisUtils redisUtils, PyAiWebClient pyAiWebClient) {
        this.redisUtils = redisUtils;
        this.pyAiWebClient = pyAiWebClient;
    }

    /**
     * 获取配置
     */
    @Override
    public AiConfig getAiConfig() {
        AiConfig config = redisUtils.get(AiConstants.getConfigKey(SecurityUtils.getUserId()), AiConfig.class);
        if (config == null) {
            try {
                config = SpringUtils.getBean(AiWebClient.class).getConfig();
            } catch (Exception e) {
                // 解决AI平台无法获取到配置时也能正常运行
                config = AiConfig.newInstance();
                e.printStackTrace();
            }
        }
        // TODO python服务需要调用接口去设置配置所以用这种写法
        try {
            AiConfig pyConfig = pyAiWebClient.getConfig(SecurityUtils.getUserId());
            config.setPySimilarityThreshold(pyConfig.getPySimilarityThreshold());
            config.setPyTopK(pyConfig.getPyTopK());
            config.setPyMcpSettings(pyConfig.getPyMcpSettings());
            config.setPyAutoAcceptedPlan(pyConfig.getPyAutoAcceptedPlan());
            config.setPyMaxPlanIterations(pyConfig.getPyMaxPlanIterations());
            config.setPyMaxStepNum(pyConfig.getPyMaxStepNum());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return config;
    }

    /**
     * 更新配置
     * @param config AI配置
     */
    @Override
    public boolean updateAiConfig(AiConfig config) {
        try {
            AiConfig _config = getAiConfig();
            BeanUtils.merger(_config, config);
            if (pyAiWebClient.updateConfig(SecurityUtils.getUserId(), _config)) {
                // TODO redis里面保存的python配置是不生效的，因为python端需要调用接口去配置
                redisUtils.setObj(AiConstants.getConfigKey(SecurityUtils.getUserId()), _config);
                return true;
            }
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 重置AI配置
     */
    @Override
    public void resetAiConfig() {
        if (pyAiWebClient.deleteConfig(SecurityUtils.getUserId())) {
            redisUtils.delete(AiConstants.getConfigKey(SecurityUtils.getUserId()));
        } else {
            throw new AiException("配置重置失败");
        }
    }
}
