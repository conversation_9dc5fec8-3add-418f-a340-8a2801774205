package com.cb.ai.data.analysis.petition.converter.pipe;

import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

/**
 * 管道接口
 * <AUTHOR>
 */
public abstract class IPipe {
    public boolean execute(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        if (check(document, paragraph, config, pos, documentInfo)) {
            format(document, paragraph, config, pos, documentInfo);
            return isBreak();
        }
        return false;
    }

    /**
     * 加工完成后就结束
     */
    public boolean isBreak() {
        return true;
    }

    abstract boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo);

    abstract void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo);
}
