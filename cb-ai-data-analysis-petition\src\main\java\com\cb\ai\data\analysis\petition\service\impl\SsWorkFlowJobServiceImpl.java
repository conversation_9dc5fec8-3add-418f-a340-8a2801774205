package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.cb.ai.data.analysis.petition.domain.entity.SsWorkFlowJobEntity;
import com.cb.ai.data.analysis.petition.enums.WorkFlowJobStatusEnum;
import com.cb.ai.data.analysis.petition.enums.WorkFlowTypeEnum;
import com.cb.ai.data.analysis.petition.mapper.SsWorkFlowJobMapper;
import com.cb.ai.data.analysis.petition.service.SsWorkFlowJobService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SsWorkFlowJobServiceImpl extends BaseServiceImpl<SsWorkFlowJobMapper, SsWorkFlowJobEntity> implements SsWorkFlowJobService {

    @Override
    public SsWorkFlowJobEntity publish(WorkFlowTypeEnum workFlowTypeEnum) {
        SsWorkFlowJobEntity ssWorkFlowJobEntity = new SsWorkFlowJobEntity();
        ssWorkFlowJobEntity.setId(IdUtil.getSnowflakeNextId());
        ssWorkFlowJobEntity.setJobType(workFlowTypeEnum.getJobType());
        ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.INITIALIZED.getStatus());
        ssWorkFlowJobEntity.setJobName(workFlowTypeEnum.getDescription());

        this.baseMapper.insert(ssWorkFlowJobEntity);
        return ssWorkFlowJobEntity;
    }

    @Override
    public void success(SsWorkFlowJobEntity ssWorkFlowJobEntity, String workFlowMessage) {
        ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_SUCCESS.getStatus());
        ssWorkFlowJobEntity.setWorkFlowResponse(workFlowMessage);
        ssWorkFlowJobEntity.setFinishTime(new Date());
        this.baseMapper.updateById(ssWorkFlowJobEntity);
    }

    @Override
    public void error(SsWorkFlowJobEntity ssWorkFlowJobEntity, String workFlowMessage, String errorMessage) {
        ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_FAILED.getStatus());
        ssWorkFlowJobEntity.setWorkFlowResponse(workFlowMessage);
        ssWorkFlowJobEntity.setErrorMessage(errorMessage);
        ssWorkFlowJobEntity.setFinishTime(new Date());
        this.baseMapper.updateById(ssWorkFlowJobEntity);
    }

}
