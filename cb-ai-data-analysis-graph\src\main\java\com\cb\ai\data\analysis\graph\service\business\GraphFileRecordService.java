package com.cb.ai.data.analysis.graph.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.domain.vo.ExtractLabelVo;
import com.cb.ai.data.analysis.graph.domain.vo.RgFileRecordVo;
import com.xong.boot.common.service.BaseService;

import java.io.IOException;
import java.util.List;

/**
 * 关系图谱-上传文件记录(RgFileRecord)表服务接口
 */
public interface GraphFileRecordService extends BaseService<GraphFileRecord> {

    //分页查询
    Page<GraphFileRecord> page(RgFileRecordVo.RgFileRecordPageQueryReq req);

    /**
     * 清空表
     */
    void truncate();

    /**
     * 将文件的信息同步到neo4j
     *
     * @param graphFileRecord
     */
    void fileSyncNeo4j(GraphFileRecord graphFileRecord, <PERSON><PERSON><PERSON> precise, String promote);

    List<ExtractLabelVo> extractGraphLabels();

    String promote(List<ExtractLabelVo> extractLabelVos) throws IOException;
}
