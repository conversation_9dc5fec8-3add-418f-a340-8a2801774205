<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.ai.mapper.AiChatHistoryMapper">
    <resultMap type="com.cb.ai.data.analysis.ai.domain.entity.AiChatHistory" id="AiChatHistoryMap">
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="title" column="content" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

