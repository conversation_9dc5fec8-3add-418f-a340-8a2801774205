package com.cb.ai.data.analysis.petition.converter.pipe;

import com.cb.ai.data.analysis.petition.converter.CharLenConstant;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocCompleteDate;
import com.cb.ai.data.analysis.petition.converter.model.DocOrgName;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.Comparator;
import java.util.List;

/**
 * 发文机关
 * <AUTHOR>
 */
public class OrgPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        DocOrgName docOrgName = documentInfo.getOrgName();
        if (docOrgName != null) {
            return docOrgName.getStart() <= pos && docOrgName.getEnd() >= pos;
        }
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
        DocCompleteDate docCompleteDate = documentInfo.getCompleteDate();
        DocOrgName docOrgName = documentInfo.getOrgName();
        int completeDateLength = 0;
        if (docCompleteDate != null) {
            completeDateLength = docCompleteDate.getText().trim().length(); // 时间长度
        }

        List<String> texts = docOrgName.getTexts();
        String currOrgName = paragraph.getText().trim();
        Long dateLen = CharLenConstant.calcCharactersLen(docCompleteDate.getText());
        Long orgNameLen = CharLenConstant.calcCharactersLen(currOrgName);
        int minDateLen = 9; // 日期最小长度
        int diff = completeDateLength - minDateLen;
        if (texts.size() > 1) { // 多机关行文
            // 获取
            Integer maxLength = texts.stream().map(String::trim).map(String::length).max((Comparator.comparingInt(o -> o))).orElseGet(() -> 0);
            if (completeDateLength > maxLength) {
                // 若成文时间长度大于发文机关署名长度，成文日期右边空二字编排，对齐发文机关署名第三个字
                long c = dateLen - orgNameLen + 4 * CharLenConstant.CHINESE_CHAR;
                if (completeDateLength > minDateLen) {
                    c = c - diff * (CharLenConstant.SPACE - 28); // 该行代码没有任何算法，单纯的格式调整
                }
                FormatTools.formatParagraphRightInd(paragraph, config, c);
            } else {  // 若成文时间长度小于发文机关署名长度,发文机关署名右空2字,成文日期按发文机关署名居中编排
                Integer characterLen = currOrgName.length();
                if (maxLength == characterLen) {
                    Long c = 2 * CharLenConstant.CHINESE_CHAR;
                    FormatTools.formatParagraphRightInd(paragraph, config, c);  // 再计算右边距离*/
                    // 重新排版成文时间
                    XWPFParagraph docCompleteDateParagraph = docCompleteDate.getSection();
                    if (docCompleteDateParagraph != null) {
                        long l = (orgNameLen - dateLen) / 2 + c;
                        FormatTools.formatParagraphRightInd(docCompleteDateParagraph, config, l);
                    }
                } else {
                    Long maxOrgNameLen = (maxLength + 4) * CharLenConstant.CHINESE_CHAR;
                    long c = (maxOrgNameLen - orgNameLen) / 2;
                    FormatTools.formatParagraphRightInd(paragraph, config, c);  // 再计算右边距离*/
                }
            }
        } else {  // 单一机关行文
            Integer characterLen = currOrgName.length();
            if (completeDateLength > characterLen) {
                // 若成文时间长度大于发文机关署名长度，成文日期右边空二字编排，对齐发文机关署名第三个字
                long c = dateLen - orgNameLen + 4 * CharLenConstant.CHINESE_CHAR;
                if (completeDateLength > minDateLen) {
                    c = c - diff * (CharLenConstant.SPACE - 28); // 该行代码没有任何算法，单纯的格式调整
                }
                FormatTools.formatParagraphRightInd(paragraph, config, c);
            } else {
                Long c = 2 * CharLenConstant.CHINESE_CHAR;
                if (completeDateLength > minDateLen) {
                    c = c - diff * (CharLenConstant.SPACE - 28); // 该行代码没有任何算法，单纯的格式调整
                }
                FormatTools.formatParagraphRightInd(paragraph, config, c);
                // 重新排版成文时间
                XWPFParagraph docCompleteDateParagraph = docCompleteDate.getSection();
                if (docCompleteDateParagraph != null) {
                    long l = orgNameLen - dateLen;
                    FormatTools.formatParagraphRightInd(docCompleteDateParagraph, config, l);
                }
            }
        }
    }
}
