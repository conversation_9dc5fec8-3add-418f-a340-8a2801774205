//package com.cb.ai.data.analysis.graph.domain.vo.Excel;
//
//import com.alibaba.excel.annotation.ExcelProperty;
//
//import java.lang.reflect.Field;
//import java.lang.reflect.Modifier;
//import java.util.LinkedHashMap;
//import java.util.Map;
//
/// **
// * @Description:
// * @Author: ARPHS
// * @Date: 2025-04-29 18:02
// * @Version: 1.0
// **/
//public interface ExcelVo {
//
//    default Map<String, Object> toMap(){
//        Map<String,Object> rst = new LinkedHashMap<>();
//        Field[] declaredFields = this.getClass().getDeclaredFields();
//        for (Field field : declaredFields) {
//            if(!field.isAccessible()){
//                field.setAccessible(true);
//            }
//            boolean staticFlag = Modifier.isStatic(field.getModifiers());
//            if(staticFlag){
//                continue;
//            }
//            try {
//                String fieldName = field.getName();
//                Object fieldValue = field.get(this);
//                ExcelProperty excelPropertyAnnotation = field.getAnnotation(ExcelProperty.class);
//                if(excelPropertyAnnotation != null){
//                    String[] value = excelPropertyAnnotation.value();
//                    fieldName = value[0];
//                }
//                rst.put(fieldName,fieldValue);
//
//            } catch (IllegalAccessException e) {
//                e.printStackTrace();
//            }
//        }
//        return rst;
//    }
//
//}
