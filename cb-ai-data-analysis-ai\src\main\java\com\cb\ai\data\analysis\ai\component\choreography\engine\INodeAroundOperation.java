package com.cb.ai.data.analysis.ai.component.choreography.engine;


import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:25
 * @Copyright (c) 2025
 * @Description 节点环绕操作
 */
public interface INodeAroundOperation<R> extends INode {
    /**
     * 节点数据请求前的额外操作
     */
    default void executeBefore() {}

    /**
     * 节点数据响应中的额外操作
     */
    default void executing(R rawData) {}

    /**
     * 节点数据请求后的额外操作
     */
    default void executeAfter(NodeContext nodeContext) {}

}
