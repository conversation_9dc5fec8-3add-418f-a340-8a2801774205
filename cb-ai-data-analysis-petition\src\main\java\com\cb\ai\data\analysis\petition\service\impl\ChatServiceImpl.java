package com.cb.ai.data.analysis.petition.service.impl;



import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson2.JSON;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.cb.ai.data.analysis.petition.service.ChatService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.RedisUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import java.util.HashMap;
import java.util.Map;

@Service
public class ChatServiceImpl implements ChatService {

   /* @Resource
    private WebClient.Builder webClientBuilder;*/

    /*@Resource
    private ChatProperties chatProperties;*/


    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/chat'}")
    private String chatUrl;

    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/rag/ocr'}")
    private String ocrUrl;

    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization;

    @Value("${cb.ai.private-ai-base.llm.role}")
    private String llmRole;

    @Autowired
    private AiConfigService aiConfigService;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public String chat(String prompt, String data) {
        Map<String, Object> requestBody = getLlmRequestBody(prompt, data);

        Result result=this.llmSynChat(requestBody);
        if(result.getCode()== ResultCode.SUCCESS.getCode()){
            return result.getData().toString();
        }else {
            return result.getMessage();
        }
    }

    @Override
    public String ocrAnalysis(String base64Img) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("base64",base64Img);
        Result result=this.sendOcrReq(requestBody);
        if(result.getCode()== ResultCode.SUCCESS.getCode()){
            return result.getData().toString();
        }else {
            return result.getMessage();
        }
    }

    private Map<String, Object> getLlmRequestBody(String prompt, String data) {
        String systemPromote = prompt + "\n" + data;
        // 构造 messages 列表
        AiConfig aiConfig=this.getAiConfig();
        Map<String, Object> message = new HashMap<>();
        message.put("role", llmRole);
        message.put("systemPromote", systemPromote);
        message.put("top_p",aiConfig.getTopP());
        message.put("frequency_penalty",aiConfig.getFrequencyPenalty());
        message.put("presence_penalty",aiConfig.getPresencePenalty());
        message.put("temperature", aiConfig.getTemperature());
        message.put("max_tokens",aiConfig.getMaxTokens()*0.8);
        return message;
    }

    private AiConfig getAiConfig(){
        try{
            String AI_KEY="AI_KEY_"+SecurityUtils.getUserId();
            if(ObjectUtils.isEmpty(redisUtils.get(AI_KEY))){
                AiConfig aiConfig=aiConfigService.getAiConfig();
                redisUtils.setObj (AI_KEY,aiConfig);
                return aiConfig;
            }else{
                return redisUtils.get(AI_KEY,AiConfig.class);
            }
        }catch (Exception e){
            e.printStackTrace();
            return aiConfigService.getAiConfig();
        }
    }


    public Result sendOcrReq(Map<String, Object> ocrData) {
        try{
            if(ObjectUtils.isEmpty(ocrData)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(ocrUrl,JSON.toJSONString(ocrData),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return Result.successData(httpResponse.body());
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(500,e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail(500,"调用ai接口失败！");
        }
    }

    public Result llmSynChat(Map<String, Object> llmSynChatReq) {
        try{
            if(ObjectUtils.isEmpty(llmSynChatReq)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(chatUrl+"/sync",JSON.toJSONString(llmSynChatReq),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return Result.successData(httpResponse.body());
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(500,e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail(500,"调用ai接口失败！");
        }
    }
}
