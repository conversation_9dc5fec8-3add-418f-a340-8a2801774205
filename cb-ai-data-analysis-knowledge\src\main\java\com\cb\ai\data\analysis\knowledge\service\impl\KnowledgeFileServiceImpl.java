package com.cb.ai.data.analysis.knowledge.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeFileEntity;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBase;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFile;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeFileVo;
import com.cb.ai.data.analysis.knowledge.mapper.KnowledgeFileMapper;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeFileService;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/***
 * <AUTHOR>
 * 知识库文件管理
 */
@Service
public class KnowledgeFileServiceImpl implements KnowledgeFileService {



    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/knowledge/base/file'}")
    private String knowledgeFileUrl;

    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization;


    @Autowired
    private KnowledgeFileMapper knowledgeFileMapper;


    @Override
    public String saveupload(KnowledgeBaseFile KnowledgeBaseFile) throws Exception{
        try{
            if(ObjectUtils.isEmpty(KnowledgeBaseFile)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse=HttpUtils.sendPost(knowledgeFileUrl+"/saveupload",JSON.toJSONString(KnowledgeBaseFile),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            insertKnowledgeFile(KnowledgeBaseFile,httpResponse.body());
            return ResultCode.SUCCESS.getMessage();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public Page<KnowledgeFileVo> pageKnowledgeFile(KnowledgeFile knowledgeFile) throws Exception {
        try{
            if(ObjectUtils.isEmpty(knowledgeFile)){
                throw new XServiceException("请求对象为空！");
            }
            if(ObjectUtils.isEmpty(knowledgeFile.getPageCurrent()) && knowledgeFile.getPageCurrent()<=0){
                knowledgeFile.setPage(0);
            }else{
                knowledgeFile.setPage(knowledgeFile.getPageCurrent());
            }
            if(ObjectUtils.isEmpty(knowledgeFile.getPageSize()) &&  knowledgeFile.getPageSize()<=0){
                knowledgeFile.setSize(10);
            }else{
                knowledgeFile.setSize(knowledgeFile.getPageSize());
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse=HttpUtils.sendPost(knowledgeFileUrl+"/page",JSON.toJSONString(knowledgeFile),headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            Page<KnowledgeFileVo> page=JSON.parseObject(httpResponse.body(),new TypeReference<Page<KnowledgeFileVo>>() {});
            if(ObjectUtils.isEmpty(page)){
                return null;
            }
            this.setFilePage(page);
            return page;
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String delKnowledgeFile(String knowledgeFileId) throws Exception {
        try{
            if(ObjectUtils.isEmpty(knowledgeFileId)){
                throw new XServiceException("请选择要删除的数据项！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);

            Map<String,Object> params=new HashMap<>();
            params.put("id",knowledgeFileId);
            HttpResponse httpResponse=HttpUtils.sendDelete(knowledgeFileUrl,params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            deleteFile(knowledgeFileId);
            return httpResponse.body();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String reUploadAnalysis(String knowledgeFileId) throws Exception {
        try{
            if(ObjectUtils.isEmpty(knowledgeFileId)){
                throw new XServiceException("请选择要解析的数据项！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);

            Map<String,Object> params=new HashMap<>();
            params.put("id",knowledgeFileId);
            HttpResponse httpResponse=HttpUtils.sendPost(knowledgeFileUrl+"/reUploadAnalysis",params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            this.editUpdateTime(knowledgeFileId);
            return httpResponse.body();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public KnowledgeFileVo getKnowledgeFileById(String id){
        try{
            if(ObjectUtils.isEmpty(id)){
                throw new XServiceException("请选择要删除的数据项！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);

            Map<String,Object> params=new HashMap<>();
            params.put("id",id);
            HttpResponse httpResponse=HttpUtils.sendGet(knowledgeFileUrl,params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            KnowledgeFileVo knowledgeFile=JSON.parseObject(httpResponse.body(),KnowledgeFileVo.class);
            if(ObjectUtils.isEmpty(knowledgeFile)){
                throw new XServiceException("获取知识库文件详情失败！");
            }
            return knowledgeFile;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String fileUrgent(String fileId) throws Exception {
        try{
            if(StringUtils.isBlank(fileId)){
                throw new XServiceException("文件ID不能为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            Map<String,Object> params=new HashMap<>();
            params.put("fileId",fileId);
            HttpResponse httpResponse=HttpUtils.sendPost(knowledgeFileUrl+"/urgent",params,headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            this.editUpdateTime(fileId);
            return httpResponse.body();
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String getFileId(String aiFileId) {
        try{
            LambdaQueryWrapper<KnowledgeFileEntity> queryWrapper =new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeFileEntity::getAiFileId,aiFileId);
            KnowledgeFileEntity file=knowledgeFileMapper.selectOne(queryWrapper);
            if(!ObjectUtils.isEmpty(file)){
                return file.getFileId();
            }
            return null;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 同步新增业务侧知识库文件，方便后续删除的时候同步删除文件管理
     * @param KnowledgeBaseFile
     * @param respBody
     */
    void insertKnowledgeFile(KnowledgeBaseFile KnowledgeBaseFile,String respBody){
        try{
            if(StringUtils.isNotBlank(respBody)){
                JSONObject jsonObject=JSONObject.parseObject(respBody);
                if(jsonObject.getInteger("code").equals(ResultCode.SUCCESS.getCode())){
                    List<KnowledgeFileEntity> knowledgeFileEntitiesList=jsonObject.getList("data",KnowledgeFileEntity.class);
                    for(KnowledgeFileEntity fileEntity:knowledgeFileEntitiesList){
                        fileEntity.setBaseId(KnowledgeBaseFile.getBaseId());
                        fileEntity.setAiFileId(fileEntity.getId());
                        fileEntity.setId(IdUtil.getSnowflakeNextIdStr());
                        fileEntity.setCreateTime(LocalDateTime.now());
                        fileEntity.setCreateBy(SecurityUtils.getUsername());
                        fileEntity.setUpdateTime(fileEntity.getCreateTime());
                        fileEntity.setUpdateBy(fileEntity.getCreateBy());
                        for(KnowledgeFile file:KnowledgeBaseFile.getFileList()){
                            if(StringUtils.equals(fileEntity.getFileName(),file.getFileName()) && StringUtils.equals(fileEntity.getFileUrl(),file.getFileUrl())){
                                fileEntity.setFileId(file.getFileId());
                            }
                        }
                    }
                    knowledgeFileMapper.insert(knowledgeFileEntitiesList);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /***
     * 设置分页返回数据，加上文件管理的文件ID
     * @param page
     */
    void setFilePage(Page<KnowledgeFileVo> page){
        try{
            if(!CollectionUtils.isEmpty(page.getRecords())){
                for(KnowledgeFileVo knowledgeFileVo:page.getRecords()){
                    LambdaQueryWrapper<KnowledgeFileEntity> queryWrapper =new LambdaQueryWrapper<>();
                    queryWrapper.eq(KnowledgeFileEntity::getAiFileId,knowledgeFileVo.getId());
                    KnowledgeFileEntity file=knowledgeFileMapper.selectOne(queryWrapper);
                    if(!ObjectUtils.isEmpty(file)){
                        knowledgeFileVo.setFileId(file.getFileId());
                        knowledgeFileVo.setUpdateTime(file.getUpdateTime());
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /***
     * 删除业务侧文件表信息
     * @param knowledgeFileId
     */
    void deleteFile(String knowledgeFileId){
        try{
            LambdaQueryWrapper<KnowledgeFileEntity> queryWrapper =new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeFileEntity::getAiFileId,knowledgeFileId);
            knowledgeFileMapper.delete(queryWrapper);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /***
     * 修改业务侧更新时间
     * @param knowledgeFileId
     */
    void editUpdateTime(String knowledgeFileId){
        try{
            LambdaUpdateWrapper<KnowledgeFileEntity> editWrapper = new LambdaUpdateWrapper<>();
            editWrapper.eq(KnowledgeFileEntity::getAiFileId,knowledgeFileId);
            editWrapper.set(KnowledgeFileEntity::getUpdateTime, LocalDateTime.now());
            editWrapper.set(KnowledgeFileEntity::getUpdateBy,SecurityUtils.getUsername());
            knowledgeFileMapper.update(editWrapper);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
