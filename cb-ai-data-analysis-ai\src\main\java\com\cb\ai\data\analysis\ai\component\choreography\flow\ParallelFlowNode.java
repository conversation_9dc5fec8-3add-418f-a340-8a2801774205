package com.cb.ai.data.analysis.ai.component.choreography.flow;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/1 10:34
 * @Copyright (c) 2025
 * @Description 并行流程节点处理类
 */
@Getter
public class ParallelFlowNode<E> implements IFlowNode {
    /**
     * 节点和上下文列表
     */
    private final List<IFlowProcessNode<E, ?>> nodes;

    public ParallelFlowNode() {
        this.nodes = new ArrayList<>();
    }

    public void addNode(IFlowProcessNode<E, ?> node) {
        this.nodes.add(node);
    }

}
