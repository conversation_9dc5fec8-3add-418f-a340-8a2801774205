package com.cb.ai.data.analysis.petition.domain.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SsPetitionVo implements Serializable {
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    /**
     * -1:解析失败 0:解析中 1:解析完成
     */
    private Integer status;

    /**
     * 上传批次号
     */
    private Long uploadBatchNo;

    /**
     * 文件类型
     * @see com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum
     */
    private Integer fileType;


    private String excelHeaderNames;

    private static final long serialVersionUID = 1L;
}
