package com.cb.ai.data.analysis.ai.domain.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/30 11:11
 * @Copyright (c) 2025
 * @Description 市委办政策合规审查助手参数
 */
@Data
@Accessors(chain = true)
public class PcraGovernmentParam {
    /** 审查机关名称（昆明市人民政府办公室） **/
    @NotBlank(message = "审查机关名称不能为空")
    @Size(max = 48, message = "审查机关名称不能超过48个字符")
    @JsonAlias("review_authority")
    private String reviewAuthority;

    /** 审查文号 **/
    @NotBlank(message = "审查文号不能为空")
    @Size(max = 48, message = "审查文号不能超过48个字符")
    @JsonAlias("review_file_number")
    private String reviewFileNumber;

    /** 联系人 **/
    @NotBlank(message = "联系人不能为空")
    @Size(max = 48, message = "联系人不能超过48个字符")
    @JsonAlias("contact_person")
    private String contactPerson;

    /** 联系电话 **/
    @NotBlank(message = "联系电话不能为空")
    @Size(max = 48, message = "联系电话不能超过48个字符")
    @JsonAlias("contact_phone")
    private String contactPhone;

    /** 审查日期 **/
    @NotBlank(message = "审查日期不能为空")
    @Size(max = 48, message = "审查日期不能超过48个字符")
    @JsonAlias("review_date")
    private String reviewDate;

    /** 同审单位 **/
    @NotBlank(message = "同审单位不能为空")
    @Size(max = 48, message = "同审单位不能超过48个字符")
    @JsonAlias("review_unit")
    private String reviewUnit;

    /** 抄送单位 **/
    @NotBlank(message = "抄送单位不能为空")
    @Size(max = 48, message = "抄送单位不能超过48个字符")
    @JsonAlias("send_unit")
    private String sendUnit;

    /** 文件名称 **/
    @NotBlank(message = "文件名称不能为空")
    @Size(max = 48, message = "文件名称不能超过48个字符")
    private String name;
}
