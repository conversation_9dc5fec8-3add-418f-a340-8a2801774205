package com.cb.ai.data.analysis.dbtable.service.impl;

import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableIndex;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableIndexMapper;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableIndexService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 动态数据表索引 ServiceImpl
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableIndexServiceImpl extends BaseServiceImpl<AnalysisDbTableIndexMapper, AnalysisDbTableIndex> implements AnalysisDbTableIndexService {
}
