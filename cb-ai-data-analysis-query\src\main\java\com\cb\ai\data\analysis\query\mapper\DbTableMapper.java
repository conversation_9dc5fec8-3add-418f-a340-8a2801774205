package com.cb.ai.data.analysis.query.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cb.ai.data.analysis.query.domain.vo.ColumnVo;
import com.cb.ai.data.analysis.query.domain.vo.DbTableVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DbTableMapper {

    DbTableVo selectMysqlDbTableVo( String tableName);

    List<ColumnVo> selectMysqlTableColumnList(String tableName);


    @DS("clickhouse")
    DbTableVo selectClickHouseDbTableVo( String tableName);
    @DS("clickhouse")
    List<ColumnVo> selectClickHouseTableColumnList(String tableName);


//    @DS("slave")
//    @Deprecated
//    DbTableVo selectSlaveDbTableVo( String tableName);
//    @DS("slave")
//    @Deprecated
//    List<ColumnVo> selectSlaveTableColumnList(String tableName);
}
