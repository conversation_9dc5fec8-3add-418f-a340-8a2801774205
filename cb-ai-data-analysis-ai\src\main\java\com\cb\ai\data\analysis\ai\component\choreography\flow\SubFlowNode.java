package com.cb.ai.data.analysis.ai.component.choreography.flow;

import cn.hutool.core.lang.Assert;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import lombok.Data;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:24
 * @Copyright (c) 2025
 * @Description 子流程节点
 * 子流程节点是流程节点的一部分，用于实现复杂的业务逻辑
 * 子流程节点可以包含多个流程节点，用于实现复杂的业务逻辑
 * 子流程节点可以包含多个子流程节点，用于实现复杂的业务逻辑
 */
@Data
public abstract class SubFlowNode<E> implements IFlowNode {
    /**
     * 子流程节点的执行链
     */
    private FlowChain subChain;

    /**
     * 子流程属性上下文
     */
    private final FlowAttribute subFlowAttribute;

    /**
     * 主流程属性上下文
     */
    private FlowAttribute mainFlowAttribute;

    public SubFlowNode() {
        this.subFlowAttribute = new FlowAttribute();
    }

    public void setSubChain(FlowChain subChain) {
        this.subChain = subChain;
        this.subFlowAttribute.getFlowContext().setLength(subChain.getNodes().size());
    }

    /**
     * 子流程的执行方法
     * @param flowAttribute 流程属性上下文
     * @param requestContext 节点上下文
     * @return 子流程节点的执行结果
     */
    public Flux<ResultData<?>> execute(FlowAttribute flowAttribute, E requestContext) {
        Assert.notNull(flowAttribute, "主流程的流程属性不能为空");
        Assert.notNull(requestContext, "子流程的初始请求上下文不能为空");
        this.mainFlowAttribute = flowAttribute;
        return execute(requestContext);
    }

    /**
     * 子流程的具体实现的执行方法
     * @param requestContext 节点上下文
     * @return 子流程节点的执行结果
     */
    abstract Flux<ResultData<?>> execute(E requestContext);
}
