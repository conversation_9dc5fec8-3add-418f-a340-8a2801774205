package com.cb.ai.data.analysis.ai.component.extensions;

import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIChat;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateDeepThinkChat;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI基础问答扩展
 */
@ExtensionProvider(desc = "私有化AI基础问答扩展", businessScenes = {
    @Route(tag = "基础问答", business = BusinessTypeEnum.PRIVATE_BUSINESS),
    @Route(tag = "深度思考", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivateAiChatExtension implements PrivateAIExtension<CommonAIRequestContext> {

    @Override
    public Flux<ResultData<PrivateAIBackData>> invoke(CommonAIRequestContext requestContext) {
        if (requestContext.isDeepThink()) {
            return new PrivateDeepThinkChat().processData(requestContext);
        }
        return new PrivateAIChat().maxTokens(data -> (int) (data * 0.08)).processData(requestContext);
    }
}
