package com.cb.ai.data.analysis.voucher.utils.ocr;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;

import java.nio.charset.Charset;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TableMarkdownConverter {

    /**
     * 将字符串中的<table>标签内容转换为Markdown表格
     * @param input 输入字符串
     * @return 转换后的字符串
     */
    public static String convertTableToMarkdown(String input) {
        if (StrUtil.isBlank(input)) {
            return input;
        }

        // 正则匹配<table>标签及其内容
        Pattern pattern = Pattern.compile("<table>(.*?)</table>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String tableContent = matcher.group(1);
            String markdownTable = convertSingleTable(tableContent);
            matcher.appendReplacement(result, markdownTable);
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 转换单个表格内容为Markdown格式
     * @param tableContent 表格HTML内容
     * @return Markdown格式的表格
     */
    private static String convertSingleTable(String tableContent) {
        // 解析表格行
        Pattern rowPattern = Pattern.compile("<tr>(.*?)</tr>", Pattern.DOTALL);
        Matcher rowMatcher = rowPattern.matcher(tableContent);

        StringBuilder markdownTable = new StringBuilder();
        int rowCounter = 0;

        while (rowMatcher.find()) {
            String rowContent = rowMatcher.group(1);
            String rowMarkdown = convertTableRow(rowContent, rowCounter == 0);

            if (rowCounter == 0) {
                // 第一行后添加分隔行
                String headerSeparator = rowMarkdown.replaceAll("[^|]", "-");
                markdownTable.append(rowMarkdown).append("\n").append(headerSeparator).append("\n");
            } else {
                markdownTable.append(rowMarkdown).append("\n");
            }

            rowCounter++;
        }

        return markdownTable.toString();
    }

    /**
     * 转换表格行
     * @param rowContent 行内容
     * @param isHeader 是否为标题行
     * @return Markdown格式的行
     */
    private static String convertTableRow(String rowContent, boolean isHeader) {
        // 解析单元格
        Pattern cellPattern = Pattern.compile("<t[hd]>(.*?)</t[hd]>", Pattern.DOTALL);
        Matcher cellMatcher = cellPattern.matcher(rowContent);

        StringBuilder rowMarkdown = new StringBuilder("|");

        while (cellMatcher.find()) {
            String cellContent = cellMatcher.group(1);
            // 清理HTML标签并处理空白
            String cleanContent = HtmlUtil.cleanHtmlTag(cellContent).trim();
            // 处理合并单元格的情况
            cleanContent = cleanContent.replaceAll("\\s+", " ");
            rowMarkdown.append(cleanContent).append("|");
        }

        return rowMarkdown.toString();
    }

//    public static void main(String[] args) {
//        String filePath = "C:\\Users\\<USER>\\Desktop\\test.txt";
//        String input =  FileUtil.readString(filePath, Charset.forName("UTF-8"));
//        String result = convertTableToMarkdown(input);
//        result = result.replaceAll("\r\n\r\n","\r\n").replaceAll("\n\n","\n");
//        System.out.println(result);
//    }

}
