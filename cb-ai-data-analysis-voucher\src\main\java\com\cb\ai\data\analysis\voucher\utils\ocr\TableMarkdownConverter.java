package com.cb.ai.data.analysis.voucher.utils.ocr;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TableMarkdownConverter {

    /**
     * 将字符串中的<table>标签内容转换为Markdown表格
     * @param input 输入字符串
     * @return 转换后的字符串
     */
    public static String convertTableToMarkdown(String input) {
        if (StrUtil.isBlank(input)) {
            return input;
        }

        // 正则匹配<table>标签及其内容，支持嵌套属性和换行
        Pattern pattern = Pattern.compile("<table[^>]*>(.*?)</table>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String tableContent = matcher.group(1);
            String markdownTable = convertSingleTable(tableContent);
            // 转义特殊字符，防止正则替换时出错
            markdownTable = Matcher.quoteReplacement(markdownTable);
            matcher.appendReplacement(result, "\n\n" + markdownTable + "\n\n");
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 转换表格行
     * @param rowContent 行内容
     * @param maxColumns 最大列数，用于补齐空列
     * @return Markdown格式的行
     */
    private static String convertTableRow(String rowContent, int maxColumns) {
        List<String> cells = extractCells(rowContent);

        StringBuilder rowMarkdown = new StringBuilder("|");

        // 处理实际的单元格
        for (int i = 0; i < cells.size(); i++) {
            String cellContent = cells.get(i);
            String cleanContent = cleanCellContent(cellContent);
            rowMarkdown.append(" ").append(cleanContent).append(" |");
        }

        // 补齐空列以保持表格对齐
        for (int i = cells.size(); i < maxColumns; i++) {
            rowMarkdown.append("  |");
        }

        return rowMarkdown.toString();
    }

    /**
     * 提取行中的所有单元格
     * @param rowContent 行内容
     * @return 单元格列表
     */
    private static List<String> extractCells(String rowContent) {
        List<String> cells = new ArrayList<>();

        // 匹配th和td标签，支持属性
        Pattern cellPattern = Pattern.compile("<(th|td)([^>]*)>(.*?)</\\1>",
                Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher cellMatcher = cellPattern.matcher(rowContent);

        while (cellMatcher.find()) {
            String attributes = cellMatcher.group(2);
            String cellContent = cellMatcher.group(3);

            // 处理colspan属性
            int colspan = extractColspan(attributes);
            String cleanContent = cellContent;

            // 添加主单元格
            cells.add(cleanContent);

            // 如果有colspan，添加空单元格
            for (int i = 1; i < colspan; i++) {
                cells.add("");
            }
        }

        return cells;
    }

    /**
     * 提取colspan属性值
     * @param attributes 属性字符串
     * @return colspan值，默认为1
     */
    private static int extractColspan(String attributes) {
        if (StrUtil.isBlank(attributes)) {
            return 1;
        }

        Pattern colspanPattern = Pattern.compile("colspan\\s*=\\s*[\"']?(\\d+)[\"']?",
                Pattern.CASE_INSENSITIVE);
        Matcher matcher = colspanPattern.matcher(attributes);

        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return 1;
            }
        }

        return 1;
    }

    /**
     * 清理单元格内容
     * @param cellContent 原始单元格内容
     * @return 清理后的内容
     */
    private static String cleanCellContent(String cellContent) {
        if (StrUtil.isBlank(cellContent)) {
            return "";
        }

        // 清理HTML标签
        String cleanContent = HtmlUtil.cleanHtmlTag(cellContent);

        // 处理HTML实体
        cleanContent = cleanContent.replace("&nbsp;", " ")
                                 .replace("&lt;", "<")
                                 .replace("&gt;", ">")
                                 .replace("&amp;", "&")
                                 .replace("&quot;", "\"");

        // 先处理换行符，避免被正则表达式误处理
        cleanContent = cleanContent.replace("\r\n", " ")
                                 .replace("\n", " ")
                                 .replace("\r", " ");

        // 规范化空白字符
        cleanContent = cleanContent.replaceAll("\\s+", " ").trim();

        // 转义Markdown特殊字符
        cleanContent = cleanContent.replace("|", "\\|");

        // 如果内容为空，返回空格以保持表格结构
        return StrUtil.isBlank(cleanContent) ? "" : cleanContent;
    }

    /**
     * 转换单个表格内容为Markdown格式
     * @param tableContent 表格HTML内容
     * @return Markdown格式的表格
     */
    private static String convertSingleTable(String tableContent) {
        if (StrUtil.isBlank(tableContent)) {
            return "";
        }

        // 提取所有行
        List<String> rows = extractTableRows(tableContent);
        if (rows.isEmpty()) {
            return "";
        }

        // 计算最大列数
        int maxColumns = calculateMaxColumns(rows);
        if (maxColumns == 0) {
            return "";
        }

        StringBuilder markdownTable = new StringBuilder();
        boolean hasHeader = false;

        for (int i = 0; i < rows.size(); i++) {
            String rowContent = rows.get(i);
            String rowMarkdown = convertTableRow(rowContent, maxColumns);

            // 跳过空行
            if (isEmptyRow(rowMarkdown)) {
                continue;
            }

            markdownTable.append(rowMarkdown).append("\n");

            // 在第一行后添加分隔行（Markdown表格头部分隔符）
            if (!hasHeader) {
                String separator = createHeaderSeparator(maxColumns);
                markdownTable.append(separator).append("\n");
                hasHeader = true;
            }
        }

        return markdownTable.toString().trim();
    }

    /**
     * 提取表格中的所有行
     * @param tableContent 表格内容
     * @return 行列表
     */
    private static List<String> extractTableRows(String tableContent) {
        List<String> rows = new ArrayList<>();

        // 先处理tbody, thead, tfoot等标签
        String processedContent = tableContent.replaceAll("</?t(head|body|foot)[^>]*>", "");

        // 匹配tr标签
        Pattern rowPattern = Pattern.compile("<tr[^>]*>(.*?)</tr>",
                Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher rowMatcher = rowPattern.matcher(processedContent);

        while (rowMatcher.find()) {
            String rowContent = rowMatcher.group(1);
            if (StrUtil.isNotBlank(rowContent)) {
                rows.add(rowContent);
            }
        }

        return rows;
    }

    /**
     * 计算表格的最大列数
     * @param rows 所有行
     * @return 最大列数
     */
    private static int calculateMaxColumns(List<String> rows) {
        int maxColumns = 0;

        for (String rowContent : rows) {
            List<String> cells = extractCells(rowContent);
            maxColumns = Math.max(maxColumns, cells.size());
        }

        return maxColumns;
    }

    /**
     * 创建Markdown表格头部分隔符
     * @param columnCount 列数
     * @return 分隔符行
     */
    private static String createHeaderSeparator(int columnCount) {
        StringBuilder separator = new StringBuilder("|");
        for (int i = 0; i < columnCount; i++) {
            separator.append(" --- |");
        }
        return separator.toString();
    }

    /**
     * 检查是否为空行
     * @param rowMarkdown Markdown行
     * @return 是否为空行
     */
    private static boolean isEmptyRow(String rowMarkdown) {
        if (StrUtil.isBlank(rowMarkdown)) {
            return true;
        }

        // 移除管道符和空格，检查是否还有内容
        String content = rowMarkdown.replace("|", "").replace(" ", "");
        return StrUtil.isBlank(content);
    }

    /**
     * 后处理转换结果，清理多余的空行和格式问题
     * @param result 转换结果
     * @return 清理后的结果
     */
    public static String postProcessResult(String result) {
        if (StrUtil.isBlank(result)) {
            return result;
        }

        // 清理多余的空行 - 使用replace处理固定字符串，replaceAll处理正则
        result = result.replaceAll("\n{3,}", "\n\n");
        result = result.replaceAll("\r\n(\r\n){2,}", "\r\n\r\n");

        // 确保表格前后有适当的空行
        result = result.replaceAll("([^\n])\n\\|", "$1\n\n|");
        result = result.replaceAll("\\|([^\n\r]*?)\n([^\n\r\\|])", "|$1\n\n$2");

        return result.trim();
    }

    /**
     * 转换表格并进行后处理
     * @param input 输入字符串
     * @return 转换并清理后的字符串
     */
    public static String convertAndClean(String input) {
        String result = convertTableToMarkdown(input);
        return postProcessResult(result);
    }

    public static void main(String[] args) {
//        // 测试用例1：简单表格
//        String simpleTable = "<table><tr><th>姓名</th><th>年龄</th></tr><tr><td>张三</td><td>25</td></tr></table>";
//        System.out.println("简单表格测试：");
//        System.out.println(convertTableToMarkdown(simpleTable));
//        System.out.println();
//
//        // 测试用例2：包含合并单元格的表格
//        String complexTable = "<table><tr><th colspan='2'>个人信息</th></tr><tr><td>姓名</td><td>张三</td></tr><tr><td>年龄</td><td>25</td></tr></table>";
//        System.out.println("复杂表格测试：");
//        System.out.println(convertTableToMarkdown(complexTable));
//        System.out.println();
//
//        // 测试用例3：多个表格
//        String multipleTables = "这是第一个表格：<table><tr><th>A</th><th>B</th></tr><tr><td>1</td><td>2</td></tr></table>这是第二个表格：<table><tr><th>X</th><th>Y</th><th>Z</th></tr><tr><td>a</td><td>b</td><td>c</td></tr></table>";
//        System.out.println("多表格测试：");
//        System.out.println(convertTableToMarkdown(multipleTables));

        // 如果有测试文件，可以取消注释下面的代码
        String filePath = "C:\\Users\\<USER>\\Desktop\\test.txt";
        if (FileUtil.exist(filePath)) {
            String input = FileUtil.readString(filePath, Charset.forName("UTF-8"));
            String result = convertTableToMarkdown(input);
            result = result.replace("\r\n\r\n", "\r\n").replace("\n\n", "\n");
            System.out.println("文件测试结果：");
            System.out.println(result);
        }
    }

}
