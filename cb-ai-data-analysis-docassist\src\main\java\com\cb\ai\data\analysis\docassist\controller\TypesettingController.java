package com.cb.ai.data.analysis.docassist.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.cb.ai.data.analysis.docassist.converter.DocFormatConverter;
import com.cb.ai.data.analysis.docassist.req.FormatReq;
import com.cb.ai.data.analysis.docassist.utils.Constants;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.utils.StringUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * 排版
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/docassist/format")
public class TypesettingController {

    @GetMapping({"/{docId:-?[\\w.]{1,32}}"})
    public void getFormatDoc(HttpServletResponse response, @PathVariable String docId, String filename) throws IOException {
        if (StringUtils.isBlank(filename)) {
            return;
        }
        OutputStream os = null;
        try (InputStream is = FileUtil.getInputStream(Constants.getTempPath(docId))) {
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.setContentType("application/octet-stream");
            os = response.getOutputStream();
            IoUtil.copy(is, os);
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                os.close();
            }
        }
    }

    @PostMapping
    public Result formatDocument(FormatReq formatReq) throws IOException {
        if (formatReq == null) {
            return Result.fail("参数不存在");
        }
        if (StringUtils.isBlank(formatReq.getDocId())) {
            return Result.fail("文件ID不存在");
        }
        MultipartFile file = formatReq.getFile();
        if (file == null) {
            return Result.fail("校对文件不存在");
        }
        OutputStream os = null;
        try (InputStream is = file.getInputStream()) {
            XWPFDocument xwpfDocument = new XWPFDocument(is);
            DocFormatConverter.formatDocument(xwpfDocument, formatReq.getDocConfig());
            os = FileUtil.getOutputStream(Constants.getTempPath(formatReq.getDocId()));
            xwpfDocument.write(os);
            os.flush();
            return Result.success();
        } catch (IOException e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        } finally {
            if (os != null) {
                os.close();
            }
        }
    }

//    @PostMapping
//    public Result<?> formatDocument(@PathParam("docId") String docId, @PathParam("docConfig") String docConfig, MultipartFile file) throws IOException {
//        if (StringUtils.isBlank(docId)) {
//            return Result.fail("文件ID不存在");
//        }
//        if (file == null) {
//            return Result.fail("校对文件不存在");
//        }
//        DocConfigReq config = JSON.parseObject(docConfig, DocConfigReq.class);
//        OutputStream os = null;
//        try (InputStream is = file.getInputStream()) {
//            XWPFDocument xwpfDocument = new XWPFDocument(is);
//            DocFormatConverter.formatDocument(xwpfDocument, config.getDocConfig());
//            os = FileUtil.getOutputStream(Constants.getTempPath(docId));
//            xwpfDocument.write(os);
//            os.flush();
//            return Result.success();
//        } catch (IOException e) {
//            e.printStackTrace();
//            return Result.fail(e.getMessage());
//        } finally {
//            if (os != null) {
//                os.close();
//            }
//        }
//    }
}
