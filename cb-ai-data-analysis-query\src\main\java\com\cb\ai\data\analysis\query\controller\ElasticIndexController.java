package com.cb.ai.data.analysis.query.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.DynamicFieldMapping;
import com.cb.ai.data.analysis.query.domain.vo.IndexInfo;
import com.cb.ai.data.analysis.query.service.ElasticService;
import com.cb.ai.data.analysis.query.service.IndexService;
import com.cb.ai.data.analysis.query.utils.ElasticIndexUtils;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(Constants.API_ROOT_PATH + "/elastic/index")
@Slf4j
public class ElasticIndexController {
    private final IndexService indexService;

    public ElasticIndexController(IndexService indexService) {
        this.indexService = indexService;
    }

    /**
     * 获取索引列表
     *
     * @return
     */
    @GetMapping("/page")
    public Result page(IndexInfo info) {
        Page<IndexInfo> page = indexService.indexManageList(info);
        return Result.successData(page);
    }

    /**
     * 获取所有字段类型
     *
     * @return
     */
    @GetMapping("/fieldTypeList")
    public Result fieldTypeList() {
        String[] defaultFieldType = ElasticIndexUtils.DEFAULT_FIELD_TYPE;
        return Result.successData(defaultFieldType);
    }

    /**
     * 获取分司类型列表
     *
     * @return
     */
    @GetMapping("/participleList")
    public Result participleList() {
        String[] participle = ElasticIndexUtils.PARTICIPLE;
        log.info("分词列表：{}", participle);
        return Result.successData(participle);
    }


    @PostMapping("/createIndex")
    public Result createIndex(String indexName, List<DynamicFieldMapping> mappings) {
        indexService.createIndex(indexName, mappings);
        return Result.success();
    }

    /**
     * 添加字段映射
     *
     * @return
     */
    @PostMapping("/addFieldMapping")
    public Result addFieldMapping(String indexName, List<DynamicFieldMapping> mappings) {
        indexService.addFieldMapping(indexName, mappings);
        return Result.success();
    }


    /**
     * 迁移数据
     *
     * @return
     */
    @PostMapping("/migrationData")
    public Result migrationData(String sourceIndex, String destIndex) {
        Long count = indexService.migrationData(sourceIndex, destIndex);
        return Result.successData(count);
    }

    /**
     * 删除索引
     *
     * @param indexName
     * @return
     */

    @DeleteMapping("/deleteIndex")
    public Result deleteIndex(String indexName) {
        boolean delete = indexService.deleteIndex(indexName);
        return Result.successData(delete);
    }

}
