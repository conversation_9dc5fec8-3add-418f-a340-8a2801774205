# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8084

spring:
  codec:
    max-in-memory-size: 10MB
  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:mysql://*************:3306/db_ai_data_analysis?characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true
          username: root
          password: 1234
          driver-class-name: com.mysql.cj.jdbc.Driver
        clickhouse:
          url: jdbc:clickhouse://*************:8123/db_data_analysis?socket_timeout=600000
          username: default
          password: 123456
          driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
          druid:
            filters: stat #去除wall不然无法创建表

  data:
    redis:
      host: *************
      port: 16379
      password: 123456
    neo4j:
      database: neo4j

  # 文件上传大小设置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 55MB

  # elasticsearch 配置
  elasticsearch:
    uris:
      - http://*************:9210  # 测试环境 可新增多个节点
    username:
    password:
    connectionTimeout: 30
    readTimeout: 30

  # neo4j 配置
  neo4j:
    uri: neo4j://*************:7687
    authentication:
      username: neo4j
      password: 1q2w3e4r

cb:
  dbtable:
    db-name: db_data_analysis #动态表数据库名
  ai:
    # Authorization: 1
    # url:
    # knowledgeBase: http://***********:8686/cb-ai/knowledge/base
    # knowledgeVectorSearch: http://***********:8686/cb-ai/rag/embedding/search
    # chat: http://***********:8686/cb-ai/chat
    # knowledgeFile: http://***********:8686/cb-ai/knowledge/base/file
    # textEmbeddingVector: http://***********:8686/cb-ai/rag/embedding/textVector

    # 下面的配置是cb-ai-data-analysis-ai模块的配置
    private-ai-base:
      base-url: http://*************:8686/cb-ai
      llm:
        model: qwen3
        role: user
        temperature: 0.7
      header-map:
        Authorization: 1
      chat:
        stream-url: /chat/basic
        block-url: /chat/sync
      knowledge: /chat/knowledge
      ocr: /rag/ocr
      file-store: /knowledge/base/file/saveupload
      finance: http://*************:8055/api/v1/chat/stream
      file-analysis: /tool/file/analysis/upload
      audio-analysis:
        base-url: http://*************:9991/api/v1
        invoke-url: /upload
        health-url: /health
        task-status-url: /status/{task_id}
        task-result-url: /task/{task_id}
        task-list-url: /tasks
        delete-task-url: /delete/{task_id}

logging:
  level:
    #    org: debug
    #    org.springframework: debug
    #    com.baomidou.dynamic: debug
    com.xong.boot: debug
    #    org.activiti.engine.impl.persistence.entity: debug
    org.springframework.data.neo4j: debug


minio:
  endpoint: http://*************:9002
  bucket: cb-ai-knowledge
  accessKey: minioadmin
  secretKey: minioadmin
  expiry: 1 # redis 文件信息失效时间 天
  breakpointTime: 1 # 分片地址失效时间 天


cbxxjs:
  bisheng:
    baseUrl: http://10.10.10.92:3001
  workflow:
    extract-relation: 472d303cfd8342fc87fd5b9bbbc21376
    material-analysis-id: 871eb0307a0f4b5fb9685c0bfbdc536a
    max-content-length: 10
    analyze-content-length: 10
    baseUrl: http://10.10.10.92:3001
    generate-report-id: 70b3523f633148e9a35276809c5a3cef
    analyze-data: bc9b800ec22944aa9fa460249f9635d0
    conversation-brief: 86d3f4a63eca41e790664dc679790f16
    knowledge-flow-id: 8d59faef8cb4473aaecae8e85250b309
    common-report-gen-id: fabe24a7f3504bbe850a1b2a83c74019
    call-back-url: http://***********:19005/knowledge/file/upload/callBack
    title-generate-id: 5264003b4e064b29ba60d022e9f338be
    content-generate-id: 6fee1d274a374b36bbafe3af6a3ecb38
    content-regenerate-id: 8760e6ed36cf40d09782d2b54351bc5f
    talk_plan_review_id: 42c65af9c86a41dea07eb889d20b4e8f
  nl2cypher:
    baseUrl: http://*************:8066/api/v1/nl2cypher/stream


upload:
  xfFolderId: 1946100942269550592  # 信访库文件上传目录
  problemFolderId: 1946101241042407424 #问答批处理文件上传目录