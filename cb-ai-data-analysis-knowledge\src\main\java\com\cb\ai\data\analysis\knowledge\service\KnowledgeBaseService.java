package com.cb.ai.data.analysis.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeBaseEntity;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBase;
import com.xong.boot.common.service.BaseService;

import java.util.List;

/***
 * <AUTHOR>
 * 知识库管理
 */
public interface KnowledgeBaseService  extends BaseService<KnowledgeBaseEntity> {


    /***
     * 新增知识库
     * @param knowledgeBase
     * @return
     */
    String addKnowledgeBase(KnowledgeBase knowledgeBase)throws Exception;

    /***
     * 编辑知识库
     * @param knowledgeBase
     * @return
     */
    String editKnowledgeBase(KnowledgeBase knowledgeBase)throws Exception;

    /***
     * 根据ID删除知识库
     * @param knowledgeBaseId
     * @return
     */
    String delKnowledgeBase(String knowledgeBaseId)throws Exception;

    /***
     * 知识库分页查询
     * @param knowledgeBase
     * @return
     */
    Page<KnowledgeBaseEntity> pageKnowledgeBase(KnowledgeBase knowledgeBase)throws Exception;

    /***
     * 根据ID获取知识库
     * @param knowledgeBaseId
     * @return
     */
    KnowledgeBaseEntity getKnowledgeBase(String knowledgeBaseId)throws Exception;


    /***
     * 知识库列表查询
     * @param parentId
     * @return
     */
    List<KnowledgeBaseEntity> listKnowledgeBase(String parentId,String searchKey)throws Exception;

    /***
     * 知识库整库重解
     * @param knowledgeBaseId
     */
    String reparse(String knowledgeBaseId)throws Exception;

}
