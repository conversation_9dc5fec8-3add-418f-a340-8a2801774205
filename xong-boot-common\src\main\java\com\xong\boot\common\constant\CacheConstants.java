package com.xong.boot.common.constant;

/**
 * 缓存常量
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 验证码过时KEY
     */
    public static final String PREFIX = "xong";
    /**
     * 全局配置
     */
    public static final String GLOBAL_CONFIG_KEY = PREFIX + ":global:config";
    /**
     * 登录账号锁KEY
     */
    public static final String LOGIN_ACCOUNT_LOCK_KEY = PREFIX + ":security:login:account:lock";
    /**
     * token缓存KEY前缀
     */
    public static final String TOKEN_PREFIX = PREFIX + ":security:tokens:";
    /**
     * 字典缓存
     */
    public static final String SYSTEM_DICT_KEY = PREFIX + ":system:dict";
    /**
     * 全部用户缓存
     */
    public static final String USER_CACHE = PREFIX + ":userCache";

    /**
     *  数据库信息缓存key
     */
    public static final String DB_INFO_CACHE_KEY =PREFIX + ":dbTable";

    /**
     * 验证码过时KEY
     * @param sessionId 会话ID
     */
    public static String getCaptchaExpireKey(String sessionId) {
        return PREFIX + ":security:login:captcha:" + sessionId;
    }

    /**
     * 获取token缓存KEY
     * @param userId    用户ID
     * @param sessionId 会话ID
     */
    public static String getTokenAuthKey(String userId, String sessionId) {
        return TOKEN_PREFIX + userId + ":" + sessionId;
    }

    /**
     * 获取已被禁用的TOKEN缓存KEY
     * @param sessionId 会话ID
     */
    public static String getTokenDisabledKey(String sessionId) {
        return TOKEN_PREFIX + "disabled:" + sessionId;
    }

    /**
     * 获取密码登录记住我缓存KEY
     * @param sessionId 会话ID
     */
    public static String getLoginRememberMe(String sessionId) {
        return TOKEN_PREFIX + "remember:password:" + sessionId;
    }
}
