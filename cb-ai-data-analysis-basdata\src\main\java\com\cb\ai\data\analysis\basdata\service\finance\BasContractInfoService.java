package com.cb.ai.data.analysis.basdata.service.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasContractInfo;

import java.util.List;

/**
 * 合同信息表(BasContractInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14 21:55:36
 */
@DS("clickhouse")
public interface BasContractInfoService extends IService<BasContractInfo> {
    boolean save(BasContractInfo info);

    boolean updateById(BasContractInfo info);

    boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<BasContractInfo> list);
}

