package com.cb.ai.data.analysis.knowledge.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFile;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeFileVo;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeFileService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件管理
 */
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/file")
public class KnowledgeFileController {

    @Autowired
    private KnowledgeFileService knowledgeFileService;

    /***
     * 知识库文件分页查询
     * @param
     * @return
     */
    @GetMapping("/page")
    public Result pageKnowledgeFile(KnowledgeFile knowledgeFile) {
        try{
            Page<KnowledgeFileVo> pageKnowledgeFile=knowledgeFileService.pageKnowledgeFile(knowledgeFile);
            return Result.successData(pageKnowledgeFile);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库文件查询失败！");
        }
    }


    /***
     * 知识库文件列表查询
     * @param
     * @return
     */
    @PostMapping("/list")
    public Result getFileList(@RequestBody KnowledgeFile knowledgeFile) {
        try{
            return Result.successData(knowledgeFileService.getFileList(knowledgeFile.getBaseId(),knowledgeFile.getFileIds()));
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库文件列表查询失败！");
        }
    }

    /***
     * 删除知识库文件
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @Deprecated
    public Result delete(@PathVariable String id){
        try{
            String respMsg=knowledgeFileService.delKnowledgeFile(id);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库删除失败！");
        }
    }

    /*@PostMapping
    public Result saveupload(@RequestBody KnowledgeBaseFile KnowledgeBaseFile){
        try{
            String respMsg=knowledgeFileService.saveupload(KnowledgeBaseFile);
            return Result.successData(respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("知识库删除失败！");
        }
    }*/


    /***
     * 重新解析
     * @param id
     * @return
     */
    @GetMapping("/analysis/{id}")
    public Result reUploadAnalysis(@PathVariable String id){
        try{
            String respMsg=knowledgeFileService.reUploadAnalysis(id);
            return Result.success("已重新发起解析任务！",respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("解析失败！");
        }
    }


    /***
     * 插队解析
     * @param fileId
     * @return
     */
    @GetMapping("/urgent/{fileId}")
    public Result fileUrgent(@PathVariable String fileId){
        try{
            String respMsg=knowledgeFileService.fileUrgent(fileId);
            return Result.success("提交解析任务成功！",respMsg);
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("提交解析任务失败！");
        }
    }
}
