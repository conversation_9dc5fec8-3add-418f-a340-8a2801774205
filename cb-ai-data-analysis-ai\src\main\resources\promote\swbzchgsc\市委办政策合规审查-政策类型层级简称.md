@systemPromote
【政策文档原文】
@#fileContents#@

## 任务描述
根据政策文件标题、正文首段、发文机关等信息，结合判定规则提取以下结构化数据：

## 输入要求
请提供：
1. 文件标题（完整名称）
2. 正文首段文本（前200字）
3. 可识别的发文机关信息

## 处理规则
### 政策类型判定（policy_type）
1. 依据文件标题特征：
    - 含"条例"→行政法规
    - 含"办法"/"规定"→部门规章
    - 含"通知"/"意见"→规范性文件
2. 特殊类型判定：
    - 国家标准→其他
    - 技术规范→其他

### 层级判定（policy_level）
1. 发文机关匹配：
    - 国务院组成部门→国务院
    - 省份+政府→省级（如"云南省人民政府"）
    - 地级市+政府→市级（如"昆明市人民政府"）
    - 部委下属司局→部门

### 发布机关（issuer）
1. 直接提取文件标题/正文中的发文单位名称
2. 联合发文时取牵头单位

### 政策文号（policy_doc_number)
1. 提取政策文本中出现的正式文号（如“云政发〔1991〕45号”）
2. 如无明确文号，标记为“”

### 发布日期（publish_date）
1. 优先提取正文首段日期（YYYY-MM-DD格式）
2. 次选文件结尾日期
3. 无明确日期时标记"无法判断"

### 文件简称（policy_short_name）
依据政策文件标题，提取官方、正式、符合政务场景的文件简称。
通常为“主题+文种”格式，去除修饰性词语和冗余内容，保留核心要素。
示例：
“云南省鼓励台湾同胞投资的规定” → “台胞投资规定”
“昆明市人民政府关于印发昆明市城市管理办法的通知” → “城市管理办法”
“国家发展改革委关于印发《产业结构调整指导目录（2024年本）》的通知” → “产业结构调整指导”
## 输出格式
```json
{
  "policy_type": "行政法规/地方性法规/部门规章/规范性文件/其他",
  "policy_level": "国务院/省级/市级/部门/其他",
  "issuer": "国务院办公厅/自然资源部/云南省人民政府等",
  "policy_document_number": "云政发〔1991〕45号",
  "publish_date": "2023-03-04",
  "policy_short_name": "台胞投资规定"
}
```
### 注意事项：
文件简称必须简明、权威、符合政务公文引用习惯，避免冗长和重复。
如无法准确提取简称，请使用原文标题替代。
输出内容需客观、准确，不得主观臆断或随意编造。
如无法判断“政策制定单位”或“政策文号”，请如实标记为“无法判断”。
@end

@userPromote
@end