package com.xong.boot.common.api;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * API请求返回构建器
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result {
    /**
     * 状态编码
     */
    private int code;
    /**
     * 状态信息
     */
    private String message;
    /**
     * 数据
     */
    private Object data;

    private Result() {
        setResult(ResultCode.FAIL);
    }

    public static Result success() {
        return new Result().setResult(ResultCode.SUCCESS);
    }

    public static Result successData(Object data) {
        return new Result()
                .setResult(ResultCode.SUCCESS)
                .setData(data);
    }
    public static Result success(String message) {
        return new Result()
                .setResult(ResultCode.SUCCESS)
                .setMessage(message);
    }

    public static Result success(String message, Object data) {
        return new Result()
                .setResult(ResultCode.SUCCESS)
                .setMessage(message)
                .setData(data);
    }

    public static Result fail() {
        return new Result();
    }

    public static Result fail(String message) {
        return new Result().setMessage(message);
    }

    public static Result fail(ResultCode resultCode) {
        return new Result().setResult(resultCode);
    }

    public static Result fail(int code, String message) {
        return new Result().setCode(code).setMessage(message);
    }

    private Result setResult(ResultCode resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
        return this;
    }

    public int getCode() {
        return code;
    }

    public Result setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public Result setMessage(String message) {
        this.message = message;
        return this;
    }

    public Object getData() {
        return data;
    }

    public Result setData(Object data) {
        this.data = data;
        return this;
    }
}
