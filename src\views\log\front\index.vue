<template>
  <x-page-wrapper class="log-front">
    <x-table ref="tableRef" :columns="columns" :load-data="loadData" row-key="id">
      <template #toolbar="{ selectedRowKeys }">
        <a-button
          v-if="selectedRowKeys && selectedRowKeys.length > 0"
          danger
          type="primary"
          @click="onClickDeletes(selectedRowKeys)"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </a-button>
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'title'">
          {{ text }}
        </template>
        <template v-else-if="column.dataIndex === 'msg'">
          {{ text }}
        </template>
        <template v-else-if="column.dataIndex === 'stack'">
          {{ text }}
        </template>
        <template v-else-if="column.dataIndex = 'data'">
          {{ record.data }}
        </template>
        <template v-else-if="column.dataIndex === 'dateTime'">
          {{ text }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a @click="onClickDetail(record)"> 详情 </a>
          <a @click="onClickDetail(record)"> 下载 </a>
          <template v-if="$auth.hasPermission('log:front:delete')">
            <a-divider type="vertical" />
            <a-popconfirm title="是否删除日志？" @confirm="onClickDelete(record)">
              <a> 删除 </a>
            </a-popconfirm>
          </template>
        </template>
      </template>
    </x-table>
    <DetailComp :id="detailAttrs.key" v-model:visible="detailAttrs.visible" />
  </x-page-wrapper>
</template>

<script lang="ts" name="LogFront" setup>
import { type ComponentCustomProperties, computed, getCurrentInstance, reactive, ref } from 'vue'
import { DeleteOutlined } from '@ant-design/icons-vue'
import { login } from '@/api/log'
import DetailComp from './Detail.vue'
import errorLog, { type ErrorItem } from '@/utils/errorLog'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties

const tableRef = ref()
const detailAttrs = reactive({
  key: undefined,
  visible: false
})
const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'title',
      title: '标题',
      width: 140,
      align: 'center'
    },
    {
      dataIndex: 'msg',
      title: '错误消息',
      width: 140,
      align: 'center'
    },
    {
      dataIndex: 'stack',
      title: '调用栈',
      width: 120
    },
    {
      dataIndex: 'data',
      title: '原始数据',
      width: 160
    },
    {
      dataIndex: 'dateTime',
      title: '创建时间',
      width: 120,
      align: 'center'
    }
  ] as TableColumn[]
  if (_this.$auth.hasPermission('log:front:*')) {
    columnItems.push({
      key: 'actions',
      title: '操作',
      width: 100,
      align: 'center'
    })
  }
  return columnItems
})

function loadData(): ErrorItem[] {
  const records = errorLog.get()
  console.log(records)
  return Promise.resolve({
    code: 200,
    message: '成功',
    data: {
      current: 1,
      pages: 999999,
      size: 999999,
      total: records.length,
      records
    }
  })
}

function onClickDetail(record: { id: any }) {
  detailAttrs.key = record.id
  detailAttrs.visible = true
}

async function onClickDelete(record: { id: string }) {
  try {
    _this.$message.loading({
      key: 'delete',
      content: '删除中...',
      duration: 0
    })
    const { message } = await login.del(record.id)
    _this.$message.success({ key: 'delete', content: message })
    tableRef.value.refresh()
  } catch (error: any) {
    _this.$message.error({ key: 'delete', content: error.message })
  }
}

async function onClickDeletes(ids: string[]) {
  _this.$confirm({
    title: '是否批量删除登录日志？',
    okType: 'danger',
    onOk: async (destroy: () => void) => {
      try {
        const { message } = await login.del(ids)
        _this.$message.success(message)
        tableRef.value.refresh()
      } catch (error: any) {
        _this.$message.error(error.message)
      } finally {
        destroy()
      }
    }
  })
}
</script>
