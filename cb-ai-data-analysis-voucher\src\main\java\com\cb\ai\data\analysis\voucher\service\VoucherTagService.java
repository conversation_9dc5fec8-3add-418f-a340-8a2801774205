package com.cb.ai.data.analysis.voucher.service;

import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTag;
import com.xong.boot.common.service.BaseService;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface VoucherTagService extends BaseService<VoucherTag> {

    /**
     * 根据凭证ID获取凭证标签
     * @param voucherIds
     * @return
     */
    Map<String, List<String>> getTagsByVoucherIds(List<String> voucherIds);


    /**
     * 批量添加标签
     * @param infoIds
     * @param tags
     * @return
     */
    int batchAddTag(Set<String> infoIds, String[]  tags);

    /**
     * 批量删除标签
     * @param infoIds
     * @param tags
     * @return
     */
    int batchDelTag(Set<String> infoIds, String[]  tags);

    /**
     * 获取所有标签
     * @return
     */
    List<Map<String,String>> getAllTags();

}
