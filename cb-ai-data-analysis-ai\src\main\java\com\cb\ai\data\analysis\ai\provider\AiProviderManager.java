package com.cb.ai.data.analysis.ai.provider;

import com.cb.ai.data.analysis.ai.model.AIContext;
import com.cb.ai.data.analysis.ai.model.AiResultData;
import com.cb.ai.data.analysis.ai.model.EventStreamResult;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * Provider 管理器
 * <AUTHOR>
 */
@Component
public class AiProviderManager {
    private final List<AbstractAiProvider<AiResultData>> providers;

    public AiProviderManager(List<AbstractAiProvider<AiResultData>> providers) {
        this.providers = providers;
    }

    public Flux<EventStreamResult<AiResultData>> getFlux(AIContext context) {
        for (AbstractAiProvider<AiResultData> provider : providers) {
            if (provider.matcher(context)) {
                return provider.execute(context);
            }
        }
        return Flux.empty();
    }
}
