package com.cb.ai.data.analysis.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySession;
import com.cb.ai.data.analysis.ai.service.AiChatHistorySessionService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * AI历史会话 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(AiConstants.API_AI_ROOT_PATH + "/chat/history/session")
public class AiChatHistorySessionController extends BaseController<AiChatHistorySessionService, AiChatHistorySession> {
    /**
     * 删除历史会话
     * @param ids 会话ID
     */
    @DeleteMapping
    public Result delete(@NotEmpty(message = "会话ID不存在") String[] ids) {
        baseService.removeBySessionId(Arrays.asList(ids));
        return Result.success("删除历史会话成功");
    }

    /**
     * 会话历史记录列表
     * @param params 会话历史
     */
    @GetMapping("/page")
    public Result page(AiChatHistorySession params) {
        LambdaQueryWrapper<AiChatHistorySession> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda()
                .orderByDesc(AiChatHistorySession::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
