package com.cb.ai.data.analysis.dbtable.service.impl;

import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumn;
import com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableColumnMapper;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableColumnService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 动态数据表字段 ServiceImpl
 * <AUTHOR>
 */
@Service
public class AnalysisDbTableColumnServiceImpl extends BaseServiceImpl<AnalysisDbTableColumnMapper, AnalysisDbTableColumn> implements AnalysisDbTableColumnService {

}
