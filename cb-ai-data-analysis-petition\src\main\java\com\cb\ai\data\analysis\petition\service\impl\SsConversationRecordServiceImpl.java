package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsConversationRecordEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsWorkFlowJobEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsConversationRecordVo;
import com.cb.ai.data.analysis.petition.enums.WorkFlowTypeEnum;
import com.cb.ai.data.analysis.petition.mapper.SsConversationRecordMapper;
import com.cb.ai.data.analysis.petition.service.ChatService;
import com.cb.ai.data.analysis.petition.service.SsConversationRecordService;
import com.cb.ai.data.analysis.petition.service.SsWorkFlowJobService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/***
 * <AUTHOR>
 * 谈话解析
 */
@Service
public class SsConversationRecordServiceImpl extends BaseServiceImpl<SsConversationRecordMapper, SsConversationRecordEntity> implements SsConversationRecordService {

    @Resource
    private SsConversationRecordMapper ssConversationRecordMapper;

    @Autowired
    private SsWorkFlowJobService ssWorkFlowJobService;


    @Autowired
    private ChatService chatService;


    @Override
    public Page<SsConversationRecordEntity> selectByPage(SsConversationRecordVo ssConversationRecord) {
        Page<SsConversationRecordEntity> page = new Page<>(ssConversationRecord.getPageNo(), ssConversationRecord.getPageSize());
        QueryWrapper<SsConversationRecordEntity> queryWrapper = new QueryWrapper<>();
        if(!ObjectUtils.isEmpty(ssConversationRecord.getStartTime()) && !ObjectUtils.isEmpty(ssConversationRecord.getEndTime())){
             queryWrapper.between("create_time",ssConversationRecord.getStartTime(),ssConversationRecord.getEndTime());
        }
        if(StringUtils.isNotBlank(ssConversationRecord.getFileName())){
            queryWrapper.like("file_name",ssConversationRecord.getFileName());
        }
        queryWrapper.orderByDesc("create_time");
        return ssConversationRecordMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Result ocrBatchAnalysis(List<MultipartFile> files) throws Exception {
        try{
            if(CollectionUtils.isEmpty(files)){
                throw new XServiceException("文件列表为空");
            }
            List<SsConversationRecordEntity> conversationRecordEntityList=new ArrayList<>();
            for (MultipartFile file : files) {
                SsConversationRecordEntity conversationRecordEntity = new SsConversationRecordEntity();
                String fileName = file.getOriginalFilename();
                conversationRecordEntity.setFileName(fileName);
                conversationRecordEntity.setConversationContent("- 解析中 -");
                conversationRecordEntity.setId(IdUtil.getSnowflakeNextId());
                conversationRecordEntity.setCreateTime(LocalDateTime.now());
                conversationRecordEntity.setConversationBrief("- 解析中 -");
                conversationRecordEntity.setConversationDate("- 解析中 -");
                conversationRecordEntity.setConversationAnalyzed("- 解析中 -");
                conversationRecordEntity.setConversationHolder("- 解析中 -");
                conversationRecordEntity.setConversationObj("- 解析中 -");
                conversationRecordEntity.setCreateTime(LocalDateTime.now());
                conversationRecordEntity.setCreateBy(SecurityUtils.getUsername());
                conversationRecordEntity.setFile(file);
                conversationRecordEntityList.add(conversationRecordEntity);
            }
            ssConversationRecordMapper.insert(conversationRecordEntityList);
            for(SsConversationRecordEntity entity:conversationRecordEntityList){
                new Thread(() -> {
                    // 这里方法内部代码需要提供接口
                    Result ocrAnalysisResp = ocrAnalysis(entity.getFile());
                    if (!ObjectUtils.isEmpty(ocrAnalysisResp)  && ocrAnalysisResp.getCode() == 200) {
                        Object recognitionObj = ocrAnalysisResp.getData();
                        entity.setConversationContent(recognitionObj.toString());
                        SsWorkFlowJobEntity workFlowJobEntity=ssWorkFlowJobService.publish(WorkFlowTypeEnum.CONVERSATION_BRIEF);
                        new Thread(() -> {
                            brief(entity,workFlowJobEntity);
                        }).start();
                    }
                }).start();
            }
            return Result.success("提交任务成功");
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }
    @Override
    public Result ocrAnalysis(MultipartFile file){
        try{
            //return Result.successData("请再次说明 我于2019年4月3日上午8时从合休市速8酒店拉了三个乘客到洛阳洛南社区院，到达医院后出租车计价器显示费用为92.5元，到达后乘客要求我在此等候，而后返回合休汽车站，口头答应我来回费用140元整，下车支付70元，返程回到合休再付70元，我们双方达成口头协议。我足足等待两个小时以后，乘客才看完病时间已经十一点左右，乘客又要求我按照大巴车的价钱每人15元共计45元将他们送到合休东站，因为我等待多时，乘客无理的情况下和他们商量每人15元共计45元将他们拉回合休汽车站，但未达成一致意见，我只好开上空车返回合休，在此期间我和乘客发生了争吵，我本人认识到做为服务行业，确实不该发生争吵，并诚恳认识到自己的错误，在客运所工作人员的教育下，我向乘客致歉，但客运处工作人员多次打电话拒绝，导致无法联系。在今后运营过程中绝不会");
            String base64Img=Base64.getEncoder().encodeToString(file.getBytes());
            return Result.successData(chatService.ocrAnalysis(base64Img));
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("ORC解析失败！");
        }
    }

    @Override
    public int saveOrUpdate(SsConversationRecordVo vo) {
        SsConversationRecordEntity conversationRecordEntity = new SsConversationRecordEntity();
        BeanUtils.copyProperties(vo, conversationRecordEntity);
        if (vo.getId() == null) {
            conversationRecordEntity.setId(IdUtil.getSnowflakeNextId());
            conversationRecordEntity.setConversationBrief("- 解析中 -");
            conversationRecordEntity.setConversationDate("- 解析中 -");
            conversationRecordEntity.setConversationAnalyzed("- 解析中 -");
            conversationRecordEntity.setConversationHolder("- 解析中 -");
            conversationRecordEntity.setConversationObj("- 解析中 -");
            conversationRecordEntity.setCreateTime(LocalDateTime.now());
            conversationRecordEntity.setCreateBy(SecurityUtils.getUsername());
            ssConversationRecordMapper.insert(conversationRecordEntity);
        } else {
            ssConversationRecordMapper.updateById(conversationRecordEntity);
        }
        SsWorkFlowJobEntity workFlowJobEntity=ssWorkFlowJobService.publish(WorkFlowTypeEnum.CONVERSATION_BRIEF);
        new Thread(() -> {
            brief(conversationRecordEntity,workFlowJobEntity);
        }).start();
        return 1;
    }

    private void brief(SsConversationRecordEntity ssConversationRecordEntity,SsWorkFlowJobEntity workFlowJobEntity) {
        String message = "";
        try {
            String prompt=this.getAnalysisPromote();
            String data=ssConversationRecordEntity.getConversationContent();
            String respMsg=chatService.chat(prompt,data);
            message=replaceCharacter(respMsg);
            SsConversationRecordEntity ssConversationRecordVo = JSON.parseObject(message, SsConversationRecordEntity.class);
            ssConversationRecordEntity.setConversationAnalyzed(ssConversationRecordVo.getConversationAnalyzed());
            ssConversationRecordEntity.setConversationBrief(ssConversationRecordVo.getConversationBrief());
            ssConversationRecordEntity.setConversationDate(ssConversationRecordVo.getConversationDate());
            ssConversationRecordEntity.setConversationObj(ssConversationRecordVo.getConversationObj());
            ssConversationRecordEntity.setConversationHolder(ssConversationRecordVo.getConversationHolder());
            ssConversationRecordEntity.setTalkStatement(ssConversationRecordVo.getTalkStatement());
            ssConversationRecordEntity.setConversationAddress(ssConversationRecordVo.getConversationAddress());
            ssConversationRecordEntity.setRecordUser(ssConversationRecordVo.getRecordUser());
            ssConversationRecordEntity.setTalkGender(ssConversationRecordVo.getTalkGender());
            ssConversationRecordEntity.setTalkBirthday(ssConversationRecordVo.getTalkBirthday());
            ssConversationRecordEntity.setTalkNationality(ssConversationRecordVo.getTalkNationality());
            ssConversationRecordEntity.setTalkPolitical(ssConversationRecordVo.getTalkPolitical());
            ssConversationRecordEntity.setTalkEducation(ssConversationRecordVo.getTalkEducation());
            ssConversationRecordEntity.setTalkPost(ssConversationRecordVo.getTalkPost());
            ssConversationRecordEntity.setTalkOrg(ssConversationRecordVo.getTalkOrg());
            ssConversationRecordEntity.setTalkAddress(ssConversationRecordVo.getTalkAddress());
            ssConversationRecordMapper.updateById(ssConversationRecordEntity);
            ssWorkFlowJobService.success(workFlowJobEntity,message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
            ssWorkFlowJobService.error(workFlowJobEntity, message, e.getMessage());
        }
    }

    private String replaceCharacter(String message) {
        try {
            JSONObject jsonObject=JSONObject.parseObject(message,JSONObject.class);
            JSONObject choicesObject=jsonObject.getJSONArray("choices").getJSONObject(0);
            JSONObject messageObject=choicesObject.getJSONObject("message");
            String content=messageObject.getString("content");
            Integer firstCharacterIndex = content.indexOf("{");
            Integer lastCharacterIndex = content.lastIndexOf("}");
            content = content.substring(firstCharacterIndex, lastCharacterIndex + 1);
            return content;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    private String getAnalysisPromote() throws IOException {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("promote/talk.md");
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append(System.lineSeparator());
        }
        return content.toString();
    }
}
