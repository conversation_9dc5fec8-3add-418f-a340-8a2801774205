package com.cb.ai.data.analysis.file.service;

import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.util.DocumentParser;
import com.cb.ai.data.analysis.query.domain.bo.SuperviseFileBo;
import com.cb.ai.data.analysis.query.repository.SuperviseFileRepository;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncEsService {
    private final SuperviseResourceFileService fileService;
    private final SuperviseFileRepository fileRepository;

    @Async // 标记为异步方法
    public void asyncInsertToEs(String fileId) {
        try {
            SuperviseResourceFile file = fileService.getById(fileId);
            InputStream fileStream = fileService.getFileStream(file.getId());

            if (StringUtils.isBlank(file.getFileContent())) {
                String content = DocumentParser.parse(fileStream, file.getFilename());
                file.setFileContent(content);
                fileService.updateById(file);
            }

            SuperviseFileBo fileBo = new SuperviseFileBo();
            BeanUtils.copyProperties(file, fileBo);
            fileBo.setFileName(file.getFilename());
            fileBo.setFileTags(file.getFileTags());

            ZonedDateTime zonedDateTime = file.getCreateTime().atZone(ZoneId.systemDefault());
            fileBo.setCreateTime(Date.from(zonedDateTime.toInstant()));
            // 设置部门Id
            fileBo.setDeptId(SecurityUtils.getDeptId());
            // 设置区域ID
            fileBo.setDistrictId(SecurityUtils.getDistrictId());
            fileRepository.save(fileBo);
        } catch (Exception e) {
            // 处理异常，建议记录日志
            log.error("异步插入ES失败，文件ID: {}", fileId, e);
        }
    }

    // 批量删除文件
    @Async
    public void deleteFileFromEs(List<String> fileIds) {
        try {
            fileRepository.deleteAllById(fileIds);
        } catch (Exception e) {
            // 处理异常，建议记录日志
            log.error("S删除文件失败，文件IDs: {}", fileIds, e);
        }
    }
}