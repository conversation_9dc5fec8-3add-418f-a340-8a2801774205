<template>
  <a-modal
    v-if="visible"
    :open="visible"
    @ok="onCancel"
    @cancel="onCancel"
    width="80%"
    :footer="null"
    :destroyOnClose="false"
    title="解析对话">
    <!-- 标题 + 按钮区域 -->
    <div class="dialog-header">
     <span v-if="internalChatList.length<=0">
       <LoadingOutlined></LoadingOutlined>
        加载中
      </span>
      <span v-else style="float: right;" >
        <a-button @click="downloadResult" v-if="checkboxValues.length>0" style="margin-right: 10px;">导出问答结果</a-button>
        <a-button @click="downloadQaWord">导出为word</a-button>
        <a-button @click="downloadQaExcel" style="margin-left: 10px;">导出为excel</a-button>
      </span>
    </div>
    <div class="chat-container">
      <div v-for="(item, index) in internalChatList" :key="index" :class="['chat-item', item.role === 'user' ? 'right' : 'left']">
        <a-avatar v-if="item.role === 'ai'" :size="30" class="avatar">
          AI
        </a-avatar>
        <div class="chat-bubble" style="word-wrap: break-word;overflow-wrap: break-word;white-space: normal;">
          <p style="color: #7f7f7f" v-if="item.role === 'ai' && !item.loading">
            <a-checkbox :value="item.id"
                        :checked="checkboxValues.includes(item.id)"
                        @change="checkboxChange">导出问答</a-checkbox>
          </p>
          <span v-if="!item.loading" v-html="item.text"></span>
          <span v-else class="dot-loading">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </span>
          <div v-if="item.fileList?.length>0 && !item.loading && (item.text && item.text?.indexOf('没有找到相关内容')<0)">参考文献：
            <p v-for="(file, findex) in item.fileList" :key="findex" v-html="file.fileName" @click="downloadFile(file.fileUrl,file.fileName)" style="color:#1a73e8;cursor: pointer;"></p>
          </div>
        </div>
        <a-avatar v-if="item.role === 'user'" :size="30" class="avatar">
          你
        </a-avatar>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts" name="viewAnalysisChat">
import {type ComponentCustomProperties, getCurrentInstance, ref, watch} from 'vue'
import {problem} from "@/api/petition";
import markdownIt from '@/utils/markdown-it.js'
import {resourceFile} from "@/api/file";
import { LoadingOutlined } from '@ant-design/icons-vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const props = defineProps<{
  visible: boolean,
  problemId:string,
  fileName:string
}>()

const internalChatList =ref([])
const timer=ref();
const checkboxValues=ref([]);

function checkboxChange(e){
  const value = e.target.value;
  if (e.target.checked) {
    checkboxValues.value.push(value);
  } else {
    checkboxValues.value = checkboxValues.value.filter(v => v !== value);
  }
}

function onCancel(){
  stopPolling()
  emits('update:visible', false)
  checkboxValues.value=[]
}
async function downloadFile(fileUrl: string,fileName:string){
  await resourceFile.downloadFile(fileUrl,fileName)
}
async function downloadResult(){
  const closeLoading = _this.$message.loading({content: '导出中...', duration: 0 })
  try {
    const {data} = await problem.downloadZip(checkboxValues.value);
    const blob = new Blob([data], {
      type: 'application/zip'
    });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `${props.fileName}_${Date.now()}.zip`;
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    }, 100);
  } catch (error) {
    _this.$message.error('下载失败');
  }finally {
    closeLoading()
  }
}

async function downloadQaWord(){
  const closeLoading = _this.$message.loading({content: '导出中...', duration: 0 })
  try {
    const {data} = await problem.downloadQaWord(props.problemId);
    const blob = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `${props.fileName}_${Date.now()}.docx`;
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    }, 100);
  } catch (error) {
    _this.$message.error('下载失败');
  }finally {
    closeLoading()
  }
}

async function downloadQaExcel(){
  const closeLoading = _this.$message.loading({content: '导出中...', duration: 0 })
  try {
    const {data} = await problem.downloadQaExcel(props.problemId);
    const blob = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `${props.fileName}_${Date.now()}.xlsx`;
    document.body.appendChild(link);
    link.click();
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    }, 100);
  } catch (error) {
    _this.$message.error('下载失败');
  }finally {
    closeLoading()
  }
}

function content2Md(text){
  const result = text?.replace(/\[\^\d+\]/g, '');
  return markdownIt.render(result || '')
}

async function startPolling() {
  if(!timer.value){
    timer.value = setInterval(async () => {
      const stillLoading = internalChatList.value.some(item =>
        item.role === 'ai' && item.loading
      )
      if (internalChatList.value.length > 0) {
        if (stillLoading) {
          await analysisResult()
        } else {
          stopPolling()
        }
      } else {
        await analysisResult()
      }

    }, 2000)
  }
}
function stopPolling() {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}
async function analysisResult(){
  if(props.problemId){
    try {
      const { data } = await problem.analysisResult(props.problemId)
      internalChatList.value = data.map(item => [
        {
          id:item.id,
          role: 'user',
          text: `${item.problemName}`
        },
        {
          id:item.id,
          role: 'ai',
          text: content2Md(item.content) || '',
          fileList:item.fileListJson?.length>0?
            [...new Set(
              JSON.parse(item.fileListJson).map(item => JSON.stringify({
                fileName: item.fileName,
                fileUrl: item.fileUrl
              }))
            )].map(str => JSON.parse(str)):'',
          loading: !item.content
        }
      ]).flat()
    } catch (error: any) {
      _this.$message.error("获取解析结果失败！")
    }
  }
}
watch(
  () => props.problemId,
  (newValue) => {
    if(newValue){
      analysisResult()
      startPolling()
    }
  },
  { immediate: true }
)
</script>
<style scoped>
/* 保持原有样式不变 */
.chat-container {
  max-height: 60vh;
  min-height: 40vh;
  overflow-y: auto;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-item {
  display: flex;
}

.chat-item.left {
  justify-content: flex-start;
}

.chat-item.right {
  justify-content: flex-end;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin: 0 8px;
}

.chat-bubble {
  max-width: 75%;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  word-break: break-word;
  white-space: pre-wrap;
}

.chat-item.left .chat-bubble {
  background-color: #eee6e6;
  border-radius: 4px;
}

.chat-item.right .chat-bubble {
  background-color: #cce5ff;
  border-radius: 4px;
}

.dot-loading {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.dot {
  width: 6px;
  height: 6px;
  background-color: #999;
  border-radius: 50%;
  animation: blink 1.4s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0%, 80%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.dialog-header {
  display: flow-root;
  border-bottom: 1px solid #DCDFE6;
  padding: 0 0 1vh 0;
  margin-bottom: 1vh;
  font-weight: 600;
  font-size: 18px;
}
</style>
