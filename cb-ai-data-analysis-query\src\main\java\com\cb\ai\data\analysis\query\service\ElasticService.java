package com.cb.ai.data.analysis.query.service;

import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;

import java.util.List;

/**
 * @description: ElasticService 通用服务
 * @date 2025/07/03
 * @author: yxc
 */
public interface ElasticService {


    /**
     * 添加文档
     *
     * @param req
     * @return
     * @throws Exception
     */
    String addDocument(String indexName, ElasticReqBo.Req req) throws Exception;

    /**
     * 批量添加文档
     *
     * @param indexName 索引
     * @param reqList   添加内容
     * @throws Exception
     */
    void addDocumentsBatch(String indexName, List<ElasticReqBo.Req> reqList) throws Exception;


    /**
     * 修改文档
     *
     * @param indexName 索引
     * @param req       修改内容
     * @return
     * @throws Exception
     */
    String updateDocument(String indexName, ElasticReqBo.Req req) throws Exception;

    /**
     * @param indexName 索引
     * @param id        文档id
     * @return
     * @throws Exception
     */
    long deleteDocument(String indexName, String id) throws Exception;
}
