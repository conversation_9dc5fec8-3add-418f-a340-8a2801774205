package com.cb.ai.data.analysis.basdata.service.finance.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasTransactionDetailAccount;
import com.cb.ai.data.analysis.basdata.mapper.finance.BasTransactionDetailAccountMapper;
import com.cb.ai.data.analysis.basdata.repository.finance.BasTransactionDetailAccountRepository;
import com.cb.ai.data.analysis.basdata.repository.finance.esBo.BasTransactionDetailAccountBo;
import com.cb.ai.data.analysis.basdata.service.finance.BasTransactionDetailAccountService;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 交易明细账表(BasTransactionDetailAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14 16:32:32
 */
@Service("basTransactionDetailAccountService")
public class BasTransactionDetailAccountServiceImpl extends ServiceImpl<BasTransactionDetailAccountMapper, BasTransactionDetailAccount> implements BasTransactionDetailAccountService {

    @Autowired
    private BasTransactionDetailAccountRepository repository;

    @Override
    public boolean save(BasTransactionDetailAccount info) {
        int insert = baseMapper.insert(info);
        if (insert > 0) {
            BasTransactionDetailAccountBo bo = convert2Bo(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateById(BasTransactionDetailAccount info) {
        int update = baseMapper.updateById(info);
        if (update > 0) {
            BasTransactionDetailAccountBo bo = convert2Bo(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<BasTransactionDetailAccount> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            List<BasTransactionDetailAccountBo> collect = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }


    private BasTransactionDetailAccountBo convert2Bo(BasTransactionDetailAccount entity) {
        BasTransactionDetailAccountBo bo = new BasTransactionDetailAccountBo();
        BeanUtils.copyProperties(entity, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }

}

