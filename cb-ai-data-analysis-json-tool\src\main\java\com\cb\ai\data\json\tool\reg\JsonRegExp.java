package com.cb.ai.data.json.tool.reg;

/**
 * <AUTHOR>
 * 2025/7/31
 */
public class JsonRegExp {

    // 正则表达式用于匹配配置文件，格式如 config_123.json
    public static final String CONFIG_FILE_REG = "config_\\d+.json";
    // 正则表达式用于匹配数据文件，格式如 data_123_456.json
    public static final String DATA_FILE_REG = "data_\\d+_\\d+.json";
    // 正则表达式用于匹配报告上下文中的配置文件引用，格式如 ;static.report.context.config_123=
    public static final String REPORT_CONTEXT_CONFIG = ";static\\.report\\.context\\.config_\\d+=";
    // 正则表达式用于匹配报告上下文中的数据文件引用，格式如 ;static.report.context.data_123_456=
    public static final String REPORT_CONTEXT_DATA = ";static\\.report\\.context\\.data_\\d+_\\d+=";
}
