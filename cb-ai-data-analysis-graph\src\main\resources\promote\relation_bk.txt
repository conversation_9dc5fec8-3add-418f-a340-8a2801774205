1.我会像你提供一个json数组或一个条独立的json或者一段长文本。其中包含一些文本字段。每条数据的多个字段你需要进行分析，有可能要结合同一条数据的多个字段来分析。之后从这些字段中提取我要的信息
2.请按照第4.我要求的格式固定输出
3.别的信息都不要输出，一定记住，你最终输出给我的信息将是一个标准的json数组。
4.以下为json结构的模板，其中包含了我所需要的字段信息，包括字段说明
#{template}
5.请严格注意，这些数据中可能存在企业与人，人与人之间的关联关系，你也需要在组装的数据中体现出来
6.这些文件有可能只有企业信息，也有可能只有人员。你能解析到什么就组装什么数据。如果找不到的信息就给空数组即可。比如没有任何公司/政府部门/机构组织和亲属的信息,但是有人员信息，则输出样例为
7.除开上述描述的字段，即上面的json模板，不要输出别的任意信息。填充到字段中的信息一定是明确的。不要填充json模板中没有的字段。
8.任何字段信息，如果不明确或者找不到，這个字段就空着。不要填充模棱两可的信息。
9.输出的结果中的每条数据一定要按照上面给的英文字段名来进行构建,如果提取不到指定的信息，则以空字符串的形式返回,不要填充 类似 找不到或者不明确
10.这些数据非常重要，你可以多花一些时间进行思考，也可以重复验证后再返回。一定注意以标准的 JSON 格式返回，所有字段必须带双引号、键值对用冒号分隔、数组和对象结构完整，必须通过标准 JSON 校验器校验通过，不允许输出非 JSON 格式文本或混杂注释。
11.一定记住，我要的不是将所有你提取到的信息都填充进去，而是在你提取到的信息里面，根据我上述的要求，严格过滤，组装后再返回符合我要求和定义的数据。不满足要求，或不明确的信息均可不返回
12.不要抽取以及输出模板中未规定的信息，不要抽取以及输出模板中未规定的信息，不要抽取以及输出模板中未规定的信息
13.输出前校验json的合法性并修复破损的json结构
