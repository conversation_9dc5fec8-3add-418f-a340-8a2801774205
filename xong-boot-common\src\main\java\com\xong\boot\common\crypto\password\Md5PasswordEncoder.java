package com.xong.boot.common.crypto.password;

import cn.hutool.crypto.SecureUtil;
import com.xong.boot.common.utils.SecureUtils;
import com.xong.boot.common.utils.StringUtils;

import java.security.MessageDigest;

/**
 * SM5加密密码
 * <AUTHOR>
 */
public class Md5PasswordEncoder implements PasswordEncoder {
    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return encode(password);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return matches(password, encodedPassword);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     */
    @Override
    public String encode(CharSequence rawPassword) {
        if (StringUtils.isBlank(rawPassword)) {
            throw new IllegalArgumentException("rawPassword cannot be null");
        }
        return SecureUtils.md5(rawPassword.toString());
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        if (StringUtils.isBlank(rawPassword)) {
            throw new IllegalArgumentException("rawPassword cannot be null");
        }
        if (StringUtils.isBlank(encodedPassword)) {
            return false;
        }
        return MessageDigest.isEqual(SecureUtil.md5(rawPassword.toString()).getBytes(), encodedPassword.getBytes());
    }
}
