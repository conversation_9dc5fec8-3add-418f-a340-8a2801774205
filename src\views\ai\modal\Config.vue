<template>
  <a-modal :open="visible" @cancel="onCancel" width="60%" title="AI设置">
    <a-spin :spinning="loading" tip="加载中...">
      <a-form :model="formData" :label-col="{ style: { width: '100px' } }" ref="formRef">
        <a-divider orientation="left"> AI大模型设置 </a-divider>
        <a-row :gutter="16">
          <!-- <a-col :span="12">
            <a-form-item label="服务器地址" name="baseUrl">
              <a-input
                v-model:value="formData.baseUrl"
                :maxlength="255"
                placeholder="请输入服务器地址"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="12">
            <a-form-item label="应用KEY" name="appKey">
              <a-input
                v-model:value="formData.appKey"
                :maxlength="64"
                placeholder="请输入应用KEY"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="12">
            <a-form-item label="模型名称" name="modelName">
              <a-select v-model:value="formData.modelName" allow-clear placeholder="请选择模型">
                <a-select-option value="gpt-4">gpt-4</a-select-option>
                <a-select-option value="qwen-max">qwen-max</a-select-option>
                <a-select-option value="chatglm-6b">chatglm-6b</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <!--          <a-col :span="12">-->
          <!--            <a-form-item label="最大token数量">-->
          <!--              <a-input-number :value="formData.maxTokens" :min="0" :precision="0" disabled />-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col :span="12">
            <a-form-item label="相似结果数" name="topK">
              <a-input-number v-model:value="formData.topK" :min="1" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="温度" name="temperature">
              <a-slider
                v-model:value="formData.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                :marks="{
                  0: '0',
                  1: '1',
                  2: '2'
                }"
              >
              </a-slider>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="采样策略" name="topP">
              <a-slider
                v-model:value="formData.topP"
                :min="0"
                :max="1"
                :step="0.1"
                :marks="{
                  0: '0',
                  0.5: '0.5',
                  1: '1'
                }"
              >
              </a-slider>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="频率惩罚项" name="frequencyPenalty">
              <a-slider
                v-model:value="formData.frequencyPenalty"
                :min="-2"
                :max="2"
                :step="0.1"
                :marks="{
                  '-2': '-2',
                  '-1.5': '-1.5',
                  '-1': '-1',
                  '-0.5': '-0.5',
                  0: '0',
                  0.5: '0.5',
                  1: '1',
                  1.5: '1.5',
                  2: '2'
                }"
              >
              </a-slider>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出现惩罚项" name="presencePenalty">
              <a-slider
                v-model:value="formData.presencePenalty"
                :min="-2"
                :max="2"
                :step="0.1"
                :marks="{
                  '-2': '-2',
                  '-1.5': '-1.5',
                  '-1': '-1',
                  '-0.5': '-0.5',
                  0: '0',
                  0.5: '0.5',
                  1: '1',
                  1.5: '1.5',
                  2: '2'
                }"
              >
              </a-slider>
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider orientation="left"> 深度研究配置 </a-divider>
        <a-row :gutter="16">
          <!-- <a-col :span="12">
            <a-form-item label="服务器地址" name="pyBaseUrl">
              <a-input
                v-model:value="formData.pyBaseUrl"
                :maxlength="255"
                placeholder="请输入服务器地址"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="12">
            <a-form-item label="应用KEY" name="pyAppKey">
              <a-input
                v-model:value="formData.pyAppKey"
                :maxlength="64"
                placeholder="请输入应用KEY"
              />
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="相似性阈值" name="pySimilarityThreshold">
              <a-slider
                v-model:value="formData.pySimilarityThreshold"
                :min="0"
                :max="1"
                :step="0.1"
                :marks="{
                  0: '0',
                  0.5: '0.5',
                  1: '1'
                }"
              >
              </a-slider>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="知识块数量" name="pyTopK">
              <a-input-number v-model:value="formData.pyTopK" :min="3" :max="15" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最大步数" name="pyMaxStepNum">
              <a-input-number v-model:value="formData.pyMaxStepNum" :min="0" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最大迭代次数" name="pyMaxPlanIterations">
              <a-input-number
                v-model:value="formData.pyMaxPlanIterations"
                :min="0"
                :precision="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="自动接受计划" name="pyAutoAcceptedPlan">
              <a-switch
                v-model:checked="formData.pyAutoAcceptedPlan"
                checked-children="开"
                un-checked-children="关"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="MCP配置" name="pyMcpSettings">
              <a-input v-model:value="formData.pyMcpSettings" placeholder="请输入MCP配置" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider orientation="left"> 网页生成配置 </a-divider>
        <a-row :gutter="16">
          <!-- <a-col :span="12">
            <a-form-item label="模型名称" name="mhCurrentModel">
              <a-select
                v-model:value="formData.mhCurrentModel"
                allow-clear
                placeholder="请选择网页模型"
              >
                <a-select-option value="qwen3-30b-a3b">qwen3-30b-a3b</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="转换格式" name="mhLanguage">
              <a-select
                v-model:value="formData.mhLanguage"
                allow-clear
                placeholder="请选择转换格式"
              >
                <a-select-option value="html">html</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="开启搜索" name="mhEnableSearch">
              <a-switch
                v-model:checked="formData.mhEnableSearch"
                checked-children="开"
                un-checked-children="关"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button @click="downloadLog"> 下载前端日志</a-button>
      <a-button @click="cleanErrlog"> 清空前端日志缓存</a-button>
      <a-button :loading="loading" @click="onCancel"> 取消 </a-button>
      <a-button :loading="loading" danger type="dashed" @click="onReset"> 重置 </a-button>
      <a-button :loading="loading" type="primary" @click="onOk"> 保存 </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { type ComponentCustomProperties, getCurrentInstance, reactive, ref, watch } from 'vue'
import { isNull } from '@/utils'
import { useAiStore } from '@/stores'
import errorLog from '@/utils/errorLog'
import { message } from 'ant-design-vue'

const aiStore = useAiStore()

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const porps = withDefaults(
  defineProps<{
    visible?: boolean
  }>(),
  { visible: false }
)
const loading = ref(false)
const formRef = ref()
const formData = reactive<Record<string, any>>({})

// 关闭
function onCancel() {
  formRef.value.resetFields()
  emits('update:visible', false)
}
// 确定
function onOk() {
  _this.$form.validate(formRef.value, async (errors, values) => {
    if (errors) {
      return false
    }
    try {
      loading.value = true
      await aiStore.updateConfig(values)
      _this.$message.success('修改成功')
      onCancel()
    } catch (error: any) {
      _this.$message.error(error.message)
    } finally {
      loading.value = false
    }
  })
}

function downloadLog() {
  errorLog.downloadErrorLog()
}

function cleanErrlog() {
  errorLog.clean()
  message.success('清除成功')
}
// 重置配置
async function onReset() {
  try {
    loading.value = true
    const data = await aiStore.resetConfig()
    formRef.value.resetFields()
    Object.keys(data).forEach((k) => {
      const val = (<{ [key: string]: any }>data)[k]
      if (!isNull(val)) {
        if (k === 'pyMcpSettings') {
          formData[k] = JSON.stringify(val)
        } else {
          formData[k] = val
        }
      }
    })
    _this.$message.success('重置成功')
    onCancel()
  } catch (error: any) {
    _this.$message.error(error.message)
  } finally {
    loading.value = false
  }
}
// 加载数据
async function loadData() {
  try {
    loading.value = true
    const data = await aiStore.loadConfig()
    Object.keys(data).forEach((k) => {
      const val = (<{ [key: string]: any }>data)[k]
      if (!isNull(val)) {
        if (k === 'pyMcpSettings') {
          formData[k] = JSON.stringify(val)
        } else {
          formData[k] = val
        }
      }
    })
  } catch (error: any) {
    _this.$message.error(error.message)
  } finally {
    loading.value = false
  }
}

watch(
  () => porps.visible,
  (nowValue) => {
    if (nowValue) {
      loadData()
    }
  }
)
</script>
