package com.cb.ai.data.analysis.ai.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.type.NClobTypeHandler;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/22 22:05
 * @Copyright (c) 2025
 * @Description AI会话历史详情
 */
@Data
@TableName(autoResultMap = true)
public class AiChatHistoryDetail implements Serializable {

    private static final long serialVersionUID = 110119120L;

    /* 主键 会话详情ID */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /* 会话ID */
    private String sessionId;

    /* 用户ID */
    private String userId;

    /* 角色 */
    private String role;

    /* 事件 */
    private String event;

    /* 思考内容 */
    @TableField(typeHandler = NClobTypeHandler.class)
    private String reasoningContent;

    /* 回答内容 */
    @TableField(typeHandler = NClobTypeHandler.class)
    private String content;

    /* 思考内容 */
    @TableField(typeHandler = NClobTypeHandler.class)
    private String dataList;

    /* 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSSSSS")
    private LocalDateTime createTime;

}
