package com.cb.ai.data.analysis.ai.config;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 对象池配置
 * @Createtime 2023/12/21 20:20
 * @Copyright: Copyright (c) 2023
 */
@Configuration
public class ObjectPoolConfigure {
    /**
     * WebClient对象池
     * @createtime 2025/7/15 下午5:45
     * <AUTHOR>
     * @version 1.0
     */
    @Bean
    @Primary
    public GenericObjectPool<WebClient> webClientPool(GenericObjectPoolConfig<WebClient> config) {
        // 创建池对象工厂
        PooledObjectFactory<WebClient> factory = new BasePooledObjectFactory<WebClient>() {
            @Override
            public WebClient create() {
                return WebClientConfig.getWebClient();
            }

            @Override
            public PooledObject<WebClient> wrap(WebClient t) {
                return new DefaultPooledObject<>(t);
            }
        };
        // 创建对象池
        return new GenericObjectPool<>(factory, config);
    }

    @Bean
    @ConditionalOnMissingBean
    public <T> GenericObjectPoolConfig<T> objectPoolConfig() {
        return ObjectPoolConfig.genericObjectPoolConfig();
    }

    @Bean
    @ConditionalOnMissingBean
    public <T> GenericKeyedObjectPoolConfig<T> keyedObjectPoolConfig() {
        return ObjectPoolConfig.keyedObjectPoolConfig();
    }

}
