package com.cb.ai.data.analysis.knowledge.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件选择实体
 */
@Data
public class KnowledgeSuperviseFile implements Serializable {

    /***
     * key 如果是文件夹，就是文件夹ID，如果是文件就是文件ID
     */
    private String key;
    /***
     * title 如果是文件夹，就是文件夹名称，如果是文件就是文件名称
     */
    private String title;

    /***
     * isDir 是否是目录
     */
    private Boolean isDir;

    /***
     * isDir 是否选中
     */
    private Boolean isSelected;

    /***
     * 父级KEY
     */
    private String parentKey;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /***
     * children 子节点
     */
    private List<KnowledgeSuperviseFile> children;

}
