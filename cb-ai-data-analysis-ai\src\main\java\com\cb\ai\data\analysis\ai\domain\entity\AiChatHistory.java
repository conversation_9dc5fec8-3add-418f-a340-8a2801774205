package com.cb.ai.data.analysis.ai.domain.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI 会话历史记录(AiChatHistory)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-07 14:23:51
 */
@Data
@TableName(autoResultMap = true)
public class AiChatHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键
    //会话ID
    @TableId(type = IdType.ASSIGN_ID)
    private String sessionId;

    //用户ID
    private String userId;

    //创建时间
    @TableField(updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    //修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    @TableField(condition = SqlCondition.LIKE)
    @NotBlank(message = "部门名称不存在", groups = {AddGroup.class, UpdateGroup.class})
    private String title;

}

