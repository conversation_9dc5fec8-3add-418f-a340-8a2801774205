package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionManager;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChain;
import com.cb.ai.data.analysis.ai.component.choreography.model.*;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIKnowledge;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.common.MinioFileData;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.MdPromoteExtractorUtil;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.deepoove.poi.XWPFTemplate;
import com.xong.boot.common.exception.CustomException;
import com.xong.boot.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI生成报告扩展
 */
@ExtensionProvider(desc = "私有化AI生成报告扩展", businessScenes = {
    @Route(tag = "生成报告(纪委)", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PrivateAiJwReportExtension implements PrivateAIExtension<CommonAIRequestContext> {

    private final SuperviseResourceFileService fileService;

    @Override
    public Flux<?> invoke(CommonAIRequestContext requestContext) {
        String fileName;
        String reportTplPath;
        boolean isCustomTemp = false;
        String tplPath = requestContext.getAnalyseTag()[0].replaceFirst("^生成报告(?:[\\\\(（][^）\\\\)]*[）\\\\)])?", "");
        if(StrUtil.isNotBlank(tplPath)){
            // 判断是否为内置模版
            reportTplPath = "templates/report/纪委" + (tplPath.startsWith("/")? tplPath : "/" + tplPath);
            // 检查模板文件是否存在，不存在则是自定义模版
            isCustomTemp = !CommonUtil.existsResourcesFile(reportTplPath);
            fileName = FilenameUtils.getName(tplPath);
        } else {
            reportTplPath = "templates/report/纪委/通用模板.docx";
            fileName = "生成报告-" + System.currentTimeMillis() + ".docx";
        }
        requestContext.setSource(false);
        if (isCustomTemp) {
            PrivateAIExtension<CommonAIRequestContext> extension = ExtensionManager.getExtension(BusinessScene.of("文件分析", BusinessTypeEnum.PRIVATE_BUSINESS), PrivateAIExtension.class);
            CommonAIRequestContext copy = requestContext.copy();
            copy.setSystemPromote("""
                你是一位资深的政府办公文书写作专家。
                需要根据【参考文本】书写相关报告，
                并按照用户要求返回一份完整、规范的报告。
                禁止在内容外输出任何说明或注释。保证内容合规严谨，不得出现XXX字样。
                """
            );
            copy.setMinioFileDataList(List.of(new MinioFileData(null, StrUtil.subAfter(tplPath, "/", true), tplPath)));
            return extension.invoke(copy);
        } else {
            // 这里要根据docx模板的路径获取提示词的路径
            String promptPath = reportTplPath.replace("templates", "promote").replace(".docx", ".md");
            requestContext.setSystemPromote(MdPromoteExtractorUtil.getPromote(promptPath));
            return FlowChain.newChain(requestContext.getRequestId()).addSerialNode(PrivateAIKnowledge.class, node -> node
                .nodeName("生成报告")
                .context(requestContext)
                .hideContent()
                .addBeforeData((nodeContext, reqContext) -> new PrivateAIBackData(
                    reqContext.getSessionId(),
                    RoleEnum.assistant,
                    null,
                    null,
                    JsonMap.of("progress", 10),
                    null
                ))
                .dispose((nodeId, data, context) -> fileHandle(fileName, reportTplPath, data, context))
                .addAfterData((nodeContext, reqContext) -> new PrivateAIBackData(
                    reqContext.getSessionId(),
                    RoleEnum.assistant,
                    null,
                    null,
                    JsonMap.of("progress", 50),
                    null
                ))
            ).addProcessNode("生成报告", (flowContext, nodeContext) -> {
                String filePath = flowContext.getData().getStr("localFilePath");
                Path path = Paths.get(filePath);
                return readWordWithFormatting(path.toFile(), requestContext, nodeContext)
                    .concatWith(Flux.just(new PrivateAIBackData(requestContext.getSessionId(), RoleEnum.assistant, null, null, new JsonMap(flowContext.getData()), null)))
                    .onErrorResume(Flux::error)
                    .doFinally(signalType ->
                        CompletableFuture.runAsync(() -> {
                            try {
                                Files.delete(path);
                            } catch (Exception ignored) {}
                        })
                    );
            })
            .execute();
        }
    }

    private void fileHandle(String fileName, String reportTplPath, PrivateAIBackData data, FlowContext context) {
        String newFileName = FilenameUtils.getBaseName(fileName).replace("模板", "") + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH_mm_ss")) + "." + FilenameUtils.getExtension(fileName);
        String relationPath = "report_gen/" + DateUtils.date().toString("yyyy/MM/dd") + "/" + newFileName;
        String filePath = System.getProperty("user.dir") + File.separator + relationPath;
        try (InputStream is = CommonUtil.getFileInputStream(reportTplPath)) {
            // 确认目录存在或创建
            Path path = Paths.get(filePath);
            if (Files.notExists(path.getParent())) {
                Files.createDirectories(path.getParent());
            }
            // 生成并写入文件
            try (FileOutputStream fos = new FileOutputStream(filePath);
                 XWPFTemplate template = XWPFTemplate.compile(is).render(JSONObject.parseObject(data.getContent()))) {
                template.writeAndClose(fos);
            } catch (Exception e) {
                throw new CustomException("文件写入失败");
            }
            // 文件写入完成后，再上传
            try (FileInputStream fileStream = new FileInputStream(filePath)) {
                SuperviseResourceFile resourceFile = fileService.uploadFileStreamByFolderName(
                        fileStream,
                        null,
                        newFileName,
                        "AI文件报告",
                        (long) fileStream.available(),
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        null,
                        "admin"
                );
                //String minioFilePath = fileService.getDownloadUrl(resourceFile.getId());
                context.set("fileId", resourceFile.getId());
                context.set("fileName", newFileName);
                context.set("fileFrom", "sys");
                context.set("fileUrl", resourceFile.getFilePath());
                context.set("localFilePath", filePath);
            } catch (Exception e) {
                throw new CustomException("文件上传失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Flux<PrivateAIBackData> readWordWithFormatting(File wordFile, CommonAIRequestContext requestContext, NodeContext nodeContext) {
        return Flux.create(emitter -> {
            emitter.next(new PrivateAIBackData(
                requestContext.getSessionId(),
                RoleEnum.assistant,
                null,
                null,
                JsonMap.of("progress", 100),
                null
            ));
            try (XWPFDocument doc = new XWPFDocument(new FileInputStream(wordFile))) {
                for (XWPFParagraph p : doc.getParagraphs()) {
                    StringBuilder paragraphText = new StringBuilder();
                    // 处理段落中的格式和文本
                    for (XWPFRun run : p.getRuns()) {
                        // 保留格式信息（如加粗、斜体等）
                        if (run.isBold()) { paragraphText.append("**"); }
                        if (run.isItalic()) { paragraphText.append("*"); }
                        if (run.getUnderline() != UnderlinePatterns.NONE) { paragraphText.append("__"); }

                        paragraphText.append(run.getText(0));

                        if (run.getUnderline() != UnderlinePatterns.NONE) { paragraphText.append("__"); }
                        if (run.isItalic()) { paragraphText.append("*"); }
                        if (run.isBold()) { paragraphText.append("**"); }
                    }
                    nodeContext.collectContent(paragraphText + "\n\n");
                    // 添加段落分隔符
                    emitter.next(new PrivateAIBackData(requestContext.getSessionId(), RoleEnum.assistant, null, paragraphText + "\n\n", null));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.error(new CustomException("读取Word文件失败: " + e.getMessage()));
            }
        });
    }

}
