package com.cb.ai.data.analysis.petition.domain.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ss_word_excel_generate_log
 *
 * <AUTHOR>
@Data
@TableName("ss_word_excel_generate_log")
public class SsWordExcelGenerateLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 源文件Id
     */
    private String filePath;
    /**
     * 使用的相关文件Id
     */
    private String fileIds;
    /**
     * 创建时间
     */
    @TableField(updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;
    /**
     * 完成时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime finishTime;
    /**
     * -1:生成失败 0:生成中 1:生成完成
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
}
