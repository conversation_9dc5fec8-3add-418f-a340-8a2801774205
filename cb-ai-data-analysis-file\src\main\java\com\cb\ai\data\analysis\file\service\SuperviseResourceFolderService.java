package com.cb.ai.data.analysis.file.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.BaseService;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface SuperviseResourceFolderService  extends IService<SuperviseResourceFolder> {
    void dropFolder(DropParams dropParams);



    @Transactional
    boolean removeFolders(String[] ids);

    /**
     * 更新文件夹的原路径信息
     * @param ids
     * @return
     */
    Boolean updateFoldersOriginalPath(List<String> ids);

    /**
     * 将传入的文件夹全路径列表包含的数据查出来，并且以map形式返回
     * @param
     * @return
     */
    Map<String, SuperviseResourceFolder> folderIdMap(List<String> fullPaths);

    /**
     * 已删除的文件夹列表
     * @param page
     * @param superviseResourceFile
     * @return
     */
    Page<SuperviseResourceFolder> pageDeletedFolders(Page<SuperviseResourceFolder> page, SuperviseResourceFolder superviseResourceFile);

    /**
     * 还原文件夹
     * @param ids
     * @param folderId
     */
    void restoreFolder(List<String> ids, String folderId);

    /**
     * 永久删除,获取永久删除的文件夹及其子文件夹
     * @param ids
     * @return
     */
    List<SuperviseResourceFolder> permanentlyDelete(List<String> ids);

    /**
     * 自定义条件删除
     * @param wrapper
     * @return
     */
    Integer deleteByCustom(Wrapper<SuperviseResourceFolder> wrapper);
}
