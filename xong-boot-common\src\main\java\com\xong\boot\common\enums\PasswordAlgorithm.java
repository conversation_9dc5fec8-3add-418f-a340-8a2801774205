package com.xong.boot.common.enums;

import com.xong.boot.common.utils.StringUtils;

/**
 * 密码加密算法
 * <AUTHOR>
 */
public enum PasswordAlgorithm {
    /**
     * argon2 算法
     */
    ARGON2,
    /**
     * bcrypt 算法
     */
    BCRYPT,
    /**
     * md5 算法
     */
    MD5,
    /**
     * pbkdf2 算法
     */
    PBKDF2,
    /**
     * scrypt 算法
     */
    SCRYPT,
    /**
     * SM3 算法
     */
    SM3;

    public static PasswordAlgorithm getByValue(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (PasswordAlgorithm val : values()) {
            if (name.equalsIgnoreCase(val.name())) {
                return val;
            }
        }
        return null;
    }
}
