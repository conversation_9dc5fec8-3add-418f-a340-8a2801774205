package com.cb.ai.data.analysis.dbtable.domain;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import com.cb.ai.data.analysis.dbtable.enums.TableAlign;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.handlers.ArrayStringTypeHandler;
import com.xong.boot.common.valid.AddGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 动态数据表字段
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTableColumn extends SimpleBaseDomain {
    /**
     * ID
     */
    @TableId
    private String id;
    /**
     * 表名称
     */
    private String tableName;
    /**
     * 字段名
     */
    @NotBlank(message = "字段名不存在", groups = AddGroup.class)
    private String columnName;
    /**
     * 字段备注
     */
    @NotBlank(message = "字段备注不存在", groups = AddGroup.class)
    @TableField(condition = SqlCondition.LIKE)
    private String columnComment;
    /**
     * 字段类型
     */
    @NotNull(message = "字段类型不存在", groups = AddGroup.class)
    private CHColumnType columnType;
    /**
     * 字段精度
     */
    private Integer columnPrecise;
    /**
     * 字段规模
     */
    private Integer columnScale;
    /**
     * 字段默认值
     */
    private String columnDefault;
    /**
     * 是否主键
     */
    private Boolean isPrimary;
    /**
     * 不能为空
     */
    private Boolean notNull;
    /**
     * 禁用列（不进行展示）
     */
    private Boolean tableDisabled;
    /**
     * 隐藏列
     */
    private Boolean tableHidden;
    /**
     * 列宽度
     */
    private String tableWidth;
    /**
     * 最大列宽
     */
    private Integer tableMaxWidth;
    /**
     * 最小列宽
     */
    private Integer tableMinWidth;
    /**
     * 对齐方式
     */
    private TableAlign tableAlign;
    /**
     * 固定列
     */
    private String tableFixed;
    /**
     * 自动省略
     */
    private Boolean tableEllipsis;
    /**
     * 列排序
     */
    private Boolean tableSorter;
    /**
     * 列筛选项
     */
    @TableField(typeHandler = ArrayStringTypeHandler.class)
    private List<Map<String, Object>> tableFilters;
    /**
     * 多筛选列
     */
    private Boolean tableFilterMultiple;
    /**
     * 高级搜索
     */
    private Boolean tableAdvancedQuery;
    /**
     * 表单控件
     */
    private String formComp;
    /**
     * 表单添加
     */
    private Boolean formAllowAdd;
    /**
     * 表单编辑
     */
    private Boolean formAllowEdit;
    /**
     * 表单label宽度
     */
    private Integer formLabelWidth;
    /**
     * 表单布局跨度
     */
    private Integer formSpan;
    /**
     * 表单必填
     */
    private Boolean formRequired;
    /**
     * 字段单位
     */
    private String columnUnit;
    /**
     * 字段帮助
     */
    private String columnHelp;
    /**
     * 字段选项或字典类型
     */
    private String columnOptions;
    /**
     * 是否数据库字段
     */
    private Boolean isDb;
    /**
     * 是否摘要字段
     */
    private Boolean isDigest;
    /**
     * 排序
     */
    private Integer sortOn;
}
