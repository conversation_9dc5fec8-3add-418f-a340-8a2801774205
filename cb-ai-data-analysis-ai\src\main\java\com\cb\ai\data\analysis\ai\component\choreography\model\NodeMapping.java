package com.cb.ai.data.analysis.ai.component.choreography.model;


import com.cb.ai.data.analysis.ai.component.choreography.engine.INodeAttributeOperation;
import com.cb.ai.data.analysis.ai.component.choreography.flow.IFlowProcessNode;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/12 11:47
 * @Copyright (c) 2025
 * @Description 子节点处理映射
 */
public record NodeMapping<I, O, N extends IFlowProcessNode<I, O>>(Class<N> nodeClass, Consumer<INodeAttributeOperation<I, O>> nodeConsumer) {}

