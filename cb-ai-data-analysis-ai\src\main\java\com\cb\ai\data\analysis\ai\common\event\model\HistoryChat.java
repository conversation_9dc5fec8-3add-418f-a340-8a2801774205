package com.cb.ai.data.analysis.ai.common.event.model;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:21
 * @Copyright (c) 2025
 * @Description Disruptor流程历史会话事件
 */
@Data
@Accessors(chain = true)
public class HistoryChat {
    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 本次上下文
     */
    private String content;

    /**
     * 角色
     */
    private String role;

    /**
     * 创建时间
     */
    private String createTime;

    public HistoryChat() {
        this.createTime = LocalDateTime.now().format(DatePattern.NORM_DATETIME_MS_FORMATTER);
    }
}