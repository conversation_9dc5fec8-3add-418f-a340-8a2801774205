.xong-layout-chat-tool-drag-tips{
  color: rgba(0,0,0,0.6);
  margin-bottom: 10px;
}

.xong-layout-chat-tool-drag-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid #eee;
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  &:hover{
    box-shadow: 0px 5px 5px 2px rgba(0, 0, 0, .1), 0px 0px 1px 0px rgba(0, 0, 0, .15);
    background: rgba(0,0,0,0.06);
  }
  &_handle{
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
  }
  &_text{
    flex: 1;
    &__title{
      color: #333333;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      img{
        width: 36px;
        height: 36px;
      }
    }
    &__desc{
      color: rgba(0,0,0,0.3);
    }
  }
  &_fixed{
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

}
.fixed-icon{
  color: #1a73e8;
}
.xong-layout-chat-tool-drag-footer{
  width: 100%;
}
