package com.cb.ai.data.analysis.petition.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * excel处理器
 *
 * <AUTHOR>
 */
public class ExcelReaderHandler extends FileReaderHandler<Map<String, List<JSONObject>>> {
    /**
     * 根据传入的{inputStream}读取excel数据
     *
     * 方法会读取存在的所有sheet
     * sheet名作为 key sheet中数据
     * @param inputStream 需要解析的excel文件流
     * @return
     */
    @Override
    public Map<String, List<JSONObject>> read(InputStream inputStream) {
        Map<String, List<JSONObject>> result = new LinkedHashMap<>();
        byte[] excelBytes;

        try {
            excelBytes = cloneInputStreamToBytes(inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        try (InputStream sheetStream = new ByteArrayInputStream(excelBytes)) {
            ExcelReader excelReader = EasyExcel.read(sheetStream).build();
            List<ReadSheet> sheetList = excelReader.excelExecutor().sheetList();
            excelReader.finish();

            for (ReadSheet sheet : sheetList) {
                List<JSONObject> dataList = new ArrayList<>();
                List<String> headerList = new ArrayList<>();

                InputStream preReadStream = new ByteArrayInputStream(excelBytes);
                List<Map<Integer, String>> previewRows = previewRows(preReadStream, sheet.getSheetNo(), 3);
                int dynamicHeadRowNumber = detectHeadRow(previewRows) + 1;

                try (InputStream singleSheetStream = new ByteArrayInputStream(excelBytes)) {
                    EasyExcel.read(singleSheetStream, new AnalysisEventListener<Map<Integer, String>>() {
                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            headerList.clear();
                            headerList.addAll(headMap.values());
                        }

                        @Override
                        public void invoke(Map<Integer, String> data, AnalysisContext context) {
                            JSONObject json = new JSONObject();
                            for (Map.Entry<Integer, String> entry : data.entrySet()) {
                                int colIndex = entry.getKey();
                                if (colIndex < headerList.size()) {
                                    String key = headerList.get(colIndex);
                                    String value = entry.getValue();
                                    if (StringUtils.isEmpty(value) && !CollectionUtils.isEmpty(dataList)) {
                                        JSONObject lastRowData = dataList.get(dataList.size() - 1);
                                        Object lastRowDataValue = lastRowData.get(key);
                                        value = lastRowDataValue == null ? "" : lastRowDataValue.toString();
                                    }
                                    json.put(key, value);
                                }
                            }
                            dataList.add(json);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).headRowNumber(dynamicHeadRowNumber).sheet(sheet.getSheetNo()).doRead();

                    result.put(sheet.getSheetName(), dataList);
                } catch (IOException e) {
                    throw new RuntimeException("读取 sheet 失败：" + sheet.getSheetName(), e);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("读取 sheet 列表失败", e);
        }

        return result;
    }

    /**
     * 预读取数据，用于动态判定表头行
     * @param inputStream
     * @param sheetNo
     * @param maxRows
     * @return
     */
    private List<Map<Integer, String>> previewRows(InputStream inputStream, int sheetNo, int maxRows) {
        List<Map<Integer, String>> previewRows = new ArrayList<>();

        try {
            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    previewRows.add(data);
                    if (previewRows.size() >= maxRows) {
                        // 读取够了，抛异常中断 EasyExcel 的读取
                        throw new StopReadException();
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet(sheetNo).headRowNumber(0).doRead();
        } catch (StopReadException ignore) {
            // 捕获我们自定义的中断异常即可
        }

        return previewRows;
    }

    // 自定义异常，用于中断预读流程
    private static class StopReadException extends RuntimeException {
    }

    /**
     * 复制输入/上传的文件流 方便在一个excel下有多个sheet的情况进行重复读取
     * @param inputStream 输入/上传文件流
     * @return
     * @throws IOException
     */
    private byte[] cloneInputStreamToBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 动态判定表头是在第几行
     *
     * 从当前sheet的第一行开始查询，如果下一行的数据列长度column.length 大于第一行，则定义第二行为表头行。否则第一行为表头
     * @param previewRows
     * @return
     */
    private int detectHeadRow(List<Map<Integer, String>> previewRows) {
        for (int i = 0; i < previewRows.size() - 1; i++) {
            int currentSize = countNullableProperties(previewRows.get(i));
            int nextSize = countNullableProperties(previewRows.get(i + 1));
            if (nextSize > currentSize) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * count非空value的entry数量
     * @param map
     * @return
     */
    private int countNullableProperties(Map<Integer, String> map) {
        return map.values().stream().filter(value -> StringUtils.isNotBlank(value)).collect(Collectors.toList()).size();
    }
}
