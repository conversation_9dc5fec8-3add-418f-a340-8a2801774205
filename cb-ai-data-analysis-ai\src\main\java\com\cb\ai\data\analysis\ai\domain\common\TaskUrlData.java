package com.cb.ai.data.analysis.ai.domain.common;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 10:37
 * @Copyright (c) 2025
 * @Description 异步任务类型的Url数据
 */
@Getter
@Setter
public class TaskUrlData extends UrlData {
    /*
     * 健康检查Url
     */
    private String healthUrl;
    /*
     * 执行任务Url
     */
    private String invokeUrl;
    /*
     * 查看任务处理状态Url
     */
    private String taskStatusUrl;
    /*
     * 查看任务列表Url
     */
    private String taskListUrl;
    /*
     * 查看任务处理结果Url
     */
    private String taskResultUrl;
    /*
     * 删除任务Url
     */
    private String deleteTaskUrl;

}
