package com.cb.ai.data.analysis.voucher.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 凭证信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoucherInfo extends BaseDomain {

    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    /**
     * 上传的文件名称
     */
    private String name;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * OCR解析后文本内容
     */
    private String ocrText;

    /**
     * 凭证文件状态 0-未解析 1-解析中 2-解析成功 3-解析失败
     */
    private Integer status;

    /**
     * ocr解析失败信息
     */
    private String ocrErr;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "ocr开始时间")
    private LocalDateTime ocrStartTime;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "ocr结束时间")
    private LocalDateTime ocrEndTime;

    /**
     * 标签名
     */
    @Transient
    @TableField(exist = false)
    private List<String> tags;

}
