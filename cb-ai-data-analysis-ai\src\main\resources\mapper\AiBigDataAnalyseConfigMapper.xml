<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.ai.mapper.AiBigDataAnalyseConfigMapper">
    <resultMap type="com.cb.ai.data.analysis.ai.domain.entity.AiBigDataAnalyseConfig" id="AiBigDataAnalyseConfigMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="depId" column="dep_id" jdbcType="VARCHAR"/>
        <result property="domain" column="domain" jdbcType="VARCHAR"/>
        <result property="analyseType" column="analyse_type" jdbcType="VARCHAR"/>
        <result property="oneType" column="one_type" jdbcType="VARCHAR"/>
        <result property="twoType" column="two_type" jdbcType="VARCHAR"/>
        <result property="tableNames" column="table_names" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

