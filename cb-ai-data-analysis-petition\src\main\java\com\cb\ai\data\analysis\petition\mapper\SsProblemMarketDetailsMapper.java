package com.cb.ai.data.analysis.petition.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsProblemMarketVo;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SsProblemMarketDetailsMapper extends BaseMapper<SsProblemMarketDetailsEntity> {

    IPage<SsProblemMarketDetailsEntity> detailsPage(IPage<SsProblemMarketVo> page, @Param(Constants.WRAPPER) Wrapper<SsProblemMarketVo> wrapper);
}
