.chat-modal-knowledge-wrapper{
  width: 900px;
  height: 40vh;
  .chat-modal-knowledge-header{
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    &_left{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &__return{
        cursor:  pointer;
        font-size: 12px;
        color: #1a73e8;
        margin-right: 10px;
        user-select: none;
        position: relative;
        &::before{
          content: '|';
          position: absolute;
          right: -8px;
          color:rgba(0,0,0,0.15);
        }
      }
    }
    &_right{
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 300px;
    }
  }
  .chat-modal-knowledge-content{
    width: 100%;
    height: calc(40vh - 40px - 10px);
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: flex-start;
    gap: 10px;
    padding: 8px;
    overscroll-behavior: contain;
    scrollbar-gutter: stable;
    .chat-modal-knowledge-item{
      width: calc((100% - 10px * 4) / 5);  // 5列 → 4个间隙
      height: 140px;
      background-color: #fff;
      border: 1px solid rgba(0,0,0,0.15);
      border-radius: 8px;
      padding: 10px 8px;
      display: flex;
      flex-direction: column;
      justify-content: center; // 顶部对齐
      align-items: center;
      box-sizing: border-box;
      position: relative;
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
        z-index: 1;
      }
      //文件夹
      &_folder{
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 54px;
          height: 52px;
          user-select: none;
          -webkit-user-drag: none;
        }
      }
      //文件
      &_file{
        width: 100%;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 46px;
          height: 46px;
          user-select: none;
          -webkit-user-drag: none;
        }
      }
      &_text{
        user-select: none;
        cursor: default;
        text-align: center;
        margin-top: 10px;
        line-height: 1.4;
        span{
          overflow : hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
      }
      &_size{
        text-align: center;
        margin-top: 4px;
        color: rgba(0,0,0,.3);
        user-select: none;
        cursor: default;
      }
      &_check{
        position: absolute;
        right: 10px;
        top: 10px;
        pointer-events: auto;
      }
    }
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f0f0f0;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
      transition: background-color 0.3s;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, 0.35);
    }
  }
  .chat-modal-knowledge-default-content{
    width: 100%;
    height: calc(35vh - 40px - 10px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    &_img{
      width: 168px;
      height: 168px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    &_text{
      margin-top: 10px;
      font-size: 18px;
      color: #666666;
    }
  }
}
