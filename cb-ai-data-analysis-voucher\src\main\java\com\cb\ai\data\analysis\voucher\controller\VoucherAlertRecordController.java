package com.cb.ai.data.analysis.voucher.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherAlertRecordVo;
import com.cb.ai.data.analysis.voucher.service.VoucherAlertRecordService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.framework.utils.QueryHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@RequestMapping(Constants.API_VOUCHER_ROOT_PATH + "/alertRecord")
public class VoucherAlertRecordController extends BaseController<VoucherAlertRecordService, VoucherAlertRecord> {

    @GetMapping("/page")
    public Result page(VoucherAlertRecordVo.PageReq entity){
        Page<VoucherAlertRecordVo.RespItem> page = baseService.pageByEntity(QueryHelper.getPage(), entity);
        return Result.successData(page);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public Result detail(@PathVariable("id") String id){
        return Result.successData(baseService.detail(id));
    }

    /**
     * 分页查询,根据任务id
     * @param taskId
     * @param entity
     * @return
     */
    @GetMapping("/pageByTaskId/{taskId}")
    public Result pageByTaskId(@PathVariable String taskId, VoucherAlertRecordVo.PageReq entity){
        entity.setTaskId(taskId);
        Page<VoucherAlertRecordVo.RespItem> page = baseService.pageByEntity(QueryHelper.getPage(), entity);
        return Result.successData(page);
    }

    /**
     * 根据凭证id获取预警详情
     * @param voucherId
     * @return
     */
    @PostMapping("/detailByVoucherId/{voucherId}")
    public Result detailByVoucherId(@PathVariable("voucherId") String voucherId){
        VoucherAlertRecordVo.RespItem respItem = baseService.detailByVoucherId(voucherId);
        return Result.successData(respItem);
    }

}
