package com.cb.ai.data.analysis.file.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.constant.Constants;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.domain.dto.IdsDto;
import com.cb.ai.data.analysis.file.model.FileUploadInfo;
import com.cb.ai.data.analysis.file.model.UploadUrlsVO;
import com.cb.ai.data.analysis.file.service.AsyncEsService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.exception.XFileException;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(Constants.API_UNI_SUPERVISE_ROOT_PATH + "/resource/file")
public class SuperviseResourceFileController extends BaseController<SuperviseResourceFileService, SuperviseResourceFile> {

    private final SuperviseResourceFolderService superviseResourceFolderService;
    private final AsyncEsService asyncEsService;

    @GetMapping("/getFileListByFolder")
    public Result getFileListByFolder(SuperviseResourceFile superviseResourceFile) {
        QueryWrapper<SuperviseResourceFile> superviseResourceFileXQueryWrapper = new QueryWrapper<>(superviseResourceFile);
        List<SuperviseResourceFile> list = baseService.list(superviseResourceFileXQueryWrapper);
        return Result.successData(list);
    }

    @PutMapping
    public Result update(@Validated(UpdateGroup.class) @RequestBody SuperviseResourceFile superviseResourceFile) {
        boolean b = baseService.updateById(superviseResourceFile);
        if (b) {
            return Result.success("修改成功！");
        }
        return Result.fail("修改失败！");
    }

    @GetMapping
    public Result detail(String id) {
        SuperviseResourceFile byId = baseService.getById(id);
        return Result.successData(byId);
    }

    @DeleteMapping
    @XLog(title = "删除文件", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "文件ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        //更新被删除文件的原路径
        baseService.updateFilesOriginalPath(idList);
        if (baseService.removeByIds(idList)) {
            return Result.success("删除成功");
        }
        return Result.fail("删除失败");
    }

    @PostMapping("/upload")
    public Result upload(MultipartFile file, String folderId, String fileTags, String digestMd5) throws Exception {
        try {
            SuperviseResourceFolder byId = superviseResourceFolderService.getById(folderId);
            if (byId == null) {
                byId = new SuperviseResourceFolder();
                byId.setId("0");
                byId.setFullPath("0");
            }
            /**
             * 临时注释。解决批量上传问题
            LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
            lambda.eq(SuperviseResourceFile::getDigestMd5,digestMd5);
            SuperviseResourceFile one = baseService.getOne(lambda);
            if (one != null) {
                return Result.successData(one);
            }*/
            digestMd5=null;
            SuperviseResourceFile superviseResourceFile = baseService.uploadFile(file, byId, digestMd5,fileTags);
            asyncEsService.asyncInsertToEs(superviseResourceFile.getId());
//        boolean save = baseService.save(superviseResourceFile);
//        if (save) {
//            return Result.successData(superviseResourceFile);
//        }
            return Result.successData(superviseResourceFile);
        }catch (XFileException e){
            throw new Exception(e.getMessage());
        }catch (Exception e){
            throw new Exception("文件上传失败");
        }
    }

    @GetMapping("/download/{fileId}")
    public Result downloadFile(@PathVariable String fileId, HttpServletResponse response) throws IOException {
        String downloadUrl = baseService.getDownloadUrl(fileId);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("downloadUrl", downloadUrl);
        return Result.successData(stringStringHashMap);
    }

    /**
     * 获取已删除的文件
     *
     * @param superviseResourceFile
     * @return
     */
    @GetMapping("/getFileListByAlreadyDeleted")
    public Result getFileListByAlreadyDeleted(SuperviseResourceFile superviseResourceFile) {
        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
        String username = userDetails.getUsername();
        superviseResourceFile.setCreateBy(username);
        Page<SuperviseResourceFile> page = baseService.pageDeletedFiles(QueryHelper.getPage(), superviseResourceFile);
        return Result.successData(page);
    }

    @PostMapping("/restoreFile")
    public Result restoreFile(@RequestBody IdsDto idsDto) {
        baseService.restoreFile(idsDto.getIds(),idsDto.getFolderId());
        return Result.success("还原成功！");
    }

    @DeleteMapping("/permanentlyDelete")
    @XLog(title = "删除文件", execType = ExecType.DELETE)
    public Result permanentlyDelete(@NotEmpty(message = "文件ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        //更新被删除文件的原路径
        if (baseService.permanentlyDelete(idList)) {
            asyncEsService.deleteFileFromEs(idList);
            return Result.success("删除成功");
        }
        return Result.fail("删除失败");
    }

    /**
     * 检查文件是否存在
     */

    @GetMapping("/multipart/check")
    public Result checkFileByMd5(String digestMd5,String folderId,String filename) {
        log.info("查询 <{}> 文件是否存在、是否进行断点续传", digestMd5);
        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
        String username = userDetails.getUsername();
        FileUploadInfo fileUploadInfo = baseService.checkFileByMd5(digestMd5,filename,username,folderId);
        //如果秒传完成，也应该再入一次es
//        fileUploadInfo.getUploadStatus().equals()
        return Result.successData(fileUploadInfo);
    }

    /**
     * 初始化文件分片地址及相关数据
     */
    @PostMapping("/multipart/init")
    public Result initMultiPartUpload(@RequestBody FileUploadInfo fileUploadInfo) {
        log.info("通过 <{}> 初始化上传任务", fileUploadInfo);
        UploadUrlsVO uploadUrlsVO = baseService.initMultipartUpload(fileUploadInfo);
        return Result.successData(uploadUrlsVO);
    }

    /**
     * 文件合并（单文件不会合并，仅信息入库）
     */
    @PostMapping("/multipart/merge")
    public Result mergeMultipartUpload( @RequestBody FileUploadInfo fileUploadInfo) {
        log.info("通过 <{}> 合并上传任务", fileUploadInfo.getDigestMd5());
        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
        String username = userDetails.getUsername();
        SuperviseResourceFile superviseResourceFile = baseService.mergeMultipartUpload(fileUploadInfo.getDigestMd5(),
                fileUploadInfo.getFolderId(),fileUploadInfo.getFilename(),username);
        //插入es
        asyncEsService.asyncInsertToEs(superviseResourceFile.getId());
        return Result.successData(superviseResourceFile);
    }

    /**
     * 下载文件（分片）
     */
//    @GetMapping("/download/{id}")
    public ResponseEntity<byte[]> downloadMultipartFile(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("通过 <{}> 开始分片下载", id);
        byte[] bytes = baseService.downloadMultipartFile(id, request, response);
//        return baseService.downloadMultipartFile(id, request, response);
        return   new ResponseEntity<>(bytes, HttpStatus.OK);
    }


    @GetMapping("/getDownloadUrlByFilePath")
    public Result getDownloadUrlByFilePath(@RequestParam("filePath") String filePath) {
        return Result.successData(baseService.getDownloadUrlByFilePath(filePath));
    }
}
