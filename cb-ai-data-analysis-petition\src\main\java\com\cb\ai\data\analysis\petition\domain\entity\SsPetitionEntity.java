package com.cb.ai.data.analysis.petition.domain.entity;

import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * ss_origin_content
 * <AUTHOR>
@Data
@NoArgsConstructor
public class SsPetitionEntity extends BaseDomain implements Serializable {
    private static final long serialVersionUID = 1L;
    public SsPetitionEntity(Long id, String overview) {
        this.id = id;
        this.overview = overview;
    }


    private Long id;

    /**
     * 信访件编号
     */
    private String petitionNumber;

    /**
     * 姓名
     */
    private String petitionUsername;

    /**
     * 登记部门
     */
    private String registerDeptName;

    /**
     * 信访形式
     */
    private String petitionType;

    /**
     * 信访目的
     */
    private String petitionPurpose;

    /**
     * 问题属地名称
     */
    private String petitionRegion;

    /**
     * 登记日期
     */
    private Date registerDate;

    /**
     * 一级内容分类
     */
    private String categoryFirst;

    /**
     * 二级内容分类
     */
    private String categorySecond;

    /**
     * 三级内容分类
     */
    private String categoryThird;

    /**
     * 初重件
     */
    private String caseType;

    /**
     * 概况信息（投诉内容）
     */
    private String overview;

    /**
     * 是否流程性办结
     */
    private String isFinished;

    /**
     * 国家局信访件编号
     */
    private String petitionCountryCode;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 身份证
     */
    private String certificationCode;

    /**
     * 信访人住址
     */
    private String petitionUserResidenceAddress;

    /**
     * 登记机构名称
     */
    private String registerOrgName;

    /**
     * 登记机构类别
     */
    private String registerOrgType;

    /**
     * 信访日期
     */
    private Date petitionRegisterDate;

    /**
     * 要求办结时间
     */
    private Date petitionDeadlineDate;

    /**
     * 是否扬言
     */
    private String isThreaten;

    /**
     * 是否涉法涉诉
     */
    private String isIllegal;

    /**
     * 办结时间
     */
    private Date finishDate;

    /**
     * 扬言词汇
     */
    private String threatenWords;

    /**
     * 本单位去向机构
     */
    private String selfDeptSendOrgName;

    /**
     * 本单位办理方式名称
     */
    private String selfDeptHandleType;

    /**
     * 办理意见
     */
    private String handleSuggestion;

    /**
     * 出具处理意见单位
     */
    private String handleSuggestionResponsibleOrg;

    /**
     * 处理意见内容
     */
    private String handleSuggestionDetail;

    /**
     * 上传批次号
     */
    private Long batchNo;

    /**
     * 状态 0:未解析摘要 1:解析中 2:解析完成
     */
    private Integer status;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 所属文件名
     */
    private String fileName;
}
