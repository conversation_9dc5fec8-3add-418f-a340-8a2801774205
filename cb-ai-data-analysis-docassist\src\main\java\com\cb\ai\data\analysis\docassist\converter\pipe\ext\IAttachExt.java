package com.cb.ai.data.analysis.docassist.converter.pipe.ext;

import cn.hutool.core.util.ReUtil;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import java.util.List;

/**
 * 附件功能扩展接口
 */

public interface IAttachExt {
    /**
     * 检查前十个段落内是否出现 附件列表
     *
     * @param document 文档
     * @param pos      当前段落位置
     * @return
     */
    default boolean pre10ParagraphHasAttach(XWPFDocument document, Integer pos) {
        boolean hasAttach = false;
        int pre10Pos = Math.max(pos - 10, 0);
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = pos - 1; i >= pre10Pos; i--) {
            String text = paragraphs.get(i).getText().trim();
            if (StringUtils.isBlank(text)) {
                continue;
            }
            if (ReUtil.isMatch("^[  　]*附[  　]{0,2}件[:：].+", text)) {
                hasAttach = true;
                break;
            }
        }
        return hasAttach;
    }
}
