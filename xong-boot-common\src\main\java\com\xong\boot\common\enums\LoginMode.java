package com.xong.boot.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 登录模式
 * <AUTHOR>
 **/
public enum LoginMode {
    /**
     * 密码登录
     */
    PASSWORD,
    /**
     * 短信验证码登录
     */
    SMS,
    /**
     * 退出登录
     */
    LOGOUT;

    /**
     * 根据模块名获取枚举
     * @param value 模块名称
     */
    public static LoginMode getByValue(String value) {
        if (StrUtil.isBlank(value)) {
            return PASSWORD;
        }
        for (LoginMode mode : values()) {
            if (mode.name().equalsIgnoreCase(value)) {
                return mode;
            }
        }
        return PASSWORD;
    }
}
