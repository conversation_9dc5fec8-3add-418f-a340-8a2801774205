package com.cb.ai.data.analysis.ai.domain.common;

import com.cb.ai.data.analysis.ai.utils.JsonUtil;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/9 17:50
 * @Copyright (c) 2025
 * @Description 自定义上下文接口
 */
public class Context implements Cloneable, Serializable {

    private static final long serialVersionUID = 6604615627443393310L;

    @SuppressWarnings("unchecked")
    public <Context> Context copy() {
        try {
            return (Context) super.clone();
        } catch (CloneNotSupportedException e) {
            return (Context) JsonUtil.toBean(this, this.getClass());
        }
    }

}
