package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIKnowledge;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.google.common.base.Throwables;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI问题列表问答扩展
 */
@ExtensionProvider(desc = "私有化AI问题列表问答扩展", businessScenes = {
    @Route(tag = "问题列表问答", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivateQuestionsExtension implements PrivateAIExtension<CommonAIRequestContext> {
    @Override
    public Flux<?> invoke(CommonAIRequestContext aiMergeRequestContext) { return null; }

    @Override
    public Object syncInvoke(CommonAIRequestContext context) {
        Assert.notEmpty(context.getBaseIds(), "知识库ID不能为空");
        Assert.notEmpty(context.getFileData(), "问题列表xlsx文件不能为空");
        List<Question> questionList = new ArrayList<>();
        context.getFileData().forEach((k, list) -> {
            list.forEach(item -> {
                // 读取 Excel
                try (ExcelReader excelReader = ExcelUtil.getReader(item.getInputStream())){
                     questionList.addAll(
                         excelReader
                             .addHeaderAlias("序号", "id")
                             .addHeaderAlias("问题描述", "question")
                             .read(1, 2, Question.class)
                     );
                } catch (Exception e) {
                    throw new RuntimeException(item.getOriginalFilename() + "解析失败：", e);
                }
            });
        });
        List<Question> results = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(questionList)) {
            CountDownLatch countDownLatch = new CountDownLatch(questionList.size());
            questionList.parallelStream().forEach(question -> {
                StringBuilder answerBuilder = new StringBuilder();
                CommonAIRequestContext newContext = context.copy();
                newContext.setPromote(question.getQuestion());
                newContext.setFileData(null);
                Flux<PrivateAIBackData> fluxResult = new PrivateAIKnowledge().process(newContext);
                fluxResult.subscribe(
                    aiBackData -> {
                        if (StrUtil.isNotBlank(aiBackData.getContent())) {
                            answerBuilder.append(aiBackData.getContent());
                        }
                    },
                    error -> CommonLog.error("问题({})问答失败：{}", question.question, Throwables.getRootCause(error)),
                    () -> {
                        results.add(new Question(question.getId(), question.getQuestion(), answerBuilder.toString()));
                        countDownLatch.countDown();
                    }
                );
            });
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return results;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Question {
        private String id;
        private String question;
        private String answer;
    }

}
