package com.cb.ai.data.analysis.ai.common.event;

import com.cb.ai.data.analysis.ai.common.event.model.HistoryChatEvent;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.lmax.disruptor.RingBuffer;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:36
 * @Copyright (c) 2025
 * @Description 流程事件生产者
 */
public class HistoryChatEventProducer {

    private final RingBuffer<HistoryChatEvent> ringBuffer;

    public HistoryChatEventProducer(RingBuffer<HistoryChatEvent> ringBuffer) {
        this.ringBuffer = ringBuffer;
    }

    public void publishEvent(String sessionId, RoleEnum roleEnum, NodeContext data) {
        publishEvent(event -> {
            event.setSessionId(sessionId);
            event.setRoleEnum(roleEnum);
            event.addNodeContext(data);
        });
    }

    public void publishEvent(String sessionId, RoleEnum roleEnum, List<NodeContext> nodeContexts) {
        publishEvent(event -> {
            event.setSessionId(sessionId);
            event.setRoleEnum(roleEnum);
            event.setNodeContextList(nodeContexts);
        });
    }

    private void publishEvent(Consumer<HistoryChatEvent> eventConsumer) {
        long sequence = ringBuffer.next();
        try {
            HistoryChatEvent event = ringBuffer.get(sequence);
            eventConsumer.accept(event);
        } finally {
            ringBuffer.publish(sequence);
        }
    }
}
