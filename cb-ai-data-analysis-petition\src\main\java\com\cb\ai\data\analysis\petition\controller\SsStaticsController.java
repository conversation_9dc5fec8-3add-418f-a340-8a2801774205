package com.cb.ai.data.analysis.petition.controller;



import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionPurposeDateQueryConditionVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.RegisterDateQueryCondition;
import com.cb.ai.data.analysis.petition.domain.vo.request.SelfDeptSendOrgNameQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsStaticsQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo;
import com.cb.ai.data.analysis.petition.service.SsMapStaticsService;
import com.xong.boot.common.api.Result;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/***
 * <AUTHOR>
 * 信访报告图表
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/statics")
public class SsStaticsController {


    /*@Resource
    private WorkFlowProperties workFlowProperties;*/

    /*@Value("${cbxxjs.nl2cypher.baseUrl}")
    private String nl2cypherBaseUrl;*/

    @Resource
    private SsMapStaticsService ssMapStaticsService;

    @PostMapping("/region")
    public Result region(@RequestBody SsStaticsQueryVo ssStaticsQueryVo) {
        List<MapStaticsVo> list = ssMapStaticsService.countByRegion(ssStaticsQueryVo);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success("未查询到统计数据，请检查查询条件或等待解析任务完成");
        }
        return Result.successData(list);
    }

    @PostMapping("/domain")
    public Result domain(@RequestBody SsStaticsQueryVo ssStaticsQueryVo) {
        List<MapStaticsVo> list = ssMapStaticsService.countByDomain(ssStaticsQueryVo);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success("未查询到统计数据，请检查查询条件或等待解析任务完成");
        }
        return Result.successData(list);
    }


    @GetMapping("/select/options/{type}")
    public Result selectOptions(@PathVariable("type") Integer type) {
        return ssMapStaticsService.selectOptions(type);
    }

    @PostMapping("/selfDeptSendOrgName")
    public Result selfDeptSendOrgName(@RequestBody SelfDeptSendOrgNameQueryVo selfDeptSendOrgNameQueryVo) {
        return ssMapStaticsService.countBySelfDeptSendOrgName(selfDeptSendOrgNameQueryVo);
    }

    @PostMapping("/registerDate")
    public Result registerDate(@RequestBody RegisterDateQueryCondition registerDateQueryCondition) {
        return ssMapStaticsService.registerDate(registerDateQueryCondition);
    }

    @GetMapping("/analyzed/status")
    public Result analyzedStatus() {
        return ssMapStaticsService.analyzedStatus();
    }

    @PostMapping("/petition/purpose")
    public Result petitionPurpose(@RequestBody PetitionPurposeDateQueryConditionVo petitionPurposeDateQueryConditionVo) {
        return ssMapStaticsService.petitionPurpose(petitionPurposeDateQueryConditionVo);
    }

    /*@GetMapping("/config")
    public Result getConfig() {
        SysConfigVo sysConfigVo = new SysConfigVo();

        sysConfigVo.setWorkFlowBaseUrl(workFlowProperties.getBaseUrl());
        sysConfigVo.setGenerateReport(workFlowProperties.getGenerateReportId());
        sysConfigVo.setKnowledgeFlowId(workFlowProperties.getKnowledgeFlowId());

        sysConfigVo.setNl2cypherBaseUrl(nl2cypherBaseUrl);

        return Result.successData(sysConfigVo);
    }*/

}
