package com.cb.ai.data.analysis.query.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.domain.bo.DynamicFieldMapping;
import com.cb.ai.data.analysis.query.domain.vo.IndexInfo;

import java.util.List;

/**
 * ES 索引操作服务
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
public interface IndexService {
    /**
     * 索引列表
     * @return
     */

    Page<IndexInfo> indexManageList(IndexInfo info);

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @param mappings  分词映射
     * @return
     */
    boolean createIndex(String indexName, List<DynamicFieldMapping> mappings);

    /**
     * 添加字段映射
     *
     * @param indexName
     * @param mappings
     * @return
     */
    boolean addFieldMapping(String indexName, List<DynamicFieldMapping> mappings);

    /**
     * 迁移数据到新索引
     *
     * @param sourceIndex 源索引名称
     * @param destIndex   目标索引名称
     * @return
     */
    public Long migrationData(String sourceIndex, String destIndex);

    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return
     */
    boolean deleteIndex(String indexName);

    /**
     * 查看索引是否存在
     *
     * @param indexName 索引名称
     * @return
     */
    boolean existsIndex(String indexName);

}
