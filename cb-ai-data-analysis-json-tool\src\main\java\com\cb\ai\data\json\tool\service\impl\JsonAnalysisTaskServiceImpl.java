package com.cb.ai.data.json.tool.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.json.tool.constants.Constant;
import com.cb.ai.data.json.tool.domain.JsonAnalysisTask;
import com.cb.ai.data.json.tool.enums.AnalysisStatus;
import com.cb.ai.data.json.tool.mapper.JsonAnalysisTaskMapper;
import com.cb.ai.data.json.tool.service.JsonAnalysisTaskService;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;

@Service
public class JsonAnalysisTaskServiceImpl extends ServiceImpl<JsonAnalysisTaskMapper, JsonAnalysisTask> implements JsonAnalysisTaskService {
    @Override
    public int saveTask(JsonAnalysisTask task) {
        return baseMapper.insert(task);
    }

    @Override
    public JsonAnalysisTask saveTask(String jsonToolDir, MultipartFile originalFile, File uploadFile, File extractFile) {
        // 保存解析任务
        JsonAnalysisTask task = new JsonAnalysisTask(
                jsonToolDir,
                originalFile.getOriginalFilename(),
                uploadFile.getName(),
                extractFile.getName(),
                AnalysisStatus.PROCESSING.getStatus(),
                LocalDateTime.now()
        );
        baseMapper.insert(task);
        return task;
    }

    @Override
    public int deleteTask(String taskId) {
        JsonAnalysisTask task = baseMapper.selectById(taskId);
        String basePath = task.getBasePath();
        String sourceFileName = task.getSourceFileName();
        String extractFileName = task.getExtractFileName();

        File extractFileDir = new File(basePath + File.separator + extractFileName);
        try {
            FileUtils.deleteDirectory(extractFileDir);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        File sourceFile = new File(basePath + File.separator + sourceFileName);
        if (sourceFile.exists()) {
            sourceFile.delete();
        }
        return baseMapper.deleteById(taskId);
    }
}
