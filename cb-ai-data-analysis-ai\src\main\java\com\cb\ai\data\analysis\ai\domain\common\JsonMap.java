package com.cb.ai.data.analysis.ai.domain.common;

import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Filter;
import cn.hutool.core.lang.mutable.MutablePair;
import cn.hutool.core.map.CaseInsensitiveLinkedMap;
import cn.hutool.core.map.CaseInsensitiveTreeMap;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.map.MapWrapper;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.*;
import cn.hutool.json.serialize.JSONWriter;
import com.cb.ai.data.analysis.ai.domain.func.GetConsumer;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.RefUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2024/12/27 14:30
 * @Copyright Copyright (c) 2024
 * @Description 自定义的JsonMap，参考hutool的JsonMap，简化不常用功能，增加自定义扩展
 */
public class JsonMap extends MapWrapper<String, Object> implements JSON, JSONGetter<String> {
    private static final long serialVersionUID = -330220388580734346L;
    /** 默认初始大小 */
    public static final int DEFAULT_CAPACITY = MapUtil.DEFAULT_INITIAL_CAPACITY;
    /** 配置项 */
    private JSONConfig config;

    /**
     * 构造，初始容量为 {@link #DEFAULT_CAPACITY}，KEY无序
     */
    public JsonMap() {
        this(JSONConfig.create().setIgnoreNullValue(false));
    }

    /**
     * 构造
     * @param config JSON配置项
     */
    public JsonMap(JSONConfig config) {
        this(DEFAULT_CAPACITY, config);
    }

    /**
     * 构造
     * @param capacity 初始大小
     * @param config   JSON配置项，{@code null}则使用默认配置
     */
    public JsonMap(int capacity, JSONConfig config) {
        super(createRawMap(capacity, ObjectUtil.defaultIfNull(config, JSONConfig.create())));
        this.config = ObjectUtil.defaultIfNull(config, JSONConfig.create());
    }

    /**
     * 构建JsonMap，JavaBean默认忽略null值，其它对象不忽略，规则如下：
     * <ol>
     * <li>value为Map，将键值对加入JSON对象</li>
     * <li>value为JSON字符串（CharSequence），使用JSONTokener解析</li>
     * <li>value为JSONTokener，直接解析</li>
     * <li>value为普通JavaBean，如果为普通的JavaBean，调用其getters方法（getXXX或者isXXX）获得值，加入到JSON对象。
     * 例如：如果JavaBean对象中有个方法getName()，值为"张三"，获得的键值对为：name: "张三"</li>
     * </ol>
     * @param source JavaBean或者Map对象或者String
     */
    public JsonMap(Object source) {
        this(source, (!(source instanceof CharSequence)) && (!(source instanceof JSONTokener)) && (!(source instanceof Map)));
    }

    /**
     * 构建JsonMap，规则如下：
     * @param source          JavaBean或者Map对象或者String
     * @param ignoreNullValue 是否忽略空值
     */
    public JsonMap(Object source, boolean ignoreNullValue) {
        this(source, JSONConfig.create().setIgnoreNullValue(ignoreNullValue));
    }

    /**
     * 构建JsonMap，规则如下：
     * @param source          JavaBean或者Map对象或者String
     */
    public JsonMap(Object source, JSONConfig jsonConfig) {
        this(DEFAULT_CAPACITY, jsonConfig);
        if (source != null) {
            putAll(JsonUtil.toMap(source));
        }
    }

    public static JsonMap of(String key, Object value) {
        return new JsonMap().putOpt(key, value);
    }

    public static JsonMap of(String key, Object value, Predicate<Object> predicate) {
        JsonMap entries = new JsonMap();
        if (predicate.test(value)) {
            entries.putOpt(key, value);
        }
        return entries;
    }

    public static <K, V> JsonMap of(GetConsumer<K> key, Supplier<V> value) {
        return new JsonMap().putOpt(key, value);
    }

    public static <K, V> JsonMap of(GetConsumer<K> key, Supplier<V> value, Predicate<V> predicate) {
        JsonMap entries = new JsonMap();
        if (predicate.test(value.get())) {
            entries.putOpt(key, value);
        }
        return entries;
    }

    public <K> boolean containsKey(GetConsumer<K> key) {
        for (String lambdaName : getLambdaNames(key)) {
            if (super.containsKey(lambdaName)) {
                return true;
            }
        }
        return false;
    }

    public <K> boolean containsValue(GetConsumer<K> value) {
        return super.containsValue(value);
    }

    public <K, V> V get(GetConsumer<K> key) {
        return this.getObj(key, null);
    }

    public <K> JsonMap getJsonMap(GetConsumer<K> key) {
        return this.getJsonMap(CommonUtil.getLambdaName(key));
    }

    public JsonMap getJsonMap(String key) {
        Object value = getObj(key, null);
        if (value instanceof JsonMap) {
            return (JsonMap) value;
        } else {
            return new JsonMap(value);
        }
    }

    @SuppressWarnings("unchecked")
    public <K, V> V getObj(GetConsumer<K> key, Supplier<V> defaultValue) {
        V v;
        for (String lambdaName : getLambdaNames(key)) {
            v = (V) super.get(lambdaName);
            if (v != null) {
                return v;
            }
        }
        return defaultValue == null? null : defaultValue.get();
    }

    public <K> Character getChar(GetConsumer<K> key) {
        return this.getChar(key, null);
    }

    public <K> Character getChar(GetConsumer<K> key, Character defaultValue) {
        return getChar(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> String getStr(GetConsumer<K> key) {
        return this.getStr(key, null);
    }

    public <K> String getStr(GetConsumer<K> key, String defaultValue) {
        return getStr(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> String getJsonStr(GetConsumer<K> key) {
        return this.getJsonStr(key, null);
    }

    public <K> String getJsonStr(GetConsumer<K> key, String defaultValue) {
        return getJsonStr(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> String getJsonStr(String key) {
        return this.getJsonStr(key, null);
    }

    public <K> String getJsonStr(String key, String defaultValue) {
        return Optional.ofNullable(get(key))
            .map(JsonUtil::toStr)
            .orElse(defaultValue);
    }

    public <K> Integer getInt(GetConsumer<K> key) {
        return this.getInt(key, null);
    }

    public <K> Integer getInt(GetConsumer<K> key, Integer defaultValue) {
        return getInt(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Short getShort(GetConsumer<K> key) {
        return this.getShort(key, null);
    }

    public <K> Short getShort(GetConsumer<K> key, Short defaultValue) {
        return getShort(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Long getLong(GetConsumer<K> key) {
        return this.getLong(key, null);
    }

    public <K> Long getLong(GetConsumer<K> key, Long defaultValue) {
        return getLong(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> BigInteger getBigInteger(GetConsumer<K> key) {
        return this.getBigInteger(key, null);
    }

    public <K> BigInteger getBigInteger(GetConsumer<K> key, BigInteger defaultValue) {
        return getBigInteger(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Double getDouble(GetConsumer<K> key) {
        return this.getDouble(key, null);
    }

    public <K> Double getDouble(GetConsumer<K> key, Double defaultValue) {
        return getDouble(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Float getFloat(GetConsumer<K> key) {
        return this.getFloat(key, null);
    }

    public <K> Float getFloat(GetConsumer<K> key, Float defaultValue) {
        return getFloat(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> BigDecimal getBigDecimal(GetConsumer<K> key) {
        return this.getBigDecimal(key, null);
    }

    public <K> BigDecimal getBigDecimal(GetConsumer<K> key, BigDecimal defaultValue) {
        return getBigDecimal(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Boolean getBool(GetConsumer<K> key) {
        return this.getBool(key, null);
    }

    public <K> Boolean getBool(GetConsumer<K> key, Boolean defaultValue) {
        return getBool(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Byte getByte(GetConsumer<K> key) {
        return this.getByte(key, null);
    }

    public <K> Byte getByte(GetConsumer<K> key, Byte defaultValue) {
        return getByte(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <K> Date getDate(GetConsumer<K> key) {
        return this.getDate(key, null);
    }

    public <K> Date getDate(GetConsumer<K> key, Date defaultValue) {
        return getDate(CommonUtil.getLambdaName(key), defaultValue);
    }

    public <T extends Iterable<V>, K, V> T getCollection(GetConsumer<K> key, TypeReference<T> reference) {
        return JsonUtil.toBean(get(key), reference);
    }

    public <T extends Iterable<V>, K, V> T getCollection(String key, TypeReference<T> reference) {
        return JsonUtil.toBean(get(key), reference);
    }

    public <T extends Map<K, V>, K, V> T getMap(GetConsumer<K> key, TypeReference<T> reference) {
        return JsonUtil.toBean(get(key), reference);
    }

    public <T extends Map<K, V>, K, V> T getMap(String key, TypeReference<T> reference) {
        return JsonUtil.toBean(get(key), reference);
    }

    public <K, V> JsonMap put(GetConsumer<K> key, Supplier<V> value)  {
        return this.set(CommonUtil.getLambdaName(key), value == null? null : value.get());
    }

    public <K, V> JsonMap putOnce(GetConsumer<K> key, Supplier<V> value)  {
        return this.putOnce(CommonUtil.getLambdaName(key), value == null? null : value.get());
    }

    public <K, V> JsonMap putOpt(GetConsumer<K> key, Supplier<V> value)  {
        return this.putOpt(CommonUtil.getLambdaName(key), value == null? null : value.get());
    }

    public <K, V> JsonMap accumulate(GetConsumer<K> key, Supplier<V> value)  {
        return this.accumulate(CommonUtil.getLambdaName(key), value == null? null : value.get());
    }

    public <K, V> JsonMap append(GetConsumer<K> key, Supplier<V> value)  {
        return this.append(CommonUtil.getLambdaName(key), value == null? null : value.get());
    }

    public <K> JsonMap increment(GetConsumer<K> key)  {
        return this.increment(CommonUtil.getLambdaName(key));
    }

    public <K> Object remove(GetConsumer<K> key) {
        return super.remove(CommonUtil.getLambdaName(key));
    }

    @Override
    public JSONConfig getConfig() {
        return this.config;
    }

    /**
     * 设置转为字符串时的日期格式，默认为时间戳（null值）<br>
     * 此方法设置的日期格式仅对转换为JSON字符串有效，对解析JSON为bean无效。
     *
     * @param format 格式，null表示使用时间戳
     * @return this
     * @since 4.1.19
     */
    public JsonMap setDateFormat(String format) {
        this.config.setDateFormat(format);
        return this;
    }

    /**
     * 将指定KEY列表的值组成新的JSONArray
     *
     * @param names KEY列表
     * @return A JSONArray of values.
     * @ If any of the values are non-finite numbers.
     */
    public JSONArray toJSONArray(Collection<String> names)  {
        if (CollectionUtil.isEmpty(names)) {
            return null;
        }
        final JSONArray ja = new JSONArray(this.config);
        Object value;
        for (String name : names) {
            value = this.get(name);
            if (null != value) {
                ja.set(value);
            }
        }
        return ja;
    }

    @Override
    public Object getObj(String key, Object defaultValue) {
        return this.getOrDefault(key, defaultValue);
    }

    @Override
    public Object getByPath(String expression) {
        return BeanPath.create(expression).get(this);
    }

    @Override
    public <T> T getByPath(String expression, Class<T> resultType) {
        return JsonUtil.toBean(getByPath(expression), resultType);
    }

    @Override
    public void putByPath(String expression, Object value) {
        BeanPath.create(expression).set(this, value);
    }

    /**
     * 设置键值对到JsonMap中，在忽略null模式下，如果值为{@code null}，将此键移除
     *
     * @param key   键
     * @param value 值对象. 可以是以下类型: Boolean, Double, Integer, JSONArray, JsonMap, Long, String, or the JSONNull.NULL.
     * @return this.
     * @ 值是无穷数字抛出此异常
     */
    public JsonMap set(String key, Object value)  {
        return set(key, value, null, false);
    }

    /**
     * 设置键值对到JsonMap中，在忽略null模式下，如果值为{@code null}，将此键移除
     *
     * @param key            键
     * @param value          值对象. 可以是以下类型: Boolean, Double, Integer, JSONArray, JsonMap, Long, String, or the JSONNull.NULL.
     * @param filter         键值对过滤编辑器，可以通过实现此接口，完成解析前对键值对的过滤和修改操作，{@code null}表示不过滤
     * @param checkDuplicate 是否检查重复键，如果为{@code true}，则出现重复键时抛出{@link JSONException}异常
     * @return this.
     * @ 值是无穷数字抛出此异常
     * @since 5.8.0
     */
    public JsonMap set(String key, Object value, Filter<MutablePair<String, Object>> filter, boolean checkDuplicate)  {
        if (null == key) {
            return this;
        }

        // 添加前置过滤，通过MutablePair实现过滤、修改键值对等
        if (null != filter) {
            final MutablePair<String, Object> pair = new MutablePair<>(key, value);
            if (filter.accept(pair)) {
                // 使用修改后的键值对
                key = pair.getKey();
                value = pair.getValue();
            } else {
                // 键值对被过滤
                return this;
            }
        }

        final boolean ignoreNullValue = this.config.isIgnoreNullValue();
        if (ObjectUtil.isNull(value) && ignoreNullValue) {
            // 忽略值模式下如果值为空清除key
            this.remove(key);
        } else {
            if (checkDuplicate && containsKey(key)) {
                throw new JSONException("Duplicate key \"{}\"", key);
            }
            super.put(key, value);
        }
        return this;
    }

    /**
     * 一次性Put 键值对，如果key已经存在抛出异常，如果键值中有null值，忽略
     *
     * @param key   键
     * @param value 值对象，可以是以下类型: Boolean, Double, Integer, JSONArray, JsonMap, Long, String, or the JSONNull.NULL.
     * @return this.
     * @ 值是无穷数字、键重复抛出异常
     */
    public JsonMap putOnce(String key, Object value)  {
        return setOnce(key, value, null);
    }

    /**
     * 一次性Put 键值对，如果key已经存在抛出异常，如果键值中有null值，忽略
     *
     * @param key    键
     * @param value  值对象，可以是以下类型: Boolean, Double, Integer, JSONArray, JsonMap, Long, String, or the JSONNull.NULL.
     * @param filter 键值对过滤编辑器，可以通过实现此接口，完成解析前对键值对的过滤和修改操作，{@code null}表示不过滤
     * @return this
     * @ 值是无穷数字、键重复抛出异常
     * @since 5.8.0
     */
    public JsonMap setOnce(String key, Object value, Filter<MutablePair<String, Object>> filter)  {
        return set(key, value, filter, true);
    }

    /**
     * 在键和值都为非空的情况下put到JsonMap中
     *
     * @param key   键
     * @param value 值对象，可以是以下类型: Boolean, Double, Integer, JSONArray, JsonMap, Long, String, or the JSONNull.NULL.
     * @return this.
     * @ 值是无穷数字
     */
    public <V> JsonMap putOpt(String key, V value) {
        if (key != null && value != null) {
            this.set(key, value);
        }
        return this;
    }

    public <K, V> JsonMap putOpt(GetConsumer<K> key, Supplier<V> value, Predicate<V> predicate) {
        return putOpt(CommonUtil.getLambdaName(key), value.get(), predicate);
    }

    public <V> JsonMap putOpt(String key, V value, Predicate<V> predicate) {
        if (key == null) {
            return this;
        }
        if (predicate != null) {
            if (!predicate.test(value)) {
                return this;
            }
        } else if (value == null) {
            return this;
        }
        this.set(key, value);
        return this;
    }

    @Override
    public void putAll(Map<? extends String, ?> m) {
        for (Entry<? extends String, ?> entry : m.entrySet()) {
            if (config != null && config.isIgnoreNullValue()) {
                if (entry.getValue() == null) {
                    continue;
                }
            }
            this.set(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 积累值。类似于set，当key对应value已经存在时，与value组成新的JSONArray. <br>
     * 如果只有一个值，此值就是value，如果多个值，则是添加到新的JSONArray中
     *
     * @param key   键
     * @param value 被积累的值
     * @return this.
     * @ 如果给定键为{@code null}或者键对应的值存在且为非JSONArray
     */
    public JsonMap accumulate(String key, Object value)  {
        Object object = this.getObj(key);
        if (object == null) {
            this.set(key, value);
        } else if (object instanceof JSONArray) {
            ((JSONArray) object).set(value);
        } else {
            this.set(key, JSONUtil.createArray(this.config).set(object).set(value));
        }
        return this;
    }

    /**
     * 追加值，如果key无对应值，就添加一个JSONArray，其元素只有value，如果值已经是一个JSONArray，则添加到值JSONArray中。
     *
     * @param key   键
     * @param value 值
     * @return this.
     * @ 如果给定键为{@code null}或者键对应的值存在且为非JSONArray
     */
    public JsonMap append(String key, Object value)  {
        Object object = this.getObj(key);
        if (object == null) {
            this.set(key, new JSONArray(this.config).set(value));
        } else if (object instanceof JSONArray) {
            this.set(key, ((JSONArray) object).set(value));
        } else {
            throw new JSONException("JsonMap [" + key + "] is not a JSONArray.");
        }
        return this;
    }

    /**
     * 对值加一，如果值不存在，赋值1，如果为数字类型，做加一操作
     *
     * @param key A key string.
     * @return this.
     * @ 如果存在值非Integer, Long, Double, 或 Float.
     */
    public JsonMap increment(String key)  {
        Object value = this.getObj(key);
        if (value == null) {
            this.set(key, 1);
        } else if (value instanceof BigInteger) {
            this.set(key, ((BigInteger) value).add(BigInteger.ONE));
        } else if (value instanceof BigDecimal) {
            this.set(key, ((BigDecimal) value).add(BigDecimal.ONE));
        } else if (value instanceof Integer) {
            this.set(key, (Integer) value + 1);
        } else if (value instanceof Long) {
            this.set(key, (Long) value + 1);
        } else if (value instanceof Double) {
            this.set(key, (Double) value + 1);
        } else if (value instanceof Float) {
            this.set(key, (Float) value + 1);
        } else {
            throw new JSONException("Unable to increment [" + JSONUtil.quote(key) + "].");
        }
        return this;
    }

    /**
     * 返回JSON字符串<br>
     * 如果解析错误，返回{@code null}
     *
     * @return JSON字符串
     */
    @Override
    public String toString() {
        return this.toJSONString(0);
    }

    /**
     * 返回JSON字符串<br>
     * 支持过滤器，即选择哪些字段或值不写出
     *
     * @param indentFactor 每层缩进空格数
     * @param filter       过滤器，同时可以修改编辑键和值
     * @return JSON字符串
     * @since 5.7.15
     */
    public String toJSONString(int indentFactor, Filter<MutablePair<Object, Object>> filter) {
        final StringWriter sw = new StringWriter();
        synchronized (sw.getBuffer()) {
            return this.write(sw, indentFactor, 0, filter).toString();
        }
    }

    @Override
    public Writer write(Writer writer, int indentFactor, int indent)  {
        return write(writer, indentFactor, indent, null);
    }

    /**
     * 将JSON内容写入Writer<br>
     * 支持过滤器，即选择哪些字段或值不写出
     *
     * @param writer       writer
     * @param indentFactor 缩进因子，定义每一级别增加的缩进量
     * @param indent       本级别缩进量
     * @param filter       过滤器，同时可以修改编辑键和值
     * @return Writer
     * @ JSON相关异常
     * @since 5.7.15
     */
    public Writer write(Writer writer, int indentFactor, int indent, Filter<MutablePair<Object, Object>> filter)  {
        final JSONWriter jsonWriter = JSONWriter.of(writer, indentFactor, indent, config)
                .beginObj();
        this.forEach((key, value) -> jsonWriter.writeField(new MutablePair<>(key, value), filter));
        jsonWriter.end();
        // 此处不关闭Writer，考虑writer后续还需要填内容
        return writer;
    }

    @Override
    public JsonMap clone() throws CloneNotSupportedException {
        final JsonMap clone = (JsonMap) super.clone();
        clone.config = this.config;
        return clone;
	}

    private String[] getLambdaNames(Serializable getter) {
        Class<?> zclass = CommonUtil.getLambdaClass(getter);
        String[] results = new String[] {CommonUtil.getLambdaName(getter)};
        Field curField = RefUtil.getDeclaredField(zclass, results[0]);
        JsonAlias alias = curField.getAnnotation(JsonAlias.class);
        if (alias != null && ArrayUtil.isNotEmpty(alias.value())) {
            results = ArrayUtil.append(results, alias.value());
        }
        return results;
    }

    static Map<String, Object> createRawMap(int capacity, JSONConfig config) {
        final Map<String, Object> rawHashMap;
        if (null == config) {
            config = JSONConfig.create();
        }
        final Comparator<String> keyComparator = config.getKeyComparator();
        if (config.isIgnoreCase()) {
            if (null != keyComparator) {
                rawHashMap = new CaseInsensitiveTreeMap<>(keyComparator);
            } else {
                rawHashMap = new CaseInsensitiveLinkedMap<>(capacity);
            }
        } else {
            if (null != keyComparator) {
                rawHashMap = new TreeMap<>(keyComparator);
            } else {
                rawHashMap = new LinkedHashMap<>(capacity);
            }
        }
        return rawHashMap;
    }
}

