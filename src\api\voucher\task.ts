import { http } from '@/utils/http'

const prefix = '/api/voucher/task'

/**
 * 分页获取分析任务列表
 * @param params 查询条件
 */
export function page(params?: Record<string, any>){
  return http.get(`${prefix}/page`, { params })
}

/**
 * 分页获取当前用户的分析任务列表
 * @param params 查询条件
 */
export function pageBySelf(params?: Record<string, any>){
  return http.get(`${prefix}/pageBySelf`, { params })
}

/**
 * 新增分析任务
 * @param data 任务数据
 */
export function add(data: Record<string, any>){
  return http.post(prefix, data)
}

/**
 * 运行分析任务
 * @param id 任务ID
 */
export function runTask(id: string){
  return http.post(`${prefix}/runTask/${id}`)
}

/**
 * 删除分析任务
 * @param id 任务ID
 */
export function del(id: string){
  return http.delete(`${prefix}/${id}`)
}
