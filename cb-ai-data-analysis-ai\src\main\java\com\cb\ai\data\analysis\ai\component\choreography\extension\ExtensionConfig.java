package com.cb.ai.data.analysis.ai.component.choreography.extension;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:02
 * @Copyright (c) 2025
 * @Description 扩展点配置注解
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExtensionConfig {
    /**
     * 扩展点描述
     */
    String desc();

    /**
     * 扩展点类型
     */
    ExtensionType type();

    /**
     * 是否必须
     */
    boolean must() default false;
}
