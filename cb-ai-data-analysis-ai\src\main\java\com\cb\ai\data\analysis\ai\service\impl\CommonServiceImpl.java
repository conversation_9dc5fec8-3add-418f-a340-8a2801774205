package com.cb.ai.data.analysis.ai.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionManager;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessScene;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.extensions.PrivateAIExtension;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.request.context.ExtendCommonAIRequestContext;
import com.cb.ai.data.analysis.ai.service.AiChatHistoryService;
import com.cb.ai.data.analysis.ai.service.ICommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:21
 * @Copyright (c) 2025
 * @Description TODO
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonServiceImpl implements ICommonService {

    private final AiChatHistoryService aiChatHistoryService;


    @Override
    @SuppressWarnings("unchecked")
    public Flux<?> invoke(ExtendCommonAIRequestContext<?> requestContext) {
        if (StrUtil.isNotBlank(requestContext.getSessionId())) {
            CompletableFuture.supplyAsync(() -> aiChatHistoryService.initAiChatHistory(requestContext.getSessionId()));
        }
        return ExtensionManager.getExtension(businessScene(requestContext), PrivateAIExtension.class).invoke(requestContext);

    }

    @Override
    public Object syncInvoke(ExtendCommonAIRequestContext<?> requestContext) {
        return ExtensionManager.getExtension(businessScene(requestContext), PrivateAIExtension.class).syncInvoke(requestContext);
    }

    private BusinessScene businessScene(CommonAIRequestContext context) {
        //优先处理深度思考
        if (context.isDeepThink()) {
            return BusinessScene.of("深度思考", BusinessTypeEnum.PRIVATE_BUSINESS);
        } else if (ArrayUtil.isEmpty(context.getAnalyseTag())) {
            if (ArrayUtil.isNotEmpty(context.getBaseIds()) || ArrayUtil.isNotEmpty(context.getFileIds())) {
                return BusinessScene.of("知识库问答", BusinessTypeEnum.PRIVATE_BUSINESS);
            } else {
                return BusinessScene.of("基础问答", BusinessTypeEnum.PRIVATE_BUSINESS);
            }
        } else {
            // 此处有一个点， 如果同时传两个标签， 则只取第一个，例如 ["生成报告", "MCP"]， 只取"生成报告"
            String analyseTag = context.getAnalyseTag()[0];
            String[] analyseTags = analyseTag.split("/");
            if (analyseTags.length == 1) {
                return BusinessScene.of(analyseTag, BusinessTypeEnum.PRIVATE_BUSINESS);
            } else {
                return BusinessScene.of(analyseTags[0], BusinessTypeEnum.PRIVATE_BUSINESS);
            }
        }
    }
}
