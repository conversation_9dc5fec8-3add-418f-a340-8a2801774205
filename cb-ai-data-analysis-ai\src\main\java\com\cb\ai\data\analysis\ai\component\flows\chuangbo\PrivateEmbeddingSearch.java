package com.cb.ai.data.analysis.ai.component.flows.chuangbo;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.common.properties.PrivateAIBaseProperties;
import com.cb.ai.data.analysis.ai.component.choreography.engine.BaseNode;
import com.cb.ai.data.analysis.ai.component.choreography.engine.ISyncNode;
import com.cb.ai.data.analysis.ai.component.http.HttpClient;
import com.cb.ai.data.analysis.ai.component.http.OkHttpsClient;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.EmbeddingResult;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import com.xong.boot.common.exception.CustomException;
import org.springframework.http.HttpMethod;

import java.util.Comparator;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/4 13:56
 * @Copyright (c) 2025
 * @Description 向量库查询接口
 */
public class PrivateEmbeddingSearch extends BaseNode implements ISyncNode<CommonAIRequestContext, List<EmbeddingResult>> {

    private final PrivateAIBaseProperties aiProp;

    public PrivateEmbeddingSearch() {
        this.aiProp = SpringUtil.getBean(PrivateAIBaseProperties.class);
    }

    @Override
    public String getNodeName() {
        return "私有化-向量库查询-接口";
    }

    @Override
    public List<EmbeddingResult> syncProcess(CommonAIRequestContext requestContext) {
        JsonMap requestBody = JsonMap.of(CommonAIRequestContext::getBaseIds, requestContext::getBaseIds, ObjectUtil::isNotEmpty)
            .putOpt(CommonAIRequestContext::getFileIds, requestContext::getFileIds, ObjectUtil::isNotEmpty)
            .putOpt(CommonAIRequestContext::getPromote, requestContext::getPromote, ObjectUtil::isNotEmpty)
            .putOpt(CommonAIRequestContext::isUsePresetScoreThreshold, requestContext::isUsePresetScoreThreshold)
            .putOpt(CommonAIRequestContext::getTopK, requestContext::getTopK, ObjectUtil::isNotEmpty)
            .putOpt("similarityHolds", 0.4)
            .putOpt("milvusDbType", 0);

        HttpClient httpClient = new OkHttpsClient();
        return httpClient.url(MergeUtil.mergePath(aiProp.getBaseUrl(), aiProp.getEmbeddingUrl()))
                .method(HttpMethod.POST)
                .headers(headers -> headers.setAll(aiProp.getHeaderMap()))
                .body(requestBody)
                .onError(e -> new CustomException("向量库查询接口调用失败, 原因：" + e.getMessage(), e))
                .onSuccess(data -> {
                    List<EmbeddingResult> list = JsonUtil.toList(data, EmbeddingResult.class);
                    // 按文本长度从大到小排序
                    list.sort(Comparator.comparingInt((EmbeddingResult er) -> removeSpace(er.getText()).length()).reversed());
                    // 设置索引，从1开始
                    IntStream.range(0, list.size()).forEach(i -> {
                        EmbeddingResult embeddingResult = list.get(i);
                        embeddingResult.setIndex(i + 1).setText(removeSpace(embeddingResult.getText()));
                    });
                    return list;
                })
                .executeBlock();
    }

    private String removeSpace(String text) {
        return text.replaceAll("[\r\n\t]*", "").replaceAll("\\s*", "");
    }

}
