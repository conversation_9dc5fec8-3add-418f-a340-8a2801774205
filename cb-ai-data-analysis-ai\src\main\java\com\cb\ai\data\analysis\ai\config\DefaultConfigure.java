package com.cb.ai.data.analysis.ai.config;

import cn.hutool.extra.spring.EnableSpringUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.common.event.HistoryChatEventProducer;
import com.cb.ai.data.analysis.ai.common.registry.AIPropertiesRegistryBase;
import com.cb.ai.data.analysis.ai.common.registry.ExtensionManagerRegistryBase;
import com.cb.ai.data.analysis.ai.common.registry.StringToTimestampLocalDateTimeConverter;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChainService;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 15:50
 * @Copyright (c) 2025
 * @Description 默认的Configure配置
 */
@Configuration
@EnableWebMvc
@EnableSpringUtil
public class DefaultConfigure implements WebMvcConfigurer {

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 设置超时时间为30分钟（单位：毫秒）
        configurer.setDefaultTimeout(30 * 60 * 1000);
        configurer.setTaskExecutor(SpringUtil.getBean("taskExecutor"));
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new StringToTimestampLocalDateTimeConverter());
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder.setConnectTimeout(Duration.ofSeconds(300))
            .setReadTimeout(Duration.ofSeconds(300))
            .additionalMessageConverters(
                // 确保有JSON转换器
                new MappingJackson2HttpMessageConverter(),
                // 添加文本转换器
                new StringHttpMessageConverter()
            )
            .build();
    }

    @Bean
    @DependsOn("historyChatEventProducer")
    public FlowChainService flowChainService(HistoryChatEventProducer historyChatEventProducer) {
        return new FlowChainService(historyChatEventProducer);
    }

    @Bean
    public ExtensionManagerRegistryBase extensionManagerRegistry() {
        return new ExtensionManagerRegistryBase();
    }

    @Bean
    public AIPropertiesRegistryBase aIPropertiesRegistry() {
        return new AIPropertiesRegistryBase();
    }


}
