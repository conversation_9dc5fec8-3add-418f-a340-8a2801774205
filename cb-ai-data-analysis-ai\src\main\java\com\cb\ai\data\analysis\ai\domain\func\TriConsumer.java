package com.cb.ai.data.analysis.ai.domain.func;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/13 18:21
 * @Copyright (c) 2025
 * @Description 自定义三元无返回函数
 */
@FunctionalInterface
public interface TriConsumer<T, U, O> {

    void accept(T t, U u, O o);

    default TriConsumer<T, U, O> andThen(TriConsumer<? super T, ? super U, ? super O> after) {
        Objects.requireNonNull(after);

        return (l, r, y) -> {
            accept(l, r, y);
            after.accept(l, r, y);
        };
    }
}
