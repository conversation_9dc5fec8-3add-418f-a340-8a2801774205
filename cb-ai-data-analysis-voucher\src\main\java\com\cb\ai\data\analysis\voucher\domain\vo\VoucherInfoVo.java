package com.cb.ai.data.analysis.voucher.domain.vo;

import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

public interface VoucherInfoVo {

    /**
     * 分页查询参数
     */
    @Data
    class PageReq extends VoucherInfo{
        //起始时间
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date beginDate;
        //截止时间
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date endDate;
    }

    /**
     * 批量设置标签
     */
    @Data
    class BatchTagReq{
        @NotEmpty(message = "请选择凭证")
        private Set<String> infoIds;
        @NotEmpty(message = "请设置标签")
        private String[] tags;
    }

}
