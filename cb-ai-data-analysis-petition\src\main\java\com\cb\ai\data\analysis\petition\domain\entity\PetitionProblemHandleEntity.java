package com.cb.ai.data.analysis.petition.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;

/***
 * <AUTHOR>
 * 问题批处理问题列表实体
 */
@Data
@TableName("ss_petition_problem_handle")
public class PetitionProblemHandleEntity extends BaseDomain implements Serializable {

    /***
     * 主键IDD
     */
    private String id;

    /***
     * 知识库ID
     */
    private String baseId;

    /***
     * 文件ID
     */
    private String fileId;

    /***
     * 文件名称
     */
    private String fileName;
    /***
     * 文件路径
     */
    private String filePath;

    /***
     * 文件读取状态，用来做二次接卸用，如果读取成功了，就不再去读取miniio的文件，直接发起问答
     */
    private Integer fileReadStatus;

    /***
     * 问题数量
     */
    private Integer qaCount;


    /***
     * 完成的解析的问题数量
     */
    private Integer finishedQaCount;

    /***
     * 状态 0:等待解析 1：解析中 2:解析完成 -1:解析失败
     */
    private Integer handleStatus;

    private String createUserId;

    /***
     * 知识库ID
     */
    @TableField(exist = false)
    private String[] baseIds;

}
