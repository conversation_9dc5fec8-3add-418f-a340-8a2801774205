package com.xong.boot.common.crypto.file;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * AES文件加解密器
 * <AUTHOR>
 */
public class AesFileEncoder implements FileEncoder {
    /**
     * 加密算法
     */
    private final AES aes;

    public AesFileEncoder(String secretKey) {
        aes = SecureUtil.aes(HexUtil.decodeHex(secretKey));
    }

    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        aes.encrypt(is, out, isClose);
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        aes.decrypt(is, out, isClose);
    }
}
