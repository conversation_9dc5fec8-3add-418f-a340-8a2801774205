package com.cb.ai.data.analysis.dbtable.model;

import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import com.cb.ai.data.analysis.dbtable.converts.Convert;
import lombok.Data;

import java.util.List;

/**
 * Excel数据转换
 * <AUTHOR>
 */
@Data
public class ExcelConvert {
    /**
     * 数据库字段名
     */
    private String columnName;
    /**
     * 数据库字段注释
     */
    private String columnComment;
    /**
     * 数据类型
     */
    private CHColumnType columnType;
    /**
     * 数据转换规则
     */
    private String convertKey;
    /**
     * Excel列位置
     */
    private Integer excelColumnNo;
    /**
     * ETL属性
     */
    private List<Convert.Variable> variables;
    /**
     * 输入值
     */
    private Object inValue;
}
