package com.cb.ai.data.analysis.voucher.service;

import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTask;
import com.xong.boot.common.service.BaseService;

public interface VoucherTaskService extends BaseService<VoucherTask> {

    /**
     * 添加任务
     * @param task
     */
    public void add(VoucherTask task);

    /**
     * 异步执行任务
     * @param taskId
     */
    public void asyncRunTask(String taskId);

    /**
     * 删除任务
     * @param taskId
     */
    public void deleteById(String taskId);

}
