package com.xong.boot.common.crypto.password;

import com.xong.boot.common.enums.PasswordAlgorithm;
import com.xong.boot.common.utils.StringUtils;

/**
 * 密码算法加解密器
 * <AUTHOR>
 */
public class DelegatingPasswordEncoder implements PasswordEncoder {
    private static final String PREFIX = "{";
    private static final String SUFFIX = "}";
    private final PasswordAlgorithm idForEncode;
    private final PasswordEncoder passwordEncoderForEncode;

    public DelegatingPasswordEncoder(PasswordAlgorithm idForEncode) {
        this.idForEncode = idForEncode;
        this.passwordEncoderForEncode = getPasswordEncoder(idForEncode);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        return PREFIX + idForEncode.name().toLowerCase() + SUFFIX + passwordEncoderForEncode.encode(rawPassword, salt);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        if (StringUtils.isBlank(rawPassword) || StringUtils.isBlank(encodedPassword)) {
            return false;
        }
        PasswordEncoder delegate = extractDigestEncoder(encodedPassword);
        if (delegate == null) {
            return false;
        }
        String ePassword = extractEncodedPassword(encodedPassword);
        return delegate.matches(rawPassword, ePassword, salt);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     */
    @Override
    public String encode(CharSequence rawPassword) {
        return PREFIX + idForEncode.name().toLowerCase() + SUFFIX + passwordEncoderForEncode.encode(rawPassword);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        if (StringUtils.isBlank(rawPassword) || StringUtils.isBlank(encodedPassword)) {
            return false;
        }
        PasswordEncoder delegate = extractDigestEncoder(encodedPassword);
        if (delegate == null) {
            return false;
        }
        String ePassword = extractEncodedPassword(encodedPassword);
        return delegate.matches(rawPassword, ePassword);
    }

    /**
     * 返回加密器
     * @param idForEncode 加密方式
     */
    private PasswordEncoder getPasswordEncoder(PasswordAlgorithm idForEncode) {
        switch (idForEncode) {
            case ARGON2 -> {
                return new Argon2PasswordEncoder();
            }
            case BCRYPT -> {
                return new BCryptPasswordEncoder();
            }
            case MD5 -> {
                return new Md5PasswordEncoder();
            }
            case PBKDF2 -> {
                return new Pbkdf2PasswordEncoder();
            }
            case SCRYPT -> {
                return new SCryptPasswordEncoder();
            }
            case SM3 -> {
                return new Sm3PasswordEncoder();
            }
        }
        return null;
    }

    /**
     * 提取加密密码
     * @param prefixEncodedPassword 加密密码
     */
    private String extractEncodedPassword(String prefixEncodedPassword) {
        int index = prefixEncodedPassword.indexOf(SUFFIX);
        if (index > 0) {
            return prefixEncodedPassword.substring(index + 1);
        }
        return prefixEncodedPassword;
    }

    /**
     * 提取加密编译器
     * @param prefixEncodedPassword 带有加密类型前缀的密码文本
     */
    private PasswordEncoder extractDigestEncoder(String prefixEncodedPassword) {
        if (StringUtils.isBlank(prefixEncodedPassword)) {
            return null;
        }
        PasswordAlgorithm encodingId = extractId(prefixEncodedPassword);
        if (encodingId == null) {
            return null;
        }
        return getPasswordEncoder(encodingId);
    }

    /**
     * 提取加密模式
     * @param encodedPassword 加密密码
     */
    private PasswordAlgorithm extractId(String encodedPassword) {
        if (encodedPassword.startsWith(PREFIX)) {
            int end = encodedPassword.indexOf(SUFFIX);
            if (end < 2) {
                return null;
            }
            String val = encodedPassword.substring(1, end);
            return PasswordAlgorithm.getByValue(val);
        }
        return null;
    }
}
