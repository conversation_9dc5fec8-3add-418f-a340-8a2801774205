package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.ai.domain.entity.AiBigDataAnalyseConfig;
import com.cb.ai.data.analysis.ai.mapper.AiBigDataAnalyseConfigMapper;
import com.cb.ai.data.analysis.ai.service.AiBigDataAnalyseConfigService;
import org.springframework.stereotype.Service;

/**
 * 大数据分析配置(AiBigDataAnalyseConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-27 15:25:48
 */
@Service("aiBigDataAnalyseConfigService")
public class AiBigDataAnalyseConfigServiceImpl extends ServiceImpl<AiBigDataAnalyseConfigMapper, AiBigDataAnalyseConfig> implements AiBigDataAnalyseConfigService {

}

