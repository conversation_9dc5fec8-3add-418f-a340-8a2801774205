//package com.cb.ai.data.analysis.ai.provider;
//
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.cb.ai.data.analysis.ai.enums.EventStreamState;
//import com.cb.ai.data.analysis.ai.model.AIContext;
//import com.cb.ai.data.analysis.ai.model.EventStreamResult;
//import com.cb.ai.data.analysis.ai.model.resp.QuesKnowledgeResultData;
//import com.cb.ai.data.analysis.ai.utils.AiWebClient;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Flux;
//
//import java.util.List;
//
///**
// * 知识库问答
// * <AUTHOR>
// */
//@Component
//public class QuesKnowledgeProvider implements AiProvider {
//    private final static String TAG = "QUES_KNOWLEDGE";
//
//    private final AiWebClient aiWebClient;
//
//    public QuesKnowledgeProvider(AiWebClient aiWebClient) {
//        this.aiWebClient = aiWebClient;
//    }
//
//    @Override
//    public boolean matcher(AIContext context) {
//        List<String> knowledge = context.getKnowledge();
//        return TAG.equals(context.getTag()) && (knowledge != null && knowledge.size() > 0);
//    }
//
//    public Flux<EventStreamResult> request(AIContext context) {
//        return Flux.from(aiWebClient.requestQuesKnowledge(context))
//                .map(content -> {
//                    try {
//                        QuesKnowledgeResultData resultData = new QuesKnowledgeResultData();
//                        JSONObject object = JSON.parseObject(content);
//                        if (object.containsKey("reasoning_content")) {
//                            resultData.setReasoningContent(object.getString("reasoning_content"));
//                        }
//                        if (object.containsKey("content")) {
//                            resultData.setContent(object.getString("content"));
//                        }
//                        if (object.containsKey("index")) {
//                            resultData.setFileId(object.getString("index"));
//                        }
//                        if (object.containsKey("fileId")) {
//                            resultData.setFileId(object.getString("fileId"));
//                        }
//                        if (object.containsKey("fileName")) {
//                            resultData.setFileId(object.getString("fileName"));
//                        }
//                        if (object.containsKey("fileUrl")) {
//                            resultData.setFileId(object.getString("fileUrl"));
//                        }
//                        if (object.containsKey("references")) {
//                            resultData.setFileId(object.getString("references"));
//                        }
//                        return EventStreamResult.newInstance(context.getSessionId(), EventStreamState.streaming)
//                                .setRawData(content)
//                                .setData(resultData);
//                    } catch (Exception e) {
//                        return EventStreamResult.generateErrorStreamingResult(context.getSessionId(), e).setRawData(content);
//                    }
//                })
//                .startWith(EventStreamResult.generateStartResult(context.getSessionId()))
//                .onErrorResume(throwable -> Flux.just(EventStreamResult.generateErrorResult(context.getSessionId(), throwable)))
//                .concatWith(Flux.just(EventStreamResult.generateEndResult(context.getSessionId())));
//    }
//}
