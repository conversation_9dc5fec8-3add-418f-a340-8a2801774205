<template>
  <x-page-wrapper hiddenTitle class="voucher-info">
    <x-table-search :model="searchFormData" :tableRef="tableRef" :col="4">
      <a-form-item label="凭证名称" name="name">
        <a-input v-model:value="searchFormData.name" :maxlength="100" allow-clear placeholder="请输入凭证名称" />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-select v-model:value="searchFormData.status" allow-clear placeholder="请选择状态">
          <a-select-option :value="0">未解析</a-select-option>
          <a-select-option :value="1">解析中</a-select-option>
          <a-select-option :value="2">解析成功</a-select-option>
          <a-select-option :value="3">解析失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="开始时间" name="beginDate">
        <a-date-picker v-model:value="searchFormData.beginDate" placeholder="请选择开始时间" style="width: 100%" />
      </a-form-item>
      <a-form-item label="结束时间" name="endDate">
        <a-date-picker v-model:value="searchFormData.endDate" placeholder="请选择结束时间" style="width: 100%" />
      </a-form-item>
    </x-table-search>
    <x-table ref="tableRef" :columns="columns" :loadData="loadData" :rowSelection="true" title="凭证信息管理" row-key="id">
      <template #toolbar="{ selectedRowKeys }">
        <a-button type="primary" @click="onClickUpload">
          <template #icon>
            <x-icon type="UploadOutlined" />
          </template>
          上传凭证
        </a-button>
        <a-button v-if="selectedRowKeys && selectedRowKeys.length > 0" type="primary"
          @click="onClickBatchTag(selectedRowKeys)">
          <template #icon>
            <x-icon type="TagOutlined" />
          </template>
          批量标签
        </a-button>
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'ocrStartTime'">
          {{ $date.format(text, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.dataIndex === 'ocrEndTime'">
          {{ $date.format(text, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.dataIndex === 'tags'">
          <a-tag v-for="tag in record.tags" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ $date.formatDateTime(text) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button v-if="record.status === 2" type="link" size="small" @click="onClickAnalysis(record)">
              分析
            </a-button>
            <a-button v-if="record.status === 3" type="link" size="small" @click="onClickReOcr(record)">
              重新解析
            </a-button>
            <a-button type="link" size="small" @click="onClickView(record)">
              查看
            </a-button>
            <a-button v-if="record.status === 2" type="link" size="small" @click="onClickViewAlert(record)">
              查看告警
            </a-button>
          </a-space>
        </template>
      </template>
    </x-table>

    <!-- 上传凭证组件 -->
    <UploadComp v-model:visible="uploadVisible" @success="onSuccess" />
    <!-- 告警详情弹窗 -->
    <AlertModal v-model:visible="alertModalAttrs.visible" :voucher-id="alertModalAttrs.voucherId"
      :voucher-name="alertModalAttrs.voucherName" />
  </x-page-wrapper>
</template>

<script setup lang="ts" name="VoucherInfo">
import { computed, getCurrentInstance, reactive, ref, type ComponentCustomProperties } from 'vue'
import { info } from '@/api/voucher'
import UploadComp from './Upload.vue'
import AlertModal from './AlertModal.vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const tableRef = ref()
const uploadVisible = ref(false)
const alertModalAttrs = reactive({
  visible: false,
  voucherId: '',
  voucherName: ''
})

const searchFormData = ref({
  name: '',
  status: undefined,
  beginDate: undefined,
  endDate: undefined
})

const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'name',
      title: '凭证名称',
      width: 200,
      ellipsis: true
    },
    {
      dataIndex: 'status',
      title: '状态',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'tags',
      title: '标签',
      width: 150
    },
    {
      dataIndex: 'ocrStartTime',
      title: 'OCR开始时间',
      width: 150,
      align: 'center'
    },
    {
      dataIndex: 'ocrEndTime',
      title: 'OCR结束时间',
      width: 150,
      align: 'center'
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
      align: 'center'
    }
  ] as TableColumn[]

  columnItems.push({
    key: 'actions',
    title: '操作',
    width: 150,
    align: 'center'
  })

  return columnItems
})

/**
 * 加载数据
 */
async function loadData(params: Record<string, any>) {
  const res = await info.page(params)
  return res
}

/**
 * 获取状态颜色
 */
function getStatusColor(status: number) {
  const colorMap = {
    0: 'default',
    1: 'processing',
    2: 'success',
    3: 'error'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
function getStatusText(status: number) {
  const textMap = {
    0: '未解析',
    1: '解析中',
    2: '解析成功',
    3: '解析失败'
  }
  return textMap[status] || '未知'
}

/**
 * 上传凭证
 */
function onClickUpload() {
  uploadVisible.value = true
}

/**
 * 成功回调
 */
function onSuccess() {
  tableRef.value?.refresh()
}

/**
 * 批量添加标签
 */
function onClickBatchTag(selectedRowKeys: string[]) {
  _this.$message.info('批量标签功能待实现')
}

/**
 * 分析凭证
 */
async function onClickAnalysis(record: any) {
  try {
    await info.analysisById(record.id)
    _this.$message.success('分析任务已提交，后台分析中...')
    tableRef.value?.refresh()
  } catch (error) {
    _this.$message.error('分析失败')
  }
}

/**
 * 重新OCR解析
 */
async function onClickReOcr(record: any) {
  try {
    await info.reOcr(record.id)
    _this.$message.success('重新解析任务已提交')
    tableRef.value?.refresh()
  } catch (error) {
    _this.$message.error('重新解析失败')
  }
}

/**
 * 查看凭证详情
 */
function onClickView(record: any) {
  _this.$message.info('查看功能待实现')
}

/**
 * 查看凭证告警记录
 */
function onClickViewAlert(record: any) {
  alertModalAttrs.visible = true
  alertModalAttrs.voucherId = record.id
  alertModalAttrs.voucherName = record.name
}
</script>

<style scoped lang="less">
.voucher-info {
  .ant-tag {
    margin-right: 4px;
  }
}
</style>
