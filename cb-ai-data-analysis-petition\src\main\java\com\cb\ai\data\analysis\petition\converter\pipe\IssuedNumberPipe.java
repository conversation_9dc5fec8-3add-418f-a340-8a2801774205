package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 发文字号
 * <AUTHOR>
 */
public class IssuedNumberPipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.contains("^[\\u4e00-\\u9fa5]+〔20[0-9Xx× ]{2}〕[0-9Xx× ]+号$", text);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化段落
        FormatTools.formatParagraph(paragraph, config).setCTJcCenter();
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
