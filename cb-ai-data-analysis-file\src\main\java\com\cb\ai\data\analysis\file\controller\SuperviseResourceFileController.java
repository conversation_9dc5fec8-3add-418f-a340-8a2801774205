//package com.cb.ai.data.analysis.file.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.cb.ai.data.analysis.file.constant.Constants;
//import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
//import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
//import com.cb.ai.data.analysis.file.domain.dto.IdsDto;
//import com.cb.ai.data.analysis.file.model.FileUploadInfo;
//import com.cb.ai.data.analysis.file.model.UploadUrlsVO;
//import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
//import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
//import com.xong.boot.common.annotation.XLog;
//import com.xong.boot.common.api.Result;
//import com.xong.boot.common.controller.BaseController;
//import com.xong.boot.common.enums.ExecType;
//import com.xong.boot.common.valid.UpdateGroup;
//import com.xong.boot.framework.utils.QueryHelper;
//import io.minio.credentials.Credentials;
//import jakarta.annotation.Resource;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.NotEmpty;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//@Slf4j
//@Validated
//@RestController
//@RequestMapping(Constants.API_UNI_SUPERVISE_ROOT_PATH + "/resource/file")
//public class SuperviseResourceFileController extends BaseController<SuperviseResourceFileService, SuperviseResourceFile> {
//
//    private final SuperviseResourceFolderService superviseResourceFolderService;
//
//    public SuperviseResourceFileController(SuperviseResourceFolderService superviseResourceFolderService) {
//        this.superviseResourceFolderService = superviseResourceFolderService;
//    }
//
//    @GetMapping("/getFileListByFolder")
//    public Result getFileListByFolder(SuperviseResourceFile superviseResourceFile) {
//        QueryWrapper<SuperviseResourceFile> superviseResourceFileXQueryWrapper = new QueryWrapper<>(superviseResourceFile);
//        List<SuperviseResourceFile> list = baseService.list(superviseResourceFileXQueryWrapper);
//        return Result.successData(list);
//    }
//
//    @PutMapping
//    public Result update(@Validated(UpdateGroup.class) @RequestBody SuperviseResourceFile superviseResourceFile) {
//        boolean b = baseService.updateById(superviseResourceFile);
//        if (b) {
//            return Result.success("修改成功！");
//        }
//        return Result.fail("修改失败！");
//    }
//
//    @DeleteMapping
//    @XLog(title = "删除文件", execType = ExecType.DELETE)
//    public Result delete(@NotEmpty(message = "文件ID不存在") String[] ids) {
//        List<String> idList = Arrays.asList(ids);
//        //更新被删除文件的原路径
//        baseService.updateFilesOriginalPath(idList);
//        if (baseService.removeByIds(idList)) {
//            return Result.success("删除成功");
//        }
//        return Result.fail("删除失败");
//    }
//
//    @PostMapping("/upload")
//    public Result upload(MultipartFile file, String folderId, String fileTags, String digestMd5) throws Exception {
//        SuperviseResourceFolder byId = superviseResourceFolderService.getById(folderId);
//        if (byId == null) {
//            byId = new SuperviseResourceFolder();
//            byId.setId("0");
//            byId.setFullPath("0");
//        }
//        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
//        lambda.eq(SuperviseResourceFile::getDigestMd5,digestMd5);
//        SuperviseResourceFile one = baseService.getOne(lambda);
//        if (one != null) {
//            return Result.successData(one);
//        }
//        SuperviseResourceFile superviseResourceFile = baseService.uploadFile(file, byId, digestMd5,fileTags);
////        boolean save = baseService.save(superviseResourceFile);
////        if (save) {
////            return Result.successData(superviseResourceFile);
////        }
//        return Result.successData(superviseResourceFile);
//    }
//
//    @GetMapping("/download/{fileId}")
//    public Result downloadFile(@PathVariable String fileId, HttpServletResponse response) throws IOException {
//        String downloadUrl = baseService.getDownloadUrl(fileId);
//        HashMap<String, String> stringStringHashMap = new HashMap<>();
//        stringStringHashMap.put("downloadUrl", downloadUrl);
//        return Result.successData(stringStringHashMap);
//    }
//
//    /**
//     * 获取已删除的文件
//     *
//     * @param superviseResourceFile
//     * @return
//     */
//    @GetMapping("/getFileListByAlreadyDeleted")
//    public Result getFileListByAlreadyDeleted(SuperviseResourceFile superviseResourceFile) {
//        Page<SuperviseResourceFile> page = baseService.pageDeletedFiles(QueryHelper.getPage(), superviseResourceFile);
//        return Result.successData(page);
//    }
//
//    @PostMapping("/restoreFile")
//    public Result restoreFile(@RequestBody IdsDto idsDto) {
//        baseService.restoreFile(idsDto.getIds(),idsDto.getFolderId());
//        return Result.success("还原成功！");
//    }
//
//    @DeleteMapping("/permanentlyDelete")
//    @XLog(title = "删除文件", execType = ExecType.DELETE)
//    public Result permanentlyDelete(@NotEmpty(message = "文件ID不存在") String[] ids) {
//        List<String> idList = Arrays.asList(ids);
//        //更新被删除文件的原路径
//        if (baseService.permanentlyDelete(idList)) {
//            return Result.success("删除成功");
//        }
//        return Result.fail("删除失败");
//    }
//
//    /**
//     * 检查文件是否存在
//     */
//
//    @GetMapping("/multipart/check/{md5}")
//    public Result checkFileByMd5(@PathVariable String md5) {
//        log.info("查询 <{}> 文件是否存在、是否进行断点续传", md5);
//        FileUploadInfo fileUploadInfo = baseService.checkFileByMd5(md5);
//        return Result.successData(fileUploadInfo);
//    }
//
//    /**
//     * 初始化文件分片地址及相关数据
//     */
//    @PostMapping("/multipart/init")
//    public Result initMultiPartUpload(@RequestBody FileUploadInfo fileUploadInfo) {
//        log.info("通过 <{}> 初始化上传任务", fileUploadInfo);
//        UploadUrlsVO uploadUrlsVO = baseService.initMultipartUpload(fileUploadInfo);
//        return Result.successData(uploadUrlsVO);
//    }
//
//    /**
//     * 文件合并（单文件不会合并，仅信息入库）
//     */
//    @PostMapping("/multipart/merge/{md5}")
//    public Result mergeMultipartUpload(@PathVariable String md5) {
//        log.info("通过 <{}> 合并上传任务", md5);
//        SuperviseResourceFile superviseResourceFile = baseService.mergeMultipartUpload(md5);
//        return Result.successData(superviseResourceFile);
//    }
//
//    /**
//     * 下载文件（分片）
//     */
////    @GetMapping("/download/{id}")
//    public ResponseEntity<byte[]> downloadMultipartFile(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
//        log.info("通过 <{}> 开始分片下载", id);
//        byte[] bytes = baseService.downloadMultipartFile(id, request, response);
////        return baseService.downloadMultipartFile(id, request, response);
//        return   new ResponseEntity<>(bytes, HttpStatus.OK);
//    }
//}
