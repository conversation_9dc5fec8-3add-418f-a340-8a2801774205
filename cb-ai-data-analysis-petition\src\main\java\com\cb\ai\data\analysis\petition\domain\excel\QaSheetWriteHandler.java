package com.cb.ai.data.analysis.petition.domain.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Sheet;

public class QaSheetWriteHandler implements SheetWriteHandler {
    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // 设置第1列宽度（单位是字符宽度 * 256）
        sheet.setColumnWidth(0, 10 * 256);  // 第1列设置为20字符宽度
        sheet.setColumnWidth(1, 30 * 256);  // 第2列设置为30字符宽度
        sheet.setColumnWidth(2, 150 * 256);  // 第2列设置为30字符宽度
    }
}
