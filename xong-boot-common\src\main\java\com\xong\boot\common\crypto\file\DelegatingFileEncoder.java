package com.xong.boot.common.crypto.file;

import com.xong.boot.common.enums.FileAlgorithm;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件加解密器
 * <AUTHOR>
 */
public class DelegatingFileEncoder implements FileEncoder {
    /**
     * 文件加密密钥
     * 默认 32位
     */
    private final String secretKey;
    /**
     * 文件非对称加密 私钥
     */
    private final String privateKey;
    /**
     * 文件非对称加密 公钥
     */
    private final String publicKey;
    /**
     * 文件加密算法
     */
    private final FileAlgorithm idForEncode;
    private final FileEncoder fileEncoderForEncode;

    public DelegatingFileEncoder(FileAlgorithm idForEncode, String secretKey, String privateKey, String publicKey) {
        this.idForEncode = idForEncode;
        this.secretKey = secretKey;
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.fileEncoderForEncode = getFileEncoder();
    }

    /**
     * 加密后关闭输入流
     * @param is  加密的字符串
     * @param out 输出流，可以是文件或网络位置
     */
    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        fileEncoderForEncode.encode(is, out, true);
    }

    /**
     * 加密
     * @param is      加密的字符串
     * @param out     输出流，可以是文件或网络位置
     * @param isClose 是否关闭输入流
     */
    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        fileEncoderForEncode.encode(is, out, isClose);
    }

    /**
     * 解密后关闭输入流
     * @param is  加密的字符串
     * @param out 输出流，可以是文件或网络位置
     */
    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        fileEncoderForEncode.decode(is, out, true);
    }

    /**
     * 解密
     * @param is      加密的字符串
     * @param out     输出流，可以是文件或网络位置
     * @param isClose 是否关闭输入流
     */
    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        fileEncoderForEncode.decode(is, out, isClose);
    }

    /**
     * 返回加密器
     */
    private FileEncoder getFileEncoder() {
        switch (idForEncode) {
            case AES -> {
                return new AesFileEncoder(secretKey);
            }
            case DES -> {
                return new DesFileEncoder(secretKey);
            }
            case RSA -> {
                return new RsaFileEncoder(privateKey, publicKey);
            }
            case SM2 -> {
                return new Sm2FileEncoder(secretKey, publicKey);
            }
            case SM4 -> {
                return new Sm4FileEncoder(secretKey);
            }
            case NONE -> {
                return new NoneFileEncoder();
            }
        }
        return null;
    }
}
