package com.cb.ai.data.analysis.voucher.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 凭证检查规则
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoucherCheckRule extends BaseDomain {

    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String name;

    /**
     * 规则说明
     */
    private String description;

    /**
     * 提示词
     */
    @NotBlank(message = "规则提示词不能为空")
    @Size(min = 10, message = "规则提示词长度不能少于{min}个字符")
    private String promote;

    /**
     * 是否禁用 0-否 1-是
     */
    private Integer disable;

}
