<template>
  <a-modal :open="visible" :title="modalTitle" width="700px" :footer="null" @cancel="onCancel">
    <div class="batch-tag-content">
      <!-- 操作模式选择 -->
      <div class="mode-selection">
        <a-radio-group v-model:value="operationMode" @change="onModeChange">
          <a-radio-button value="add">批量添加标签</a-radio-button>
          <a-radio-button value="delete">批量删除标签</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 选中的凭证信息 -->
      <div class="selected-info">
        <h4>已选择 {{ selectedCount }} 个凭证</h4>
        <p class="tip">{{ operationTip }}</p>
      </div>

      <!-- 添加模式：标签输入区域 -->
      <div v-if="operationMode === 'add'" class="tag-input-section">
        <h4>添加标签：</h4>
        <div class="tag-input-area">
          <a-input v-model:value="newTag" placeholder="输入标签名称，按回车添加" :maxlength="20" @press-enter="addTag"
            @keydown.enter.prevent="addTag" />
          <a-button type="primary" @click="addTag" :disabled="!newTag.trim()">
            添加
          </a-button>
        </div>
      </div>

      <!-- 删除模式：现有标签选择 -->
      <div v-if="operationMode === 'delete'" class="existing-tags-section">
        <h4>选择要删除的标签：</h4>
        <div class="existing-tags-container">
          <a-checkbox-group v-model:value="selectedExistingTags" @change="onExistingTagsChange">
            <div class="tags-grid">
              <a-checkbox v-for="tag in existingTags" :key="tag" :value="tag" class="tag-checkbox">
                <a-tag color="orange">{{ tag }}</a-tag>
              </a-checkbox>
            </div>
          </a-checkbox-group>
          <div v-if="existingTags.length === 0" class="empty-tags">
            所选凭证暂无共同标签
          </div>
        </div>
      </div>

      <!-- 操作标签列表 -->
      <div class="tags-list-section">
        <h4>{{ operationMode === 'add' ? '待添加的标签：' : '待删除的标签：' }}</h4>
        <div class="tags-container">
          <a-tag v-for="(tag, index) in operationTags" :key="index" closable
            :color="operationMode === 'add' ? 'blue' : 'red'" @close="removeOperationTag(index)">
            {{ tag }}
          </a-tag>
          <div v-if="operationTags.length === 0" class="empty-tags">
            {{ operationMode === 'add' ? '暂无标签，请先添加标签' : '暂无标签，请先选择要删除的标签' }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button @click="onCancel">取消</a-button>
          <a-button :type="operationMode === 'add' ? 'primary' : 'danger'" @click="onConfirm" :loading="loading"
            :disabled="operationTags.length === 0">
            {{ operationMode === 'add' ? '确认添加' : '确认删除' }}
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts" name="BatchTagModal">
import { computed, getCurrentInstance, ref, watch, type ComponentCustomProperties } from 'vue'
import { info } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible', 'success'])

const props = defineProps<{
  visible: boolean
  selectedIds: string[]
  selectedRecords?: any[]  // 选中的记录，用于获取现有标签
}>()

const loading = ref(false)
const operationMode = ref<'add' | 'delete'>('add')
const newTag = ref('')
const operationTags = ref<string[]>([])
const selectedExistingTags = ref<string[]>([])
const existingTags = ref<string[]>([])

const selectedCount = computed(() => props.selectedIds.length)

const modalTitle = computed(() => {
  return operationMode.value === 'add' ? '批量添加标签' : '批量删除标签'
})

const operationTip = computed(() => {
  return operationMode.value === 'add'
    ? '将为这些凭证批量添加标签'
    : '将从这些凭证中批量删除标签'
})

// 监听弹窗显示状态，重置数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
      loadExistingTags()
    }
  }
)

function onCancel() {
  emits('update:visible', false)
}

function resetForm() {
  operationMode.value = 'add'
  newTag.value = ''
  operationTags.value = []
  selectedExistingTags.value = []
  existingTags.value = []
  loading.value = false
}

function onModeChange() {
  operationTags.value = []
  selectedExistingTags.value = []
  newTag.value = ''
}

// 加载现有标签（获取所选凭证的共同标签）
function loadExistingTags() {
  if (!props.selectedRecords || props.selectedRecords.length === 0) {
    existingTags.value = []
    return
  }

  // 获取所有选中凭证的共同标签
  const allTags = props.selectedRecords.map(record => record.tags || [])
  if (allTags.length === 0) {
    existingTags.value = []
    return
  }

  // 找出所有凭证都有的标签
  const commonTags = allTags[0].filter((tag: string) =>
    allTags.every((tags: string[]) => tags.includes(tag))
  )

  existingTags.value = commonTags
}

function addTag() {
  const tag = newTag.value.trim()
  if (!tag) {
    return
  }

  // 检查是否已存在
  if (operationTags.value.includes(tag)) {
    _this.$message.warning('标签已存在')
    return
  }

  // 检查标签数量限制
  if (operationTags.value.length >= 10) {
    _this.$message.warning('最多只能添加10个标签')
    return
  }

  operationTags.value.push(tag)
  newTag.value = ''
}

function onExistingTagsChange() {
  operationTags.value = [...selectedExistingTags.value]
}

function removeOperationTag(index: number) {
  const removedTag = operationTags.value[index]
  operationTags.value.splice(index, 1)

  // 如果是删除模式，同时从选中的现有标签中移除
  if (operationMode.value === 'delete') {
    const existingIndex = selectedExistingTags.value.indexOf(removedTag)
    if (existingIndex > -1) {
      selectedExistingTags.value.splice(existingIndex, 1)
    }
  }
}

async function onConfirm() {
  if (operationTags.value.length === 0) {
    _this.$message.warning(`请先${operationMode.value === 'add' ? '添加' : '选择'}标签`)
    return
  }

  if (props.selectedIds.length === 0) {
    _this.$message.warning('请先选择凭证')
    return
  }

  try {
    loading.value = true

    const apiData = {
      infoIds: props.selectedIds,
      tags: operationTags.value
    }

    if (operationMode.value === 'add') {
      await info.batchAddTag(apiData)
      _this.$message.success(`成功为 ${props.selectedIds.length} 个凭证添加了 ${operationTags.value.length} 个标签`)
    } else {
      await info.batchDelTag(apiData)
      _this.$message.success(`成功从 ${props.selectedIds.length} 个凭证删除了 ${operationTags.value.length} 个标签`)
    }

    emits('success')
    emits('update:visible', false)
  } catch (error) {
    _this.$message.error(`批量${operationMode.value === 'add' ? '添加' : '删除'}标签失败`)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="less">
.batch-tag-content {
  .mode-selection {
    margin-bottom: 20px;
    text-align: center;

    .ant-radio-group {
      .ant-radio-button-wrapper {
        padding: 8px 20px;
        font-weight: 500;
      }
    }
  }

  .selected-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f6f8fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
    }

    .tip {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .tag-input-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .tag-input-area {
      display: flex;
      gap: 8px;

      .ant-input {
        flex: 1;
      }
    }
  }

  .existing-tags-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .existing-tags-container {
      min-height: 80px;
      padding: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: #fafafa;

      .tags-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .tag-checkbox {
          margin: 4px 0;

          .ant-checkbox {
            margin-right: 8px;
          }

          .ant-tag {
            margin: 0;
            cursor: pointer;
          }
        }
      }

      .empty-tags {
        color: #999;
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }

  .tags-list-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .tags-container {
      min-height: 60px;
      padding: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: #fafafa;

      .ant-tag {
        margin: 4px 8px 4px 0;
        padding: 4px 8px;
        font-size: 13px;
      }

      .empty-tags {
        color: #999;
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }

  .action-buttons {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
