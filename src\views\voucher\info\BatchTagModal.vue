<template>
  <a-modal :open="visible" title="批量添加标签" width="600px" :footer="null" @cancel="onCancel">
    <div class="batch-tag-content">
      <!-- 选中的凭证信息 -->
      <div class="selected-info">
        <h4>已选择 {{ selectedCount }} 个凭证</h4>
        <p class="tip">将为这些凭证批量添加标签</p>
      </div>

      <!-- 标签输入区域 -->
      <div class="tag-input-section">
        <h4>添加标签：</h4>
        <div class="tag-input-area">
          <a-input v-model:value="newTag" placeholder="输入标签名称，按回车添加" :maxlength="20" @press-enter="addTag"
            @keydown.enter.prevent="addTag" />
          <a-button type="primary" @click="addTag" :disabled="!newTag.trim()">
            添加
          </a-button>
        </div>
      </div>

      <!-- 已添加的标签列表 -->
      <div class="tags-list-section">
        <h4>待添加的标签：</h4>
        <div class="tags-container">
          <a-tag v-for="(tag, index) in tags" :key="index" closable color="blue" @close="removeTag(index)">
            {{ tag }}
          </a-tag>
          <div v-if="tags.length === 0" class="empty-tags">
            暂无标签，请先添加标签
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button @click="onCancel">取消</a-button>
          <a-button type="primary" @click="onConfirm" :loading="loading" :disabled="tags.length === 0">
            确认添加
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts" name="BatchTagModal">
import { computed, getCurrentInstance, ref, watch, type ComponentCustomProperties } from 'vue'
import { info } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible', 'success'])

const props = defineProps<{
  visible: boolean
  selectedIds: string[]
}>()

const loading = ref(false)
const newTag = ref('')
const tags = ref<string[]>([])

const selectedCount = computed(() => props.selectedIds.length)

// 监听弹窗显示状态，重置数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)

function onCancel() {
  emits('update:visible', false)
}

function resetForm() {
  newTag.value = ''
  tags.value = []
  loading.value = false
}

function addTag() {
  const tag = newTag.value.trim()
  if (!tag) {
    return
  }

  // 检查是否已存在
  if (tags.value.includes(tag)) {
    _this.$message.warning('标签已存在')
    return
  }

  // 检查标签数量限制
  if (tags.value.length >= 10) {
    _this.$message.warning('最多只能添加10个标签')
    return
  }

  tags.value.push(tag)
  newTag.value = ''
}

function removeTag(index: number) {
  tags.value.splice(index, 1)
}

async function onConfirm() {
  if (tags.value.length === 0) {
    _this.$message.warning('请先添加标签')
    return
  }

  if (props.selectedIds.length === 0) {
    _this.$message.warning('请先选择凭证')
    return
  }

  try {
    loading.value = true

    await info.batchAddTag({
      infoIds: props.selectedIds,
      tags: tags.value
    })

    _this.$message.success(`成功为 ${props.selectedIds.length} 个凭证添加了 ${tags.value.length} 个标签`)
    emits('success')
    emits('update:visible', false)
  } catch (error) {
    _this.$message.error('批量添加标签失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="less">
.batch-tag-content {
  .selected-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f6f8fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
    }

    .tip {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .tag-input-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .tag-input-area {
      display: flex;
      gap: 8px;

      .ant-input {
        flex: 1;
      }
    }
  }

  .tags-list-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .tags-container {
      min-height: 60px;
      padding: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: #fafafa;

      .ant-tag {
        margin: 4px 8px 4px 0;
        padding: 4px 8px;
        font-size: 13px;
      }

      .empty-tags {
        color: #999;
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }
    }
  }

  .action-buttons {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
