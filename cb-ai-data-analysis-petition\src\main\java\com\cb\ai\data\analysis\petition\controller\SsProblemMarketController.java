package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketClassifyEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsProblemMarketVo;
import com.cb.ai.data.analysis.petition.service.SsProblemMarketService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/***
 * <AUTHOR>
 * 问题矩阵
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/problem/market")
public class SsProblemMarketController {

    @Autowired
    private SsProblemMarketService problemMarketService;

    /***
     * 分页查询问题分类列表
     * @param problemMarket
     * @return
     */
    @GetMapping("/classify/page")
    public Result classifyPage(SsProblemMarketVo problemMarket) {
        try{
            Page<SsProblemMarketClassifyEntity> classifyPage=problemMarketService.classifyPage(problemMarket);
            return Result.successData(classifyPage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询问题分类列表失败！");
        }
    }
    /***
     * 分页查询问题明细列表
     * @param problemMarket
     * @return
     */
    @PostMapping("/details/page")
    public Result detailsPage(@RequestBody SsProblemMarketVo problemMarket) {
        try{
            IPage<SsProblemMarketDetailsEntity> detailsPage=problemMarketService.detailsPage(problemMarket);
            return Result.successData(detailsPage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询问题明细列表失败！");
        }
    }

    @PostMapping("/submitUpload")
    public Result submitUpload(@RequestParam("file") MultipartFile file,@RequestParam("domain")String domain) {
        try{
            problemMarketService.submitUpload(file,domain);
            return Result.success("文件上传成功");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("文件上传失败！");
        }
    }

    @DeleteMapping("/{id}")
    public Result delProblemMarket(@PathVariable String id){
        try{
            problemMarketService.delProblemMarket(id);
            return Result.success("删除提问矩阵成功！");
        }catch (XServiceException e){
            e.printStackTrace();
            return Result.fail(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("删除提问矩阵信息失败！");
        }
    }
    @DeleteMapping("/details/{id}")
    public Result delProblemDetails(@PathVariable String id){
        try{
            problemMarketService.delProblemDetails(id);
            return Result.success("删除提问矩阵详情成功！");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("删除提问矩阵详情信息失败！");
        }
    }
}
