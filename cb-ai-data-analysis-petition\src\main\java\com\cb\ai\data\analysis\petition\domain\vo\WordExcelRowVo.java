package com.cb.ai.data.analysis.petition.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WordExcelRowVo {

    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty("文件名")
    private String fileName;

    @ExcelProperty("内容")
    @ContentStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, wrapped = BooleanEnum.TRUE, shrinkToFit = BooleanEnum.TRUE)
    private String content;
}
