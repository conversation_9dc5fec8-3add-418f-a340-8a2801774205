package com.cb.ai.data.analysis.query.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Refresh;
import co.elastic.clients.elasticsearch.core.*;
import com.cb.ai.data.analysis.query.domain.bo.ElasticReqBo;
import com.cb.ai.data.analysis.query.service.ElasticService;
import com.cb.ai.data.analysis.query.service.IndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

@Slf4j
@Service
public class ElasticServiceImpl implements ElasticService {
    private final ElasticsearchClient restClient;
    private final IndexService indexService;

    public ElasticServiceImpl(ElasticsearchClient restClient, IndexService indexService) {
        this.restClient = restClient;
        this.indexService = indexService;
    }

    @Override
    public String addDocument(String indexName, ElasticReqBo.Req req) throws Exception {
        Assert.hasLength(indexName, "Elasticsearch exception indexName null");
        Assert.notNull(req, "Request object is null");
        Assert.hasLength(req.getDocumentId(), "Elasticsearch exception id null");
        Assert.notNull(req.getData(), "Data to save is null");

        if (!indexService.existsIndex(indexName)) {
            log.error("Index does not exist: {}", indexName);
            throw new RuntimeException("索引不存在: " + indexName);
        }

        IndexResponse response = restClient.index(i -> i
                .index(indexName)
                .id(req.getDocumentId())
                .document(req.getData())
                .refresh(Refresh.True)
        );

        log.info("Document indexed successfully - Index: {}, ID: {}, Version: {}",
                indexName, response.id(), response.version());
        return response.id();
    }

    @Override
    public void addDocumentsBatch(String indexName, List<ElasticReqBo.Req> reqList) throws Exception {
        Assert.hasLength(indexName, "Elasticsearch exception indexName null");
        Assert.notEmpty(reqList, "Request list is empty");
        if (!indexService.existsIndex(indexName)) {
            log.error("Index does not exist: {}", indexName);
            throw new RuntimeException("索引不存在: " + indexName);
        }

        BulkRequest.Builder bulkRequestBuilder = new BulkRequest.Builder();
        reqList.forEach(item -> {
            Assert.hasLength(item.getDocumentId(), "Elasticsearch exception id null");
            Assert.notNull(item.getData(), "Data to save is null");
            bulkRequestBuilder.operations(op -> op
                    .index(b -> b
                            .index(indexName)
                            .id(item.getDocumentId())
                            .document(item.getData())
                    )
            );
        });

        BulkResponse response = restClient.bulk(bulkRequestBuilder.build());
        if (response.errors()) {
            log.warn("Bulk operation had errors");
        }
    }


    @Override
    public String updateDocument(String indexName, ElasticReqBo.Req req) throws Exception {
        Assert.hasLength(indexName, "Elasticsearch exception indexName null");
        Assert.notNull(req, "Request object is null");
        Assert.hasLength(req.getDocumentId(), "Elasticsearch exception id null");
        Assert.notNull(req.getData(), "Data to update is null");

        if (!indexService.existsIndex(indexName)) {
            log.error("Index does not exist: {}", indexName);
            throw new RuntimeException("索引不存在: " + indexName);
        }

        UpdateResponse response = restClient.update(b -> b
                        .index(indexName)
                        .id(req.getDocumentId())
                        .doc(req.getData())
                        .upsert(req.getData())
                        .refresh(Refresh.True),
                Object.class);

        log.info("Document updated successfully - Index: {}, ID: {}, Version: {}",
                indexName, response.id(), response.version());
        return response.id();
    }

    @Override
    public long deleteDocument(String indexName, String id) throws Exception {
        Assert.hasLength(indexName, "Elasticsearch exception indexName null");
        Assert.hasLength(id, "Elasticsearch exception id null");

        if (!indexService.existsIndex(indexName)) {
            log.error("Index does not exist: {}", indexName);
            throw new RuntimeException("索引不存在: " + indexName);
        }

        DeleteResponse response = restClient.delete(b -> b
                .index(indexName)
                .id(id)
                .refresh(Refresh.True)
        );
        log.info("Document deleted successfully - Index: {}, ID: {}, Version: {}",
                indexName, response.id(), response.version());
        return response.version(); // 返回版本号作为删除结果标识
    }
}
