package com.cb.ai.data.analysis.knowledge.service;

import com.cb.ai.data.analysis.knowledge.domain.KnowledgeSuperviseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFileReq;

import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件选择
 */
public interface KnowledgeSuperviseFileService {

    /***
     * 根据文件ID，查询文件类别，含文件夹和文件
     * @param folderId
     * @return
     * @throws Exception
     */
    List<KnowledgeSuperviseFile> getKnowledgeSelectFile(String folderId) throws Exception;


    /***
     * 保存选择的文件至向量库
     * @return
     * @throws Exception
     */
    void superviseSaveUpload(KnowledgeFileReq KnowledgeFile) throws Exception;

}
