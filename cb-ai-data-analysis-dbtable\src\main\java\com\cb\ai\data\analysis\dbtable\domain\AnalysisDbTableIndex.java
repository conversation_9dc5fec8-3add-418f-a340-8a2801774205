package com.cb.ai.data.analysis.dbtable.domain;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.dbtable.enums.IndexType;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.handlers.ArrayStringTypeHandler;
import com.xong.boot.common.valid.AddGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 动态数据表索引
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTableIndex extends SimpleBaseDomain {
    /**
     * ID
     */
    @TableId
    private String id;
    /**
     * 表名称
     */
    private String tableName;
    /**
     * 字段名
     */
    @TableField(typeHandler = ArrayStringTypeHandler.class)
    @NotEmpty(message = "字段名不存在", groups = AddGroup.class)
    private List<String> columnNames;
    /**
     * 索引名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String indexName;
    /**
     * 索引类型
     */
    @NotBlank(message = "索引类型不存在", groups = AddGroup.class)
    private IndexType indexType;
    /**
     * 索引备注
     */
    private String indexComment;
    /**
     * 排序
     */
    private Integer sortOn;
}
