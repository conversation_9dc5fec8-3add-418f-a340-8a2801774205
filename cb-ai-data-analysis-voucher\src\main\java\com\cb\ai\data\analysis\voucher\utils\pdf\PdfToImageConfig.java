package com.cb.ai.data.analysis.voucher.utils.pdf;

import lombok.Data;

/**
 * PDF转图片配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-01
 */
@Data
public class PdfToImageConfig {

    /**
     * 默认DPI（每英寸点数）
     */
    public static final float DEFAULT_DPI = 150.0f;

    /**
     * 高质量DPI
     */
    public static final float HIGH_QUALITY_DPI = 300.0f;

    /**
     * 低质量DPI（用于预览）
     */
    public static final float LOW_QUALITY_DPI = 72.0f;

    /**
     * 默认图片格式
     */
    public static final String DEFAULT_FORMAT = "PNG";

    /**
     * 支持的图片格式
     */
    public static final String[] SUPPORTED_FORMATS = {"PNG", "JPG", "JPEG", "BMP", "GIF"};

    /**
     * DPI设置
     */
    private float dpi = DEFAULT_DPI;

    /**
     * 图片格式
     */
    private String format = DEFAULT_FORMAT;

    /**
     * 是否启用抗锯齿
     */
    private boolean antiAliasing = true;

    /**
     * 图片质量（0.0-1.0，仅对JPEG格式有效）
     */
    private float quality = 0.9f;

    /**
     * 分页处理时每批处理的页数（默认10页）
     */
    private int pageSize = 10;

    /**
     * 是否在分页处理时自动进行垃圾回收
     */
    private boolean autoGc = true;

    /**
     * 创建默认配置
     * 
     * @return 默认配置实例
     */
    public static PdfToImageConfig defaultConfig() {
        return new PdfToImageConfig();
    }

    /**
     * 创建高质量配置
     * 
     * @return 高质量配置实例
     */
    public static PdfToImageConfig highQualityConfig() {
        PdfToImageConfig config = new PdfToImageConfig();
        config.setDpi(HIGH_QUALITY_DPI);
        config.setFormat("PNG");
        config.setAntiAliasing(true);
        return config;
    }

    /**
     * 创建低质量配置（用于预览）
     *
     * @return 低质量配置实例
     */
    public static PdfToImageConfig lowQualityConfig() {
        PdfToImageConfig config = new PdfToImageConfig();
        config.setDpi(LOW_QUALITY_DPI);
        config.setFormat("PNG");
        config.setQuality(0.7f);
        return config;
    }

    /**
     * 创建内存优化配置（适合大文件处理）
     *
     * @return 内存优化配置实例
     */
    public static PdfToImageConfig memoryOptimizedConfig() {
        PdfToImageConfig config = new PdfToImageConfig();
        config.setDpi(DEFAULT_DPI);
        config.setFormat("JPEG");
        config.setQuality(0.8f);
        config.setPageSize(5); // 每次只处理5页
        config.setAutoGc(true);
        config.setAntiAliasing(false); // 关闭抗锯齿以节省内存
        return config;
    }

    /**
     * 验证配置参数
     *
     * @throws IllegalArgumentException 如果配置参数无效
     */
    public void validate() {
        if (dpi <= 0) {
            throw new IllegalArgumentException("DPI必须大于0");
        }
        if (quality < 0.0f || quality > 1.0f) {
            throw new IllegalArgumentException("图片质量必须在0.0到1.0之间");
        }
        if (format == null || format.trim().isEmpty()) {
            throw new IllegalArgumentException("图片格式不能为空");
        }
        if (pageSize <= 0) {
            throw new IllegalArgumentException("分页大小必须大于0");
        }

        boolean formatSupported = false;
        String upperFormat = format.toUpperCase();
        for (String supportedFormat : SUPPORTED_FORMATS) {
            if (supportedFormat.equals(upperFormat)) {
                formatSupported = true;
                break;
            }
        }
        if (!formatSupported) {
            throw new IllegalArgumentException("不支持的图片格式: " + format);
        }
    }
}
