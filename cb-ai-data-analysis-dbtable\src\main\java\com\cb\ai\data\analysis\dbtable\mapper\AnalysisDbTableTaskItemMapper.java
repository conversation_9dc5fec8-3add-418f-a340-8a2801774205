package com.cb.ai.data.analysis.dbtable.mapper;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据导入工作日志Mapper接口
 * <AUTHOR>
 */
public interface AnalysisDbTableTaskItemMapper extends BaseMapper<AnalysisDbTableTaskItem> {

    /**
     * 分页查询数据导入工作日志
     * @param page
     * @param bigdataPoolJobLog
     * @return
     */
    Page<AnalysisDbTableTaskItem> pageTableTaskItemList(Page<?> page, @Param(Constants.ENTITY)AnalysisDbTableTaskItem bigdataPoolJobLog);

    /**
     * 批量新增数据导入工作
     * @param bigdataPoolJobLogs 数据导入工作日志集
     * @return 结果
     */
    int insertBatch(@Param("list") List<AnalysisDbTableTaskItem> bigdataPoolJobLogs);

}
