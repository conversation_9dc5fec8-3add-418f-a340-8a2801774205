package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketClassifyEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsProblemMarketVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/***
 * <AUTHOR>
 * 问题矩阵
 */
public interface SsProblemMarketService {


    /***
     * 问题分类分页查询
     * @param problemMarketVo
     * @return
     */
    Page<SsProblemMarketClassifyEntity> classifyPage(SsProblemMarketVo problemMarketVo);


    /***
     * 问题明细分页查询
     * @param problemMarketVo
     * @return
     */
    IPage<SsProblemMarketDetailsEntity> detailsPage(SsProblemMarketVo problemMarketVo);

    /***
     * 文件上传
     * @param file
     * @param domain
     * @throws Exception
     */
    void submitUpload(MultipartFile file,String domain)throws Exception;

    /***
     * 删除问题矩阵，同时删除文件上传里和明细
     * @param id
     * @throws Exception
     */
    void delProblemMarket(String id)throws Exception;


    /***
     * 删除问题矩阵详情
     * @param id
     * @throws Exception
     */
    int delProblemDetails(String id);

}
