package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.*;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile;
import com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo;
import com.cb.ai.data.analysis.petition.enums.FileAnalysisStatus;
import com.cb.ai.data.analysis.petition.enums.SsPetitionStatusEnum;
import com.cb.ai.data.analysis.petition.enums.WorkFlowJobStatusEnum;
import com.cb.ai.data.analysis.petition.enums.WorkFlowTypeEnum;
import com.cb.ai.data.analysis.petition.mapper.PetitionSourceFileMapper;
import com.cb.ai.data.analysis.petition.mapper.SsPetitionAnalyzedMapper;
import com.cb.ai.data.analysis.petition.mapper.SsPetitionOriginMapper;
import com.cb.ai.data.analysis.petition.service.ChatService;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileService;
import com.cb.ai.data.analysis.petition.service.SsWorkFlowJobService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import com.cb.ai.data.analysis.petition.redis.RedisCache;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * 信访源文件
 */
@Service
public class PetitionSourceFileServiceImpl extends BaseServiceImpl<PetitionSourceFileMapper, PetitionSourceFileEntity> implements PetitionSourceFileService {

    @Autowired
    private PetitionSourceFileMapper petitionSourceFileMapper;

    @Autowired
    private SsPetitionOriginMapper ssPetitionOriginMapper;

    @Autowired
    private SsWorkFlowJobService ssWorkFlowJobService;


    @Resource
    private ChatService chatService;

    @Resource
    private RedisCache redisCache;


    @Resource(name="workFlowThreadPoolExecutor")
    private ThreadPoolExecutor workFlowThreadPoolExecutor;


    @Autowired
    private SsPetitionAnalyzedMapper ssPetitionAnalyzedMapper;

    /***
     * 源文件分页查询
     * @param file
     * @return
     */
    @Override
    public IPage<SourceFileVo> querySourceFileList(PetitionSourceFile file) {
        Page<PetitionSourceFile> page = new Page<>(file.getPageNo(), file.getPageSize());
        QueryWrapper<PetitionSourceFile> wrapper = new QueryWrapper<>();
        wrapper.eq("t1.folder_id", file.getFolderId());
        wrapper.eq("del_flag",false);
        if(StringUtils.hasLength(file.getFileName())){
            wrapper.like("t1.filename",file.getFileName());
        }
        wrapper.orderByDesc("t1.create_time");
        return petitionSourceFileMapper.querySourceFileList(page,wrapper);
    }

    /***
     * 信访件解析 提交
     * @param ids
     * @param queryByStatus
     * @return
     */
    @Override
    public Result submitAnalyze(List<Long> ids, Boolean queryByStatus) {
        List<Integer> statusList = new ArrayList<>();

        if (queryByStatus) {
            statusList.add(SsPetitionStatusEnum.UN_ANALYZED.getStatus());
            statusList.add(SsPetitionStatusEnum.ANALYZED.getStatus());
        }

        List<SsPetitionOriginEntity> ssPetitionEntities = ssPetitionOriginMapper.findByIdsAndStatus(ids, statusList);
        if (CollectionUtils.isEmpty(ssPetitionEntities)) {
            return Result.fail("未查询到记录（或线索处于解析中，请等待）");
        }

        Integer analyzeContentLength = 10;

        for (int i = 0; i < ssPetitionEntities.size(); i += analyzeContentLength) {
            List<SsPetitionOriginEntity> subList;
            if (i + analyzeContentLength > ssPetitionEntities.size()) {
                subList = ssPetitionEntities.subList(i, ssPetitionEntities.size());
            } else {
                subList = ssPetitionEntities.subList(i, i + analyzeContentLength);
            }

            workFlowThreadPoolExecutor.submit(() -> {
                SsWorkFlowJobEntity ssWorkFlowJobEntity = ssWorkFlowJobService.publish(WorkFlowTypeEnum.ANALYZE_DATA);
                int dataCount=analyze(subList, ssWorkFlowJobEntity);
                updateFileStstus(subList,dataCount);
            });
        }

        return Result.success("提交解析任务成功！");
    }

    /**
     * 修改文件状态，解析过程中修改状态
     * @param ssPetitionEntities
     * @param dataCount
     */
    private void updateFileStstus(List<SsPetitionOriginEntity> ssPetitionEntities,int dataCount){
        try{
            Long batchNo = ssPetitionEntities.get(0).getUploadBatchNo();
            // 使用 Redis 原子操作减少数量
            Object fileId = redisCache.getCacheObject("batchNo_" + batchNo);
            redisCache.deleteObject("batchNo_" + batchNo);
            /*更新状态*/
            if(fileId != null) {
                UpdateWrapper<PetitionSourceFileEntity> editWrapper = new UpdateWrapper<>();
                editWrapper.eq("file_id",fileId);
                editWrapper.set("analysis_status",FileAnalysisStatus.COMPLETED.getCode());
                editWrapper.set("data_count",dataCount);
                this.baseMapper.update(editWrapper);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 源文件解析
     * @param ssPetitionEntities
     * @param ssWorkFlowJobEntity
     * @return
     */

    private int analyze(List<SsPetitionOriginEntity> ssPetitionEntities, SsWorkFlowJobEntity ssWorkFlowJobEntity) {
        int dataCount=0;
        try {
            String promote = getAnalysisPromote();

            String message = chatService.chat(promote, ssPetitionEntities.toString());
            message = replaceCharacter(message);
//            String message = workFlowRequestService.singleRequest(ssPetitionEntities, workFlowProperties.getAnalyzeData());


            ssWorkFlowJobEntity.setWorkFlowResponse(message);
            if (StringUtils.hasText(message)) {
                JSONArray messageArray = JSONArray.parseArray(message);
                List<SsPetitionAnalyzedEntity> ssPetitionAnalyzedEntities = messageArray.stream().map(element -> {
                    JSONObject jsonObject=JSONObject.parseObject(element.toString(),JSONObject.class);
                    /*SsPetitionAnalyzedEntity analyzedResult = JSONObject.parseObject(JSON.toJSONString(element), SsPetitionAnalyzedEntity.class);

                    analyzedResult.setRegisterDate(formatDate(analyzedResult.getRegisterDate()));
                    analyzedResult.setPetitionDate(formatDate(analyzedResult.getPetitionDate()));*/

                    SsPetitionAnalyzedEntity analyzedEntity = new SsPetitionAnalyzedEntity();
                    if(jsonObject.containsKey("origin_id") && !ObjectUtils.isEmpty(jsonObject.get("origin_id"))){
                        analyzedEntity.setOriginId(jsonObject.getLong("origin_id"));
                    }
                    if(jsonObject.containsKey("petition_person") && !ObjectUtils.isEmpty(jsonObject.get("petition_person"))){
                        analyzedEntity.setPetitionPerson(jsonObject.getString("petition_person"));
                    }
                    if(jsonObject.containsKey("petition_province") && !ObjectUtils.isEmpty(jsonObject.get("petition_province"))){
                        analyzedEntity.setPetitionProvince(jsonObject.getString("petition_province"));
                    }
                    if(jsonObject.containsKey("petition_city") && !ObjectUtils.isEmpty(jsonObject.get("petition_city"))){
                        analyzedEntity.setPetitionCity(jsonObject.getString("petition_city"));
                    }
                    if(jsonObject.containsKey("petition_district") && !ObjectUtils.isEmpty(jsonObject.get("petition_district"))){
                        analyzedEntity.setPetitionDistrict(jsonObject.getString("petition_district"));
                    }
                    if(jsonObject.containsKey("user_residence_province")&& !ObjectUtils.isEmpty(jsonObject.get("user_residence_province"))){
                        analyzedEntity.setUserResidenceProvince(jsonObject.getString("user_residence_province"));
                    }
                    if(jsonObject.containsKey("user_residence_city") && !ObjectUtils.isEmpty(jsonObject.get("user_residence_city"))){
                        analyzedEntity.setUserResidenceCity(jsonObject.getString("user_residence_city"));
                    }
                    if(jsonObject.containsKey("user_residence_district") && !ObjectUtils.isEmpty(jsonObject.get("user_residence_district"))){
                        analyzedEntity.setUserResidenceDistrict(jsonObject.getString("user_residence_district"));
                    }
                    if(jsonObject.containsKey("user_residence_street") && !ObjectUtils.isEmpty(jsonObject.get("user_residence_street"))){
                        analyzedEntity.setUserResidenceStreet(jsonObject.getString("user_residence_street"));
                    }
                    if(jsonObject.containsKey("petition_date") && !ObjectUtils.isEmpty(jsonObject.get("petition_date"))){
                        analyzedEntity.setPetitionDate(formatDate(jsonObject.getString("petition_date")));
                    }
                    if(jsonObject.containsKey("register_date") && !ObjectUtils.isEmpty(jsonObject.get("register_date"))){
                        analyzedEntity.setRegisterDate(formatDate(jsonObject.getString("register_date")));
                    }
                    if(jsonObject.containsKey("petition_belong_org") && !ObjectUtils.isEmpty(jsonObject.get("petition_belong_org"))){
                        analyzedEntity.setPetitionBelongOrg(jsonObject.getString("petition_belong_org"));
                    }
                    if(jsonObject.containsKey("petition_handle_suggestion") && !ObjectUtils.isEmpty(jsonObject.get("petition_handle_suggestion"))){
                        analyzedEntity.setPetitionHandleSuggestion(jsonObject.getString("petition_handle_suggestion"));
                    }
                    if(jsonObject.containsKey("petition_purpose_category") && !ObjectUtils.isEmpty(jsonObject.get("petition_purpose_category"))){
                        analyzedEntity.setPetitionPurposeCategory(jsonObject.getString("petition_purpose_category"));
                    }
                    if(jsonObject.containsKey("petition_purpose") && !ObjectUtils.isEmpty(jsonObject.get("petition_purpose"))){
                        analyzedEntity.setPetitionPurpose(jsonObject.getString("petition_purpose"));
                    }
                    if(jsonObject.containsKey("petition_domain_category") && !ObjectUtils.isEmpty(jsonObject.get("petition_domain_category"))){
                        analyzedEntity.setPetitionDomainCategory(jsonObject.getString("petition_domain_category"));
                    }
                    if(jsonObject.containsKey("petition_domain") && !ObjectUtils.isEmpty(jsonObject.get("petition_domain"))){
                        analyzedEntity.setPetitionDomain(jsonObject.getString("petition_domain"));
                    }
                    if(jsonObject.containsKey("petition_accused_person") && !ObjectUtils.isEmpty(jsonObject.get("petition_accused_person"))){
                        analyzedEntity.setPetitionAccusedPerson(jsonObject.getString("petition_accused_person"));
                    }
                    if(jsonObject.containsKey("brief") && !ObjectUtils.isEmpty(jsonObject.get("brief"))){
                        analyzedEntity.setBrief(jsonObject.getString("brief"));
                    }
                    analyzedEntity.setId(IdUtil.getSnowflakeNextId());
                    analyzedEntity.setCreateTime(LocalDateTime.now());

                    return analyzedEntity;
                }).collect(Collectors.toList());

                List<Long> originIds = ssPetitionAnalyzedEntities.stream().map(ssPetitionAnalyzedEntity -> Long.valueOf(ssPetitionAnalyzedEntity.getOriginId())).collect(Collectors.toList());

                //删除之前的解析记录
                QueryWrapper<SsPetitionAnalyzedEntity> analyzedDelWrapper = new QueryWrapper<SsPetitionAnalyzedEntity>();
                analyzedDelWrapper.in("origin_id",originIds);
                ssPetitionAnalyzedMapper.delete(analyzedDelWrapper);

                ssPetitionAnalyzedMapper.insert(ssPetitionAnalyzedEntities);

                QueryWrapper<SsPetitionOriginEntity> originEditWrapper = new QueryWrapper<SsPetitionOriginEntity>();
                originEditWrapper.in("id",originIds);
                SsPetitionOriginEntity ssPetitionOriginEntity=new SsPetitionOriginEntity();
                ssPetitionOriginEntity.setStatus(SsPetitionStatusEnum.ANALYZED.getStatus());
                ssPetitionOriginMapper.update(ssPetitionOriginEntity, originEditWrapper);
                //更新元数据状态

                dataCount=ssPetitionAnalyzedEntities.size();
                ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_SUCCESS.getStatus());
            } else {
                List<Long> ids=ssPetitionEntities.stream().map(SsPetitionOriginEntity::getId).collect(Collectors.toList());
                QueryWrapper<SsPetitionOriginEntity> originEditWrapper = new QueryWrapper<SsPetitionOriginEntity>();
                originEditWrapper.in("id",ids);
                SsPetitionOriginEntity ssPetitionOriginEntity=new SsPetitionOriginEntity();
                ssPetitionOriginEntity.setStatus(SsPetitionStatusEnum.UN_ANALYZED.getStatus());
                ssPetitionOriginMapper.update(ssPetitionOriginEntity, originEditWrapper);

                log.error("解析失败：调用模型未获取到数据"); // 这里返回具体的结果
                ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_FAILED.getStatus());
                ssWorkFlowJobEntity.setErrorMessage("解析失败：调用模型未获取到数据");

            }
        } catch (Exception e) {
            List<Long> ids=ssPetitionEntities.stream().map(SsPetitionOriginEntity::getId).collect(Collectors.toList());
            QueryWrapper<SsPetitionOriginEntity> originEditWrapper = new QueryWrapper<SsPetitionOriginEntity>();
            originEditWrapper.in("id",ids);
            SsPetitionOriginEntity ssPetitionOriginEntity=new SsPetitionOriginEntity();
            ssPetitionOriginEntity.setStatus(SsPetitionStatusEnum.UN_ANALYZED.getStatus());
            ssPetitionOriginMapper.update(ssPetitionOriginEntity, originEditWrapper);

            ssWorkFlowJobEntity.setJobStatus(WorkFlowJobStatusEnum.FINISHED_FAILED.getStatus());
            ssWorkFlowJobEntity.setErrorMessage("任务失败：" + e.getMessage());
            e.printStackTrace();
        }

        ssWorkFlowJobEntity.setFinishTime(new Date());
        ssWorkFlowJobService.updateById(ssWorkFlowJobEntity);
        return dataCount;
    }

    /***
     * 获取解析提示词，从毕晟拉去下来的数据。存储在analysis.md文件
     * @return
     * @throws IOException
     */
    private String getAnalysisPromote() throws IOException {
        InputStream is = this.getClass().getClassLoader().getResourceAsStream("promote/analysis.md");
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append(System.lineSeparator());
        }

        return content.toString();
    }


    private String replaceCharacter(String message) {
        try {
            JSONObject jsonObject=JSONObject.parseObject(message,JSONObject.class);
            JSONObject choicesObject=jsonObject.getJSONArray("choices").getJSONObject(0);
            JSONObject messageObject=choicesObject.getJSONObject("message");
            String content=messageObject.getString("content");
            System.out.println(content);
            Integer firstCharacterIndex = content.indexOf("[");
            Integer lastCharacterIndex = content.lastIndexOf("}");

            content = content.substring(firstCharacterIndex, lastCharacterIndex + 1)+"]";

            return content;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    private String formatDate(String dateStr) {
        SimpleDateFormat standardFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (dateStr == null || dateStr.isEmpty()) {
            return standardFormat.format(new Date());
        }

        // 支持的输入格式集合（注意顺序）
        String[] inputFormats = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyyMMddHH:mm:ss",
                "yyyyMMddHHmmss"
        };

        for (String format : inputFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(true); // 宽松模式，容忍非法日期
                Date date = sdf.parse(dateStr);
                return standardFormat.format(date);
            } catch (ParseException ignored) {
                // 尝试下一个格式
            }
        }
        return standardFormat.format(new Date());
    }
}
