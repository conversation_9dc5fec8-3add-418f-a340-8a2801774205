package com.cb.ai.data.analysis.ai.domain.response;

import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/10 09:25
 * @Copyright (c) 2025
 * @Description 统一对外返回结果类
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ResultData<T> implements Serializable {
    private static final long serialVersionUID = -7756576434460199415L;
    /**
     * 请求Id
     */
    private String requestId;

    /**
     * 父节点Id
     */
    private String parentNodeId;

    /**
     * 节点名称
     */
    private String nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 自定义节点名称
     */
    private String nodeDesc;

    /**
     * 数据状态
     */
    private ResultDataStatusEnum status;

    /**
     * 返回数据
     */
    private T result;

    /**
     * 异常信息
     */
    private Throwable error;

    public ResultData(T result) {
        this(null, result);
    }

    public ResultData(Throwable error) {
        this(ResultDataStatusEnum.ERROR, error);
    }

    public ResultData(ResultDataStatusEnum status, T result) {
        this(null, status, result);
    }

    public ResultData(ResultDataStatusEnum status, Throwable error) {
        this(null, status, error);
    }

    public ResultData(String nodeName, ResultDataStatusEnum status, T result) {
        this.nodeName = nodeName;
        this.status = status;
        this.result = result;
    }

    public ResultData(String nodeName, ResultDataStatusEnum status, Throwable error) {
        this.nodeName = nodeName;
        this.status = status;
        this.error = error;
    }

    public String getError() {
        return error != null? error.getMessage() : null;
    }

    public String getStatus() {
        return status != null? status.getStatus() : null;
    }
}
