//package com.cb.ai.data.analysis.ai.provider;
//
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.cb.ai.data.analysis.ai.enums.EventStreamState;
//import com.cb.ai.data.analysis.ai.model.AIContext;
//import com.cb.ai.data.analysis.ai.model.EventStreamResult;
//import com.cb.ai.data.analysis.ai.model.resp.AnalysisMaterialResultData;
//import com.cb.ai.data.analysis.ai.utils.AiWebClient;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Flux;
//
///**
// * 材料分析
// * <AUTHOR>
// */
//@Component
//public class AnalysisMaterialProvider extends AbstractAiProvider {
//    private final static String TAG = "ANALYSIS_MATERIAL";
//
//    private final AiWebClient aiWebClient;
//
//    public AnalysisMaterialProvider(AiWebClient aiWebClient) {
//        super(null);
//        this.aiWebClient = aiWebClient;
//    }
//
//    @Override
//    public boolean matcher(AIContext context) {
//        return TAG.equals(context.getTag());
//    }
//
//    @Override
//    Flux<EventStreamResult> run(String sessionId, String promote, AIContext context) {
//        return null;
//    }
//
//    public Flux<EventStreamResult> request(AIContext context) {
//        return Flux.from(aiWebClient.requestAnalysisMaterial(context))
//                .map(content -> {
//                    try {
//                        AnalysisMaterialResultData resultData = new AnalysisMaterialResultData();
//                        JSONObject object = JSON.parseObject(content);
//                        if (object.containsKey("reasoning_content")) {
//                            resultData.setReasoningContent(object.getString("reasoning_content"));
//                        }
//                        if (object.containsKey("content")) {
//                            resultData.setContent(object.getString("content"));
//                        }
//                        if (object.containsKey("FileId")) {
//                            resultData.setFileId(object.getString("FileId"));
//                        }
//                        return EventStreamResult.newInstance(context.getSessionId(), EventStreamState.streaming)
//                                .setRawData(content)
//                                .setData(resultData);
//                    } catch (Exception e) {
//                        return EventStreamResult.generateErrorStreamingResult(context.getSessionId(), e).setRawData(content);
//                    }
//                })
//                .startWith(EventStreamResult.generateStartResult(context.getSessionId()))
//                .onErrorResume(throwable -> Flux.just(EventStreamResult.generateErrorResult(context.getSessionId(), throwable)))
//                .concatWith(Flux.just(EventStreamResult.generateEndResult(context.getSessionId())));
//    }
//}
