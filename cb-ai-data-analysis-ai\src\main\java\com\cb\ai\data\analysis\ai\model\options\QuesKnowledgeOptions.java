package com.cb.ai.data.analysis.ai.model.options;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 知识库问题
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuesKnowledgeOptions extends AIOptions {
    /**
     * 知识库ID集
     */
    private List<String> baseIds;
    /**
     * 向量相似度检索（范围 0~1.0）将用户的输入转换为一个向量，然后和向量数据库中的文档向量做相似度比较。1 完全相同
     */
    private String similarityHolds;
    /**
     * 向量相似度检索的结果数量， 最相似的前 K 个
     */
    private String topK;
    /**
     *
     */
    private String fileIds;
}
