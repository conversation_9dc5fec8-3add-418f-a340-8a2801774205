package com.cb.ai.data.analysis.talkaudit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.talkaudit.domain.TalkPlanReview;
import com.cb.ai.data.analysis.talkaudit.mapper.TalkPlanReviewMapper;
import com.cb.ai.data.analysis.talkaudit.service.TalkPlanReviewService;
import org.springframework.stereotype.Service;

/**
 * 谈话方案审查(TalkPlanReview)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-09 14:36:27
 */
@Service("talkPlanReviewService")
public class TalkPlanReviewServiceImpl extends ServiceImpl<TalkPlanReviewMapper, TalkPlanReview> implements TalkPlanReviewService {

}

