package com.cb.ai.data.analysis.dbtable.model.req;

import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTable;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumn;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumnRule;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableIndex;
import jakarta.validation.Valid;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 动态数据表请求
 * <AUTHOR>
 */
@Data
public class TableDataReq {
    /**
     * 数据表
     */
    @Valid
    private AnalysisDbTable table;
    /**
     * 数据表字段
     */
    @Valid
    private List<AnalysisDbTableColumn> columns;
    /**
     * 数据表索引
     */
    @Valid
    private List<AnalysisDbTableIndex> indexs;
    /**
     * 数据表规则
     */
    @Valid
    private List<AnalysisDbTableColumnRule> rules;
}
