package com.cb.ai.data.json.tool.utils;

import com.alibaba.excel.EasyExcel;
import com.cb.ai.data.json.tool.constants.Constant;
import com.cb.ai.data.json.tool.domain.ReportContextData;
import com.xong.boot.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.SpreadsheetVersion;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 导出工具类
 *
 * <AUTHOR>
 * 2025/7/31
 */
@Slf4j
public class ExportUtils {
    /**
     * 将报表数据写入Excel文件
     *
     * @param data 报表数据对象，包含要写入Excel的数据和配置信息
     * @param path 文件保存的路径
     */
    public static void write2Excel(ReportContextData data, String path) {
        // 构造完整的文件路径和名称
        String filename = path + File.separator + data.getFileName() + Constant.DELIMITER + Constant.CSV_EXTENSION;
        // 清理非法字符并提取最后一级目录或文件名作为 sheet 名称
        String cleanedSheetName = data.getFileName().replaceAll("[:\\\\/?*\\[\\]]", "_");

        // Excel sheet 名称长度限制为 31 字符
        if (cleanedSheetName.length() > 31) {
            cleanedSheetName = cleanedSheetName.substring(0, 31);
        }

        // 获取数据列表
        List<LinkedHashMap<Integer, Object>> dataList = data.getData();

        // 使用 EasyExcel 写入数据到文件流
        try (OutputStream outputStream = new FileOutputStream(filename)) { // 使用 try-with-resources 确保流被正确关闭
            // 利用反射强制将EXCEL2007中的_maxTextLength属性值修改为Integer.MAX_VALUE；
            // 否则会单元格字符超出32767时抛出The maximum length of cell contents (text) is 32767 characters 异常
            resetCellMaxTextLength();
            EasyExcel.write(outputStream)
                    .charset(StandardCharsets.UTF_8)
                    .head(getHeadList(data.getColumns(), data.getHeaderMap())) // 配置表头
                    .sheet(cleanedSheetName) // 使用清理后的 sheet 名称
                    .doWrite(dataList); // 写入数据
        } catch (Exception e) {
            log.error("Error writing to Excel: {},文件名为：{}", e.getMessage(), data.getFileName());
            e.printStackTrace();
        }
    }

    /**
     * 根据报表列配置生成Excel表头列表
     *
     * @param columns 报表列配置列表
     * @return 表头列表，每个元素是一个字符串列表，表示一列的多级表头
     */
    private static List<List<String>> getHeadList(LinkedList<ReportContextData.ReportConfig> columns, Map<String, String> headerMap) {
        // 确保返回的表头列表是可修改的
        List<List<String>> collect = columns.stream()
                .map(config -> {
                    List<String> head = new ArrayList<>();
                    String title = config.getTitle();
                    if (title.contains(Constant.DELIMITER)) {
                        // 如果标题包含字段分隔符，则按字段分隔符进行拆分,设置成多级表头
                        String[] titles = title.split(Pattern.quote(Constant.DELIMITER));
                        LinkedList<String> headerList = Arrays.stream(titles).map(t -> {
                            String h = headerMap.get(t);
                            return StringUtils.isNotBlank(h) ? h : t;
                        }).collect(Collectors.toCollection(LinkedList::new));
                        head.addAll(headerList);
                    } else {
                        String h = headerMap.get(title);
                        head.add(StringUtils.isNotBlank(h) ? h : title); // 每个表头字段作为单独的一列
                    }
                    return head;
                })
                .collect(Collectors.toList());
        return collect;
    }

    // 利用反射强制将EXCEL2007中的_maxTextLength属性值修改为Integer.MAX_VALUE
    public static void resetCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
