package com.cb.ai.data.analysis.ai.component.choreography.engine;

import com.cb.ai.data.analysis.ai.component.choreography.model.*;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.func.TriConsumer;

import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 17:07
 * @Copyright (c) 2025
 * @Description 基础属性节点
 */
public abstract class BaseAttributeNode<E, R, T extends BaseAttributeNode<E, R, T>> extends BaseNode implements INodeAttributeOperation<E, R> {
    /* 节点属性 */
    private final NodeAttribute<E, R> nodeAttribute;

    /* 流程属性 */
    private FlowAttribute flowAttribute;

    public BaseAttributeNode() {
        this.nodeAttribute = new NodeAttribute<>();
        this.flowAttribute = new FlowAttribute();
    }

    @Override
    public String getNodeId() {
        return nodeAttribute.getNodeId();
    }

    @Override
    public String getNodeName() {
        nodeName(this.getClass().getSimpleName());
        return nodeAttribute.getNodeName();
    }

    @Override
    public String getNodeDesc() {
        nodeDesc(this.getClass().getSimpleName());
        return nodeAttribute.getNodeDesc();
    }

    @Override
    public final T nodeId(String nodeId) {
        nodeAttribute.setNodeId(nodeId);
        return (T) this;
    }

    @Override
    public final T nodeName(String nodeName) {
        nodeAttribute.setNodeName(nodeName);
        return (T) this;
    }

    @Override
    public final T nodeDesc(String nodeDesc) {
        nodeAttribute.setNodeDesc(nodeDesc);
        return (T) this;
    }

    @Override
    public T parentNodeId(String parentNodeId) {
        nodeAttribute.setParentNodeId(parentNodeId);
        return (T) this;
    }

    @Override
    public final T context(E context, boolean override) {
        if (override || this.getRequestContext() == null) {
            this.nodeAttribute.setRequestContext(context);
        }
        return (T) this;
    }

    @Override
    public final T context(Supplier<E> contextFun, boolean override) {
        if (contextFun != null) {
            context(contextFun.get(), override);
        }
        return (T) this;
    }

    @Override
    public final T context(Function<FlowContext, E> requestContextFun) {
        if (requestContextFun != null) {
            this.nodeAttribute.setRequestContextFun(requestContextFun);
        }
        return (T) this;
    }

    @Override
    public final T dataDispose(Function<R, R> disposeFun) {
        if (disposeFun != null) {
            nodeAttribute.setDataDisposeFun(disposeFun);
        }
        return (T) this;
    }

    @Override
    public final INodeAttributeOperation<E, R> dataDispose(Consumer<R> disposeFun) {
        if (disposeFun != null) {
            nodeAttribute.setDataDisposeCon(disposeFun);
        }
        return (T) this;
    }

    @Override
    public final T dispose(TriConsumer<String, R, FlowContext> processDataFun) {
        nodeAttribute.setProcessDataFun(processDataFun);
        return (T) this;
    }

    @Override
    public final T addBeforeData(BiFunction<NodeContext, E, R> beforeFun) {
        return addBeforeData(ResultDataStatusEnum.STREAMING, beforeFun);
    }

    @Override
    public final T addBeforeData(ResultDataStatusEnum statusEnum, BiFunction<NodeContext, E, R> beforeFun) {
        if (beforeFun != null) {
            nodeAttribute.addBeforeFun(new InsertData<E, R>(Optional.ofNullable(statusEnum).orElse(ResultDataStatusEnum.UNKNOWN), beforeFun));
        }
        return (T) this;
    }

    @Override
    public final T addAfterData(BiFunction<NodeContext, E, R> afterFun) {
        return addAfterData(ResultDataStatusEnum.STREAMING, afterFun);
    }

    @Override
    public final T addAfterData(ResultDataStatusEnum statusEnum, BiFunction<NodeContext, E, R> afterFun) {
        if (afterFun != null) {
            nodeAttribute.addAfterFun(new InsertData<E, R>(Optional.ofNullable(statusEnum).orElse(ResultDataStatusEnum.UNKNOWN), afterFun));
        }
        return (T) this;
    }

    @Override
    public final T hideThinking() {
        nodeAttribute.setHideThinking(true);
        return (T) this;
    }

    @Override
    public final T hideContent() {
        nodeAttribute.setHideContent(true);
        return (T) this;
    }

    @Override
    public T maxTokens(Function<Integer, Integer> maxTokensFun) {
        nodeAttribute.setMaxTokensFun(maxTokensFun);
        return (T) this;
    }

    @Override
    public T topK(Function<Integer, Integer> topKFun) {
        nodeAttribute.setTopKFun(topKFun);
        return (T) this;
    }

    @Override
    public T topP(Function<Float, Float> topPFun) {
        nodeAttribute.setTopPFun(topPFun);
        return (T) this;
    }

    @Override
    public T temperature(Function<Float, Float> temperatureFun) {
        nodeAttribute.setTemperatureFun(temperatureFun);
        return (T) this;
    }

    protected final FlowAttribute getFlow() {
        return flowAttribute;
    }

    protected final void setFlow(FlowAttribute flowAttribute) {
        this.flowAttribute = flowAttribute;
    }

    protected final FlowContext getFlowContext() {
        return flowAttribute.getFlowContext();
    }

    protected final NodeAttribute<E, R> getNode(){
        return nodeAttribute;
    }

    protected final E getRequestContext(){
        return nodeAttribute.getRequestContext();
    }

    protected final NodeContext getNodeContext(){
        return nodeAttribute.getNodeContext();
    }

    protected final Function<FlowContext, E> getRequestContextFun(){
        return nodeAttribute.getRequestContextFun();
    }

    protected final String getParentNodeId() {
        return nodeAttribute.getParentNodeId();
    }

    public final void clear() {
        nodeAttribute.clear();
    }

}
