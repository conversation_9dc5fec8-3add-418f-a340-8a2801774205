package com.xong.boot.common.config;

import com.xong.boot.common.api.Result;
import com.xong.boot.common.api.ResultCode;
import com.xong.boot.common.exception.XServerException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.nio.file.AccessDeniedException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 异常管理器
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionConfig {

    @ExceptionHandler(Exception.class)
    public Result Exception(Exception e) {
        e.printStackTrace();
        return Result.fail(e.getMessage());
    }

    // 权限校验异常
    @ExceptionHandler(AccessDeniedException.class)
    public Result accessDeniedException(AccessDeniedException e) {
        e.printStackTrace();
        Result result = Result.fail(ResultCode.UNAUTHORIZED_FAIL);
        if (e.getMessage().startsWith("Full authentication")) {
            result.setCode(ResultCode.RE_LOGIN.getCode());
            result.setMessage("请先登录系统，再进行操作");
        } else {
            result.setMessage(e.getMessage());
        }
        return result;
    }

    // 拒绝访问异常
    @ExceptionHandler(AuthorizationDeniedException.class)
    public Result accessDeniedException(AuthorizationDeniedException e) {
        Result result = Result.fail(ResultCode.ACCESS_DENIED_FAIL);
        if (!"Access Denied".equalsIgnoreCase(e.getMessage())) {
            result.setMessage(e.getMessage());
        }
        return result;
    }

    // 参数错误
    @ExceptionHandler(BindException.class)
    public Result bindException(BindException e) {
        e.printStackTrace();
        return Result.fail(String.format("参数错误：%s", e.getMessage()));
    }

    // 参数错误
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result methodArgumentNotValidException(MethodArgumentNotValidException e) {
        e.printStackTrace();
        Result result = Result.fail(String.format("参数错误：%s", e.getMessage()));
        List<ObjectError> objectErrors = e.getBindingResult().getAllErrors();
        if (!CollectionUtils.isEmpty(objectErrors)) {
            Set<String> errors = new HashSet<>();
            for (ObjectError objectError : objectErrors) {
                errors.add(objectError.getDefaultMessage());
            }
            result.setMessage(String.join("；", errors));
        }
        return result;
    }

    // 参数格式错误
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Result httpMessageNotReadableException(HttpMessageNotReadableException e) {
        e.printStackTrace();
        return Result.fail(String.format("参数类型不匹配:%s", e.getMessage()));
    }

    // 请求不存在
    @ExceptionHandler(NoHandlerFoundException.class)
    public Result noHandlerFoundException(NoHandlerFoundException e) {
        e.printStackTrace();
        return Result.fail(e.getMessage());
    }

    // 请求方式错误
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        e.printStackTrace();
        return Result.fail(e.getMessage());
    }

    // 数据库相关异常
    @ExceptionHandler(DataAccessException.class)
    public Result dataAccessException(DataAccessException e) {
        e.printStackTrace();
        if (e instanceof DuplicateKeyException) { // 字段重复
            return Result.fail(String.format("字段重复:%s", e.getMessage()));
        } else if (e instanceof DataIntegrityViolationException) { // 数据完整性
            if(e.getMessage().contains("foreign key constraint")) {
                return Result.fail("外键约束错误");
            } else {
                return Result.fail(String.format("数据完整性:%s", e.getMessage()));
            }
        }
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler(XServerException.class)
    public Result xException(XServerException e) {
        e.printStackTrace();
        return Result.fail(e.getMessage());
    }
}
