package com.cb.ai.data.analysis.knowledge.service.impl;


import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeVector;
import com.cb.ai.data.analysis.knowledge.domain.vo.CommonVectorVo;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeVectorVo;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeVectorService;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.HttpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/***
 * <AUTHOR>
 * 向量库
 */
@Service
public class KnowledgeVectorServiceImpl implements KnowledgeVectorService {


    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/rag/embedding'}")
    private String knowledgeVectorUrl;

    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization;

    @Override
    public List<KnowledgeVectorVo> searchVector(KnowledgeVector knowledgeVector) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeVector)){
                throw new XServiceException("请求对象为空！");
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(knowledgeVectorUrl + "/search", JSON.toJSONString(knowledgeVector), headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            return JSON.parseArray(httpResponse.body(),KnowledgeVectorVo.class);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public List<KnowledgeVectorVo> searchKnowledgeVector(KnowledgeVector knowledgeVector) throws Exception {
        try{
            if(ObjectUtils.isEmpty(knowledgeVector)){
                throw new XServiceException("请求对象为空！");
            }
            if(ObjectUtils.isEmpty(knowledgeVector.getPageNum()) && knowledgeVector.getPageNum()<=0){
                knowledgeVector.setPageNum(0);
            }
            if(ObjectUtils.isEmpty(knowledgeVector.getPageSize()) &&  knowledgeVector.getPageSize()<=0){
                knowledgeVector.setPageSize(10);
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(knowledgeVectorUrl + "/searchVectors", JSON.toJSONString(knowledgeVector), headers);
            if(ObjectUtils.isEmpty(httpResponse)){
                throw new XServiceException("请求响应为空！");
            }
            if(httpResponse.getStatus() != HttpStatus.HTTP_OK){
                throw new XServiceException(httpResponse.body());
            }
            JSONObject json=JSONObject.parseObject(httpResponse.body(),JSONObject.class);
            return JSON.parseArray(json.getString("data"),KnowledgeVectorVo.class);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public List<CommonVectorVo> searchNLSQLVector(KnowledgeVector knowledgeVector) throws Exception {
        try {
            if (ObjectUtils.isEmpty(knowledgeVector)) {
                throw new XServiceException("请求对象为空！");
            }
            if (ObjectUtils.isEmpty(knowledgeVector.getPageNum()) && knowledgeVector.getPageNum() <= 0) {
                knowledgeVector.setPageNum(0);
            }
            if (ObjectUtils.isEmpty(knowledgeVector.getPageSize()) && knowledgeVector.getPageSize() <= 0) {
                knowledgeVector.setPageSize(10);
            }
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpResponse httpResponse = HttpUtils.sendPost(knowledgeVectorUrl + "/searchVectors", JSON.toJSONString(knowledgeVector), headers);
            if (ObjectUtils.isEmpty(httpResponse)) {
                throw new XServiceException("请求响应为空！");
            }
            if (httpResponse.getStatus() != HttpStatus.HTTP_OK) {
                throw new XServiceException(httpResponse.body());
            }
            JSONObject json = JSONObject.parseObject(httpResponse.body(), JSONObject.class);
            return JSON.parseArray(json.getString("data"), CommonVectorVo.class);
        } catch (XServiceException e) {
            throw new XServiceException(e.getMessage());
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public boolean deleteVectorByIds(String baseId, List<String> ids) {
        Map<String, String> headers = new HashMap<>();
        headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        headers.put(Header.AUTHORIZATION.getValue(), Authorization);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("baseId", baseId);
        jsonObject.put("ids", ids);
        HttpResponse httpResponse = HttpUtils.sendPost(knowledgeVectorUrl + "/deleteVectors", jsonObject.toString(), headers);
        if (ObjectUtils.isEmpty(httpResponse)) {
            return false;
        }
        if (httpResponse.getStatus() != HttpStatus.HTTP_OK) {
            return false;
        }
        return true;
    }

}
