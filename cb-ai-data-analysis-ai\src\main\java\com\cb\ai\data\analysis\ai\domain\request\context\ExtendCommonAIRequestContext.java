package com.cb.ai.data.analysis.ai.domain.request.context;

import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:19
 * @Copyright (c) 2025
 * @Description 扩展的AI通用请求上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExtendCommonAIRequestContext<T> extends CommonAIRequestContext {
    /** 个性化的AI请求参数（放这里） **/
    @Valid
    private T paramData;

}
