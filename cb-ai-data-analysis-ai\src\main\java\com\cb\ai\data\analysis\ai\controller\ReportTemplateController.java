package com.cb.ai.data.analysis.ai.controller;

import com.alibaba.fastjson2.JSONArray;
import com.cb.ai.data.analysis.ai.constant.AiConstants;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.utils.FileNameUtils;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告模板 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(AiConstants.API_AI_ROOT_PATH + "/report")
public class ReportTemplateController {
    /**
     * 获取模板列表
     */
    @GetMapping("/list/template")
    public Result listTemplate() {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            Resource[] resources = resolver.getResources("classpath*:templates/report/纪委/*.docx");
            List<Map<String, String>> listData = new ArrayList<>();
            for (Resource resource : resources) {
                String filename = resource.getFilename();
                if (StringUtils.isNotBlank(filename)) {
                    Map<String, String> item = new HashMap<>();
                    String name = FileNameUtils.mainName(filename);
                    item.put("value", name);
                    item.put("label", name);
                    item.put("filename", filename);
                    listData.add(item);
                }
            }
            return Result.successData(listData);
        } catch (Exception e) {
            return Result.fail("获取模板列表失败：" + e.getMessage());
        }
    }
}
