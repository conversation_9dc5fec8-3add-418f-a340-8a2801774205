package com.cb.ai.data.analysis.dbtable.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumnRule;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableColumnRuleService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 动态数据表字段规则 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table/column/rule")
public class AnalysisDbTableCoolumnRuleController extends BaseController<AnalysisDbTableColumnRuleService, AnalysisDbTableColumnRule> {
    /**
     * 分页获取数据表列表
     * @param params 查询条件
     */
    @GetMapping("/page")
//    @PreAuthorize("hasAuthority('dbtable:table:list')")
    public Result page(AnalysisDbTableColumnRule params) {
        LambdaQueryWrapper<AnalysisDbTableColumnRule> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda()
                .orderByAsc(AnalysisDbTableColumnRule::getSortOn)
                .orderByAsc(AnalysisDbTableColumnRule::getId);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
