package com.cb.ai.data.analysis.ai.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import com.cb.ai.data.analysis.ai.domain.common.Context;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/10 16:16
 * @Copyright (c) 2025
 * @Description md提示词提取器
 */
public class MdPromoteExtractorUtil {
    /**
     * 系统提示词正则表达式
     */
    private static final Pattern SYSTEM_PROMOTE_PATTERN = Pattern.compile("@systemPromote\\s*([\\s\\S]*?)(?=@end|$)");
    /**
     * 用户提示词正则表达式
     */
    private static final Pattern USER_PROMOTE_PATTERN = Pattern.compile("@userPromote\\s*([\\s\\S]*?)(?=@end|$)");
    /**
     * 自定义的提示词内部变量替换正则表达式
     */
    private static final Pattern REPLACE_CODE_PATTERN = Pattern.compile("@#([^#]+)#@");

    /**
     * @description 提取系统提示词，注意，需要有@systemPromote和@end标签
     * @param filePath 提示词文件路径
     * @return 系统提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getSysPromote(String filePath) {
        String content = extractPromote(filePath);
        Matcher systemMatcher = SYSTEM_PROMOTE_PATTERN.matcher(content);
        if (systemMatcher.find()) {
            return systemMatcher.group(1).trim();
        }
        return "";
    }

    /**
     * @description 提取系统提示词，注意，需要有@systemPromote和@end标签
     * @param markdownFile 提示词md文件
     * @return 系统提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getSysPromote(File markdownFile) {
        String content = extractPromote(markdownFile);
        Matcher systemMatcher = SYSTEM_PROMOTE_PATTERN.matcher(content);
        if (systemMatcher.find()) {
            return systemMatcher.group(1).trim();
        }
        return "";
    }

    /**
     * @description 提取用户提示词，注意，需要有@userPromote和@end标签
     * @param filePath 提示词文件路径
     * @return 用户提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getUserPromote(String filePath) {
        String content = extractPromote(filePath);
        Matcher userMatcher = USER_PROMOTE_PATTERN.matcher(content);
        if (userMatcher.find()) {
            return userMatcher.group(1).trim();
        }
        return "";
    }

    /**
     * @description 提取用户提示词，注意，需要有@userPromote和@end标签
     * @param markdownFile 提示词文件
     * @return 用户提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getUserPromote(File markdownFile) {
        String content = extractPromote(markdownFile);
        Matcher userMatcher = USER_PROMOTE_PATTERN.matcher(content);
        if (userMatcher.find()) {
            return userMatcher.group(1).trim();
        }
        return "";
    }

    /**
     * @description 提取提示词
     * @param filePath 提示词文件路径
     * @return 提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getPromote(String filePath) {
        return extractPromote(filePath);
    }

    /**
     * @description 提取提示词
     * @param markdownFile 提示词文件
     * @return 提示词
     * @exception IOException
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String getPromote(File markdownFile) {
        return extractPromote(markdownFile);
    }

    /**
     * @description 用户提示词变量替换
     * @param filePath 提示词文件路径
     * @param valueMap 变量值map
     * @return 提示词
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceUserPromoteTags(String filePath, Context context) {
        return replaceUserPromoteTags(filePath, JsonUtil.toMap(context));
    }

    /**
     * @description 用户提示词变量替换
     * @param filePath 提示词文件路径
     * @param valueMap 变量值map
     * @return 提示词
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceUserPromoteTags(String filePath, Map<String, Object> valueMap) {
        return replaceMdTags(getUserPromote(filePath), valueMap);
    }

    /**
     * @description 系统提示词变量替换
     * @param filePath 提示词文件路径
     * @param valueMap 变量值map
     * @return 提示词
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceSysPromoteTags(String filePath, Context context) {
        return replaceSysPromoteTags(filePath, JsonUtil.toMap(context));
    }

    /**
     * @description 系统提示词变量替换
     * @param filePath 提示词文件路径
     * @param valueMap 变量值map
     * @return 提示词
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceSysPromoteTags(String filePath, Map<String, Object> valueMap) {
        return replaceMdTags(getSysPromote(filePath), valueMap);
    }

    /**
     * @description 提示词变量替换
     * @param markdownContent 提示词内容
     * @param valueMap 变量值map
     * @return 提示词
     * @createtime 2025/7/10 下午4:31
     * <AUTHOR>
     * @version 1.0
     */
    public static String replaceMdTags(String markdownContent, Map<String, Object> valueMap) {
        // 匹配 @#var_xxx 格式的标记
        Matcher matcher = REPLACE_CODE_PATTERN.matcher(markdownContent);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            // 获取变量名
            String key = matcher.group(1);
            // 从map中获取值，如果没有则设为空字符串
            String value = Convert.toStr(valueMap.get(key), "");
            // 替换匹配到的标记
            matcher.appendReplacement(result, value);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    private static String extractPromote(String filePath) {
        try (InputStream inputStream = CommonUtil.getFileInputStream(filePath)) {
            return IoUtil.read(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e){
            throw new IllegalArgumentException("Error reading resource: " + filePath, e);
        }
    }

    private static String extractPromote(File markdownFile) {
        try {
            return FileUtils.readFileToString(markdownFile, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new IllegalArgumentException("Error reading resource: " + markdownFile.getAbsolutePath(), e);
        }
    }

}
