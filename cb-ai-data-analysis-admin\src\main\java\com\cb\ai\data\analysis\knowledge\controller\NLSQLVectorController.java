package com.cb.ai.data.analysis.knowledge.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeVector;
import com.cb.ai.data.analysis.knowledge.domain.req.NlsqlData;
import com.cb.ai.data.analysis.knowledge.domain.vo.CommonVectorVo;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeService;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeVectorService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/nlsql/vector")
public class NLSQLVectorController {

    @Autowired
    private KnowledgeService knowledgeService;

    @Autowired
    private KnowledgeVectorService knowledgeVectorService;

    /***
     * 知识库向量库查询
     * @param knowledgeVector
     * @return
     */
    @PostMapping("/page")
    public Result searchNLSQLVector(@RequestBody KnowledgeVector knowledgeVector) {
        try {
            List<CommonVectorVo> knowledgeVectorVo = knowledgeVectorService.searchNLSQLVector(knowledgeVector);
            return Result.successData(knowledgeVectorVo);
        } catch (XServiceException e) {
            e.printStackTrace();
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("向量库查询失败！");
        }
    }

    /***
     * NLSQL模型示例新增
     * @param nlsqlData
     * @return
     */
    @PostMapping("/saveNlsqlToFile")
    public Result saveNlsqlToFile(@RequestBody NlsqlData nlsqlData) {
        String dataArray = JSONArray.toJSONString(nlsqlData.getJsonStr());
        if (JSON.isValid(dataArray)) {
            return Result.success(knowledgeService.saveNlsqlToFile(nlsqlData));
        } else {
            return Result.fail("JSON格式错误！");
        }
    }

}
