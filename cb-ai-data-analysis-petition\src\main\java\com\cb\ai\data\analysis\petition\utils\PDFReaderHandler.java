package com.cb.ai.data.analysis.petition.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.IOException;
import java.io.InputStream;

public class PDF<PERSON><PERSON>erHandler extends FileReaderHandler<String> {
    @Override
    public String read(InputStream inputStream) throws IOException {
        PDDocument document = PDDocument.load(inputStream);
        PDFTextStripper stripper = new PDFTextStripper();
        return stripper.getText(document);
    }
}
