package com.cb.ai.data.analysis.ai.component.extensions;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.flow.FlowChain;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIFileAnalysis;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateAIKnowledge;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.request.context.ExtendCommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.xong.boot.common.exception.CustomException;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI文件分析扩展
 */
@ExtensionProvider(desc = "私有化AI文件分析扩展", businessScenes = {
    @Route(tag = "文件分析", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivateFileAnalysisExtension implements PrivateAIExtension<ExtendCommonAIRequestContext<?>> {

    @Override
    @SuppressWarnings("unchecked")
    public Flux<ResultData<PrivateAIBackData>> invoke(ExtendCommonAIRequestContext<?> requestContext) {
        if (ArrayUtil.isNotEmpty(requestContext.getBaseIds())) {
            return (Flux<ResultData<PrivateAIBackData>>) FlowChain.newChain(requestContext.getRequestId())
                .addSerialNode(PrivateAIFileAnalysis.class, node ->
                    node.context(requestContext)
                        .dispose((nodeId, data, context) -> {
                        List<Object> dataList = data.getDataList();
                        if (CollectionUtil.isNotEmpty(dataList)) {
                            List<String> list = Optional.ofNullable(context.<List<String>>get("fileIds")).orElseGet(ArrayList::new);
                            for (Object object : dataList) {
                                if (object instanceof Map<?,?> objectMap) {
                                    if (ObjectUtil.isNotEmpty(objectMap.get("FileId"))) {
                                        list.add(objectMap.get("FileId").toString());
                                    }
                                }
                            }
                            context.set("fileIds", list);
                        }
                    })
                )
                .addSerialNode(PrivateAIKnowledge.class, node ->
                    node.context(flowContext -> {
                        List<String> fileIds = flowContext.get("fileIds");
                        if (CollectionUtil.isEmpty(fileIds)) {
                            throw new CustomException("材料分析AI接口未能正常返回文件ID");
                        }
                        ExtendCommonAIRequestContext<?> newContext = requestContext.copy();
                        newContext.setExtendData(JsonMap.of("fileIds", fileIds));
                        return newContext;
                    })
                )
                .execute();
        } else {
            return new PrivateAIFileAnalysis().processData(requestContext);
        }

    }
}
