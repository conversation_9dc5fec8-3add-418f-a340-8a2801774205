package com.cb.ai.data.analysis.graph.domain.entity.basic;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识图谱-干部信息(GraphCadresInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-03 13:50:42
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class GraphCadresInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //工作单位
    @TableField(condition = SqlCondition.LIKE)
    @ExcelIgnore
    private String workUnit;

    //姓名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "姓名")
    private String name;

    //性别
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "性别")
    private String sex;

    //身份证号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "身份证号")
    private String idCard;

    //民族
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "民族")
    private String nation;

    //籍贯
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "籍贯")
    private String nativePlace;

    //出生地
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "出生地")
    private String birthPlace;

    //出生年月
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "出生年月")
    private LocalDateTime birthday;

    //政治面貌
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "政治面貌")
    private String politicalIdentity;

    //入党时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "入党时间")
    private LocalDateTime partyJoinTime;

    //参加工作时间
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "参加工作时间")
    private LocalDateTime startWorkTime;

    //现职务
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "现职务")
    private String workPost;

    //会话临时解析数据
    @ExcelIgnore
    private String sessionId;


}

