package com.cb.ai.data.analysis.ai.component.flows.chuangbo;


import com.cb.ai.data.analysis.ai.utils.MergeUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 09:42
 * @Copyright (c) 2025
 * @Description 私有化AI知识库问答
 */
public class PrivateAIKnowledge extends BasePrivateAiPropertiesNode {

    @Override
    public String getNodeName() {
        return "私有化-知识库问答-接口";
    }

    @Override
    public String setRequestUrl() {
        return MergeUtil.mergePath(aiProp.getBaseUrl(), aiProp.getKnowledge());
    }

}
