<template>
  <div class="chat-tools">
    <div
      v-for="item in toolItems"
      :key="item.id"
      @click="onClickToolItem(item)"
      class="chat-tools-item"
    >
      <a-popover v-model:open="item.visible" trigger="click">
        <template #title>
          <ToolHeader :title="item.name" @click-close="onClickClose(item)" />
        </template>
        <template #content>
          <Knowledge v-if="item.id === 'knowledge'" />
        </template>
        <a-tooltip :mouse-enter-delay="1">
          <template #title>{{ item.description }}</template>
          <div class="chat-tools-item-button">
            <x-icon :type="item.icon" />
            <span class="_item-button-label">{{ item.name }}</span>
          </div>
        </a-tooltip>
      </a-popover>
    </div>
    <div class="chat-tools-item">
      <div class="chat-tools-item-button" @click="onClickActions">
        <template v-if="isExpand || aiAppStore.chatTools.length === toolItems.length">
          <x-icon type="ToolOutlined" />
          <span class="_item-button-label"> 设置 </span>
        </template>
        <template v-else>
          <x-icon type="AppstoreAddOutlined" />
          <span class="_item-button-label"> 更多 </span>
        </template>
      </div>
    </div>
    <a-modal
      width="580px"
      v-model:open="actionsVisible"
      title="技能排序和展示设置"
      wrap-class-name="chat-tools_modal"
    >
      <div class="xong-layout-chat-tool-drag-tips">
        我们会按照你的使用进行自动排序，你可以在这个页面将你常用的技能置顶在前面。
      </div>
      <Draggable
        v-model="actionsTools"
        group="tools"
        handle=".drag-handle"
        item-key="id"
        animation="300"
        @end="onDragEnd"
        :move="onDragMove"
      >
        <template #item="{ element }">
          <div class="xong-layout-chat-tool-drag-item">
            <div class="xong-layout-chat-tool-drag-item_handle" :class="{ 'drag-handle': !element.fixed, 'disabled-icon': element.fixed }">
              <x-icon style="font-size: 18px;color: #333333;" type="HolderOutlined"></x-icon>
            </div>
            <div class="xong-layout-chat-tool-drag-item_text">
              <div class="xong-layout-chat-tool-drag-item_text__title">
                <img :src="element.picture" alt="">
                {{ element.name }}
              </div>
              <div class="xong-layout-chat-tool-drag-item_text__desc">{{ element.description }}</div>
            </div>
            <a-tooltip placement="top">
              <template #title>
                <span>{{element.fixed?'取消置顶':'置顶该功能'}}</span>
              </template>
              <div class="xong-layout-chat-tool-drag-item_fixed" :class="{ 'fixed-icon': element.fixed }">
                <x-icon type="PushpinFilled" style="font-size: 18px;"  @click="toggleFixed(element)"></x-icon>
              </div>
            </a-tooltip>
          </div>
        </template>
      </Draggable>
      <template #footer>
        <div class="xong-layout-chat-tool-drag-footer">
          <a-button @click="onClickToolDragCancelOperate">取消</a-button>
          <a-button @click="onResetActions">重置</a-button>
          <a-button :disabled="!isChanged" type="primary" @click="onClickToolDragConfirmOperate">确定</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref,computed,watch } from 'vue'
import { useAiAppStore } from '@/stores'
import ToolHeader from './Header.vue'
import Draggable from 'vuedraggable'
import { Knowledge } from '../modal'
import { AI_TOOLS_KEY, type AiTool, defaultTools } from '@/config/ai.config'
import XIcon from '@/components/XIcon/XIcon'
import {message} from 'ant-design-vue'
const aiAppStore = useAiAppStore()

const emits = defineEmits(['clickItem'])
const isExpand = ref(false)
const actionsVisible = ref(false)
const actionsTools = ref<AiTool[]>([])
const toolItems = ref<Array<AiTool & { visible?: boolean }>>([])
const savedTools = ref<AiTool[]>([])


const isChanged = computed(() => {
  return JSON.stringify(actionsTools.value) !== JSON.stringify(savedTools.value)
})

// 点击栏目
function onClickToolItem(item: AiTool) {
  emits('clickItem', item)
}

// 点击关闭
function onClickClose(e: AiTool & { visible?: boolean }) {
  e.visible = false
}

// 点击操作
function onClickActions() {
  if (isExpand.value) {
    // actionsTools.value = [...aiAppStore.chatTools]
    actionsVisible.value = true
  } else {
    // toolItems.value = aiAppStore.chatTools
    isExpand.value = true
  }
}

// 技能排序和展示重置
function onResetActions() {
  aiAppStore.resetChatTools()
  message.success('重置成功!')
  actionsVisible.value = false;
  loadData()
}


// 固定切换
const toggleFixed = (item: AiTool) => {
  item.fixed = !item.fixed
  // 先移除当前项
  const withoutCurrent = actionsTools.value.filter(i => i.id !== item.id)
  if (item.fixed) {
    // 插入最前面
    actionsTools.value = [item, ...withoutCurrent]
  } else {
    // 插入到第一个非固定项前（或列表末尾）
    const firstNonFixedIndex = withoutCurrent.findIndex(i => !i.fixed)
    if (firstNonFixedIndex === -1) {
      actionsTools.value = [...withoutCurrent, item]
    } else {
      actionsTools.value = [
        ...withoutCurrent.slice(0, firstNonFixedIndex),
        item,
        ...withoutCurrent.slice(firstNonFixedIndex),
      ]
    }
  }
}

// 拖拽结束后处理数据顺序（排除固定项）
const onDragEnd = (evt: any) => {
  const fixed = actionsTools.value.filter(i => i.fixed)
  const moved = evt.to?.__draggable_component__?.context?.element || []
  if (!Array.isArray(moved)) return
  actionsTools.value = [...fixed, ...moved]
}

/**
 * 不允许拖到固定项上面
 * @param relatedContext
 */
const onDragMove = ({ relatedContext }: any) => {
  const fixedCount = actionsTools.value.filter(i => i.fixed).length
  return relatedContext.index >= fixedCount
}

/**
 * modal弹框取消关闭方法
 */
function onClickToolDragCancelOperate(){
  actionsVisible.value = false
}

/**
 * 点击确定方法
 */
function onClickToolDragConfirmOperate(){
  aiAppStore.setChatTools(actionsTools.value);
  actionsVisible.value = false;
  message.success('保存成功!');
  loadData()
}



// 加载数据
function loadData() {
  const tempToolDragData = getLocal<AiTool[]>(AI_TOOLS_KEY, defaultTools)
  toolItems.value = tempToolDragData.filter(item => !item.hide)
}

loadData()


watch(actionsVisible, (newVal) => {
  if (newVal) {
    actionsTools.value = getLocal<AiTool[]>(AI_TOOLS_KEY, defaultTools)
    // 每次打开 modal，记录当前 actionsTools 状态为快照
    if(actionsTools.value){
      savedTools.value = JSON.parse(JSON.stringify(actionsTools.value))
    }
  }
})

function getLocal<T>(key: string, defaultValue: T): T {
  try {
    const raw = localStorage.getItem(key)
    return raw ? JSON.parse(raw) : defaultValue
  } catch (e) {
    console.warn(`读取 localStorage[${key}] 失败`, e)
    return defaultValue
  }
}


</script>

<style lang="less">
@import "../styles/chatToolToDrag";

.chat-tools {
  display: flex;
  flex-direction: row;
  column-gap: 12px;
  padding: 12px;

  .chat-tools-item {
    .chat-tools-item-button {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 5px;
      padding: 12px 16px 10px 16px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 32px;
      cursor: pointer;
      transition: background 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }

      ._item-button-label {
        margin-top: 1px;
      }
    }
  }
}
.drag-handle{
  cursor: move;
}

.chat-tools_modal {
  .chat-tools_modal-item {
    background-color: aqua;
  }
}
</style>
