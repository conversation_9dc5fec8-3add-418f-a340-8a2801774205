package com.cb.ai.data.json.tool.utils;

import com.cb.ai.data.json.tool.constants.Constant;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * 2025/7/31
 */
public class ZipUtils {

    /**
     * 解压 ZIP 文件到指定目录
     *
     * @param zipFile      ZIP 文件对象
     * @param outputFolder 输出目录路径
     * @throws IOException 如果解压过程中发生错误
     */
    public static File unZip(File zipFile, String outputFolder) throws IOException {
        // 获取ZIP文件名
        String fileName = zipFile.getName();

        // 确保输出目录存在
        Path outputPath = Paths.get(outputFolder);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }

        // 创建ZipInputStream用于读取ZIP文件
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile), StandardCharsets.ISO_8859_1)) {
            ZipEntry entry;
            // 遍历ZIP文件中的每个条目
            while ((entry = zis.getNextEntry()) != null) {
                String entryName = new String(entry.getName().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                ;
                // 使用fileName重命名解压出来的文件
                String newEntryName = fileName.substring(0, fileName.lastIndexOf(".")) + File.separator + entryName;
                Path entryPath = outputPath.resolve(newEntryName);

                if (entry.isDirectory()) {
                    // 如果是目录，创建目录
                    Files.createDirectories(entryPath);
                } else {
                    // 如果是文件，先创建父目录（如果不存在）
                    Files.createDirectories(entryPath.getParent());
                    // 写入文件内容
                    try (OutputStream os = Files.newOutputStream(entryPath)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            os.write(buffer, 0, length);
                        }
                    }
                }
                // 关闭当前条目
                zis.closeEntry();
            }
        }

        // 删除原始ZIP文件
//        zipFile.delete();

        // 返回解压后的文件夹路径
        String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.'));
        return new File(outputFolder + File.separator + fileNameWithoutExtension);
    }

    /**
     * 压缩文件或目录
     *
     * @param fileToZip
     * @return
     * @throws IOException
     */
    @Deprecated
    public static ByteArrayOutputStream compressedFileToStream(File fileToZip) throws IOException {
        // 创建ByteArrayOutputStream用于存储ZIP文件内容
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        // 创建ZipOutputStream用于写入ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            // 递归压缩文件或目录
            compressFileOrDirectory(fileToZip, fileToZip.getName(), zos);
        }
        // 返回包含ZIP文件内容的ByteArrayOutputStream
        return baos;
    }

    /**
     * 递归压缩文件或目录
     *
     * @param fileToZip 要压缩的文件或目录
     * @param fileName  在ZIP中的文件名
     * @param zos       ZipOutputStream
     * @throws IOException 如果压缩过程中发生错误
     */
    private static void compressFileOrDirectory(File fileToZip, String fileName, ZipOutputStream zos) throws IOException {
        // 过滤掉JSON文件
        if (fileName.endsWith("." + Constant.JSON_EXTENSION)) {
            return;
        }
        if (fileToZip.isHidden()) {
            return;
        }
        if (fileToZip.isDirectory()) {
            // 如果是目录，添加目录条目
            if (fileName.endsWith(File.separator)) {
                zos.putNextEntry(new ZipEntry(fileName));
                zos.closeEntry();
            } else {
                zos.putNextEntry(new ZipEntry(fileName + File.separator));
                zos.closeEntry();
            }
            // 递归压缩目录中的文件
            File[] children = fileToZip.listFiles();
            if (children != null) {
                for (File childFile : children) {
                    compressFileOrDirectory(childFile, fileName + File.separator + childFile.getName(), zos);
                }
            }
            return;
        }

        // 如果是文件，写入文件内容
        try (FileInputStream fis = new FileInputStream(fileToZip)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);

            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zos.write(bytes, 0, length);
            }
        }
    }
}
