<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsMapStaticsMapper">
    <select id="countByRegion" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        SELECT count(*) as value,
        <if test="param.statusType == 1">
            petition_province
        </if>
        <if test="param.statusType == 2">
            petition_city
        </if>
        <if test="param.statusType == 3">
            petition_district
        </if>
        as name
        from ss_petition_analyzed analyzed
        <if test="(param.batchNo != '' and param.batchNo != null) or (param.queryFileName != '' and param.queryFileName != null)">
            LEFT JOIN
            (SELECT id,file_name,upload_batch_no from ss_petition_origin ) origin
            ON
            analyzed.origin_id = origin.id
        </if>

        <where>
            1=1
            <if test="param.province != null and param.province != ''">
                AND petition_province like CONCAT('%',#{param.province},'%')
            </if>
            <if test="param.city != null and param.city != ''">
                AND petition_city like CONCAT('%',#{param.city},'%')
            </if>
            <if test="param.district != null and param.district != ''">
                AND petition_district like CONCAT('%',#{param.district},'%')
            </if>
            <if test="param.types != null and param.types.size() > 0 ">
                AND SUBSTRING_INDEX(petition_domain, '/', 1)
                IN
                <foreach collection="param.types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="param.startDate != null and param.endDate != null">
                AND (register_date BETWEEN #{param.startDate}
                AND #{param.endDate})
            </if>
            <if test="param.queryFileName != '' and param.queryFileName != null">
                AND file_name = #{param.queryFileName}
            </if>
            <if test="param.batchNo != '' and param.batchNo != null">
                AND upload_batch_no = #{param.batchNo}
            </if>
        </where>
        GROUP BY
        <if test="param.statusType == 1">
            petition_province
        </if>
        <if test="param.statusType == 2">
            petition_city
        </if>
        <if test="param.statusType == 3">
            petition_district
        </if>
    </select>

    <select id="countByDomain" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        SELECT *
        FROM (SELECT COUNT(*) AS value, name
        FROM (
        SELECT SUBSTRING_INDEX(petition_domain, '/', 1) AS name,register_date,file_name,upload_batch_no,petition_city,petition_district FROM
        ss_petition_analyzed
        analyzed LEFT JOIN
        ss_petition_origin origin
        ON analyzed.origin_id = origin.id
        UNION ALL
        SELECT SUBSTRING_INDEX(petition_domain, '/', -1) AS name,register_date,file_name,upload_batch_no,petition_city,petition_district FROM
        ss_petition_analyzed
        analyzed join
        ss_petition_origin origin
        ON analyzed.origin_id = origin.id
        ) AS t
        <where>
            1=1
            <if test="param.types != null and param.types.size() > 0">
                AND name IN
                <foreach collection="param.types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="param.city != null and param.city != ''">
                AND petition_city like CONCAT('%',#{param.city},'%')
            </if>
            <if test="param.district != null and param.district != ''">
                AND petition_district like CONCAT('%',#{param.district},'%')
            </if>
            <if test="param.startDate != null and param.endDate != null">
                AND (register_date BETWEEN #{param.startDate}
                AND #{param.endDate})
            </if>
            <if test="param.queryFileName != '' and param.queryFileName != null">
                AND file_name = #{param.queryFileName}
            </if>
            <if test="param.batchNo != '' and param.batchNo != null">
                AND upload_batch_no = #{param.batchNo}
            </if>
        </where>
        GROUP BY name
        ORDER BY value DESC) a limit 10
    </select>

    <select id="domainSelectOptions" resultType="java.lang.String">
        select distinct SUBSTRING_INDEX(petition_domain, '/', -1)
        from ss_petition_analyzed
    </select>
    <select id="regionSelectOptions" resultType="java.lang.String">
        select distinct belong_region
        from ss_analyzed_result
    </select>

    <select id="countBySelfDeptSendOrgName" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        SELECT *
        FROM (SELECT petition_belong_org as name,
        count(*) as value
        FROM
        ss_petition_analyzed
        WHERE
        petition_belong_org is not null

        <if test="selfDeptSendOrgNameQueryVo.startDate != null and selfDeptSendOrgNameQueryVo.endDate != null">
            AND register_date BETWEEN
            #{selfDeptSendOrgNameQueryVo.startDate} AND #{selfDeptSendOrgNameQueryVo.endDate}
        </if>

        GROUP BY
        petition_belong_org) a
        ORDER BY value DESC LIMIT 10
    </select>

    <select id="countByRegisterDate" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        SELECT
        <if test="registerDateQueryCondition.petitionCountByCondition != null and registerDateQueryCondition.petitionCountByCondition == 1">
            DATE_FORMAT(register_date, '%Y')
        </if>
        <if test="registerDateQueryCondition.petitionCountByCondition != null and registerDateQueryCondition.petitionCountByCondition == 2">
            DATE_FORMAT(register_date, '%Y-%m')
        </if>
        <if test="registerDateQueryCondition.petitionCountByCondition == null or registerDateQueryCondition.petitionCountByCondition == 3">
            DATE_FORMAT(register_date, '%Y-%m-%d')
        </if>
        AS name,
        COUNT(*) AS value
        FROM
        ss_petition_analyzed
        GROUP BY
        <if test="registerDateQueryCondition.petitionCountByCondition != null and registerDateQueryCondition.petitionCountByCondition == 1">
            DATE_FORMAT(register_date, '%Y')
        </if>
        <if test="registerDateQueryCondition.petitionCountByCondition != null and registerDateQueryCondition.petitionCountByCondition == 2">
            DATE_FORMAT(register_date, '%Y-%m')
        </if>
        <if test="registerDateQueryCondition.petitionCountByCondition == null or registerDateQueryCondition.petitionCountByCondition == 3">
            DATE_FORMAT(register_date, '%Y-%m-%d')
        </if>
        ORDER BY name ASC
    </select>

    <select id="countByAnalyzedStatus" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        select count(*) value,
 'total' as name
        from ss_petition_origin
        UNION ALL
        select count(*) value,
 'analyzed' as name
        from ss_petition_analyzed
    </select>

    <select id="countByPetitionPurpose" resultType="com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo">
        SELECT * FROM (
        SELECT COUNT(*) as value,
        petition_purpose as name
        FROM ss_petition_analyzed
        <where>
            <if test="petitionPurposeDateQueryConditionVo.startDate != null and petitionPurposeDateQueryConditionVo.endDate != null">
                register_date
                BETWEEN
                #{petitionPurposeDateQueryConditionVo.startDate} AND #{petitionPurposeDateQueryConditionVo.endDate}
            </if>
        </where>
        GROUP BY petition_purpose
        ) a
        ORDER BY value DESC
        <if test="petitionPurposeDateQueryConditionVo.petitionPurposeQueryGroupCondition == 1">
            LIMIT 10
        </if>
    </select>
</mapper>
