package com.cb.ai.data.analysis.petition.converter;

import lombok.Data;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;

/**
 * 公文配置
 * <AUTHOR>
 */
@Data
public class DocConfig {
    /**
     * 字体
     */
    private String fontName = "仿宋";
    /**
     * 字体大小
     */
    private Float fontSize = 32f;
    /**
     * 行间距类型
     */
    private STLineSpacingRule.Enum lineSpacingRule = STLineSpacingRule.EXACT;
    /**
     * 行间距值
     */
    private Object lineSpacing = 560;
    /**
     * 标题字体
     */
    private String titleFontName = "方正小标宋简体";
    /**
     * 标题字体大小
     */
    private Float titleFontSize = 44f;
    /**
     * 标题行间距类型
     */
    private STLineSpacingRule.Enum titleLineSpacingRule = STLineSpacingRule.AT_LEAST;
    /**
     * 标题行间距值
     */
    private Object titleLineSpacing = 0;
    /**
     * 其他字体
     */
    private String otherFontName = "Times New Roman";
    /**
     * 一级标题字体
     */
    private String serialNumber1 = "黑体";
    /**
     * 二级标题字体
     */
    private String serialNumber2 = "楷体";
    /**
     * 三级标题字体
     */
    private String serialNumber3 = "仿宋";
    /**
     * 四级标题字体
     */
    private String serialNumber4 = "仿宋";
    /**
     * 附件头
     */
    private String attachHeadFontName = "黑体";
    /**
     * 附件头字体大小
     */
    private float attachHeadFontSize = 32f;
    /**
     * 排版后是否替换源文件
     */
    private boolean formatReplaceOriginal = false;
    /**
     * 排版表格宽度
     */
    private boolean formatTableWidth = true;
    /**
     * 检查标点符号
     */
    private boolean checkSymbol = true;
    /**
     * 删除超链接
     */
    private boolean deleteHyperlink = true;

    /**
     * 孤寒控制
     */
    private boolean widowControl = true;
}
