package com.xong.boot.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {
    // 任务前缀
    private static final String THREAD_NAME_PREFIX = "xong-boot-executor-";
    // 核心线程池大小
    private static final int CORE_POOL_SIZE = 50;

    // 最大可创建的线程数
    private static final int MAX_POOL_SIZE = 200;

    // 队列最大长度
    private static final int QUEUE_CAPACITY = 1000;

    // 线程池维护线程所允许的空闲时间
    private static final int KEEP_ALIVE_SECONDS = 300;

    @Bean(name = "taskExecutor")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX); // 设置默认线程名称
        executor.setCorePoolSize(CORE_POOL_SIZE); // 设置核心线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE); // 设置最大线程数
        executor.setQueueCapacity(QUEUE_CAPACITY); // 设置队列容量
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS); // 设置线程活跃时间（秒）
        // AbortPolicy 直接抛出异常。
        // CallerRunsPolicy 在任务被拒绝添加后，会调用当前线程池的所在的线程去执行被拒绝的任务。（缺点：可能会阻塞主线程）
        // DiscardPolicy 拒绝策略，会让被线程池拒绝的任务直接抛弃，不会抛异常也不会执行。
        // DiscardOldestPolicy 当任务被拒绝添加时，会抛弃任务队列中最旧的任务也就是最先加入队列的，再把这个新任务添加进去。
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true); // 完成任务自动关闭(默认false)
        executor.setAwaitTerminationSeconds(300); // 超过这个时间还没有销毁就强制销毁
        executor.initialize();
        return executor;
    }
}
