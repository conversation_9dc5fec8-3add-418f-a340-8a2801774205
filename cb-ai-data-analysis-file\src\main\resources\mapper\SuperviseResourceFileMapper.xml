<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.file.mapper.SuperviseResourceFileMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.cb.ai.data.analysis.file.domain.SuperviseResourceFile">
        <id column="id" property="id" />
        <result column="folder_id" property="folderId" />
        <result column="filename" property="filename" />
        <result column="content_type" property="contentType" />
        <result column="file_suffix" property="fileSuffix" />
        <result column="file_size" property="fileSize" />
        <result column="file_path" property="filePath" />
        <result column="file_tags" property="fileTags" />
        <result column="algorithm" property="algorithm" />
        <result column="digest_md5" property="digestMd5" />
        <result column="sort_on" property="sortOn" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="original_path" property="originalPath" />
    </resultMap>

    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        id, folder_id, filename, content_type, file_suffix, file_size,
        file_path, file_tags, algorithm, digest_md5, sort_on, status,original_path,
        del_flag, remark, create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据文件夹ID查询 -->
    <select id="selectByFolderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM supervise_resource_file
        WHERE folder_id = #{folderId}
        <!-- MyBatis-Plus 自动添加逻辑删除条件 -->
    </select>

    <!-- 自定义查询 -->
    <select id="getFileList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM supervise_resource_file
            ${ew.customSqlSegment}
    </select>

    <update id="updateResourceFile" parameterType="com.cb.ai.data.analysis.file.domain.SuperviseResourceFile">
        UPDATE supervise_resource_file
        <set>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="filename != null">filename = #{filename},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="fileSuffix != null">file_suffix = #{fileSuffix},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileTags != null">file_tags = #{fileTags},</if>
            <if test="algorithm != null">algorithm = #{algorithm},</if>
            <if test="digestMd5 != null">digest_md5 = #{digestMd5},</if>
            <if test="sortOn != null">sort_on = #{sortOn},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            update_time = NOW() <!-- 强制更新更新时间 -->
        </set>
        WHERE id = #{id}
    </update>

    <update id="restoreFiles" parameterType="com.cb.ai.data.analysis.file.domain.SuperviseResourceFile">
        UPDATE supervise_resource_file
        set del_flag = 0,folder_id=#{folderId}
        WHERE id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>

    <delete id="deleteByCustom">
        delete from supervise_resource_file
         ${ew.customSqlSegment}
    </delete>
</mapper>