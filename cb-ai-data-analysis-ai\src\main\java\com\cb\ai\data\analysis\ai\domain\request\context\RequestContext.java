package com.cb.ai.data.analysis.ai.domain.request.context;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.domain.common.Context;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 10:26
 * @Copyright (c) 2025
 * @Description 统一的请求上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RequestContext extends Context {

    /** 当前请求Id **/
    private String requestId;

    /** 当前SessionId **/
    private String sessionId;

    /** 当前用户Id **/
    private String userId;

    public String getRequestId() {
        if (StrUtil.isBlank(requestId)) {
            this.requestId = "req_" + IdUtil.fastSimpleUUID() + System.currentTimeMillis();
        }
        return requestId;
    }

}
