package com.cb.ai.data.analysis.dbtable.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTable;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumn;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumnRule;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableIndex;
import com.xong.boot.common.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 动态数据表 Service
 * <AUTHOR>
 */
public interface AnalysisDbTableService extends BaseService<AnalysisDbTable> {
    /**
     * 保存 表、字段、索引、校验规则
     * @param table   表
     * @param columns 字段
     * @param indexs  索引
     * @param rules   校验规则
     */
    void saveTable(AnalysisDbTable table, List<AnalysisDbTableColumn> columns, List<AnalysisDbTableIndex> indexs, List<AnalysisDbTableColumnRule> rules);

    /**
     * 更新 表、字段、索引、校验规则
     * @param table   表
     * @param columns 字段
     * @param indexs  索引
     * @param rules   校验规则
     */
    void updateTable(AnalysisDbTable table, List<AnalysisDbTableColumn> columns, List<AnalysisDbTableIndex> indexs, List<AnalysisDbTableColumnRule> rules);

    /**
     * 把表同步数据库
     * @param type    同步类型 1保留数据 2删除表，重新生成
     * @param tableId 表ID
     */
    void syncdbTable(Integer type, String tableId);

    /**
     * 分页获取数据表数据
     */
    Page<Map<String, Object>> pageTableData(Page<?> page, String tableName);

    /**
     * 删除指定表的指定数据
     * @param tableName
     * @param ids
     * @return
     */
    int deleteTableData(String tableName, List<String> ids);
}
