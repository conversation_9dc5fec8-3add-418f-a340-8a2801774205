package com.cb.ai.data.analysis.ai.utils;

import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/7 09:30
 * @Copyright (c) 2025
 * @Description 安全认证工具
 */
public class SecurityContextUtils {
    private final SecurityContext originalContext;

    public SecurityContextUtils() {
        this.originalContext = SecurityContextHolder.getContext();
    }

    public void setup() {
        SecurityContextHolder.setContext(originalContext);
    }

    public void clear() {
        SecurityContextHolder.clearContext();
    }

    public <T> T withSecurityContext(Supplier<T> supplier) {
        try {
            setup();
            return supplier.get();
        } finally {
            clear();
        }
    }
}
