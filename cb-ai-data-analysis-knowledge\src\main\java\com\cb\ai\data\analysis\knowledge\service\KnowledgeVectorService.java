package com.cb.ai.data.analysis.knowledge.service;

import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeVector;
import com.cb.ai.data.analysis.knowledge.domain.vo.CommonVectorVo;
import com.cb.ai.data.analysis.knowledge.domain.vo.KnowledgeVectorVo;

import java.util.List;

/***
 * <AUTHOR>
 * 向量库相关接口
 */
public interface KnowledgeVectorService {

    /***
     * 向量库查询
     */
    List<KnowledgeVectorVo> searchVector(KnowledgeVector knowledgeVector)throws Exception;


    /***
     * 向量库查询
     */
    List<KnowledgeVectorVo> searchKnowledgeVector(KnowledgeVector knowledgeVector)throws Exception;

    /***
     * NLSQL向量库查询
     */
    List<CommonVectorVo> searchNLSQLVector(KnowledgeVector knowledgeVector) throws Exception;

    /***
     * 向量库删除
     */
    public boolean deleteVectorByIds(String baseId, List<String> ids);

}
