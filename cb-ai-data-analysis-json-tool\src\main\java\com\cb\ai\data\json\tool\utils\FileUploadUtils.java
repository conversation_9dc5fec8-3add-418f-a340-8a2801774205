package com.cb.ai.data.json.tool.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 * 2025/7/31
 */
public class FileUploadUtils {

    /**
     * 保存上传的文件并解压缩
     *
     * @param uploadDir 上传目录路径
     * @param file      上传的文件
     * @return 解压后的文件目录
     * @throws IOException 如果保存或解压过程中发生错误
     */
    public static final File uploadZip(String uploadDir, MultipartFile file) throws IOException {
        // 生成唯一文件名
        String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
        // 获取文件保存的绝对路径
        File desc = getAbsoluteFile(uploadDir, fileName);
        // 保存文件到指定位置
        file.transferTo(desc);
        // 解压文件并返回解压后的目录
        return desc;
    }

    /**
     * 获取文件的绝对路径，如果目录不存在则创建
     *
     * @param uploadDir 上传目录路径
     * @param fileName  文件名
     * @return 文件的绝对路径对象
     * @throws IOException 如果创建文件或目录过程中发生错误
     */
    public static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);
        // 如果父目录不存在，则创建父目录
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        // 如果文件不存在，则创建新文件
        if (!desc.exists()) {
            desc.createNewFile();
        }
        return desc;
    }

}
