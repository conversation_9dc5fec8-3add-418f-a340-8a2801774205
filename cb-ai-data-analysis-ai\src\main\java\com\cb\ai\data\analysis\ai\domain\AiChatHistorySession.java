package com.cb.ai.data.analysis.ai.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xong.boot.common.domain.SimpleBaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI历史会话
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AiChatHistorySession extends SimpleBaseDomain {
    /**
     * 会话ID
     */
    @TableId
    private String sessionId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 会话标题
     */
    private String title;
}
