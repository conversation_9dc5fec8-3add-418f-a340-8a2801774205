package com.cb.ai.data.analysis.dbtable.exception;

/**
 * 规则校验异常
 * <AUTHOR>
 */
public class RuleCheckException extends Exception {
    public RuleCheckException(String message) {
        super(message);
    }

    public RuleCheckException(String message, Throwable cause) {
        super(message, cause);
    }

    public RuleCheckException(Throwable cause) {
        super(cause);
    }

    public RuleCheckException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
