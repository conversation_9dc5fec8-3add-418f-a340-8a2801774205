package com.cb.ai.data.analysis.voucher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherCheckRule;
import com.cb.ai.data.analysis.voucher.mapper.VoucherCheckRuleMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherCheckRuleService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VoucherCheckRuleServiceImpl extends BaseServiceImpl<VoucherCheckRuleMapper, VoucherCheckRule> implements VoucherCheckRuleService {

    @Override
    public String buildCheckRuleText(){
        LambdaQueryWrapper<VoucherCheckRule> query = Wrappers.lambdaQuery(VoucherCheckRule.class)
                        .eq(VoucherCheckRule::getDisable, 0)
                .orderByAsc(VoucherCheckRule::getCreateTime);
        List<VoucherCheckRule> ruleList = list(query).stream().filter(rule -> StringUtils.isNotBlank(rule.getPromote())).toList();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ruleList.size(); i++) {
            int num = i + 1;
            String chineseNum = numToChinese(num);
            String text = StringUtils.format("{}、{}。规则说明：{}", chineseNum, ruleList.get(i).getName(), ruleList.get(i).getPromote());
            sb.append(text).append("\n");
        }
        return sb.toString();
    }

    @Override
    public void disableRule(String id) {
        LambdaUpdateWrapper<VoucherCheckRule> updateWrapper = Wrappers.lambdaUpdate(VoucherCheckRule.class)
                .eq(VoucherCheckRule::getId, id)
                .set(VoucherCheckRule::getDisable, 1);
        baseMapper.update(updateWrapper);
    }

    private String numToChinese(int num){
        if (num < 0 || num > 999) {
            throw new IllegalArgumentException("数字应在0-999之间");
        }
        
        String[] digits = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] units = {"", "十", "百"};
        
        if (num == 0) {
            return digits[0];
        }
        
        if (num < 10) {
            return digits[num];
        }
        
        StringBuilder result = new StringBuilder();
        
        // 处理百位
        int hundreds = num / 100;
        if (hundreds > 0) {
            result.append(digits[hundreds]).append(units[2]);
            num %= 100; // 剩余数字
            
            // 如果剩余数字为0，即整百数，直接返回
            if (num == 0) {
                return result.toString();
            }
            
            // 如果剩余数字不为0，需要加"零"（当剩余数字小于10时）
            if (num < 10) {
                result.append(digits[0]); // 加"零"
            }
        }
        
        // 处理十位和个位 (0-99的处理逻辑)
        if (num < 10) {
            result.append(digits[num]);
        } else {
            int tens = num / 10;
            int ones = num % 10;
            
            // 处理十位
            if (tens == 1) {
                // 十位是1，读作"十"（10）或"一十"（11-19，当有百位时）
                if (hundreds > 0) {
                    result.append(digits[tens]).append(units[1]);
                } else {
                    result.append(units[1]);
                }
            } else {
                // 其他情况读作"几十"
                result.append(digits[tens]).append(units[1]);
            }
            
            // 处理个位
            if (ones != 0) {
                result.append(digits[ones]);
            }
        }
        
        return result.toString();
    }
}
