package com.cb.ai.data.analysis.basdata.repository.finance.esBo;


import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Document(indexName = Constant.ES_FINANCE_DATA_INDEX + Constant.SLICING + "bas_invoice_info")
@Setting(shards = 1, replicas = 0)
public class BasInvoiceInfoBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    //主键id
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    //发票代码和号码
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String invoiceCodeNumber;

    //开票日期
    @Field(type = FieldType.Long)
    private LocalDateTime invoiceDate;

    //购买方名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String buyerName;

    //购买方纳税人识别号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String buyerTaxNumber;

    //购买方地址
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String buyerAddress;

    //购买方电话
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String buyerPhone;

    //购买方开户行及账号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String buyerBankAccount;

    //销售方名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String sellerName;

    //销售方纳税人识别号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String sellerTaxNumber;

    //销售方地址
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String sellerAddress;

    //销售方电话
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String sellerPhone;

    //销售方开户行及账号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String sellerBankAccount;

    //价税合计
    @Field(type = FieldType.Double)
    private BigDecimal priceTaxTotal;

    //金额合计
    @Field(type = FieldType.Double)
    private BigDecimal priceTotal;

    //税额合计
    @Field(type = FieldType.Double)
    private BigDecimal taxTotal;

    //开票人
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String drawer;

    //复核人
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String reviewer;

    //收款人
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String payee;

    //销售方
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String salesParty;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;

}

