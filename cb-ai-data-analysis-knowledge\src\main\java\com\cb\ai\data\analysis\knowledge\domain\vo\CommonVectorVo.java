package com.cb.ai.data.analysis.knowledge.domain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonVectorVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String baseId;

    // id
    private String id;

    // 内容
    private String content;

    // 文件ID
    private String fileId;

    // 矢量
//    private Object vector;

    // 元数据
    private CommonVectorMetadata metadata;

}
