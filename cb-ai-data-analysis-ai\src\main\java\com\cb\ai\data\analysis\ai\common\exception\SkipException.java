package com.cb.ai.data.analysis.ai.common.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 15:45
 * @Copyright (c) 2025
 * @Description 跳出异常
 */
public class SkipException extends RuntimeException {
    public SkipException() {
        super();
    }

    public SkipException(String message) {
        super(message);
    }

    public SkipException(String message, Throwable cause) {
        super(message, cause);
    }

    public SkipException(Throwable cause) {
        super(cause);
    }

    protected SkipException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
