<template>
  <x-page-wrapper hiddenTitle class="voucher-task">
    <x-table-search :model="searchFormData" :tableRef="tableRef" :col="4">
      <a-form-item label="任务名称" name="name">
        <a-input v-model:value="searchFormData.name" :maxlength="100" allow-clear placeholder="请输入任务名称" />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-select v-model:value="searchFormData.status" allow-clear placeholder="请选择状态">
          <a-select-option :value="0">待处理</a-select-option>
          <a-select-option :value="1">处理中</a-select-option>
          <a-select-option :value="2">处理完成</a-select-option>
          <a-select-option :value="3">处理失败</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="标签" name="tags">
        <a-input v-model:value="searchFormData.tags" :maxlength="200" allow-clear placeholder="请输入标签" />
      </a-form-item>
    </x-table-search>
    <x-table ref="tableRef" :columns="columns" :loadData="loadData" :rowSelection="false" title="分析任务管理" row-key="id">
      <template #toolbar>
        <a-button type="primary" @click="onClickAdd">
          <template #icon>
            <x-icon type="PlusOutlined" />
          </template>
          新增任务
        </a-button>
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'tags'">
          <a-tag v-for="tag in text?.split(',')" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'taskBeginTime'">
          {{ $date.format(text, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.dataIndex === 'taskEndTime'">
          {{ $date.format(text, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.dataIndex === 'alertNum'">
          <a-button type="link" size="small" @click="onClickViewAlert(record)" class="alert-num-btn">
            {{ text }}
          </a-button>
        </template>
        <template v-else-if="column.dataIndex === 'progress'">
          <a-progress v-if="record.status === 1" :percent="getProgress(record)" size="small" :show-info="false" />
          <span v-else>{{ record.handleNum || 0 }}/{{ record.totalNum || 0 }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ $date.formatDateTime(text) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button v-if="record.status === 0 || record.status === 3" type="link" size="small"
              @click="onClickRun(record)">
              运行
            </a-button>

            <a-button type="link" size="small" danger @click="onClickDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </x-table>

    <!-- 新增任务组件 -->
    <AddComp v-model:visible="addVisible" @success="onSuccess" />
    <!-- 告警记录弹窗 -->
    <AlertModal v-model:visible="alertModalAttrs.visible" :task-id="alertModalAttrs.taskId"
      :task-name="alertModalAttrs.taskName" />
  </x-page-wrapper>
</template>

<script setup lang="ts" name="VoucherTask">
import { computed, getCurrentInstance, reactive, ref, type ComponentCustomProperties } from 'vue'
import { task } from '@/api/voucher'
import AddComp from './Add.vue'
import AlertModal from './AlertModal.vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const tableRef = ref()
const addVisible = ref(false)
const alertModalAttrs = reactive({
  visible: false,
  taskId: '',
  taskName: ''
})

const searchFormData = ref({
  name: '',
  status: undefined,
  tags: ''
})

const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'name',
      title: '任务名称',
      width: 200,
      ellipsis: true
    },
    {
      dataIndex: 'tags',
      title: '分析标签',
      width: 150
    },
    {
      dataIndex: 'status',
      title: '状态',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'progress',
      title: '进度',
      width: 120,
      align: 'center'
    },
    {
      dataIndex: 'totalNum',
      title: '总数',
      width: 80,
      align: 'center'
    },
    {
      dataIndex: 'alertNum',
      title: '告警数',
      width: 80,
      align: 'center'
    },
    {
      dataIndex: 'taskBeginTime',
      title: '分析开始时间',
      width: 150,
      align: 'center'
    },
    {
      dataIndex: 'taskEndTime',
      title: '分析结束时间',
      width: 150,
      align: 'center'
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
      align: 'center'
    }
  ] as TableColumn[]

  columnItems.push({
    key: 'actions',
    title: '操作',
    width: 120,
    align: 'center'
  })

  return columnItems
})

/**
 * 加载数据
 */
async function loadData(params: Record<string, any>) {
  const res = await task.page(params)
  return res
}

/**
 * 获取状态颜色
 */
function getStatusColor(status: number) {
  const colorMap = {
    0: 'default',
    1: 'processing',
    2: 'success',
    3: 'error'
  }
  return colorMap[status] || 'default'
}

/**
 * 获取状态文本
 */
function getStatusText(status: number) {
  const textMap = {
    0: '待处理',
    1: '处理中',
    2: '处理完成',
    3: '处理失败'
  }
  return textMap[status] || '未知'
}

/**
 * 计算进度百分比
 */
function getProgress(record: any) {
  if (!record.totalNum || record.totalNum === 0) return 0
  return Math.round((record.handleNum / record.totalNum) * 100)
}

/**
 * 新增任务
 */
function onClickAdd() {
  addVisible.value = true
}

/**
 * 成功回调
 */
function onSuccess() {
  tableRef.value?.refresh()
}

/**
 * 运行任务
 */
async function onClickRun(record: any) {
  try {
    await task.runTask(record.id)
    _this.$message.success('任务已开始运行，后台分析中...')
    tableRef.value?.refresh()
  } catch (error) {
    _this.$message.error('运行任务失败')
  }
}

/**
 * 查看告警记录
 */
function onClickViewAlert(record: any) {
  alertModalAttrs.visible = true
  alertModalAttrs.taskId = record.id
  alertModalAttrs.taskName = record.name
}

/**
 * 删除任务
 */
function onClickDelete(record: any) {
  _this.$confirm({
    title: '确认删除',
    content: `确定要删除任务"${record.name}"吗？`,
    onOk: async () => {
      try {
        await task.del(record.id)
        _this.$message.success('删除成功')
        tableRef.value?.refresh()
      } catch (error) {
        _this.$message.error('删除失败')
      }
    }
  })
}
</script>

<style scoped lang="less">
.voucher-task {
  .ant-tag {
    margin-right: 4px;
  }

  .alert-num-btn {
    color: #ff4d4f;
    font-weight: 600;

    &:hover {
      color: #ff7875;
    }
  }
}
</style>
