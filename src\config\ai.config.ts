/**
 * AI会话工具集缓存KEY
 */
const AI_TOOLS_KEY = 'ai-tools-key'

const defaultTools = [
  {
    id: 'knowledge',
    icon: 'ReadOutlined',
    name: '知识库',
    description: '实现秒级解析海量信息，精准决策的智慧引擎。',
    fixed:false,
    picture: `/assistant/icon0.webp`,
  },
  {
    id: 'ai_3',
    icon: 'tt',
    name: '生成报告',
    description: '让AI为您智能生成一份专业、精准的完整报告。',
    hide: true,
    fixed:false,
    picture: `/assistant/icon1.webp`,
  },
  {
    id: 'ai_4',
    icon: 'tt',
    name: '大数据分析',
    description: '让AI无缝连接多源数据，实时生成精准决策洞察。',
    fixed:false,
    picture: `/assistant/icon2.webp`,
  },
  {
    id: 'ai_5',
    icon: 'tt',
    name: '方案审核',
    description: '让AI为您的谈话方案提供智能化建议与合规性检查。',
    fixed:false,
    picture: `/assistant/icon3.webp`,
  },
  {
    id: 'ai_6',
    icon: 'tt',
    name: 'AI滤网',
    description: '让AI为您批量的、自动化的处理用户问题集。',
    fixed:false,
    picture: `/assistant/icon4.webp`,
  },
  {
    id: 'ai_7',
    icon: 'tt',
    name: '提问矩阵',
    description: 'AI提问矩阵助您秒速定位知识核心，让每一次提问都直击靶心！',
    fixed:false,
    picture: `/assistant/icon5.webp`,
  }
] as AiTool[]

/** AI会话工具集 */
export declare interface AiTool {
  /** ID */
  id: string
  /** 图标 */
  icon: string
  /** 名称 */
  name: string
  /** 详情 */
  description?: string
  /** 隐藏 */
  hide?: boolean
  /** 角色 */
  role?: string[]
  /** 权限 */
  permission?: string[]
  /** 是否固定 */
  fixed?: boolean,
  /** 二类图标 */
  picture?: string
}

export { AI_TOOLS_KEY, defaultTools }
