<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cb.ai</groupId>
        <artifactId>cb-ai-data-analysis</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>cb-ai-data-analysis-dbtable</artifactId>
    <description>数据分析-动态数据表模块</description>

    <dependencies>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-common</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-framework</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-file</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
