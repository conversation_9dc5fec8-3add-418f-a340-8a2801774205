package com.xong.boot.common.crypto.password;

/**
 * Argon2密码加密
 * <AUTHOR>
 */
public class Argon2PasswordEncoder extends org.springframework.security.crypto.argon2.Argon2PasswordEncoder implements PasswordEncoder {
    public Argon2PasswordEncoder() {
        super(16, 32, 1, 16384, 2);
    }

    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return encode(password);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return matches(password, encodedPassword);
    }
}
