package com.cb.ai.data.analysis.ai.component.http;

import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.xong.boot.common.exception.CustomException;
import lombok.Getter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/6/12 19:59
 * @Copyright (c) 2025
 * @Description 通用HttpClient
 */
@Getter
public abstract class BaseClient implements HttpClient {
    /** 请求方式 **/
    private HttpMethod method;
    /** 请求url **/
    private String url;
    /** 请求头 **/
    private final HttpHeaders headers = new HttpHeaders();
    /** 请求体 **/
    private String body;
    /** 上传文件体 **/
    private MultiFileData fileBody;
    /** 请求成功函数 **/
    private Function<String, ?> onSuccess;
    /** 请求失败函数 **/
    private Function<Throwable, CustomException> onError;

    @Override
    public HttpClient method(HttpMethod method) {
        this.method = method;
        return this;
    }

    @Override
    public HttpClient url(String url) {
        this.url = url;
        return this;
    }

    @Override
    public HttpClient headers(Consumer<HttpHeaders> headersConsumer) {
        this.headers.setContentType(MediaType.APPLICATION_JSON);
        this.headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.TEXT_EVENT_STREAM, MediaType.TEXT_HTML, MediaType.TEXT_PLAIN));
        headersConsumer.accept(this.headers);
        return this;
    }

    @Override
    public HttpClient body(Object body) {
        this.body = JsonUtil.toStr(body);
        return this;
    }

    @Override
    public HttpClient fileBody(MultiFileData fileBody) {
        this.fileBody = fileBody;
        return this;
    }

    @Override
    public HttpClient onSuccess(Function<String, ?> onSuccess) {
        this.onSuccess = onSuccess;
        return this;
    }

    @Override
    public HttpClient onError(Function<Throwable, CustomException> onError) {
        this.onError = onError;
        return this;
    }

    public HttpMethod getMethod() {
        return method != null ? method : HttpMethod.POST;
    }

    public Function<String, ?> getOnSuccess() {
        if (onSuccess == null) {
            return data -> data;
        }
        return onSuccess;
    }

    public Function<Throwable, CustomException> getOnError() {
        if (onError == null) {
            return e -> new CustomException(this.getClass().getSimpleName() + "-> 请求失败，原因：" + e.getMessage(), e);
        }
        return onError;
    }

    boolean isFormData() {
        return getContentType().includes(MediaType.APPLICATION_FORM_URLENCODED);
    }

    boolean isMultiFormData() {
        return getContentType().includes(MediaType.MULTIPART_FORM_DATA);
    }

    boolean isStreamResponse(String responseContentType) {
        return MediaType.TEXT_EVENT_STREAM_VALUE.equalsIgnoreCase(responseContentType);
    }

    public MediaType getContentType() {
        return Optional.ofNullable(headers.getContentType()).orElse(MediaType.ALL);
    }

    protected <V> List<V> processSseStream(String sseStream) {
        List<V> dataList = new ArrayList<>();
        if (StrUtil.isBlank(sseStream)) {
            return dataList;
        }
        Arrays.stream(sseStream.split("\\r?\\n"))
            .forEach(line -> {
                if (line.startsWith("data:")) {
                    String wrapped = line.substring(5).trim();
                    processDataBlock(wrapped, dataList);
                }
            });
        return dataList;
    }

    @SuppressWarnings("unchecked")
    protected <V> V processResultData(String rawData) {
        try {
            return (V) getOnSuccess().apply(rawData);
        } catch (Exception e) {
            throw getOnError().apply(e);
        }
    }

    private <V> void processDataBlock(String rawData, List<V> dataList) {
        dataList.add(processResultData(rawData));
    }

    protected void clear() {
        method = null;
        url = null;
        headers.clear();
        body = null;
        fileBody = null;
        onSuccess = null;
        onError = null;
    }

}
