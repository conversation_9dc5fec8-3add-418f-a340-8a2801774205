package com.cb.ai.data.analysis.dbtable.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem;
import com.xong.boot.common.service.BaseService;

import java.util.List;

/**
 * 数据导入工作日志Service业务层处理
 * <AUTHOR>
 */
public interface AnalysisDbTableTaskItemService extends BaseService<AnalysisDbTableTaskItem> {

    /**
     * 查询数据导入工作列表
     * @param bigdataPoolJobLog 数据导入工作
     * @return 数据导入工作集合
     */
    Page<AnalysisDbTableTaskItem> pageTableTaskItemList(AnalysisDbTableTaskItem bigdataPoolJobLog);

    /**
     * 批量删除数据导入工作
     * @param ids 需要删除的数据导入工作主键集合
     * @return 结果
     */
    int deleteDynamicTableTaskItemByIds(String[] ids);

}
