package com.cb.ai.data.analysis.file.controller;


import com.cb.ai.data.analysis.file.constant.Constants;
import com.cb.ai.data.analysis.file.domain.SuperviseResourcePermission;
import com.cb.ai.data.analysis.file.service.SuperviseResourcePermissionService;
import com.xong.boot.common.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping(Constants.API_UNI_SUPERVISE_ROOT_PATH + "/resource/permission")
public class SuperviseResourcePermissionController  extends BaseController<SuperviseResourcePermissionService, SuperviseResourcePermission> {
}
