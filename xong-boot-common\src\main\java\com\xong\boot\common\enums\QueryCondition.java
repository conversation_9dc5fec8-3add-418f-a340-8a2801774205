package com.xong.boot.common.enums;

import com.xong.boot.common.utils.StringUtils;

/**
 * 查询条件枚举
 * <AUTHOR>
 **/
public enum QueryCondition {
    /**
     * 大于
     */
    GT,
    /**
     * 大于等于
     */
    GE,
    /**
     * 小于
     */
    LT,
    /**
     * 小于等于
     */
    LE,
    /**
     * 等于
     */
    EQ,
    /**
     * 不等于
     */
    NE,
    /**
     * 等于null
     */
    IS_NULL,
    /**
     * 不等于null
     */
    IS_NOT_NULL,
    /**
     * 包含
     */
    IN,
    /**
     * 不包含
     */
    NOT_IN,
    /**
     * 在之间
     */
    BETWEEN,
    /**
     * 不在之间
     */
    NOT_BETWEEN,
    /**
     * 模糊
     */
    LIKE,
    /**
     * 不包含模糊
     */
    NOT_LIKE,
    /**
     * 左匹配
     */
    LEFT_LIKE,
    /**
     * 不包含左匹配
     */
    NOT_LEFT_LIKE,
    /**
     * 右匹配
     */
    RIGHT_LIKE,
    /**
     * 不包含右匹配
     */
    NOT_RIGHT_LIKE,
    /**
     * 查找包含
     */
    CONTAINS,
    /**
     * 自定义SQL片段
     */
    SQL;

    /**
     * 根据值获取对应字典
     * @param name 规则名称
     */
    public static QueryCondition getByValue(String name) {
        String condition = StringUtils.toUnderlineCase(name);
        return valueOf(condition.toUpperCase());
    }
}
