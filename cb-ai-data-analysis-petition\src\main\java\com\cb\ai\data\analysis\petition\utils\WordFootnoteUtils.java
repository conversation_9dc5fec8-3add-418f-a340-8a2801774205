package com.cb.ai.data.analysis.petition.utils;

import cn.hutool.crypto.SecureUtil;
import com.aspose.words.*;
import com.cb.ai.data.analysis.ai.service.impl.ReportServiceImpl;
import com.cb.ai.data.analysis.petition.domain.FootnoteData;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WordFootnoteUtils {

    public static synchronized Map<String,Integer> getIndexMap(){
        return new HashMap<String,Integer>();
    }

    public static String convertFootnotes(String text, Map<String,String> params,Map<String,Integer> iMap) {
        // 匹配[^数字]格式的脚注
        Pattern pattern = Pattern.compile("\\[\\^(\\d+)\\]");
        Matcher matcher = pattern.matcher(text);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String footnoteNum = matcher.group(1);
            // 去重，取文件的md5作为判断，如果存在就默认重复了，不新加
            String replacement="";
            if(StringUtils.isNotBlank(params.get(footnoteNum))){
                String hashKey= SecureUtil.md5(params.get(footnoteNum));
                Integer footnoteNo=iMap.size();
                if(ObjectUtils.isEmpty(iMap.get(hashKey))){
                    footnoteNo++;
                    iMap.put(hashKey,footnoteNo);
                }else{
                    footnoteNo=iMap.get(hashKey);
                }
                replacement = "<sup data-footnote=\"" + params.get(footnoteNum) + "\">" + footnoteNo + "</sup>";
            }
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    public static Map<String,FootnoteData> extractFootnotes(String html) {
        Map<String,FootnoteData> footnotesMap = new LinkedHashMap<>();

        org.jsoup.nodes.Document doc = Jsoup.parse(html);
        Elements supTags = doc.select("sup[data-footnote]");

        for (Element sup : supTags) {
            String refText = sup.text().trim();
            String footnoteText = sup.attr("data-footnote").trim();

            // 只保留第一个遇到的相同 refText 的脚注内容（避免冲突）
            if (!footnotesMap.containsKey(refText)) {
                footnotesMap.put(refText, new FootnoteData(refText, footnoteText));
            }
        }
        return footnotesMap;
    }

    public static String removeDataAttributes(String html) {
        return html.replaceAll("<sup\\s+data-footnote=\"[^\"]*\"([^>]*)>", "<sup$1>");
    }

    public static void processSharedFootnotes(Document doc, Map<String,FootnoteData> footnotesMap)
            throws Exception {

        DocumentBuilder builder = new DocumentBuilder(doc);
        List<String> footnoteCreated = new ArrayList<>();

        // 收集待删除的 run
        List<Run> runsToRemove = new ArrayList<>();

        // 遍历所有段落，而不是所有 Run
        NodeCollection paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
        for (Object paraObj : paragraphs) {
            Paragraph para = (Paragraph) paraObj;

            // 只获取当前段落下的 Run（非递归）
            NodeCollection runs = para.getChildNodes(NodeType.RUN, false);
            for (Object runObj : runs) {
                Run run = (Run) runObj;

                // 先判断是否上标，再判断文本
                if (!run.getFont().getSuperscript()) continue;

                String text = run.getText().trim();

                // 确保文本是纯数字角标（可选）
                if (!text.matches("\\d+")) continue;

                // 只处理你知道的脚注
                if (!footnotesMap.containsKey(text)) continue;

                FootnoteData data = footnotesMap.get(text);
                builder.moveTo(run);

                if (!footnoteCreated.contains(text)) {
                    // 首次：插入脚注
                    Footnote footnote = builder.insertFootnote(FootnoteType.FOOTNOTE, "");
                    builder.moveTo(footnote.getFirstParagraph());
                    builder.insertHyperlink(data.getFootnoteText(), "参考文献/"+data.getFootnoteText(), false);

                    setBookmarkOnFootnote(footnote, data.getBookmarkId());
                    footnoteCreated.add(text);
                } else {
                    // 重复：插入超链接
                    String bookmarkId = footnotesMap.get(text).getBookmarkId();
                    if (bookmarkId != null) {
                        builder.insertHyperlink(text, bookmarkId, true);

                        Run newRun = (Run) builder.getCurrentNode();
                        if (newRun == null) {
                            newRun = findNewHyperlinkRun(para, text);
                        }
                        if (newRun != null) {
                            newRun.getFont().setSuperscript(true);
                            newRun.getFont().setColor(java.awt.Color.BLACK);
                            newRun.getFont().setUnderline(Underline.NONE);
                        }
                    }
                }

                runsToRemove.add(run);
            }
        }

        // 统一删除
        for (Run run : runsToRemove) {
            try {
                if (run.getParentNode() != null) {
                    run.remove();
                }
            } catch (Exception e) {
                // 忽略已被删除的
            }
        }
    }

    /**
     * 安全查找新插入的超链接Run
     */
    public static Run findNewHyperlinkRun(CompositeNode parentNode, String refText) {
        NodeCollection runs = parentNode.getChildNodes(NodeType.RUN, false);
        for (int i = runs.getCount() - 1; i >= 0; i--) {
            Run run = (Run) runs.get(i);
            if (run.getText().trim().equals(refText) && run.getFont().getSuperscript()) {
                return run;
            }
        }
        return null;
    }

    /**
     * 在脚注内容上设置书签
     */
    public static void setBookmarkOnFootnote(Footnote footnote, String bookmarkId) throws Exception {
        Paragraph firstPara = (Paragraph) footnote.getChild(NodeType.PARAGRAPH, 0, true);
        if (firstPara != null) {
            DocumentBuilder builder = new DocumentBuilder((Document) footnote.getDocument());
            builder.moveTo(firstPara);
            builder.startBookmark(bookmarkId);
            builder.write("\u200B"); // 零宽空格
            builder.endBookmark(bookmarkId);
        }
    }
}
