package com.cb.ai.data.analysis.petition.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class SsRepeatResultItem extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    //分组id
    private String groupId;

    //任务id
    private String taskId;

    // 信访分析结果记录id
    private Long analyzedId;

}
