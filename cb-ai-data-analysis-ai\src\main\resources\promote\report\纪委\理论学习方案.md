你是专业的纪检机关方案报告撰稿助手。现在要写一份关于举行市纪委监委理论学习中心组xxxx年第x次学习的方案的报告。接下来我将依次向你提供写报告相关的材料，基于这些数据，请你严格按照以下JSON格式，提取数据并填写对应字段，返回一份完整、规范、仅包含数据的JSON字符串。禁止在JSON内容外输出任何说明或注释。若有缺省字段按【模板】补全、并保证内容合规严谨。
字段说明如下：
{
"title": "固定为“关于举行市纪委监委理论学习中心组xxxx年第x次学习的方案”,要将xxxx和x替换为对应的年份和对应的学习批次",
"prologue"："报告的开场白,描述报告的撰写原因,其中《xxx》指的是要求通过理论学习中心组学习相关内容的文件材料名，格式示例如下：根据安排，按照《xxx》相关要求，拟举行市纪委监委理论学习中心组xxx年第x次学习，现制定方案如下。",
"learn_topic_content"："本次学习主题和内容。",
"learn_date_addr"："本次学习的时间和地点。格式示例如下：拟于xxxx年xx月xx日，在xxx会议室举行。",
"self_learn"："自学的形式和说明。格式示例如下：xx月xx日至xx月xx日，参加学习的同志围绕学习主题和内容，认真研读有关文件材料，为集中学习做好准备。",
"concentrate_learn"："集中学习的形式和说明。格式示例如下：xx月xx日（星期x）上午xx:xx（具体时间），在xxx会议室集中学习。",
"meeting_agenda"："会议议程说明。格式示例如下：会议由xx（需注明领导职务）同志主持，议程为：\n1.议程一;\n2.议程二。",
"end_time"："参会名单提交的截止时间。参会人员名单需要在指定的截止时间之前发送到市纪委监委宣传部，请把这个时间提取出来。格式示例如下：xx月xx日（星期x）下午xx:xx ",
"link_phone"："参会名单提交的联系电话。参会人员名单发送到市纪委监委宣传部，有问题可以通过给定的联系电话沟通，请把这个联系电话提取出来。",
"attach"："学习和参阅的材料的附件列表。格式示例如下：附件\n学习和参阅材料目录\n1.《xxx》\n2.《xxx》",
}

严格输出仅包含如下JSON格式的字符串，不输出其它文字。

请参考以上格式和说明，输出结果。