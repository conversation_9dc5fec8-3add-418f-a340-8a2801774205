package com.cb.ai.data.analysis.ai.component.choreography.engine;


import com.cb.ai.data.analysis.ai.domain.common.Context;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.RefUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 14:25
 * @Copyright (c) 2025
 * @Description 节点请求上下文转换
 */
public interface INodeContextConvert<E, C extends Context> extends INode {
    /**
     * 转换节点请求上下文
     */
    default E convertContext(C context) {
        Class<E> type = RefUtil.getGenericType(this.getClass(), 0, INodeContextConvert.class::equals);
        return JsonUtil.toBean(context, type);
    }

}
