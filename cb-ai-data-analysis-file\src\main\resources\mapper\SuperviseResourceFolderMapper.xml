<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.file.mapper.SuperviseResourceFolderMapper">

    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="full_path" property="fullPath" jdbcType="LONGVARCHAR"/>
        <result column="folder_name" property="folderName" jdbcType="VARCHAR"/>
        <result column="sort_on" property="sortOn" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="del_flag" property="delFlag" jdbcType="BIT"/>
        <result column="original_path" property="originalPath" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, parent_id, full_path, folder_name, sort_on, status, del_flag, original_path,
        remark, create_by, create_time, update_by, update_time
    </sql>

    <!-- 自定义查询 -->
    <select id="getFolderList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM supervise_resource_folder
        ${ew.customSqlSegment}
    </select>


    <!-- 根据ID查询（排除已逻辑删除的记录） -->
    <select id="selectByIdNo" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM supervise_resource_folder
        WHERE id = #{id}
    </select>

    <update id="restoreFolders" parameterType="com.cb.ai.data.analysis.file.domain.SuperviseResourceFile">
        UPDATE supervise_resource_folder
        set del_flag = 0,parent_id=#{parentId}
        WHERE id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>

    <delete id="deleteByCustom">
        delete from supervise_resource_folder
                        ${ew.customSqlSegment}
    </delete>

    <!-- 更新文件夹记录 -->
    <update id="update" parameterType="com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder">
        UPDATE supervise_resource_folder
        <set>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="fullPath != null">full_path = #{fullPath},</if>
            <if test="folderName != null">folder_name = #{folderName},</if>
            <if test="sortOn != null">sort_on = #{sortOn},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="originalPath != null">original_path = #{originalPath},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除文件夹（设置del_flag=1） -->
    <update id="deleteById" parameterType="string">
        UPDATE supervise_resource_folder
        SET
        del_flag = 1,
        original_path = full_path,  <!-- 删除前保存原始路径 -->
        full_path = NULL,           <!-- 清空当前路径 -->
        update_time = NOW()         <!-- 自动设置更新时间 -->
        WHERE id = #{id} AND del_flag = 0
    </update>

    <!-- 查询根目录文件夹 -->
    <select id="selectRootFolders" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM supervise_resource_folder
        WHERE (parent_id IS NULL OR parent_id = '') AND del_flag = 0
        ORDER BY sort_on ASC
    </select>

    <!-- 根据父文件夹ID查询子文件夹 -->
    <select id="selectByParentId" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM supervise_resource_folder
        WHERE parent_id = #{parentId} AND del_flag = 0
        ORDER BY sort_on ASC
    </select>

    <!-- 根据路径前缀查询文件夹（用于树形结构遍历） -->
    <select id="selectByPathPrefix" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM supervise_resource_folder
        WHERE full_path LIKE CONCAT(#{path}, '%') AND del_flag = 0
        ORDER BY full_path ASC
    </select>

    <!-- 检查文件夹名称在父目录下是否唯一 -->
    <select id="countByNameAndParent" resultType="int">
        SELECT COUNT(*)
        FROM supervise_resource_folder
        WHERE
        folder_name = #{folderName}
        AND (parent_id = #{parentId} OR (parent_id IS NULL AND #{parentId} IS NULL))
        AND del_flag = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>
</mapper>