package com.cb.ai.data.analysis.ai.provider;

import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;
import com.cb.ai.data.analysis.ai.enums.ChatRole;
import com.cb.ai.data.analysis.ai.model.AIContext;
import com.cb.ai.data.analysis.ai.model.AiResultData;
import com.cb.ai.data.analysis.ai.model.EventStreamResult;
import com.xong.boot.common.utils.BeanUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

/**
 * AI Provider
 * <AUTHOR>
 */
public abstract class AbstractAiProvider<T extends AiResultData> {
    private final AiProviderService aiProviderService;
    /**
     * 存储会话
     */
    private boolean isSaveChat = false;

    public AbstractAiProvider(AiProviderService aiProviderService) {
        this.aiProviderService = aiProviderService;
    }

    public void setSaveChat(boolean saveChat) {
        isSaveChat = saveChat;
    }

    /**
     * 模式匹配
     */
    abstract boolean matcher(AIContext context);

    /**
     * 运行
     */
    abstract Flux<EventStreamResult<T>> run(String sessionId, String promote, AIContext context);

    /**
     * 分析执行
     */
    public Flux<EventStreamResult<T>> execute(AIContext context) {
        LocalDateTime sendTime = LocalDateTime.now();
        String promote = context.getPromote();
        String sessionId;
        if (isSaveChat) {
            String title = promote;
            if (StringUtils.isNotBlank(title) && title.length() > 10) {
                title = title.substring(0, 10);
            }
            sessionId = aiProviderService.createSession(context.getSessionId(), SecurityUtils.getUserId(), title);
            context.setSessionId(sessionId);
            saveUserSessionMessage(sessionId, promote, context, sendTime);
        } else {
            sessionId = context.getSessionId();
        }
        StringBuffer content = new StringBuffer();
        StringBuffer reasoningContent = new StringBuffer();
        StringBuffer errorContent = new StringBuffer();
        // 错误记录
        return run(sessionId, promote, context)
                .doOnNext(eventStreamResult -> { // 提取思考内容与回答内容
                    AiResultData resultData = eventStreamResult.getData();
                    if (StringUtils.isNotBlank(resultData.getContent())) {
                        content.append(resultData.getContent());
                    }
                    if (StringUtils.isNotBlank(resultData.getReasoningContent())) {
                        reasoningContent.append(resultData.getReasoningContent());
                    }
                })
                .onErrorResume(throwable -> {
                    errorContent.append(throwable);
                    return Flux.just(EventStreamResult.generateErrorResult(sessionId, throwable));
                }) // 错误返回节点
                .doFinally(signalType -> {
                    saveAssistantSessionMessage(sessionId, content.toString(), reasoningContent.toString(), errorContent.toString(), sendTime);
                }) // 在结束后保存会话历史
                .concatWithValues(EventStreamResult.generateEndResult(sessionId)); // 在最后输出结束节点
    }

    /**
     * 保存用户会话消息
     * @param sessionId 会话ID
     * @param promote   提示词
     * @param context   请求上下文
     * @param sendTime  发送时间
     */
    private void saveUserSessionMessage(String sessionId, String promote, AIContext context, LocalDateTime sendTime) {
        // 把用户消息插入到历史会话内
        AiChatHistorySessionMessage sessionMessage = new AiChatHistorySessionMessage();
        sessionMessage.setSessionId(sessionId);
        sessionMessage.setRole(ChatRole.user);
        sessionMessage.setContent(promote);
        sessionMessage.setContextContent(BeanUtils.beanToMap(context, "promote", "sessionId"));
        sessionMessage.setSendTime(sendTime);
        aiProviderService.saveSessionMessage(sessionMessage);
    }

    /**
     * 处理后会话保存
     * @param sessionId        会话ID
     * @param content          结果内容
     * @param reasoningContent 思考内容
     * @param errorContent     错误内容
     * @param sendTime         发送时间
     */
    private void saveAssistantSessionMessage(String sessionId, String content, String reasoningContent, String errorContent, LocalDateTime sendTime) {
        // 把用户消息插入到会话历史内
        AiChatHistorySessionMessage sessionMessage = new AiChatHistorySessionMessage();
        sessionMessage.setSessionId(sessionId);
        sessionMessage.setRole(ChatRole.assistant);
        sessionMessage.setContent(content);
        sessionMessage.setReasoningContent(reasoningContent);
        sessionMessage.setErrorContent(errorContent);
        sessionMessage.setSendTime(sendTime);
        aiProviderService.saveSessionMessage(sessionMessage);
    }
}
