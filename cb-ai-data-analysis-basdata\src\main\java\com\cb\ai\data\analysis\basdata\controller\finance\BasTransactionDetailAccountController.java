package com.cb.ai.data.analysis.basdata.controller.finance;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasTransactionDetailAccount;
import com.cb.ai.data.analysis.basdata.service.finance.BasTransactionDetailAccountService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.easyexcel.EasyExcelService;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 交易明细账表(BasTransactionDetailAccount)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-14 16:32:32
 */
@RestController
@RequestMapping(Constants.API_BASIC_DATA_ROOT_PATH + "/finance/transactionDetailAccount")
public class BasTransactionDetailAccountController extends BaseController<BasTransactionDetailAccountService, BasTransactionDetailAccount> {
    @Resource
    private EasyExcelService easyExcelService;

    /**
     * 分页获取交易明细账表
     *
     * @param basTransactionDetailAccount 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(BasTransactionDetailAccount basTransactionDetailAccount) {
        LambdaQueryWrapper<BasTransactionDetailAccount> queryWrapper = XQueryWrapper.newInstance(basTransactionDetailAccount)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(BasTransactionDetailAccount::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 获取交易明细账表
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "交易明细账表ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增交易明细账表
     *
     * @param basTransactionDetailAccount 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增交易明细账表", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody BasTransactionDetailAccount basTransactionDetailAccount) {
        if (baseService.save(basTransactionDetailAccount)) {
            return Result.success("交易明细账表新增成功！");
        }
        return Result.fail("交易明细账表新增失败！");
    }

    /**
     * 修改交易明细账表
     *
     * @param basTransactionDetailAccount 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改交易明细账表", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody BasTransactionDetailAccount basTransactionDetailAccount) {
        if (baseService.updateById(basTransactionDetailAccount)) {
            return Result.success("交易明细账表修改成功！");
        }
        return Result.fail("交易明细账表修改失败！");
    }

    /**
     * 删除交易明细账表
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除交易明细账表", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "交易明细账表ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.deleteByIds(list)) {
            return Result.success("交易明细账表删除成功！");
        }
        return Result.fail("交易明细账表删除失败！");
    }

    @PostMapping("/import")
    @XLog(title = "导入交易明细账表", execType = ExecType.IMPORT)
    public Result importData(MultipartFile file) {
        AtomicInteger count = new AtomicInteger();
        List<String> errMsgList = new ArrayList<>();
        try {
            easyExcelService.importExcel(
                    file.getInputStream(),
                    BasTransactionDetailAccount.class,
                    saveFunction -> {
                        count.addAndGet(saveFunction.size());
                        return baseService.importExcel(saveFunction);
                    },
                    errorMessage -> errMsgList.add(errorMessage)

            );
        } catch (Exception e) {
            return Result.fail("导入交易明细账表失败: " + e.getMessage());
        }
        StringBuilder msg = new StringBuilder();
        if (!errMsgList.isEmpty()) {
            msg.append("</br>导入失败！部分记录中的内容不正确：</br>" + String.join("</br>", errMsgList));
        }
        if (count.get() > 0) {
            msg.insert(0, "成功导入<span style=\"color:red;\">" + count + "</span>条数据!");
        }
        return Result.success(msg.toString());
    }

    @GetMapping("/importTemplate")
    @XLog(title = "下载交易明细账表导入模板", execType = ExecType.DOWNLOAD)
    public void importTemplate(HttpServletResponse response) {
        easyExcelService.downloadExcelTemplate(BasTransactionDetailAccount.class, "交易明细账表导入模板", "交易明细账表", response);
    }

    @GetMapping("/export")
    @XLog(title = "导出交易明细账表", execType = ExecType.EXPORT)
    public void exportData(BasTransactionDetailAccount basTransactionDetailAccount, HttpServletResponse response) {
        LambdaQueryWrapper<BasTransactionDetailAccount> queryWrapper = XQueryWrapper.newInstance(basTransactionDetailAccount)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(BasTransactionDetailAccount::getCreateTime);
        List<BasTransactionDetailAccount> list = baseService.list(queryWrapper);
        easyExcelService.exportExcel(
                BasTransactionDetailAccount.class,
                "交易明细账户表",
                "交易明细账户表",
                list,
                response);
    }
}

