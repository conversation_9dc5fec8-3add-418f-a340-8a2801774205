package com.cb.ai.data.analysis.petition.utils;



import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;

import java.io.IOException;
import java.io.InputStream;

public class DocReaderHandler extends FileReaderHandler<String> {
    @Override
    public String read(InputStream inputStream) throws IOException {
        HWPFDocument document = new HWPFDocument(inputStream);
        WordExtractor extractor = new WordExtractor(document);
        return extractor.getText();
    }

}
