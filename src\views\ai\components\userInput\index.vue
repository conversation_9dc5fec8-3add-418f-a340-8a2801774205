<script lang="ts" setup>
import markdownIt from '@/utils/markdown-it.js'
import {
  type ComponentCustomProperties,
  computed,
  defineEmits,
  defineExpose,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  useTemplateRef
} from 'vue'
import XIcon from '@/components/XIcon/XIcon'
import Recorder from 'recorder-core'
import { useImStore, useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import { fetchStream } from '@/utils/http'
import MinioUploadModal from '@/views/file/components/MinioUploadModal.vue'
import inputMenu from '../inputMenu/index.vue'
import guideMenus from '../guideMenus.vue'
import swiper from '../swiper.vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties

const route = useRoute()
const userStore = useUserStore()
const imStore = useImStore()

const iptPrefixToolRef = useTemplateRef('iptPrefixToolRef')
const emits = defineEmits(['send'])
const MinioUploadModalRef = useTemplateRef('MinioUploadModalRef')
const formRef = ref()
const activeId = ref(imStore.knowledgeId)
const fnVisible = ref(false)

function openFnMenu(index) {
  fnVisible.value = true
  if (imStore.deepThink) {
    activeId.value = imStore.knowledgeId
  } else {
    if (Number.isInteger(index)) {
      activeId.value = index
    }
  }
}

const currentSession = computed(() => {
  return imStore.conversation.find((v) => v.sessionId === route.query.id)
})

async function chooseFile() {
  MinioUploadModalRef.value?.openModal()
}

function uploadSuccess(files) {
  if (files && files.length > 0) {
    const file1 = files[0]
    const isAudio = file1.file.type?.search('audio') >= 0
    if (isAudio) {
      imStore.promote = '帮我看看音频内容主要讲什么'
    }
    imStore.quotes = [
      {
        parentId: imStore.fileId,
        label: file1.name,
        params: {
          analyseTag: [isAudio ? '音频分析' : '文件分析'],
          minioFileDataList: [
            {
              fileName: file1.name,
              fileUrl: file1.filePath,
              fileId: file1.id
            }
          ]
        }
      }
    ]
    MinioUploadModalRef.value?.closeModal()
  }
}

//光标#DOM相关操作
function spanClick(e: MouseEvent) {
  e.preventDefault()
  // 获取当前选区
  const selection = window.getSelection() as Selection
  const range = document.createRange()
  // 将光标设置在不可编辑元素之后
  range.setStartAfter(e.target as HTMLSpanElement)
  range.collapse(true)
  selection.removeAllRanges()
  selection.addRange(range)
  // 保持容器聚焦
  // richTextRef.value?.focus()
}

//在光标处插入
function insertAtCursor(content: HTMLElement | Text) {
  const selection = window.getSelection() as Selection
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(content)
    // 移动光标到插入内容之后
    range.setStartAfter(content)
    range.collapse(true)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

//录音功能
const waveform = ref()
const record = reactive({
  support: true, // 录音支持
  recorder: null, //录音实例对象
  waveView: null, // 音波展示
  audioBlob: null, // 录音文件blob
  underway: false // 录音中
})

function recordOpen() {
  //创建录音对象
  record.recorder = Recorder({
    type: 'mp3', //录音格式，可以换成wav等其他格式
    sampleRate: 32000, //录音的采样率，越大细节越丰富越细腻
    bitRate: 96, //录音的比特率，越大音质越好
    onProcess: (buffers, powerLevel, bufferDuration, bufferSampleRate) => {
      //录音监控回调
      if (record.waveView) {
        record.waveView.input(buffers[buffers.length - 1], powerLevel, bufferSampleRate)
      }
    }
  })
  if (!record.recorder) {
    console.error('当前浏览器不支持录音功能！')
    return
  }
  record.recorder.open(
    () => {
      console.log('录音已打开')
      record.underway = true
      if (!record.waveView) {
        nextTick(() => {
          // 创建波形图对象
          record.waveView = Recorder.WaveView({
            elem: waveform.value // 必须传入一个 DOM 元素
          })
        })
      }
      // 打开就默认开始录音
      recordStart()
    },
    (msg, isUserNotAllow) => {
      console.error((isUserNotAllow ? '用户拒绝权限：' : '') + '无法录音: ' + msg)
    }
  )
}

function recordStart() {
  if (!record.recorder) {
    console.error('未打开录音')
    return
  }
  record.recorder.start()
  console.log('已开始录音')
}

function recordStop(whetherUpload) {
  if (!record.recorder) {
    return
  }

  record.recorder.stop(
    (blob, duration) => {
      record.underway = false
      record.audioBlob = blob
      const localUrl = URL.createObjectURL(blob)
      console.log('录音成功', blob, localUrl, '时长:' + duration + 'ms')
      if (whetherUpload) {
        console.log('whetherUpload')
      }
      record.recorder.close()
      record.recorder = null
    },
    (err) => {
      console.error('结束录音出错：' + err)
      record.recorder.close()
      record.recorder = null
    }
  )
}

async function sendMsg() {
  if (!imStore.promote?.trim()) {
    return
  }
  const session = await imStore.sendMsg(route.query.id)
  emits('send', session.sessionId)
}

function promoteChange(e: KeyboardEvent) {
  if (e.keyCode === 51 && e.shiftKey && !e.ctrlKey && !e.altKey) {
    e.stopPropagation()
    e.preventDefault()
    openFnMenu()
  } else if (e.keyCode === 13 && e.ctrlKey && !e.shiftKey && !e.altKey) {
    e.stopPropagation()
    e.preventDefault()
    imStore.promote += '\n'
  } else if (e.keyCode === 13 && !e.shiftKey && !e.ctrlKey && !e.altKey) {
    e.stopPropagation()
    e.preventDefault()
    sendMsg()
  }
}

function init() {
  window.addEventListener('beforeunload', handleBeforeUnload)
}

function exit() {
  window.removeEventListener('beforeunload', handleBeforeUnload)
}

function handleDeepThink() {
  _this.$form.validate(formRef.value, async (errors, values) => {
    if (errors) {
      return
    }
    if (
      imStore.deepThinkInfo.formData.operate === 'accept' ||
      imStore.deepThinkInfo.formData.operate === 'edit'
    ) {
      if (imStore.deepThinkInfo.formData.promote) {
        imStore.promote = imStore.deepThinkInfo.formData.promote.trim()
        imStore.deepThinkInfo.planData = {}
        imStore.deepThinkInfo.planContent = ''
      } else {
        imStore.promote = '执行计划'
      }
      sendMsg()
    } else {
      handleBeforeUnload()
    }
    imStore.deepThinkInfo.visible = false
  })
}

function handleBeforeUnload() {
  imStore.deepThinkInfo.formData.operate = ''
  imStore.deepThinkInfo.planData = {}
  imStore.deepThinkInfo.planContent = ''
  if (imStore.deepThink) {
    if (currentSession.value) {
      fetchStream({
        url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          authorization: `Bearer ${userStore.token}`
        },
        body: JSON.stringify({
          sessionId: currentSession.value.sessionId,
          deepThink: imStore.deepThink,
          promote: '结束计划',
          interruptFeedback: 'reject'
        }),
        success: (res: string) => {},
        error: (err: any) => {},
        complete: () => {}
      })
    }
  }
}

function deepThinkChange(v) {
  if (v.target.checked) {
    imStore.quotes = imStore.quotes.filter((v) =>
      [imStore.knowledgeId, imStore.knowledgeFileId].includes(v.parentId)
    )
  } else {
    // 文件只保留一个
    imStore.quotes = imStore.quotes.reduce((total, item) => {
      if (total.some((j) => j.parentId === imStore.fileId)) {
      } else {
        total.push(item)
      }
      return total
    }, [])
  }
}

function handleColseDeepThink() {
  formRef.value?.resetFields()
  imStore.deepThinkInfo.visible = false
}

function formatMarkdown(content: string) {
  return markdownIt.render(content)
}

onMounted(init)
onUnmounted(exit)
defineExpose({ chooseFile, openFnMenu })
</script>

<template>
  <div class="aiUserInput">
    <div class="prefixTool"></div>
    <div class="iptContainer">
      <div ref="iptPrefixToolRef" class="iptPrefixTool">
        <a-popover
          v-model:open="fnVisible"
          :getPopupContainer="() => iptPrefixToolRef"
          arrow-point-at-center
          overlayClassName="aiUserInputPopover"
          placement="top"
          trigger="click"
        >
          <template #content>
            <inputMenu v-model:activeId="activeId" v-model:visible="fnVisible" />
          </template>
        </a-popover>
        <swiper v-model:value="imStore.quotes" :page-size="7">
          <template v-slot:default="{ data, index }">
            <div class="quoteItem" @click="imStore.quotes.splice(index, 1)">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ data.label }}
                </template>
                {{ data.label }}
              </a-tooltip>
              <x-icon class="del" type="CloseOutlined"></x-icon>
            </div>
          </template>
        </swiper>
      </div>

      <!--      录音的效果-->
      <div v-show="record.underway" class="recorder-info">
        <div ref="waveform" class="recorder-wave"></div>
      </div>
      <!--      富文本输入框-->
      <a-textarea
        v-model:value.trim="imStore.promote"
        :auto-size="{ minRows: 2, maxRows: 5 }"
        autofocus
        class="richText scrollBeauty"
        placeholder="请输入#获取帮助，按Enter发送，按Ctrl+Enter换行"
        @keydown="promoteChange"
      />
      <!--      <div-->
      <!--        ref="richTextRef"-->
      <!--        class="richText"-->
      <!--        contenteditable="true"-->
      <!--        placeholder="请输入#获取帮助，按Enter发送，按Ctrl+Enter换行"-->
      <!--      ></div>-->
      <!--      <div class="iptSuffixTool">内部预留操作栏</div>-->
      <div class="operates">
        <div v-show="route.query.id" class="choose_left">
          <guideMenus layout="swiper" @change="openFnMenu" />
        </div>
        <a-checkbox
          v-if="!imStore.quotes.some((v) => v.parentId === imStore.fileId)"
          v-model:checked="imStore.deepThink"
          :disabled="!!currentSession?.abortController"
          @change="deepThinkChange"
          >深度研究
        </a-checkbox>
        <a-checkbox v-if="!imStore.deepThink" v-model:checked="imStore.usePresetScoreThreshold"
          >精准搜索
        </a-checkbox>
        <!--        <span-->
        <!--          :class="[imStore.deepThink && 'deepThinkActive', 'deepThink']"-->
        <!--          @click="deepThinkChange"-->
        <!--        >-->
        <!--          <x-icon type="CheckOutlined"></x-icon>-->
        <!--          <span>深度思考</span>-->
        <!--        </span>-->
        <!--        选择文件-->
        <a-tooltip v-if="!imStore.deepThink" placement="top">
          <template #title>
            <span>选择文件</span>
          </template>
          <x-icon class="icon" type="PlusOutlined" @click="chooseFile"></x-icon>
        </a-tooltip>

        <!--        未录音-->
        <!--        <x-icon-->
        <!--          v-show="record.support && !record.underway"-->
        <!--          class="icon active"-->
        <!--          type="AudioOutlined"-->
        <!--          @click="recordOpen"-->
        <!--        ></x-icon>-->
        <!--        录音中-->
        <!--        <x-icon-->
        <!--          v-show="record.underway"-->
        <!--          class="icon active"-->
        <!--          type="CloseOutlined"-->
        <!--          @click="recordStop"-->
        <!--        ></x-icon>-->
        <!--        保存-->
        <!--        <x-icon-->
        <!--          v-show="record.underway"-->
        <!--          class="icon active"-->
        <!--          type="CheckOutlined"-->
        <!--          @click="recordStop"-->
        <!--        ></x-icon>-->
        <!--        发送消息-->

        <a-tooltip v-if="currentSession?.abortController" placement="top">
          <template #title>
            <span>中断</span>
          </template>
          <x-icon
            :class="['icon', 'active']"
            type="CloseOutlined"
            @click="imStore.abort(route.query.id)"
          ></x-icon>
        </a-tooltip>
        <a-tooltip v-else-if="!imStore.loading.sendMsg && imStore.promote?.trim()" placement="top">
          <template #title>
            <span>发送消息</span>
          </template>
          <x-icon :class="['icon', 'active']" type="SendOutlined" @click="sendMsg"></x-icon>
        </a-tooltip>
        <a-tooltip v-else placement="top">
          <template #title>
            <span>请输入提示词</span>
          </template>
          <x-icon :class="['icon', 'disabled']" type="SendOutlined"></x-icon>
        </a-tooltip>
      </div>
    </div>
    <!--    <div class="suffixTool">预留的额外操作栏</div>-->
    <MinioUploadModal
      ref="MinioUploadModalRef"
      :multiple="imStore.deepThink"
      @success="uploadSuccess"
    />

    <a-modal
      v-model:open="imStore.deepThinkInfo.visible"
      :mask-closable="false"
      title="计划执行"
      width="52%"
      @cancel="handleColseDeepThink"
      @ok="handleDeepThink"
    >
      <a-form
        ref="formRef"
        :label-col="{ style: { width: '100px' } }"
        :model="imStore.deepThinkInfo.formData"
      >
        <div v-html="formatMarkdown(imStore.deepThinkInfo.planContent)"></div>
        <a-form-item
          :rules="{ message: '必须填写执行类型', required: true }"
          label="执行类型"
          name="operate"
        >
          <a-radio-group v-model:value="imStore.deepThinkInfo.formData.operate">
            <a-radio value="accept">接受计划</a-radio>
            <a-radio value="edit">编辑计划</a-radio>
            <a-radio value="reject">拒绝计划</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="imStore.deepThinkInfo.formData.operate === 'edit'"
          :rules="[
            {
              required: imStore.deepThinkInfo.formData.operate === 'edit',
              message: '必须填写修改意见'
            }
          ]"
          label="修改意见"
          name="promote"
        >
          <a-textarea v-model:value="imStore.deepThinkInfo.formData.promote" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style lang="less">
@import '@/assets/styles/utils';

.aiUserInput {
  width: 100%;
  margin: 2vh 0;

  .prefixTool {
    width: 100%;
    position: relative;
  }

  .suffixTool {
  }

  .iptContainer {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 24px;
    background: var(--layout-light-bgcolor);
    border: 1px solid #e6e9f7;
  }

  .iptPrefixTool {
    width: 100%;
    position: relative;

    .quoteItem {
      user-select: none;
      text-align: center;
      cursor: pointer;
      line-height: 1.5;
      width: 95%;
      padding: 0 5px;
      position: relative;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      border-radius: 5px;
      background-color: rgba(0, 0, 0, 0.05);

      .del {
        display: none;
        background-color: var(--error-color);
        border-radius: 50%;
        aspect-ratio: 1;
        font-size: 10px;
        color: var(--layout-dark-color);
        padding: 2px;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
      }

      &:hover {
        .del {
          display: block;
        }
      }
    }
  }

  .iptSuffixTool {
  }

  .recorder-info {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px 20px;

    .recorder-wave {
      width: 100%;
      height: 40px;
    }
  }

  .richText {
    line-height: 1.5;
    width: 100%;
    background-color: var(--layout-light-bgcolor);
    text-align: justify;
    margin: 5px 0;
    padding: 0;
    box-sizing: border-box;
    max-height: 30vh;
    resize: none;
    border: none;
    word-wrap: break-word;
    word-break: break-all;
    font-size: 1.2rem;

    &:focus {
      box-shadow: none;
      border: none;
      outline: none;
    }
  }

  .operates {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    user-select: none;

    .choose_left {
      margin: 0 auto 0 0;
      width: 0;
      height: 100%;
      flex-grow: 1;
    }

    .deepThink {
      cursor: pointer;
    }

    .deepThinkActive {
      color: var(--primary-color);
    }

    .icon {
      color: var(--layout-light-color);
      flex-shrink: 0;
      margin-left: 5px;
      font-size: 20px;
      padding: 10px;
      border-radius: 50%;
      aspect-ratio: 1;
      cursor: pointer;
    }

    .disabled {
      cursor: not-allowed;
      background-color: var(--layout-light-bgcolor);
      color: var(--layout-light-color);
    }

    .active {
      background-color: var(--primary-color);
      color: var(--layout-dark-color);
    }
  }
}

.aiUserInputPopover {
  width: 100%;
  left: 0 !important;

  .ant-popover-inner {
    width: 100%;
  }
}
</style>
