package com.cb.ai.data.analysis.petition.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SsPetitionAnalyzedExportDto {

    @ExcelProperty("提交信访者姓名")
    private String petitionPerson;

    @ExcelProperty("所属省份")
    private String petitionProvince;

    @ExcelProperty("所属城市")
    private String petitionCity;

    @ExcelProperty("所属区县")
    private String petitionDistrict;

    @ExcelProperty("所属街道")
    private String petitionStreet;

    @ExcelProperty("举报人居住省份")
    private String userResidenceProvince;

    @ExcelProperty("举报人居住市")
    private String userResidenceCity;

    @ExcelProperty("举报人居住区县")
    private String userResidenceDistrict;

    @ExcelProperty("举报人居住街道")
    private String userResidenceStreet;

    @ExcelProperty("所属领域")
    private String petitionDomain;

    @ExcelProperty("信访目的")
    private String petitionPurpose;

    @ExcelProperty("处理建议")
    private String petitionHandleSuggestion;

    @ExcelProperty("摘要")
    private String brief;

    @ExcelProperty("被举报人姓名")
    private String petitionAccusedPerson;

    @ExcelProperty("信访去向机构")
    private String petitionBelongOrg;

    @ExcelProperty("登记日期")
    private String registerDate;

    @ExcelProperty("信访日期")
    private String petitionDate;
}
