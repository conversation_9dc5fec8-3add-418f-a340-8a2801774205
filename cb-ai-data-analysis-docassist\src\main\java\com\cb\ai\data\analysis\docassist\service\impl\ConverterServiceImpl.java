package com.cb.ai.data.analysis.docassist.service.impl;

import cn.hutool.core.io.FileUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.DocFormatConverter;
import com.cb.ai.data.analysis.docassist.request.WordFormatRequestVo;
import com.cb.ai.data.analysis.docassist.service.ConverterService;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

@Service
public class ConverterServiceImpl implements ConverterService {
    @Override
    public String formatWord(WordFormatRequestVo wordFormatRequestVo) throws IOException {

        String oldFileAbsolutePath = wordFormatRequestVo.getOldFileAbsolutePath();
        String newFileName = wordFormatRequestVo.getNewFileName();
        String newFileDir = wordFormatRequestVo.getNewFileDir();

        File file = new File(oldFileAbsolutePath);
        if (!file.exists()) {
            return null;
        } else {
            XWPFDocument xwpfDocument = new XWPFDocument(FileUtil.getInputStream(oldFileAbsolutePath));
            if (wordFormatRequestVo.getWhetherMarkdown()) {
                DocFormatConverter.formatMarkdown(xwpfDocument, new DocConfig());
            } else {
                DocFormatConverter.formatDocument(xwpfDocument, new DocConfig());
            }
            OutputStream os = FileUtil.getOutputStream(newFileDir + File.separator + newFileName);
            xwpfDocument.write(os);
            os.flush();
            return newFileDir + File.separator + newFileName;
        }
    }
}
