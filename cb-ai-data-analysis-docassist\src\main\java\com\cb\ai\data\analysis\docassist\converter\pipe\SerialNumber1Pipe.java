package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 一级标题管道
 *
 * <AUTHOR>
 */
public class SerialNumber1Pipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.contains("^[一二三四五六七八九十]+[,.．，。、]", text);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatParagraphInd(paragraph, config);
        // 1级标题
        List<XWPFRun> runs = paragraph.getRuns();
        // 修改序号后标点符号
        for (XWPFRun run : runs) {
            String runText = run.text();
            if (StringUtils.isNotBlank(runText) && ReUtil.contains("[,.．，。、]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "([一二三四五六七八九十]?)([,.．，。、])(.*)", "$1、$3");
                run.setText(replaced, 0);
                break;
            }
        }

        // 修改序号字体样式
        String paragraphText = paragraph.getText().trim();
        boolean isFormatTop = false;
        if (paragraphText.indexOf("。") > 52 || paragraphText.indexOf("：") > 52) {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("、");
                if (index == -1) {
                    FormatTools.formatSerialNumber1(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber1(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber1(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        } else {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("：");
                if (index == -1) {
                    index = runText.indexOf("。");
                }
                if (index == -1) {
                    FormatTools.formatSerialNumber1(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber1(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber1(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        }
    }
}
