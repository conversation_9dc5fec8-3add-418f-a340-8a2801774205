package com.cb.ai.data.analysis.dbtable.model;

import com.cb.ai.data.analysis.dbtable.enums.Andor;
import com.cb.ai.data.analysis.dbtable.enums.Condition;
import lombok.Data;

/**
 * 查询项
 * <AUTHOR>
 */
@Data
public class QueryItem {
    /**
     * 字段名称
     */
    private String name;
    /**
     * 字段值
     */
    private Condition condition;
    /**
     * 字段值
     */
    private Object value;
    /**
     * 且或运算符
     */
    private Andor andor;
}
