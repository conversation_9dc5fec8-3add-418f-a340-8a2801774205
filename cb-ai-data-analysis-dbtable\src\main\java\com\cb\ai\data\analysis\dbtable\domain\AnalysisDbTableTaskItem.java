package com.cb.ai.data.analysis.dbtable.domain;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.dbtable.model.ColumnData;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据导入任务项对象 dynamic_table_task_item
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTableTaskItem extends SimpleBaseDomain {

    /**
     * 工作日志ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 工作ID
     */
    @NotBlank(message = "工作ID不存在", groups = AddGroup.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String jobId;
    /**
     * 数据ID
     */
    private String dataId;
    /**
     * 原始数据
     */
    private String sourceData;
    /**
     * 目标数据
     */
    private String targetData;
    /**
     * 确认人
     */
    private String confirmBy;
    /**
     * 确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 状态 0正常 1错误 2待确认 3必填字段未填备注
     */
    private Integer status;

    public void setSourceData(String sourceData) {
        this.sourceData = sourceData;
    }

    public void setTargetData(String targetData) {
        this.targetData = targetData;
    }

    public void setSourceData(Map<Integer, String> sourceData) {
        this.sourceData = JSON.toJSONString(sourceData);
    }

    public void setTargetData(List<ColumnData> targetData) {
        this.targetData = JSON.toJSONString(targetData);
    }
}
