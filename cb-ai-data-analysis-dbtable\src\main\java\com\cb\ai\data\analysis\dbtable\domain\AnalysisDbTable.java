package com.cb.ai.data.analysis.dbtable.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.handlers.ArrayStringTypeHandler;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 动态数据表 analysis_db_table
 * <AUTHOR>
 */
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTable extends BaseDomain {
    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 父表名
     */
    private String parentTableName;
    /**
     * 表名
     */
    @NotBlank(message = "表名不存在", groups = AddGroup.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String tableName;
    /**
     * 表描述
     */
    @TableField(condition = SqlCondition.LIKE)
    @NotBlank(message = "表描述不存在", groups = AddGroup.class)
    private String tableComment;
    /**
     * 表图标
     */
    private String tableIcon;
    /**
     * 表类别
     */
    private String tableCategory;
    /**
     * 表标签
     */
    @TableField(typeHandler = ArrayStringTypeHandler.class)
    private List<String> tableTags;
    /**
     * 禁止添加 0否 1是
     */
    private Boolean disableAdd;
    /**
     * 禁止删除 0否 1是
     */
    private Boolean disableDelete;
    /**
     * 禁止编辑 0否 1是
     */
    private Boolean disableEdit;
    /**
     * 禁止导入 0否 1是
     */
    private Boolean disableImport;
    /**
     * 禁止导出 0否 1是
     */
    private Boolean disableExport;
    /**
     * 隐藏标记 0显示 1隐藏
     */
    private Boolean hideFlag;
    /**
     * 排序
     */
    private Integer sortOn;
    /**
     * 状态 0正常 1禁用
     */
    private Integer status;
    /**
     * 是否删除 0正常 1删除
     */
    @JsonIgnore
    @TableLogic
    private Boolean delFlag;
}
