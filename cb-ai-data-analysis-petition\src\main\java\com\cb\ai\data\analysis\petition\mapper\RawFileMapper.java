package com.cb.ai.data.analysis.petition.mapper;


import com.cb.ai.data.analysis.petition.annotation.DataSource;
import com.cb.ai.data.analysis.petition.domain.entity.RawFileEntity;
import com.cb.ai.data.analysis.petition.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DataSourceType.SLAVE)
public interface RawFileMapper {
     void insertBatch(@Param("list") List<RawFileEntity> list);

    void insert( RawFileEntity rawFileEntity);

    List<RawFileEntity> selectPage(RawFileEntity rawFileEntity);

    RawFileEntity selectOneById(@Param("id") String id);

    List<RawFileEntity> selectByIds(@Param("idList") List<String> idList);

    int updateAnalysisStatus(@Param("id") String id, @Param("analysisStatus") int analysisStatus);

    int updateAnalysisStatusByIds(@Param("ids") List<String> ids, @Param("analysisStatus") int analysisStatus);

}
