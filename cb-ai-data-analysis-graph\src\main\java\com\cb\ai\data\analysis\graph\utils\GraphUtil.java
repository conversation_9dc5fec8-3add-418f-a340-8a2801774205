package com.cb.ai.data.analysis.graph.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresFamily;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresInfo;
import com.cb.ai.data.analysis.graph.domain.entity.basic.GraphEnterpriseInfo;
import com.cb.ai.data.analysis.graph.handler.FileProcessor;
import com.cb.ai.data.analysis.graph.handler.FileProcessorFactory;
import com.xong.boot.common.utils.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 数据采集后入库到关系数据库
 * @Author: ARPHS
 * @Date: 2025-05-28 15:24
 * @Version: 1.0
 **/
public class GraphUtil {

    private static final FileProcessor excelProcessor = FileProcessorFactory.getFileProcessor(".xlsx", true, null);

    /**
     * 处理企业信息
     *
     * @param list
     */
    public static void handleEnterpriseExcel(List<GraphEnterpriseInfo> list) {
        List<Map<String, String>> mapList = list2MapList(list);
        excelProcessor.handleEnterpriseExcel(mapList);
    }

    /**
     * 处理干部信息
     *
     * @param list
     */
    public static void handleOfficerInfo(List<GraphCadresInfo> list) {
        List<Map<String, String>> mapList = list2MapList(list);
        excelProcessor.handlePersonExcel(mapList);
    }

    /**
     * 处理干部亲属信息
     *
     * @param list
     */
    public static void handleOfficerFamily(List<GraphCadresFamily> list) {
        List<Map<String, String>> mapList = list2MapList(list);
        excelProcessor.handleKinExcel(mapList);
    }

    private static <T> List<Map<String, String>> list2MapList(List<T> list) {
        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Map<String, String>> mapList = list.stream()
                .map(GraphUtil::item2Map)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return mapList;
    }

    private static <T> Map<String, String> item2Map(T item) {
        if (null == item) {
            return null;
        }
        Map<String, String> map = new LinkedHashMap<>();
        if (item instanceof GraphEnterpriseInfo) {
            GraphEnterpriseInfo entity = (GraphEnterpriseInfo) item;
            map.put("公司名称", entity.getEnterpriseName());
            map.put("登记状态", entity.getRegisterStatus());
            map.put("法定代表人", entity.getLegalRepresentative());
            map.put("股东人员", entity.getShareholder());
            String foundDate = entity.getFoundDate() == null ? null : DateUtil.formatLocalDateTime(entity.getFoundDate());
            String approveDate = entity.getApproveDate() == null ? null : DateUtil.formatLocalDateTime(entity.getApproveDate());
            map.put("注册资本", entity.getRegisterCapital());
            map.put("实缴资本", entity.getContributedCapital());
            map.put("成立日期", foundDate);
            map.put("核准日期", approveDate);
            map.put("所属省份", entity.getProvince());
            map.put("所属城市", entity.getCity());
            map.put("所属区县", entity.getCounty());
            map.put("公司类型", entity.getEnterpriseType());
            map.put("曾用名", entity.getFormerName());
            map.put("统一社会信用代码", entity.getUnifySocialCreditCode());
            map.put("纳税人识别号", entity.getTaxpayerIdentifyNo());
            map.put("工商注册号", entity.getIndustryCommerceRegisterNo());
            map.put("备注", entity.getRemark());
            return map;
        } else if (item instanceof GraphCadresInfo) {
            GraphCadresInfo entity = (GraphCadresInfo) item;
            map.put("姓名", entity.getName());
            map.put("工作单位", entity.getWorkUnit());
            map.put("性别", entity.getSex());
            map.put("身份证号", entity.getIdCard());
            map.put("民族", entity.getNation());
            map.put("籍贯", entity.getNativePlace());
            map.put("出生地", entity.getBirthPlace());
            String birthDate = entity.getBirthday() == null ? null : DateUtil.formatLocalDateTime(entity.getBirthday());
            map.put("出生年月", birthDate);
            map.put("政治面貌", entity.getPoliticalIdentity());
            String joinPartyDate = entity.getPartyJoinTime() == null ? null : DateUtil.formatLocalDateTime(entity.getPartyJoinTime());
            String workStartDate = entity.getStartWorkTime() == null ? null : DateUtil.formatLocalDateTime(entity.getStartWorkTime());
            map.put("入党时间", joinPartyDate);
            map.put("参加工作时间", workStartDate);
            map.put("现职务", entity.getWorkPost());
            return map;
        } else if (item instanceof GraphCadresFamily) {
            GraphCadresFamily entity = (GraphCadresFamily) item;
            map.put("工作单位", entity.getWorkUnit());
            map.put("姓名", entity.getName());
            map.put("身份证号", entity.getIdCard());
            map.put("亲属姓名", entity.getFamilyName());
            map.put("亲属关系", entity.getFamilyRelation());
            map.put("亲属身份证号", entity.getFamilyIdCard());
            return map;
        } else {
            return null;
        }
    }

    /**
     * 数据预处理，按合并的规则填充
     *
     * @param dataList
     */
    public static void dataPreOfficerInfo(List<GraphCadresInfo> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        String workUnitLast = null;
        for (int i = 0; i < dataList.size(); i++) {
            GraphCadresInfo item = dataList.get(i);
            String workUnit = item.getWorkUnit();
            if (StringUtils.isNotBlank(workUnit)) {
                workUnitLast = workUnit;
            } else {
                item.setWorkUnit(workUnitLast);
            }
        }
    }

    /**
     * 数据预处理，按合并的规则填充
     *
     * @param dataList
     */
    public static void dataPreOfficerFamily(List<GraphCadresFamily> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }
        String workUnitLast = null;
        String nameLast = null;
        String idCardLast = null;
        for (int i = 0; i < dataList.size(); i++) {
            GraphCadresFamily item = dataList.get(i);
            String workUnit = item.getWorkUnit();
            String name = item.getName();
            String idCard = item.getIdCard();
            if (StringUtils.isNotBlank(workUnit)) {
                workUnitLast = workUnit;
            } else {
                item.setWorkUnit(workUnitLast);
            }
            if (StringUtils.isNotBlank(name)) {
                nameLast = name;
                idCardLast = idCard;
            } else {
                item.setName(nameLast);
                item.setIdCard(idCardLast);
            }
        }
    }

}
