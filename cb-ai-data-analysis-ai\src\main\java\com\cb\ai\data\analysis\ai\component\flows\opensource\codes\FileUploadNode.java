//package com.cb.ai.data.analysis.ai.component.flows.opensource.codes;
//
//import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
//import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
//import lombok.RequiredArgsConstructor;
//import org.reactivestreams.Publisher;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @CreateTime 2025/7/7 19:59
// * @Copyright (c) 2025
// * @Description 文件上传节点
// */
//@Component
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class FileUploadNode implements Node {
//
//    private final SuperviseResourceFileService fileService;
//
//    @Override
//    public Publisher<?> process(Object context, FlowContext flowContext) {
//        return null;
//    }
//
//    //@Override
//    //public List<JsonMap> process(MultiFileData multiFileData, Object... args) {
//    //    List<JsonMap> uploadMap = new ArrayList<>(multiFileData.size());
//    //    multiFileData.forEach((k, v) -> {
//    //        if (!v.isEmpty()) {
//    //            for (MultipartFile file : v) {
//    //                String fileType = StrUtil.subAfter(file.getOriginalFilename(), ".", true);
//    //                try {
//    //                    SuperviseResourceFile resourceFile = fileService.uploadFileStreamByFolderName(
//    //                            file.getInputStream(),
//    //                            null,
//    //                            file.getOriginalFilename(),
//    //                            "AI分析",
//    //                            file.getSize(),
//    //                            fileType,
//    //                            null,
//    //                            SecurityUtils.getUsername()
//    //                    );
//    //                    if (resourceFile != null) {
//    //                        String filePath = fileService.getDownloadUrl(resourceFile.getId());
//    //                        uploadMap.add(JsonMap.of("fileName", file.getOriginalFilename()).putOnce("fileUrl", filePath));
//    //                    }
//    //                } catch (Exception e) {
//    //                    throw new RuntimeException("上传文件失败，原因：" + Throwables.getRootCause(e).getMessage(), e);
//    //                }
//    //            }
//    //        }
//    //    });
//    //    return uploadMap;
//    //}
//}
