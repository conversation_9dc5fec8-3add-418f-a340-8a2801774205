package com.cb.ai.data.analysis.dbtable.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableColumn;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableColumnService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;

import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 动态数据表字段 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table/column")
public class AnalysisDbTableColumnController extends BaseController<AnalysisDbTableColumnService, AnalysisDbTableColumn> {
    /**
     * 分页获取数据表列表
     * @param params 查询条件
     */
    @GetMapping("/page")
//    @PreAuthorize("hasAuthority('dbtable:table:list')")
    public Result page(AnalysisDbTableColumn params) {
        LambdaQueryWrapper<AnalysisDbTableColumn> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda()
                .orderByAsc(AnalysisDbTableColumn::getSortOn)
                .orderByAsc(AnalysisDbTableColumn::getId);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}

