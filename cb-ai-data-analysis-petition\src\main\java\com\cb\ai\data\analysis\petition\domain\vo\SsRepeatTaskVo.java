package com.cb.ai.data.analysis.petition.domain.vo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.xong.boot.common.utils.StringUtils;
import lombok.Data;

import java.util.Date;

public interface SsRepeatTaskVo {

    /**
     * 创建重复信访件分析任务请求参数
     */
    @Data
    class CreateReq {
        private String title;
        private String beginDate;
        private String endDate;
        private double threshold = 0.8;//相似度阈值
    }

    /**
     * 重复信访件分析任务异步结果
     */
    @Data
    class RepeatTaskFutureResult {
        private String taskId;
        private String resultFileId;
        private long similar;
        private long total;
        private Integer status;
        private String errMsg;
        private String userName;
    }

    /**
     * 用于将信访件分组后再进行重复件的判断，否则数据量太大了
     */
    @Data
    class RepeatTaskAnalyzeGroup {
        //信访目的（一级分类）
        private String petitionPurposeCategory;
        //信访目的（二级分类）
        private String petitionPurpose;
        //所属领域（一级分类）
        private String petitionDomainCategory;
        //所属领域（二级分类）
        private String petitionDomain;
        //所属省份
        private String petitionProvince;
        //所属城市
        private String petitionCity;

        //任务id
        private String taskId;
        //范围-起始时间
        private Date beginDate;
        //范围-结束时间
        private Date endDate;

        /**
         * 生成md5摘要字符串
         * @return
         */
        public String genMD5(){
            // 将所有字段拼接成一个字符串
            String input = StringUtils.format("{}|{}|{}|{}|{}|{}|{}|{}|{}",
                    petitionPurposeCategory,
                    petitionPurpose,
                    petitionDomainCategory,
                    petitionDomain,
                    petitionProvince,
                    petitionCity,
                    taskId,
                    beginDate != null ? DateUtil.format(beginDate, "yyyy-MM-dd HH:mm:ss") : "",
                    endDate != null ? DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss") : ""
            );
            return DigestUtil.md5Hex(input);
        }

        //生成分组的向量存储的文件名
        public String genVectorFileName(){
            return StringUtils.format("vector_{}.txt", genMD5());
        }

    }

    /**
     * 向量数据项，用于读取文件时的数据结构
     */
    @Data
    class VectorItem {
        private Long id;
        private float[] vector;

        public VectorItem(Long id, float[] vector) {
            this.id = id;
            this.vector = vector;
        }

        @Override
        public String toString() {
            return String.format("VectorItem{id=%d, vectorLength=%d}", id, vector != null ? vector.length : 0);
        }
    }

}
