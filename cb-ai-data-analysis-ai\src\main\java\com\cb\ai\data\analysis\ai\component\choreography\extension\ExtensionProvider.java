package com.cb.ai.data.analysis.ai.component.choreography.extension;


import com.cb.ai.data.analysis.ai.component.choreography.model.Route;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 16:05
 * @Copyright (c) 2025
 * @Description 扩展服务者
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExtensionProvider {
    /**
     * 扩展业务路由场景
     */
    Route[] businessScenes();
    
    /**
     * 扩展场景描述
     */
    String desc();
}
