package com.cb.ai.data.analysis.ai.component.choreography.model;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 17:47
 * @Copyright (c) 2025
 * @Description 业务类型枚举
 */
@Getter
public enum BusinessTypeEnum {
    /**
     * 默认业务
     */
    DEFAULT_BUSINESS,
    /**
     * 私有化业务
     */
    PRIVATE_BUSINESS,

    ;

    //public boolean isEqual(int businessType) {
    //    return this.code == businessType;
    //}

    //public BusinessScene toBizScene() {
    //    return BusinessScene.of(code);
    //}

    //public BusinessScene toBizScene(String sceneName) {
    //    return BusinessScene.of(code, sceneName);
    //}
}
