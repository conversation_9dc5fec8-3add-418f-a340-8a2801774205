package com.cb.ai.data.analysis.ai.component.choreography.engine;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.amazonaws.util.Throwables;
import com.cb.ai.data.analysis.ai.common.event.HistoryChatEventProducer;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowAttribute;
import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeAttribute;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.cb.ai.data.analysis.ai.domain.enums.ResultDataStatusEnum;
import com.cb.ai.data.analysis.ai.domain.enums.RoleEnum;
import com.cb.ai.data.analysis.ai.domain.request.context.AIRequestContext;
import com.cb.ai.data.analysis.ai.domain.request.context.RequestContext;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.OptionalUtil;
import com.xong.boot.common.exception.CustomException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpRequest;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 19:52
 * @Copyright (c) 2025
 * @Description 基础的AI回调节点
 */
public abstract class BaseAiCallNode<E, R> extends BaseAiNode<E, R, Flux<R>> implements IRequestOperation<R>, INodeAroundOperation<R>, INodeEvent<R> {

    private final HistoryChatEventProducer eventProducer;

    public BaseAiCallNode() {
        this.eventProducer = SpringUtil.getBean(HistoryChatEventProducer.class);
    }

    @Override
    public R syncProcess(E requestContext) {
        // 同步方法要阻止外部额外操作
        getNode().setHideContent(false).setHideThinking(false);
        NodeContext nodeContext = getNode().getNodeContext();
        List<R> rList = process(requestContext).collectList().block();
        if (CollectionUtil.isNotEmpty(rList)) {
            rList.forEach(data -> collectNodeData(data, nodeContext));
            return nodeContextToData(nodeContext);
        } else {
            throw new CustomException(getNodeName() + "请求返回结果为空！");
        }
    }

    @Override
    public Flux<R> process(E requestContext) {
        // 优先情况： context(流程) > context(请求) > 请求
        E contextVar = Optional.ofNullable(getRequestContextFun())
            .map(fun -> fun.apply(getFlowContext()))
            .orElseGet(() -> OptionalUtil.ofNullable(getRequestContext()).orElse(requestContext));
        Assert.notNull(contextVar, getNodeName() + "请求失败：，请求上下文（context）为空！");
        NodeAttribute<E, R> node = getNode();
        // 转为内部请求上下文
        node.setRequestContext(contextVar);
        // 请求前处理
        executeBefore();
        AtomicBoolean isFirstElement = new AtomicBoolean(true);
        AtomicReference<HttpHeaders> headers = new AtomicReference<>();
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonLog.info("开始进行【{}({})】请求参数组装 ------>", getNodeName(), getNodeId());
        // 执行请求
        WebClient webClient = getWebClient();
        // 发送请求头会话记录
        //sendHistoryChat(RoleEnum.user, contextVal, new NodeContext().setResponseTime(LocalDateTime.now()));
        return webClient.method(setRequestMethod())
            .uri(setRequestUrl())
            .headers(h -> {
                setRequestHeader(h);
                headers.set(h);
            })
            .accept(MediaType.TEXT_EVENT_STREAM)
            .body(bodyInserter(headers.get(), stopWatch))
            .retrieve()
            .onStatus(HttpStatusCode::isError, response ->
                response.bodyToMono(String.class)
                    .defaultIfEmpty("")
                    .flatMap(body -> Mono.error(new CustomException(getNodeName() + "请求失败，状态码：" + response.statusCode().value() + "，信息：" + body)))
            )
            .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>(){})
            .mapNotNull(data -> convertToResponseData(data, node))
            .filter(ObjectUtil::isNotEmpty)
            .startWith(startDataList())
            .doFirst(() -> node.getNodeContext().setStartTime(LocalDateTime.now()))
            .doOnNext(data -> {
                if (isFirstElement.compareAndSet(true, false)) {
                    stopWatch.stop();
                    CommonLog.info("【{}({})】接收到了第一个响应数据，耗时：{}ms", getNodeName(), getNodeId(), stopWatch.getLastTaskTimeMillis());
                    stopWatch.start();
                    node.getNodeContext().setResponseTime(LocalDateTime.now());
                }
            })
            .doOnComplete(this::handleComplete)
            .concatWith(Flux.defer(this::endDataList))
            .onErrorResume(e -> Flux.error(errorConvert(e)))
            .subscribeOn(Schedulers.boundedElastic())
            .doFinally(signal ->  {
                stopWatch.stop();
                executeAfter(node.getNodeContext());
                node.getNodeContext().setEndTime(LocalDateTime.now());
                returnWebClient(webClient);
                // 如果在流程链中，则不保存会话历史，由流程统一保存
                //sendHistoryChat(RoleEnum.assistant, contextVal, node.getNodeContext());
                CommonLog.info("<------【{}({})】请求完成，耗时：{}ms", getNodeName(), getNodeId(), stopWatch.getLastTaskTimeMillis());
            });

    }

    @Override
    public Flux<ResultData<R>> processData(E context, FlowAttribute flowAttribute) {
        try {
            if (flowAttribute != null) {
                setFlow(flowAttribute);
            }
            return Flux.from(process(context))
                    .mapNotNull(data -> convertResultData(ResultDataStatusEnum.STREAMING, data, null))
                    .startWith(convertResultData(ResultDataStatusEnum.START, null, null))
                    .concatWith(Flux.defer(() -> Flux.just(convertResultData(ResultDataStatusEnum.END, null, null))))
                    .onErrorResume(e -> handleError(e, getFlowContext()))
                    .doFinally(signal -> getFlowContext().add(getNode().getNodeContext()));
        } catch (Exception e) {
            throw new CustomException(getNodeName() + "节点执行异常, 原因：" + e.getMessage(), e);
        }
    }

    @Override
    public ResultData<R> syncProcessData(E context, FlowAttribute flowAttribute) {
        if (flowAttribute != null) {
            setFlow(flowAttribute);
        }
        return convertResultData(ResultDataStatusEnum.SUCCESS, syncProcess(context), null);
    }

    private BodyInserter<?, ? super ClientHttpRequest> bodyInserter(HttpHeaders headers, StopWatch stopWatch) {
        Object object = Assert.notNull(setRequestBody(),  getNodeName() + "请求体不能为空");
        CommonLog.info("【{}({})】请求地址：{}", getNodeName(), getNodeId(), setRequestUrl());
        CommonLog.info("【{}({})】请求头：{}", getNodeName(), getNodeId(), headers.toSingleValueMap());
        CommonLog.info("【{}({})】请求体：{}", getNodeName(), getNodeId(),JsonUtil.toStr(object));
        try {
            if (isMultiFormData(headers)) {
                if (object instanceof MultiFileData multiFileData) {
                    return BodyInserters.fromMultipartData(CommonUtil.convertMultiDataToResourceData(multiFileData));
                } else {
                    throw new CustomException(getNodeName() + "请求体类型错误，文件数据应为MultiFileData");
                }
            } else {
                if (isFormData(headers)) {
                    if (object instanceof MultiValueMap<?, ?> multiValueMap) {
                        @SuppressWarnings("unchecked")
                        MultiValueMap<String, String> strMultiValueMap = (MultiValueMap<String, String>) multiValueMap;
                        return BodyInserters.fromFormData(strMultiValueMap);
                    } else {
                        throw new CustomException(getNodeName() + "请求体类型错误，表单数据应为MultiValueMap<String, String>");
                    }
                }
                return BodyInserters.fromValue(object);
            }
        } finally {
            stopWatch.stop();
            CommonLog.info(" <------【{}({})】请求参数组装完成，耗时：{}ms", getNodeName(), getNodeId(), stopWatch.getLastTaskTimeMillis());
            stopWatch.start();
            CommonLog.info("【{}({})】开始发起请求 ------>", getNodeName(), getNodeId());
        }

    }

    private boolean isFormData(HttpHeaders headers) {
        if (headers.getContentType() != null) {
            return headers.getContentType().includes(MediaType.APPLICATION_FORM_URLENCODED);
        }
        return false;
    }

    private boolean isMultiFormData(HttpHeaders headers) {
        if (headers.getContentType() != null) {
            headers.getContentType().includes(MediaType.MULTIPART_FORM_DATA);
        }
        return false;
    }

    private R convertToResponseData(ServerSentEvent<String> ssEvent, NodeAttribute<E, R> node) {
        R rawData = resultConvert(ssEvent) ;
        // 节点数据内部额外操作
        executing(rawData);
        // 数据节点外部额外操作
        if (node.getDataDisposeFun() != null) {
            rawData = node.getDataDisposeFun().apply(rawData);
        }
        // 收集数据
        collectNodeData(rawData, getNode().getNodeContext());
        // 不展示思考 或 不展示内容
        if ((node.isHideThinking() && isThinking(rawData)) || (node.isHideContent() && isContent(rawData))) {
            return null;
        }
        return rawData;
    }

    // 完成时处理方法
    @SuppressWarnings("unchecked")
    private void handleComplete() {
        getFlowContext().add(getNode().getNodeContext());
        if (getNode().getProcessDataFun() != null) {
            getNode().getProcessDataFun().accept(getNodeId(), nodeContextToData(getNode().getNodeContext()), getFlowContext());
        }
    }

    @SuppressWarnings("unchecked")
    private R[] startDataList() {
        // 添加beforeFunList处理结果
        if (CollectionUtil.isNotEmpty(getNode().getBeforeFunList())) {
            return (R[]) getNode().getBeforeFunList().stream().map(data -> data.addFun().apply(getNode().getNodeContext(), getNode().getRequestContext())).toArray();
        }
        return (R[]) new Object[0];
    }

    private Flux<R> endDataList() {
        List<R> dataList = new ArrayList<>(getNode().getAfterFunList().size() + 2);
        // 添加afterFunList处理结果
        if (CollectionUtil.isNotEmpty(getNode().getAfterFunList())) {
            getNode().getAfterFunList().forEach(data -> dataList.add(data.addFun().apply(getNode().getNodeContext(), getNode().getRequestContext())));
        }
        // 收集结果检查
        if (StrUtil.isBlank(getNode().getNodeContext().getFullThinking()) && StrUtil.isBlank(getNode().getNodeContext().getFullContent())) {
            //兼容非推理模型，只记录日志信息，返回空到前端
            CommonLog.error(getNodeName() + "AI接口返回数据为空！没有收集到思考过程和内容！");
        }
        return Flux.fromIterable(dataList);
    }

    // 错误处理方法
    private Flux<ResultData<R>> handleError(Throwable e, FlowContext flowContext) {
        CommonLog.error("节点{}执行失败:{}",
            OptionalUtil.ofBlankable(getNodeName()).orElse(getNodeDesc()),
            Throwables.getRootCause(e).getMessage()
        );
        flowContext.stop();
        return Flux.just(new ResultData<>(ResultDataStatusEnum.ERROR, e.getCause()));
    }

    private ResultData<R> convertResultData(ResultDataStatusEnum statusEnum, R data, Throwable e) {
        ResultData<R> rResultData = new ResultData<>(data)
            .setParentNodeId(getParentNodeId())
            .setNodeId(getNodeId())
            .setNodeName(getNodeName())
            .setNodeDesc(getNodeDesc())
            .setStatus(statusEnum)
            .setError(e);
        if (getRequestContext() instanceof RequestContext requestContext) {
            rResultData.setRequestId(requestContext.getRequestId());
        }
        return rResultData;
    }

    private void sendHistoryChat(RoleEnum roleEnum, E contextVal, NodeContext nodeContext) {
        if (!getFlow().isInFlowChain()) {
            if (contextVal instanceof RequestContext context && StrUtil.isNotBlank(context.getSessionId())) {
                if (RoleEnum.user.equals(roleEnum)) {
                    JsonMap jsonMap = new JsonMap(context);
                    jsonMap.remove(AIRequestContext::getSystemPromote);
                    nodeContext.collectContent(JsonUtil.toStr(jsonMap));
                }
                eventProducer.publishEvent(context.getSessionId(), roleEnum, nodeContext);
            }
        }
    }

}
