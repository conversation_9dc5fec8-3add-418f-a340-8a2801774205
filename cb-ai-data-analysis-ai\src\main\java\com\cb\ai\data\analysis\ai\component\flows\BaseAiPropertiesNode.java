package com.cb.ai.data.analysis.ai.component.flows;

import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.common.properties.BasicProperties;
import com.cb.ai.data.analysis.ai.component.choreography.engine.BaseAiCallNode;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.cb.ai.data.analysis.ai.utils.RefUtil;
import com.xong.boot.common.utils.RedisUtils;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 10:04
 * @Copyright (c) 2025
 * @Description AI基础属性配置节点
 */
public abstract class BaseAiPropertiesNode<E, AiProp extends BasicProperties, R> extends BaseAiCallNode<E, R> {

    protected final AiProp aiProp;

    protected final AiConfigService aiConfigService;

    protected final RedisUtils redisUtils;

    public BaseAiPropertiesNode() {
        Class<AiProp> type = RefUtil.getGenericType(this.getClass(), 1, BaseAiPropertiesNode.class::equals);
        this.aiProp = SpringUtil.getBean(type);
        this.aiConfigService = SpringUtil.getBean(AiConfigService.class);
        this.redisUtils = SpringUtil.getBean(RedisUtils.class);
    }
}
