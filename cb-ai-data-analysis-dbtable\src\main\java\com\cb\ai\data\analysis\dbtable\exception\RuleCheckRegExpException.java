package com.cb.ai.data.analysis.dbtable.exception;

/**
 * 规矩校验正则异常
 * <AUTHOR>
 */
public class RuleCheckRegExpException extends RuleCheckException {
    public RuleCheckRegExpException(String message) {
        super(message);
    }

    public RuleCheckRegExpException(String message, Throwable cause) {
        super(message, cause);
    }

    public RuleCheckRegExpException(Throwable cause) {
        super(cause);
    }

    public RuleCheckRegExpException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
