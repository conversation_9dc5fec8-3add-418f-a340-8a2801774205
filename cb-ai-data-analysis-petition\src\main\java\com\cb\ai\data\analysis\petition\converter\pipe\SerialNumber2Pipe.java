package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 二级标题管道
 * <AUTHOR>
 */
public class SerialNumber2Pipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.contains("^[(（][一二三四五六七八九十]+[）)]", text);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatParagraphInd(paragraph, config);
        // 2级标题
        List<XWPFRun> runs = paragraph.getRuns();
        // 修改序号后标点符号
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String runText = run.text();
            if (StringUtils.isBlank(runText)) {
                continue;
            }
            runText = runText.replace('(', '（');
            runText = runText.replace(')', '）');
            int next = i + 1; // 取出下一个
            if (next < runs.size() && ReUtil.contains("[）)]$", runText)) {
                XWPFRun nRun = runs.get(next);
                String nRunText = nRun.text();
                if (ReUtil.contains("^[,.．，。、]", nRunText)) {
                    String replaced = ReUtil.replaceAll(nRunText, "^([,.．，。、])(.*)", "$2");
                    nRun.setText(replaced, 0);
                }
                run.setText(runText, 0);
                break;
            }
            if (ReUtil.contains("[）)][,.．，。、]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "([）)])[,.．，。、]", "$1");
                run.setText(replaced, 0);
                break;
            }
        }

        // 修改序号字体样式
        String paragraphText = paragraph.getText().trim();
        boolean isFormatTop = false;
        if (paragraphText.indexOf("。") > 52 || paragraphText.indexOf("：") > 52) {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("）");
                if (index == -1) {
                    FormatTools.formatSerialNumber2(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber2(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber2(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        } else {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("：");
                if (index == -1) {
                    index = runText.indexOf("。");
                }
                if (index == -1) {
                    FormatTools.formatSerialNumber2(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber2(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber2(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        }
    }
}
