package com.cb.ai.data.analysis.petition.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.util.List;

@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class SsRepeatResultGroup extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;

    //任务id
    private String taskId;

    //信访目的（一级分类）
    private String petitionPurposeCategory;
    //信访目的（二级分类）
    private String petitionPurpose;
    //所属领域（一级分类）
    private String petitionDomainCategory;
    //所属领域（二级分类）
    private String petitionDomain;
    //所属省份
    private String petitionProvince;
    //所属城市
    private String petitionCity;
    //摘要
    private String brief;

    @TableField(exist = false)
    @Transient
    private List<SsPetitionAnalyzedEntity> items;

}
