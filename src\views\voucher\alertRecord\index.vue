<template>
  <x-page-wrapper hiddenTitle class="voucher-alert-record">
    <x-table-search :model="searchFormData" :tableRef="tableRef" :col="4">
      <a-form-item label="凭证名称" name="voucherName">
        <a-input v-model:value="searchFormData.voucherName" :maxlength="100" allow-clear placeholder="请输入凭证名称" />
      </a-form-item>
      <a-form-item label="任务名称" name="taskName">
        <a-input v-model:value="searchFormData.taskName" :maxlength="100" allow-clear placeholder="请输入任务名称" />
      </a-form-item>
      <a-form-item label="告警内容" name="content">
        <a-input v-model:value="searchFormData.content" :maxlength="200" allow-clear placeholder="请输入告警内容关键词" />
      </a-form-item>
    </x-table-search>
    <x-table ref="tableRef" :columns="columns" :loadData="loadData" :rowSelection="false" title="凭证告警记录" row-key="id">
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'content'">
          <div class="content-cell">
            <a-tooltip :title="text" placement="topLeft">
              <div class="content-text">{{ text }}</div>
            </a-tooltip>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'voucherName'">
          <a-button type="link" size="small" @click="onClickViewVoucher(record)">
            {{ text }}
          </a-button>
        </template>
        <template v-else-if="column.dataIndex === 'taskName'">
          <a-button v-if="text" type="link" size="small" @click="onClickViewTask(record)">
            {{ text }}
          </a-button>
          <span v-else class="text-muted">单文件分析</span>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ $date.formatDateTime(text) }}
        </template>
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button type="link" size="small" @click="onClickViewDetail(record)">
              查看详情
            </a-button>
          </a-space>
        </template>
      </template>
    </x-table>

    <!-- 详情弹窗 -->
    <a-modal v-model:open="detailVisible" title="告警记录详情" width="70%" :footer="null">
      <div v-if="detailData" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="凭证名称">
            {{ detailData.voucherName }}
          </a-descriptions-item>
          <a-descriptions-item label="任务名称">
            {{ detailData.taskName || '单文件分析' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建人">
            {{ detailData.createBy }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ $date.formatDateTime(detailData.createTime) }}
          </a-descriptions-item>
        </a-descriptions>

        <div class="alert-content-section">
          <h4>告警内容：</h4>
          <div class="alert-content">
            {{ detailData.content }}
          </div>
        </div>
      </div>
    </a-modal>
  </x-page-wrapper>
</template>

<script setup lang="ts" name="VoucherAlertRecord">
import { computed, getCurrentInstance, onMounted, ref, type ComponentCustomProperties } from 'vue'
import { useRoute } from 'vue-router'
import { alertRecord } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const route = useRoute()
const tableRef = ref()
const detailVisible = ref(false)
const detailData = ref(null)

const searchFormData = ref({
  voucherName: '',
  taskName: '',
  content: '',
  taskId: '',
  voucherId: ''
})

// 初始化时根据路由参数设置筛选条件
onMounted(() => {
  if (route.query.taskId) {
    searchFormData.value.taskId = route.query.taskId as string
  }
  if (route.query.taskName) {
    searchFormData.value.taskName = route.query.taskName as string
  }
  if (route.query.voucherId) {
    searchFormData.value.voucherId = route.query.voucherId as string
  }
  if (route.query.voucherName) {
    searchFormData.value.voucherName = route.query.voucherName as string
  }
})

const columns = computed(() => {
  const columnItems = [
    {
      dataIndex: 'voucherName',
      title: '凭证名称',
      width: 200,
      ellipsis: true
    },
    {
      dataIndex: 'taskName',
      title: '任务名称',
      width: 150,
      ellipsis: true
    },
    {
      dataIndex: 'content',
      title: '告警内容',
      width: 300,
      ellipsis: true
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      width: 100,
      align: 'center'
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
      align: 'center'
    }
  ]

  columnItems.push({
    key: 'actions',
    title: '操作',
    width: 100,
    align: 'center'
  })

  return columnItems
})

/**
 * 加载数据
 */
async function loadData(params: Record<string, any>) {
  const res = await alertRecord.page(params)
  return res
}

/**
 * 查看凭证详情
 */
function onClickViewVoucher(record: any) {
  _this.$message.info('查看凭证功能待实现')
}

/**
 * 查看任务详情
 */
function onClickViewTask(record: any) {
  _this.$message.info('查看任务功能待实现')
}

/**
 * 查看告警详情
 */
async function onClickViewDetail(record: any) {
  try {
    const { data } = await alertRecord.detail(record.id)
    detailData.value = data
    detailVisible.value = true
  } catch (error) {
    _this.$message.error('获取详情失败')
  }
}
</script>

<style scoped lang="less">
.voucher-alert-record {
  .content-cell {
    .content-text {
      max-width: 280px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .text-muted {
    color: #999;
    font-style: italic;
  }

  .detail-content {
    .alert-content-section {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #333;
      }

      .alert-content {
        background: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #ff4d4f;
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
      }
    }
  }
}
</style>
