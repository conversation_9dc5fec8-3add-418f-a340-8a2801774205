package com.cb.ai.data.analysis.dbtable.model.clickhouse;

import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import lombok.Data;

/**
 * clickhouse字段
 * <AUTHOR>
 */
@Data
public class DBColumn {
    /**
     * 字段名称
     */
    private String name;
    /**
     * 字段注释
     */
    private String comment;
    /**
     * 字段类型
     */
    private CHColumnType type;
    /**
     * 字段默认值
     */
    private String defVal;
    /**
     * 字段精度
     */
    private Integer precise;
    /**
     * 字段规模
     */
    private Integer scale;
    /**
     * 是否主键
     */
    private Boolean isPrimary;
}
