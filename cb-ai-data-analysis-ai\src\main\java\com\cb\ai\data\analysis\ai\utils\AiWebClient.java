package com.cb.ai.data.analysis.ai.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.ai.exception.AiApiException;
import com.cb.ai.data.analysis.ai.model.AIContext;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.model.body.AiLlmBody;
import com.cb.ai.data.analysis.ai.model.options.AnalysisMaterialOptions;
import com.cb.ai.data.analysis.ai.model.options.ResearchOptions;
import com.cb.ai.data.analysis.ai.properties.AiProperties;
import com.cb.ai.data.analysis.ai.service.AiConfigService;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

/**
 * AI客户端
 * <AUTHOR>
 */
@Component
public class AiWebClient {
    private final AiProperties aiProperties;
    private final AiConfigService aiConfigService;
    private final WebClient webClient;

    public AiWebClient(AiProperties aiProperties, AiConfigService aiConfigService) {
        this.aiProperties = aiProperties;
        this.aiConfigService = aiConfigService;
        this.webClient = WebClient.builder()
                .baseUrl(aiProperties.getBaseUrl())
                .defaultHeader(HttpHeaders.AUTHORIZATION, aiProperties.getAppKey())
                .build();
    }

    public WebClient getWebClient() {
        return webClient;
    }

    /**
     * 获取PY AI平台用户配置
     */
    public AiConfig getConfig() {
        byte[] bodyBytes = HttpUtils.createGet(aiProperties.getBaseUrl() + "/cb-ai/chat/params")
                .header(HttpHeaders.AUTHORIZATION, aiProperties.getAppKey())
                .execute()
                .bodyBytes();
        try {
            return JSON.parseObject(bodyBytes, AiConfig.class);
        } catch (Exception e) {
            throw new XServerException(e.getMessage() + new String(bodyBytes));
        }
    }

    /**
     * LLM问答请求
     * @param llmBody 请求体
     */
    public Flux<String> requestQuesLLM(AiLlmBody llmBody) {
        Authentication authentication = SecurityUtils.getAuthentication();
        return webClient.post()
                .uri("/cb-ai/chat/basic")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(mergeLlmBodyConfig(llmBody))
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(content -> {
                    if (SecurityUtils.getAuthentication() == null) {
                        // 注入用户信息，不然后面无法获取用户信息
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    }
                    JSONObject object = JSON.parseObject(String.valueOf(content));
                    if (object.containsKey("error")) {
                        throw new AiApiException(object.getString("error"));
                    }
                });
    }

    /**
     * LLM问答请求同步
     * @param llmBody 请求体
     */
    public JSONObject requestQuesLLMSync(AiLlmBody llmBody) {
        byte[] bodyBytes = HttpUtils.createPost(aiProperties.getBaseUrl() + "/cb-ai/chat/sync")
                .header(HttpHeaders.AUTHORIZATION, aiProperties.getAppKey())
                .body(JSON.toJSONBytes(mergeLlmBodyConfig(llmBody)))
                .execute()
                .bodyBytes();
        try {
            return JSON.parseObject(bodyBytes);
        } catch (Exception e) {
            throw new XServerException(e.getMessage() + new String(bodyBytes));
        }
    }

    /**
     * 知识库问答请求
     * @param context 请求上下文
     */
    public Flux<String> requestQuesKnowledge(AIContext context) {
//        QuesKnowledgeOptions options = context.getOptions(QuesKnowledgeOptions.class);
//        JSONObject body = new JSONObject();
//        body.put("sessionId", context.getSessionId());
//        body.put("promote", context.getPromote());
//        body.put("systemPromote", context.getSystemPromote());
//        if (context.getKnowledge() != null && context.getKnowledge().size() > 0) {
//            body.put("baseIds", context.getKnowledge());
//        }
//        if (options != null) {
//            body.put("similarityHolds", options.getSimilarityHolds());
//            body.put("topK", options.getTopK());
//            body.put("fileIds", options.getFileIds());
////            body.put("temperature", aiConfig.getTemperature());
////            body.put("top_p", aiConfig.getTopP());
////            body.put("max_tokens", aiConfig.getMaxTokens());
////            body.put("frequency_penalty", aiConfig.getFrequencyPenalty());
////            body.put("presence_penalty", aiConfig.getPresencePenalty());
//        }
//        return webClient.post()
//                .uri("/cb-ai/chat/knowledge")
//                .accept(MediaType.TEXT_EVENT_STREAM)
//                .bodyValue(body)
//                .retrieve()
//                .bodyToFlux(String.class);
        return null;
    }

    /**
     * 材料分析
     */
    public Flux<String> requestAnalysisMaterial(AIContext context) {
        AnalysisMaterialOptions options = context.getOptions(AnalysisMaterialOptions.class);
        JSONObject body = new JSONObject();
        body.put("sessionId", context.getSessionId());
        body.put("promote", context.getPromote());
        body.put("systemPromote", context.getSystemPromote());
        if (options != null) {
            body.put("fileName", options.getFileName());
            body.put("fileUrl", options.getFileUrl());
        }
        return webClient.post()
                .uri("/cb-ai/tool/file/analysis/upload")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class);
    }

    /**
     * 深入研究接口
     * @param context 请求上下文
     */
    private Flux<String> requestAiResearch(AIContext context) {
        ResearchOptions options = context.getOptions(ResearchOptions.class);
        JSONObject body = new JSONObject();
        body.put("thread_id", context.getSessionId());
        body.put("messages", context.getPromote());
        if (options != null) {
            body.put("debug", options.getDebug());
            body.put("max_plan_iterations", options.getMaxPlanIterations());
            body.put("max_step_num", options.getMaxStepNum());
            body.put("interrupt_feedback", options.getInterruptFeedback());
            body.put("mcp_settings", options.getMcpSettings());
            body.put("auto_accepted_plan", options.getAutoAcceptedPlan());
        }
        return webClient.post()
                .uri("/api/chat/stream")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class);
    }

    /**
     * 合并LLM请求参数
     * @param llmBody LLM请求体
     */
    private AiLlmBody mergeLlmBodyConfig(AiLlmBody llmBody) {
        AiConfig aiConfig = aiConfigService.getAiConfig();
        // 判断配置是否存在不存在就使用用户个人配置
        if (aiConfig != null) {
//            if (StringUtils.isBlank(llmBody.getModel())) {
//                llmBody.setModel(aiConfig.getModel());
//            }
            if (llmBody.getTemperature() == null) {
                llmBody.setTemperature(aiConfig.getTemperature());
            }
            if (llmBody.getTop_p() == null) {
                llmBody.setTop_p(aiConfig.getTopP());
            }
            if (llmBody.getMax_tokens() == null) {
                llmBody.setMax_tokens(aiConfig.getMaxTokens());
            }
            if (llmBody.getFrequency_penalty() == null) {
                llmBody.setFrequency_penalty(aiConfig.getFrequencyPenalty());
            }
            if (llmBody.getPresence_penalty() == null) {
                llmBody.setPresence_penalty(aiConfig.getPresencePenalty());
            }
        }
        return llmBody;
    }
}
