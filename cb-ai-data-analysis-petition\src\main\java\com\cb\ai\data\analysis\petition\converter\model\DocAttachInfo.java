package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文附件信息
// * <AUTHOR>
// */
//@Data
//public class DocAttachInfo {
//    /**
//     * 附件说明
//     */
//    private DocAttachExplain attachExplain;
//    /**
//     * 附件头
//     */
//    private List<DocAttachHead> attachHeads = new ArrayList<>();
//    /**
//     * 附件内容
//     */
//    private List<DocAttachTitle> attachTitles = new ArrayList<>();
//}
