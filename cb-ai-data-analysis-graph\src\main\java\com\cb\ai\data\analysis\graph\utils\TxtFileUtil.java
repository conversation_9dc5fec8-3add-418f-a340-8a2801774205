package com.cb.ai.data.analysis.graph.utils;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class TxtFileUtil {
    // 新增文本分块方法
    public List<String> splitTextIntoChunks(String text, int maxChunkSize) {
        List<String> chunks = new ArrayList<>();
        int length = text.length();
        int start = 0;

        while (start < length) {
            int end = Math.min(start + maxChunkSize, length);

            if (end == length) {
                chunks.add(text.substring(start));
                break;
            }

            // 向前查找最佳分割点
            int splitPoint = findSplitPosition(text, start, end);
            chunks.add(text.substring(start, splitPoint));
            start = splitPoint;
        }
        return chunks;
    }

    // 查找分割点逻辑
    private int findSplitPosition(String text, int start, int end) {
        // 优先查找句子边界
        for (int i = end; i > start; i--) {
            char c = text.charAt(i);
            if (isSentenceEnd(c)) {
                return adjustSplitPosition(text, i + 1); // 在句子结束符后分割
            }
        }

        // 次选段落边界
        for (int i = end; i > start; i--) {
            if (text.charAt(i) == '\n' && isParagraphEnd(text, i)) {
                return adjustSplitPosition(text, i + 1);
            }
        }

        // 最后使用强制分割（保留单词完整）
        for (int i = end; i > start; i--) {
            if (Character.isWhitespace(text.charAt(i))) {
                return i + 1;
            }
        }

        return end; // 无合适分割点时强制分割
    }

    // 判断句子结束符
    private boolean isSentenceEnd(char c) {
        return c == '。' || c == '！' || c == '？' || c == '.' || c == '!' || c == '?';
    }

    // 判断段落结束（连续换行）
    private boolean isParagraphEnd(String text, int position) {
        if (position > 0 && text.charAt(position - 1) == '\n') {
            return true; // 连续两个换行符
        }
        return position + 1 < text.length() && text.charAt(position + 1) == '\n';
    }

    // 调整分割位置（跳过后续空白）
    private int adjustSplitPosition(String text, int position) {
        while (position < text.length() &&
                (text.charAt(position) == '\n' || text.charAt(position) == '\r')) {
            position++;
        }
        return position;
    }
}
