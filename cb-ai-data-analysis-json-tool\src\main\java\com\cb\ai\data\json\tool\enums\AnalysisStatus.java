package com.cb.ai.data.json.tool.enums;

public enum AnalysisStatus {
    // 0:未开始 1:进行中 2:已完成 3:失败
    UNSTART("0", "未开始"),
    PROCESSING("1", "进行中"),
    COMPLETE("2", "已完成"),
    FAIL("3", "失败");

    private String status;
    private String desc;
    private AnalysisStatus(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }
    public String getStatus() {
        return status;
    }
    public String getDesc() {
        return desc;
    }
}
