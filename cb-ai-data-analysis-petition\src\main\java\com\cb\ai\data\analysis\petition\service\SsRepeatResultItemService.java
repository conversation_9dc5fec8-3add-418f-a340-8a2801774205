package com.cb.ai.data.analysis.petition.service;

import com.cb.ai.data.analysis.petition.domain.SsRepeatResultItem;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.xong.boot.common.service.BaseService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SsRepeatResultItemService extends BaseService<SsRepeatResultItem> {

    Map<String, List<SsPetitionAnalyzedEntity>> getGroupIdAnalyzedMap(Collection<String> groupIds);

}
