package com.cb.ai.data.analysis.petition.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 抽象的流式批处理ResultHandler
 * 子类只需要实现具体的批处理业务逻辑
 * @param <T> 数据类型
 * <AUTHOR> Assistant
 */
@Slf4j
public abstract class AbstractStreamBatchResultHandler<T> implements ResultHandler<T> {
    
    private final int batchSize;
    private final int maxConcurrentBatches;
    private final TaskExecutor taskExecutor;
    
    // 当前批次缓冲区
    private final List<T> currentBatch;
    // 信号量，控制并发批次数量
    private final Semaphore concurrencyControl;
    // 批处理任务的Future列表
    private final List<CompletableFuture<Void>> batchFutures = new CopyOnWriteArrayList<>();
    
    // 计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicInteger batchCounter = new AtomicInteger(0);
    
    // 处理状态
    private volatile boolean isFinished = false;
    private volatile Exception lastException = null;
    
    /**
     * 构造函数
     * @param batchSize 批次大小，默认500
     * @param maxConcurrentBatches 最大并发批次数，默认3
     * @param taskExecutor 任务执行器
     */
    public AbstractStreamBatchResultHandler(int batchSize, int maxConcurrentBatches, TaskExecutor taskExecutor) {
        this.batchSize = batchSize;
        this.maxConcurrentBatches = maxConcurrentBatches;
        this.taskExecutor = taskExecutor;
        this.currentBatch = new ArrayList<>(batchSize);
        this.concurrencyControl = new Semaphore(maxConcurrentBatches);
        
        log.info("AbstractStreamBatchResultHandler initialized: batchSize={}, maxConcurrentBatches={}", 
                batchSize, maxConcurrentBatches);
    }
    
    /**
     * 使用默认配置的构造函数
     * @param taskExecutor 任务执行器
     */
    public AbstractStreamBatchResultHandler(TaskExecutor taskExecutor) {
        this(500, 3, taskExecutor);
    }
    
    /**
     * 处理每一条查询结果
     */
    @Override
    public void handleResult(ResultContext<? extends T> resultContext) {
        try {
            T entity = resultContext.getResultObject();
            if (entity == null) {
                return;
            }
            
            // 添加到当前批次
            synchronized (currentBatch) {
                currentBatch.add(entity);
                
                // 如果达到批次大小，提交批次
                if (currentBatch.size() >= batchSize) {
                    submitCurrentBatch();
                }
            }
            
            long count = processedCount.incrementAndGet();
            
            // 每处理1000条记录打印一次日志
            if (count % 1000 == 0) {
                log.info("Processed {} records, active batches: {}", count, 
                        maxConcurrentBatches - concurrencyControl.availablePermits());
            }
            
        } catch (Exception e) {
            lastException = e;
            log.error("Error handling result at record #{}", processedCount.get() + 1, e);
        }
    }
    
    /**
     * 提交当前批次
     */
    private void submitCurrentBatch() {
        if (currentBatch.isEmpty()) {
            return;
        }
        
        // 创建批次副本
        List<T> batchToSubmit = new ArrayList<>(currentBatch);
        currentBatch.clear();
        
        // 异步处理批次
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                // 获取信号量，控制并发数
                concurrencyControl.acquire();
                
                int currentBatchNumber = batchCounter.incrementAndGet();
                long startTime = System.currentTimeMillis();
                
                // 调用子类实现的批处理逻辑
                processBatch(batchToSubmit, currentBatchNumber);
                
                long endTime = System.currentTimeMillis();
                log.info("Batch #{} processed successfully, size: {}, time: {}ms", 
                        currentBatchNumber, batchToSubmit.size(), endTime - startTime);
                
            } catch (Exception e) {
                lastException = e;
                log.error("Error processing batch #{}", batchCounter.get(), e);
                // 可以选择是否抛出异常来停止整个处理流程
                onBatchError(batchToSubmit, batchCounter.get(), e);
            } finally {
                // 释放信号量
                concurrencyControl.release();
            }
        }, taskExecutor);
        
        batchFutures.add(future);
    }
    
    /**
     * 完成处理，提交剩余数据并等待所有批次完成
     * @param timeoutSeconds 等待超时时间（秒），默认300秒
     * @return 是否成功完成
     */
    public boolean finish(int timeoutSeconds) {
        if (isFinished) {
            return true;
        }
        
        log.info("Finishing stream processing...");
        
        try {
            // 提交剩余的数据
            synchronized (currentBatch) {
                if (!currentBatch.isEmpty()) {
                    submitCurrentBatch();
                    log.info("Submitted final batch with {} records", currentBatch.size());
                }
            }
            
            // 等待所有批处理任务完成
            CompletableFuture<Void> allBatches = CompletableFuture.allOf(
                    batchFutures.toArray(new CompletableFuture[0]));
            
            allBatches.get(timeoutSeconds, TimeUnit.SECONDS);
            
            isFinished = true;
            
            log.info("Stream processing finished successfully. Total processed: {}, Total batches: {}", 
                    processedCount.get(), batchCounter.get());
            
            // 调用完成回调
            onAllBatchesCompleted();
            
            return true;
            
        } catch (TimeoutException e) {
            log.warn("Finish timeout after {} seconds", timeoutSeconds);
            return false;
        } catch (Exception e) {
            lastException = e;
            log.error("Error finishing stream processing", e);
            return false;
        }
    }
    
    /**
     * 使用默认超时时间完成处理
     */
    public boolean finish() {
        return finish(300);
    }
    
    /**
     * 获取处理统计信息
     */
    public ProcessingStats getStats() {
        return new ProcessingStats(
                processedCount.get(),
                batchCounter.get(),
                currentBatch.size(),
                concurrencyControl.availablePermits(),
                isFinished,
                lastException
        );
    }
    
    // ========== 抽象方法，由子类实现 ==========
    
    /**
     * 处理一个批次的数据
     * 子类需要实现具体的批处理业务逻辑
     * @param batch 批次数据
     * @param batchNumber 批次号（从1开始）
     * @throws Exception 处理异常
     */
    protected abstract void processBatch(List<T> batch, int batchNumber) throws Exception;
    
    // ========== 可选的回调方法，子类可以重写 ==========
    
    /**
     * 批处理出错时的回调
     * 子类可以重写此方法来处理批处理错误
     * @param batch 出错的批次数据
     * @param batchNumber 批次号
     * @param exception 异常
     */
    protected void onBatchError(List<T> batch, int batchNumber, Exception exception) {
        // 默认实现：记录错误日志
        log.error("Batch #{} processing failed with {} records", batchNumber, batch.size(), exception);
    }
    
    /**
     * 所有批次完成时的回调
     * 子类可以重写此方法来执行清理或汇总操作
     */
    protected void onAllBatchesCompleted() {
        // 默认实现：空操作
        log.info("All batches completed successfully");
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 检查是否有异常
     */
    public boolean hasException() {
        return lastException != null;
    }
    
    /**
     * 获取最后的异常
     */
    public Exception getLastException() {
        return lastException;
    }
    
    /**
     * 处理统计信息
     */
    public static class ProcessingStats {
        private final long processedCount;
        private final int totalBatches;
        private final int currentBatchSize;
        private final int availablePermits;
        private final boolean isFinished;
        private final Exception lastException;
        
        public ProcessingStats(long processedCount, int totalBatches, int currentBatchSize, 
                             int availablePermits, boolean isFinished, Exception lastException) {
            this.processedCount = processedCount;
            this.totalBatches = totalBatches;
            this.currentBatchSize = currentBatchSize;
            this.availablePermits = availablePermits;
            this.isFinished = isFinished;
            this.lastException = lastException;
        }
        
        // Getters
        public long getProcessedCount() { return processedCount; }
        public int getTotalBatches() { return totalBatches; }
        public int getCurrentBatchSize() { return currentBatchSize; }
        public int getAvailablePermits() { return availablePermits; }
        public boolean isFinished() { return isFinished; }
        public Exception getLastException() { return lastException; }
        public boolean hasException() { return lastException != null; }
        
        @Override
        public String toString() {
            return String.format("ProcessingStats{processedCount=%d, totalBatches=%d, currentBatchSize=%d, " +
                    "availablePermits=%d, isFinished=%s, hasException=%s}", 
                    processedCount, totalBatches, currentBatchSize, availablePermits, isFinished, hasException());
        }
    }
}
