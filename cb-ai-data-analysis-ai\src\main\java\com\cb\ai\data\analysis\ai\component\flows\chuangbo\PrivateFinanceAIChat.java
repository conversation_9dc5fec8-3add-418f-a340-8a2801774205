package com.cb.ai.data.analysis.ai.component.flows.chuangbo;

import cn.hutool.json.JSONArray;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.utils.JsonUtil;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import com.xong.boot.common.exception.CustomException;
import lombok.Data;
import org.springframework.http.codec.ServerSentEvent;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/7 15:52
 * @Copyright (c) 2025
 * @Description 大数据分析AI
 */
public class PrivateFinanceAIChat extends BasePrivateAiPropertiesNode {

    @Override
    public String getNodeName() {
        return "私有化-大数据分析-接口";
    }

    @Override
    public String setRequestUrl() {
        return MergeUtil.mergePath(aiProp.getBaseUrl(), aiProp.getFinance());
    }

    @Override
    public Object setRequestBody() {
        CommonAIRequestContext body = getRequestContext();
        return JsonMap.of("session_id", body.getSessionId()).putOnce("question", body.getPromote())
            .putOnce("basic_data_table", body.getExtendData().getOrDefault("basic_data_table", ""))
            .putOnce("history_messages", body.getExtendData().getOrDefault("history_messages", new JSONArray()));
    }

    @Override
    public PrivateAIBackData resultConvert(ServerSentEvent<String> ssEvent) {
        //CommonLog.info(getNodeName() + "调用私有化底座AI返回数据：{}", rawData);
        String sessionId = getRequestContext().getSessionId();
        String rawData = ssEvent.data();
        try {
            FinanceBackData bean = JsonUtil.toBean(rawData, FinanceBackData.class);
            return switch (bean.getEvent_type()) {
                case "progress" ->
                        new PrivateAIBackData(sessionId, bean.getRole(), bean.getMessage(), null, rawData);
                case "completed" ->
                        new PrivateAIBackData(sessionId, bean.getRole(), null, bean.getMessage(), bean.getData().putOpt(FinanceBackData::getStep_name, bean::getStep_name), rawData);
                default -> new PrivateAIBackData(sessionId, bean.getRole(), bean.getMessage(), null, JsonMap.of(FinanceBackData::getStep_name, bean::getStep_name), rawData);
            };
        } catch (Exception e) {
            throw new CustomException(getNodeName() + "数据解析失败，返回的原数据：（" + rawData +  "）", e);
        }
    }

    @Data
    public static class FinanceBackData {
        /**   **/
        private String role;
        /**   **/
        private String event_type;
        /**   **/
        private String step_name;
        /**   **/
        private String message;
        /**   **/
        private JsonMap data;

    }
}
