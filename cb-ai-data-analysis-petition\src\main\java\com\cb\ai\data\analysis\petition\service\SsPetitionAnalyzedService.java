package com.cb.ai.data.analysis.petition.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.xong.boot.common.service.BaseService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.ibatis.session.ResultHandler;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 * 信访件解析结果
 */
public interface SsPetitionAnalyzedService extends BaseService<SsPetitionAnalyzedEntity> {


    /***
     * 获取地图和工单统计图所需数据
     * @param startDate  开始时间
     * @param endDate 结束时间
     * @param types 工单类型
     * @param city  地市
     * @param district
     * @param size
     * @return
     */
    List<SsPetitionAnalyzedEntity> selectByDateRange(String startDate,
                                                     String endDate,
                                                     List<String> types,
                                                     String city,
                                                     String district,
                                                     Integer size);


    /***
     * 分页查询解析结果
     * @param queryPageVo
     * @return
     */
    Page<SsPetitionAnalyzedEntity> selectByPage(PetitionQueryPageQueryVo queryPageVo);

    /***
     * 导出解析结果exce
     * @param queryPageVo
     * @param response
     * @throws IOException
     */
    void export(PetitionQueryPageQueryVo queryPageVo, HttpServletResponse response) throws IOException;

    /***
     * 导出解析结果统计exce
     * @param response
     * @throws IOException
     */
    void exportStatics(HttpServletResponse response)throws IOException;

    /***
     * 获取信访分组信息
     * @param beginDate
     * @param endDate
     * @return
     */
    List<SsRepeatTaskVo.RepeatTaskAnalyzeGroup> getAnalyzeGroup(Date beginDate,Date endDate);

    /***
     * 根据分组信息，流式获取信访件数据
     * @param g
     * @param resultHandler
     */
    void streamPetitionByAnalyzeGroup(SsRepeatTaskVo.RepeatTaskAnalyzeGroup g,ResultHandler<SsPetitionAnalyzedEntity> resultHandler);

}
