package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 附注
 *
 * <AUTHOR>
 */
public class AnnotationPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.isMatch("^[　]*[(（].+[）)][　]*$", text)
                //需要排除二级标题
                && !ReUtil.contains("^[(（][一二三四五六七八九十]+[）)]", text)
                //排除四级标题
                && !ReUtil.contains("^[(（][1-9]+[0-9]*[）)]", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<XWPFRun> runs = paragraph.getRuns();
        String text = paragraph.getText().trim();
        if (paragraph.getAlignment() == ParagraphAlignment.CENTER) {
            FormatTools.formatParagraph(paragraph, config).setCTJcCenter();
            for (XWPFRun run : runs) {
                String runText = run.text();
                runText = runText.replace("(", "（").replace(")", "）");
                run.setText(runText, 0);
                FormatTools.formatSerialNumber2(run, config);
            }
            return;
        }
        FormatTools.formatParagraphInd(paragraph, config);
        boolean isMainTitle = false;
        if (pos > 0) {
            // 判断上一个段落是否是标题，如果是标题就设置为居中
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            XWPFParagraph preParagraph = paragraphs.get(pos - 1);
            String paragraphText = preParagraph.getText().trim();
            if (StringUtils.isNotBlank(paragraphText) && FormatTools.isMainTitle(paragraphText)) {
                isMainTitle = true;
                if (!text.contains("此件公开发布")) {
                    FormatTools.formatParagraph(paragraph, config).setCTJcCenter();
                }
            }
        }
        for (XWPFRun run : runs) {
            String runText = run.text();
            runText = runText.replace("(", "（").replace(")", "）");
            run.setText(runText, 0);
//            if (isMainTitle || (documentInfo.isPrintSend() && pos < 20)) {
//                FormatTools.formatSerialNumber2(run, config);
//            } else {
//                FormatTools.format(run, config);
//            }
            FormatTools.format(run, config);
        }
    }
}
