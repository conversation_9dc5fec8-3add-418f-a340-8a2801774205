package com.cb.ai.data.analysis.graph.enums;

import lombok.Getter;

/**
 * @Description:
 * @Author: ARPHS
 * @Date: 2025-04-29 18:05
 * @Version: 1.0
 **/
@Getter
public enum NodeLabelEnum {
    ENTERPRISE("企业", "企业"),
    PERSON("人员", "人员"),
    LEADER("干部", "干部"),
    ORG("组织", "组织"),
    ;

    private String label;
    private String desc;

    NodeLabelEnum(String label, String desc) {
        this.label = label;
        this.desc = desc;
    }

}
