package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ss_petition_origin
 * <AUTHOR>
@Data
@TableName("ss_petition_origin")
public class SsPetitionOriginEntity implements  Serializable {
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * -1:解析失败 0:解析中 1:解析完成
     */
    private Integer status;

    /**
     * 上传批次号
     */
    private Long uploadBatchNo;

    /**
     * 文件类型
     * @see com.cb.ai.data.analysis.petition.enums.FileContentTypeEnum
     */
    private Integer fileType;

    private static final long serialVersionUID = 1L;
}
