/*
package com.cb.ai.data.analysis.petition.controller;

import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.vo.PageResultVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.DeleteRawRequestVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionOriginQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionQueryPageQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsPetitionFileSearchRequestVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.SsPetitionVo;
import com.cb.ai.data.analysis.petition.service.SsPetitionService;
import com.xong.boot.common.api.Result;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

@RestController
@Log4j2
@RequestMapping(Constants.API_PETITION_ROOT_PATH+ "/petition")
public class SsPetitionController {

    @Autowired
    private SsPetitionService ssPetitionService;


    @GetMapping("/origin/page")
    public Result originPage(PetitionOriginQueryPageQueryVo queryPageVo) {
        */
/*PageResultVo  originPage=ssPetitionService.originPage(queryPageVo);
        return Result.successData(originPage);*//*

        return null;
    }

}
*/
