<template>
  <a-drawer v-model:open="visible" class="addReport" destroyOnClose width="95%" @close="close">
    <template #title>
      <a-steps
        :current="step"
        :items="[
          {
            title: '输入提示词'
          },
          {
            title: '生成大纲'
          },
          {
            title: '生成报告'
          }
        ]"
      ></a-steps>
    </template>
    <div v-if="thinkNode.length > 0" v-autoScroll class="think scrollBeauty">
      <div v-for="(item, index) in thinkNode" :key="index" class="thinkItem">
        <tabs>
          <template v-slot:header>
            <template v-if="item.status === 'streaming'">
              <a-spin></a-spin>
              思考中
            </template>
            <template v-else-if="item.status === 'end'">已结束</template>
          </template>
          <template v-slot:default>
            <!--            思考过程-->
            <div
              v-if="item.reasoning_content"
              class="thinking"
              v-html="item.reasoning_content"
            ></div>
          </template>
        </tabs>
        <!--            结果-->
        <div
          v-if="item.message"
          class="message"
          v-html="toMarkdown(item.message, item, index)"
        ></div>
        <div v-if="item.files.length > 0" class="fileMsg">
          <div class="fileTxt">文件列表：</div>
          <div
            v-for="(item, index) in onlyFiles(item.files)"
            :key="item.fileId"
            class="file"
            @click="openFile(item)"
          >
            <span class="fileIndex">{{ index + 1 }}</span
            >{{ item.fileName }}
          </div>
        </div>
      </div>
    </div>
    <div class="operate">
      <a-form
        ref="ruleFormRef"
        :disabled="loading"
        :model="formState"
        class="ruleForm"
        name="basic"
      >
        <a-form-item
          :rules="[{ required: true, message: '请输入提示词' }]"
          label="提示词"
          name="promote"
        >
          <a-textarea
            v-model:value="formState.promote"
            :rows="3"
            placeholder="请输入提示词"
            @keydown.enter="configByStep.saveEvent"
          />
        </a-form-item>
        <a-form-item
          :rules="[{ required: true, message: '请选择知识库' }]"
          label="知识库"
          name="baseIds"
        >
          <a-select
            v-model:value="formState.baseIds"
            :maxTagCount="5"
            allowClear
            class="knowledge"
            mode="multiple"
            placeholder="请选择知识库"
          >
            <a-select-option v-for="item in knowledgeStore.list" :key="item.id" :value="item.id"
              >{{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!--        <a-form-item-->
        <!--          :rules="[{ required: true, message: '请输入报告标题' }]"-->
        <!--          label="报告标题"-->
        <!--          name="title"-->
        <!--        >-->
        <!--          <a-input v-model:value="formState.title" placeholder="请输入报告标题" />-->
        <!--        </a-form-item>-->
      </a-form>
      <div class="bodyContent scrollBeauty">
        <reportView v-model:list="list" v-model:loading="loading" />
      </div>
    </div>
    <a-drawer
      v-model:open="drawer.visible"
      destroyOnClose
      title="参考文件预览"
      width="50%"
      @close="onDrawerClose"
    >
      <div class="preview-container">
        <iframe
          v-if="isPDF(drawer.fileName)"
          :key="drawer.key"
          :src="buildPdfUrl(drawer.fileUrl, drawer.page)"
          class="preview-embed"
        />
        <iframe
          v-else-if="isHtmlFile(drawer.fileName)"
          :key="drawer.key"
          ref="htmlIframe"
          :src="drawer.fileUrl"
          class="preview-embed"
          @load="onHtmlIframeLoad"
        />
        <img
          v-else-if="isImage(drawer.fileName)"
          :key="drawer.key"
          :src="drawer.fileUrl"
          class="preview-image"
        />
        <span v-else>暂不支持的文件类型</span>
      </div>
    </a-drawer>
    <template v-slot:footer>
      <a-button :disabled="!loading" danger ghost type="dashed" @click="stop">终止</a-button>
      <a-button
        v-if="step > 0"
        :disabled="loading"
        :loading="loading"
        type="text"
        @click="configByStep.backEvent"
      >
        {{ configByStep.back }}
      </a-button>
      <a-button :disabled="loading" :loading="loading" danger ghost type="dashed" @click="close"
        >关闭
      </a-button>
      <a-button
        :disabled="loading"
        :loading="loading"
        type="primary"
        @click="configByStep.saveEvent()"
      >
        {{ configByStep.save }}
      </a-button>
    </template>
  </a-drawer>
</template>
<script lang="ts" setup>
import { computed, nextTick, provide, reactive, ref, useTemplateRef } from 'vue'
import reportView from './reportView.vue'
import { extractTreeWithPaths, json2Dom } from '@/utils'
import { message } from 'ant-design-vue'
import { useKnowledgeStore, useUserStore } from '@/stores'
import { fetchStream } from '@/utils/http'
import { chatHistory, report } from '@/api/ai/index'
import { downloadFileByBlob } from '@/utils/fileUtil'
import markdownIt from '@/utils/markdown-it'
import tabs from '../../components/tabs.vue'
import errorLog from '@/utils/errorLog'

const knowledgeStore = useKnowledgeStore()
const userStore = useUserStore()
const ruleFormRef = useTemplateRef('ruleFormRef')
const loading = ref(false)
const step = ref(0)
const thinkNode = ref<Record<string, any>[]>([])
const visible = ref(false)
const list = ref<Record<string, any>[]>([])
const selected = ref<string[]>([]) //已选择的
let abortController = new AbortController()
const htmlIframe = useTemplateRef('htmlIframe')
const requestId = ref('')
const formState = reactive({
  promote: undefined,
  baseIds: [],
  title: ''
})
const drawer = reactive({
  visible: false,
  key: 0,
  fileName: '',
  fileUrl: '',
  page: undefined,
  traceProperties: undefined
})
const analyseTag = ref({})
const configByStep = computed(() => {
  return {
    backEvent: [undefined, reCreateSynopsis, reCreateContent][step.value],
    saveEvent: [createSynopsis, createContent, createFile][step.value],
    back: [undefined, '返回上一步', '返回上一步'][step.value],
    // title: ['输入提示词', '生成内容', '生成文件'][step.value],
    save: ['生成大纲', '生成内容', '生成文件'][step.value]
  }
})

function onlyFiles(files) {
  return (files || []).reduce((acc, item) => {
    if (acc.some((v) => v.fileId === item.fileId)) {
    } else {
      acc.push(item)
    }
    return acc
  }, [])
}

function getOutline(count = 1) {
  stop()
  const params = {
    promote: formState.promote,
    baseIds: formState.baseIds,
    analyseTag: analyseTag.value.getOutline
  }
  thinkNode.value = []
  loading.value = true
  abortController = new AbortController()
  fetchStream({
    url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
    method: 'post',
    signal: abortController.signal,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${userStore.token}`
    },
    body: JSON.stringify(params),
    success: async (res: string) => {
      try {
        const resList = JSON.parse(res)
        formState.title = resList.title
        list.value = resList.data?.map((v) => ({
          title: v
        }))
      } catch (err) {
        errorLog.push({ msg: err.message, stack: err.stack, title: '获取大纲报错', data: res })
        if (count < 5) {
          message.error(err.message)
          getOutline(count + 1)
        } else {
          message.error('已重试超过5次，请联系技术人员排查')
          stop()
          close()
        }
      }
    },
    error: (err) => {
      stop()
      message.error(err)
    },
    complete: () => {
      loading.value = false
    }
  })
}

function createSynopsis(count = 1) {
  ruleFormRef.value
    ?.validate()
    .then(() => {
      const template = []
      list.value.forEach((item, index) => {
        if (!!item.title?.trim()) {
          template.push({ [index]: item.title })
        }
      })
      const params = {
        promote: formState.promote,
        baseIds: formState.baseIds,
        analyseTag: analyseTag.value.generateOutline,
        template
      }
      stop()
      thinkNode.value = []
      loading.value = true
      abortController = new AbortController()
      fetchStream({
        url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
        method: 'post',
        signal: abortController.signal,
        headers: {
          'Content-Type': 'application/json',
          authorization: `Bearer ${userStore.token}`
        },
        body: JSON.stringify(params),
        success: async (res: string) => {
          try {
            const data = JSON.parse(res)
            const result = data.result
            if (data.status === 'flowEnd') {
              requestId.value = data.requestId
            }
            if (data.status === 'start') {
              thinkNode.value.push({
                status: 'streaming',
                nodeId: data.nodeId,
                parentNodeId: data.parentNodeId,
                files: [],
                reasoning_content: result?.reasoning_content || '',
                message: result?.content || ''
              })
            } else if (data.status === 'streaming') {
              const hasNode = thinkNode.value.find((v) => v.nodeId === data.nodeId)
              if (hasNode) {
                hasNode.reasoning_content += result?.reasoning_content || ''
                hasNode.message += result?.content || ''
                if (result?.data?.fileId) {
                  hasNode.files.push(result.data)
                }
              }
            } else if (data.status === 'end') {
              const hasNode = thinkNode.value.find((v) => v.nodeId === data.nodeId)
              if (hasNode) {
                hasNode.status = 'end'
                const messageList = hasNode.message
                  .replace(/(\[\^(\d+)]|```)/g, '')
                  .replace(/\\"/g, '"')
                  .split('@@')
                  .filter((v) => !!v?.trim())
                  .map((v) => v?.trim())
                if (messageList.length > 0) {
                  list.value[hasNode.parentNodeId].content = messageList.map((v) => ({
                    title: v
                  }))
                } else {
                  // message.error('大纲为空')
                }
              }
            } else if (data.status === 'error') {
              errorLog.push({
                msg: data.error,
                stack: 'createSynopsis344',
                title: `创建节点大纲异常`,
                data: data
              })
              message.error(data.error)
              stop()
            } else if (data.status === 'flowEnd') {
              step.value = 1
            }
          } catch (err) {
            errorLog.push({ msg: err.message, stack: err.stack, title: '生成大纲报错', data: res })
            message.error(err.message)
            if (count < 5) {
              createSynopsis(count + 1)
            }
          }
        },
        error: (err) => {
          stop()
          message.error(err)
        },
        complete: () => {
          loading.value = false
          thinkNode.value.forEach((v) => {
            v.status = 'end'
          })
        }
      })
    })
    .catch(() => {})
}

function createContent(count = 1) {
  ruleFormRef.value
    ?.validate()
    .then(() => {
      const template = []
      list.value.forEach((item, index) => {
        if (!!item.title?.trim()) {
          item.content.forEach((v, i) => {
            if (!!v.title?.trim()) {
              template.push({ [`${index}-${i}`]: v.title })
            }
          })
        }
      })
      const params = {
        promote: formState.promote,
        baseIds: formState.baseIds,
        analyseTag: analyseTag.value.createContent,
        template,
        requestId: requestId.value
      }

      stop()
      loading.value = true
      thinkNode.value = []
      abortController = new AbortController()
      fetchStream({
        url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
        method: 'post',
        signal: abortController.signal,
        headers: {
          'Content-Type': 'application/json',
          authorization: `Bearer ${userStore.token}`
        },
        body: JSON.stringify(params),
        success: async (res: string) => {
          try {
            const data = JSON.parse(res)
            const result = data.result
            if (data.status === 'start') {
              thinkNode.value.push({
                status: 'streaming',
                nodeId: data.nodeId,
                parentNodeId: data.parentNodeId,
                files: [],
                reasoning_content: result?.reasoning_content || '',
                message: result?.content || ''
              })
            } else if (data.status === 'streaming') {
              const hasNode = thinkNode.value.find((v) => v.nodeId === data.nodeId)
              if (hasNode) {
                hasNode.reasoning_content += result?.reasoning_content || ''
                hasNode.message += result?.content || ''
                if (result?.data?.fileId) {
                  hasNode.files.push(result.data)
                }
              } else {
              }
            } else if (data.status === 'end') {
              const hasNode = thinkNode.value.find((v) => v.nodeId === data.nodeId)
              if (hasNode) {
                hasNode.status = 'end'
                const messageList = hasNode.message
                  .replace(/(\[\^(\d+)]|```)/g, '')
                  .split('@@')
                  .filter((v) => !!v?.trim())
                  .map((v) => v?.trim())
                if (messageList.length > 0) {
                  const rootKey = hasNode.parentNodeId.split('-')[0]
                  const rootKey2 = hasNode.parentNodeId.split('-')[1]
                  list.value[rootKey].content[rootKey2].content = messageList.map((v) => ({
                    title: v
                  }))
                } else {
                  // message.error('内容为空')
                }
              }
            } else if (data.status === 'error') {
              errorLog.push({
                msg: data.error,
                stack: 'createContent456',
                title: `创建节点内容异常`,
                data: data
              })
              message.error(data.error)
              stop()
            } else if (data.status === 'flowEnd') {
              step.value = 2
            }
          } catch (err) {
            errorLog.push({ msg: err.message, stack: err.stack, title: '生成内容报错', data: res })
            message.error(err.message)
            if (count < 5) {
              createContent(count + 1)
            }
          }
        },
        error: (err) => {
          stop()
          message.error(err)
        },
        complete: () => {
          loading.value = false
          thinkNode.value.forEach((v) => {
            v.status = 'end'
          })
        }
      })
    })
    .catch(() => {})
}

async function createFile() {
  try {
    loading.value = true
    let data
    if (selected.value.length > 0) {
      data = extractTreeWithPaths(list.value, selected.value)
    } else {
      data = list.value
    }
    const res = await report.common({
      html: json2Dom(data),
      fileName: `报告${Date.now()}.docx`,
      whetherFormat: true,
      whetherMarkdown: true
    })
    if (res.code === 200 && res.data) {
      downloadFileByBlob(res.data, `报告${Date.now()}.docx`)
    } else {
      errorLog.push({
        msg: res.message,
        stack: 'createFile508行',
        title: '生成文件失败',
        data: data
      })
    }
  } catch (error) {
    errorLog.push({ msg: error.message, stack: error.stack, title: '生成文件失败', data: error })
    message.error(error.message)
  } finally {
    loading.value = false
  }
}

function stop() {
  abortController?.abort()
  loading.value = false
}

function onDrawerClose() {
  drawer.visible = false
  setTimeout(() => {
    drawer.page = undefined
    drawer.traceProperties = undefined
    drawer.fileName = ''
    drawer.fileUrl = ''
    drawer.key = 0
  }, 200)
}

function buildPdfUrl(fileUrl, page) {
  if (!fileUrl) return ''
  let fragment = 'zoom=1'
  if (page) {
    fragment = `page=${page}`
  }
  return `${fileUrl}#${fragment}`
}

function onHtmlIframeLoad() {
  if (!drawer.traceProperties) return

  const iframe = htmlIframe.value
  if (!iframe?.contentWindow?.document) return

  const { startRow, endRow } = drawer.traceProperties
  const doc = iframe.contentWindow.document
  const rows = doc.querySelectorAll('tr[data-row]')

  if (!rows.length) {
    console.warn('未找到含有 data-row 的 <tr>，无法高亮')
    return
  }

  let firstMatch = null
  rows.forEach((tr) => {
    const rowNum = parseInt(tr.dataset.row)
    if (rowNum >= startRow && rowNum <= endRow) {
      tr.style.backgroundColor = '#fff6c1'
      if (!firstMatch) firstMatch = tr
    }
  })

  if (firstMatch) {
    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

window.generatorAClick = async function (el: HTMLElement) {
  const index = Number(el.getAttribute('data-index'))
  const itemIndex = Number(el.getAttribute('data-item-index'))
  const source = (thinkNode.value[itemIndex].files || []).find((s) => s.index === index)
  if (source) {
    drawer.key = Date.now().toString()
    if (isPDF(source.sourceFileName)) {
      await openFileByReUrl(source)
      drawer.page = source.page
      drawer.fileName = source.sourceFileName
      drawer.fileUrl = source.fileUrl
    } else if (isHtmlFile(source.sourceFileName)) {
      const res = await openHTMLByUrl(source.sourceFileUrl)
      const blob = new Blob([res], { type: 'text/html' })
      drawer.fileUrl = URL.createObjectURL(blob)
      drawer.fileName = source.sourceFileName
      drawer.traceProperties = source.traceProperties
    } else if (isImage(source.sourceFileName)) {
      drawer.fileName = source.sourceFileName
      drawer.fileUrl = await openFile(source, false)
    } else {
      message.info('该文件暂不支持预览，已为您下载')
      return openFile(source, true)
    }

    drawer.visible = true
  }
}

function isImage(fileName) {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName)
}

function isPDF(fileName) {
  return /\.pdf$/i.test(fileName)
}

function isHtmlFile(fileName) {
  return /\.html$/i.test(fileName)
}

async function openFileByReUrl(file) {
  try {
    const res = await chatHistory.getFileByReUrl(file.sourceFileUrl)
    if (res.code === 200) {
      file.fileUrl = res.data
    } else {
      throw new Error(res.message)
    }
  } catch (err) {
    errorLog.push({ msg: err.message, stack: err.stack, title: 'openFileByReUrl失败', data: file })
    message.error(err.message)
  }
}

async function openHTMLByUrl(url) {
  try {
    const res = await chatHistory.getHTMLByUrl(url)
    if (res.code === 200) {
      return res.data
    } else {
      throw new Error(res.message)
    }
  } catch (err) {
    errorLog.push({ msg: err.message, stack: err.stack, title: 'openHTMLByUrl失败', data: url })
    message.error(err.message)
  }
}

async function openFile(file, isDownload = true) {
  try {
    //   AI的文件
    const res = await chatHistory.getFileAbsUrl(file.fileId)
    if (res.code === 200 && res.data) {
      if (isDownload) {
        window.open(res.data, '_blank')
      }
      return res.data
    } else {
      throw new Error(res.message)
    }
  } catch (error) {
    errorLog.push({ msg: error.message, stack: error.stack, title: '下载文件失败', data: file })
    message.error(error.message)
  }
}

function toMarkdown(str = '', item, itemIndex) {
  try {
    const replaced = str.replace(/\[\^(\d+)]/g, (match, index) => {
      const source = item.files.find((s) => s.index === Number(index))
      return source
        ? `<a href="javascript:void(0)" onclick="generatorAClick(this)"  class="ref-link" data-item-index="${itemIndex}" data-index="${index}">[^${index}]</a>`
        : ''
    })

    return markdownIt.render(replaced)
  } catch (err) {
    errorLog.push({ msg: err.message, stack: err.stack, title: 'md转换错误', data: str })
    message.error('md转换错误')
  }
}

function reCreateSynopsis() {
  step.value--
  list.value = list.value.map((v) => ({ title: v.title }))
  selected.value = []
  thinkNode.value = []
}

function reCreateContent() {
  step.value--
  selected.value = []
  thinkNode.value = []
  list.value = list.value.map((v) => ({
    title: v.title,
    content: v.content?.map((item) => ({
      title: item.title
    }))
  }))
}

function close() {
  if (loading.value) {
    message.warn('生成中请勿关闭')
    visible.value = true
  } else {
    visible.value = false
    abortController.abort()
  }
}

function open({ promote, baseIds, analyseTagData }) {
  visible.value = true
  nextTick(() => {
    step.value = 0
    selected.value = []
    list.value = []
    // 正确方式 - 保持响应性
    analyseTag.value = analyseTagData
    formState.promote = promote
    formState.baseIds = baseIds || []
    formState.title = ''
    getOutline()
  })
}

defineExpose({ open })
provide('getPromote', () => formState.promote)
provide('getBaseIds', () => formState.baseIds)
provide('getSelected', () => selected.value)
provide('getAllList', () => list.value)
provide('setThinkNode', (v) => {
  thinkNode.value = v
})
provide('getReCreateContent', () => analyseTag.value.reCreateContent)
provide('changeItem', (bol, id) => {
  if (bol) {
    selected.value.push(id)
  } else {
    selected.value = selected.value.filter((item) => item !== id)
  }
})
provide('setController', (controller) => {
  abortController = controller
})
knowledgeStore.getKnowledge()
</script>
<style lang="less">
.addReport {
  .ant-drawer-body {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .think {
      text-align: justify;
      word-break: break-all;
      white-space: pre-wrap;
      max-width: 30%;
      line-height: 1.5;
      padding-right: 5px;
      //background-color: #8fd5a0;
      margin-right: 5px;
      border-right: 1px dashed #000;
      color: #aaa;
      height: 100%;
      overflow: auto;

      .thinkItem {
        width: 100%;

        .thinking {
          width: 100%;
          padding-left: 10px;
          box-sizing: border-box;

          background: #f5f5f5;
          word-break: break-all;
          color: #8b8b8b;
        }

        .message {
          width: 100%;
          font-size: 20px;
          padding: 10px;
          box-sizing: border-box;
          border-radius: 5px;
          word-break: break-all;
          white-space: initial;
          overflow: hidden;

          border-left: 2px solid #e5e5e5;

          .hljs {
            white-space: break-spaces;
            word-break: break-all;
          }
        }
      }

      .fileMsg {
        width: 100%;

        .fileTxt {
          border-top: 1px solid #b6b6b6;
          padding-top: 1vh;
          width: 100%;
          font-weight: bold;
          color: var(--layout-light-color);
          margin-bottom: 1vh;
        }

        .file {
          display: block;
          width: 100%;
          margin-bottom: 1vh;
          color: var(--primary-color);
          cursor: pointer;

          &:hover {
            font-weight: bolder;
          }

          .fileIndex {
            margin-right: 10px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .operate {
      width: 0;
      flex-grow: 1;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      height: 100%;

      .ruleForm {
        margin-top: 1vh;
      }

      .bodyContent {
        padding-right: 5px;
        flex-grow: 1;
        width: 100%;
        height: 0;
        flex-shrink: 1;
      }
    }
  }

  .ant-drawer-footer {
    width: 100%;
    justify-content: flex-end;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.preview-container {
  height: 100%;
  width: 100%;

  .preview-embed {
    width: 100%;
    height: 100%;
    border: none;
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .preview-download {
    padding: 2rem;
    font-size: 1rem;
  }
}
</style>
