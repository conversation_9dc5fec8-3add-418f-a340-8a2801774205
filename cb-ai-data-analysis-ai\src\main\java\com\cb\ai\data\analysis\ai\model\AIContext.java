package com.cb.ai.data.analysis.ai.model;

import com.cb.ai.data.analysis.ai.model.item.FileItem;
import com.cb.ai.data.analysis.ai.model.item.knowledgeItem;
import com.xong.boot.common.utils.BeanUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * AI上下文接口
 * <AUTHOR>
 */
@Data
public class AIContext implements Serializable {
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 标签
     **/
    private String tag;
    /**
     * 用户提示词
     **/
    private String promote;
    /**
     * 系统提示词
     **/
    private String systemPromote;
    /**
     * 知识库
     **/
    private List<knowledgeItem> knowledges;
    /**
     * 文件
     **/
    private List<FileItem> files;
    /**
     * 配置项
     */
    private Map<String, Object> options;

    public <T> T getOptions(Class<T> clz) {
        if (options != null && options.size() > 0) {
            return BeanUtils.toBean(options, clz);
        }
        return null;
    }
}
