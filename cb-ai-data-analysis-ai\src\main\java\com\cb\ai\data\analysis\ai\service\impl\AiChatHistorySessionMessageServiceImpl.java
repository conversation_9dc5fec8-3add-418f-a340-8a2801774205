package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.ai.domain.AiChatHistorySessionMessage;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistorySessionMessageMapper;
import com.cb.ai.data.analysis.ai.service.AiChatHistorySessionMessageService;
import org.springframework.stereotype.Service;

/**
 * AI历史会话消息 Service
 * <AUTHOR>
 */
@Service
public class AiChatHistorySessionMessageServiceImpl extends ServiceImpl<AiChatHistorySessionMessageMapper, AiChatHistorySessionMessage> implements AiChatHistorySessionMessageService {
}
