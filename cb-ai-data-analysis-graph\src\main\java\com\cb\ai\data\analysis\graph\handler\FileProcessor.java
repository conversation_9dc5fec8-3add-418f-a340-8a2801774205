package com.cb.ai.data.analysis.graph.handler;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import com.cb.ai.data.analysis.graph.domain.entity.GraphFileRecord;
import com.cb.ai.data.analysis.graph.enums.NodeLabelEnum;
import com.cb.ai.data.analysis.graph.enums.RelationCertaintyEnum;
import com.cb.ai.data.analysis.graph.service.business.RelationGraphService;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.utils.spring.SpringUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 文件内容处理到图数据库的处理器
 * @Author: ARPHS
 * @Date: 2025-05-01 11:01
 * @Version: 1.0
 **/
public interface FileProcessor {

    Logger log = LoggerFactory.getLogger(FileProcessor.class);

    /**
     * 文件的处理逻辑
     *
     * @param filePath 文件路径
     */
    default void process(String filePath, GraphFileRecord graphFileRecord, String promote){
        BufferedInputStream inputStream = FileUtil.getInputStream(filePath);
        process(inputStream, graphFileRecord, promote);
    }

    void process(InputStream is, GraphFileRecord graphFileRecord, String promote);

    /**
     * 将异常抛出为RunTimeException
     *
     * @param message
     * @param e
     */
    default void handleCatchException(String message, Exception e) throws RuntimeException {
        log.error(message);
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        } else {
            throw new RuntimeException(e);
        }
    }

    /**
     * 入库企业信息
     *
     * @param mapList
     */
    default void handleEnterpriseExcel(List<Map<String, String>> mapList) {
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String companyName = MapUtils.getString(map, "公司名称");
                if (StringUtils.isBlank(companyName)) {
                    return;
                }
                String registerStatus = MapUtils.getString(map, "登记状态");
                String legalRepresentative = MapUtils.getString(map, "法定代表人");
                String shareholders = MapUtils.getString(map, "股东人员");
                String registeredCapital = MapUtils.getString(map, "注册资本");
                String paidInCapital = MapUtils.getString(map, "实缴资本");
                String establishmentDate = MapUtils.getString(map, "成立日期");
                String approvalDate = MapUtils.getString(map, "核准日期");
                String province = MapUtils.getString(map, "所属省份");
                String city = MapUtils.getString(map, "所属城市");
                String district = MapUtils.getString(map, "所属区县");
                String companyType = MapUtils.getString(map, "公司类型");
                String formerName = MapUtils.getString(map, "曾用名");
                String unifiedSocialCreditCode = MapUtils.getString(map, "统一社会信用代码");
                String taxpayerIdentificationNumber = MapUtils.getString(map, "纳税人识别号");
                String registrationNumber = MapUtils.getString(map, "工商注册号");

                //企业信息
                Map<String, Object> companyMap = new LinkedHashMap<>();
                companyMap.put("name", companyName);
                companyMap.put("登记状态", registerStatus);
                companyMap.put("注册资本", registeredCapital);
                companyMap.put("实缴资本", paidInCapital);
                companyMap.put("成立日期", establishmentDate);
                companyMap.put("核准日期", approvalDate);
                companyMap.put("所属省份", province);
                companyMap.put("所属城市", city);
                companyMap.put("所属区县", district);
                companyMap.put("公司类型", companyType);
                companyMap.put("曾用名", formerName);
                companyMap.put("统一社会信用代码", unifiedSocialCreditCode);
                companyMap.put("纳税人识别号", taxpayerIdentificationNumber);
                companyMap.put("工商注册号", registrationNumber);

                String companyId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.ENTERPRISE.getLabel(), companyMap, null,
                        new String[]{"{name: $item.name, `统一社会信用代码`: $item.`统一社会信用代码`}", "{name: $item.公司名称}"});

                //法人信息
                Map<String, Object> legalPersonMap = null;
                if (StringUtils.isNotBlank(legalRepresentative)) {
                    legalPersonMap = new LinkedHashMap<>();
                    legalPersonMap.put("name", legalRepresentative);
                    String legalPersonId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), legalPersonMap, null,
                            new String[]{"{name: $item.name}"});
                    String relationType = "法人";
                    Map<String, Object> relationProps = MapUtil.of("关系确定性", RelationCertaintyEnum.ACCURATE.getLabel());
                    relationGraphService.createRelationByPropId(companyId, legalPersonId, relationType, relationProps);
                }
                //股东
                if (StringUtils.isNotBlank(shareholders)) {
                    String[] split = shareholders.split("[,，;；、/]");
                    for (String shareholder : split) {
                        Map<String, Object> shareholderMap = new LinkedHashMap<>();
                        shareholderMap.put("name", shareholder);
                        String shareholderId = relationGraphService.createNode(NodeLabelEnum.PERSON.getLabel(), shareholderMap, null, null);
                        String relationType = "股东";
                        Map<String, Object> relationProps = MapUtil.of("关系确定性", RelationCertaintyEnum.ACCURATE.getLabel());
                        relationGraphService.createRelationByPropId(companyId, shareholderId, relationType, relationProps);
                    }
                }

            }
        } catch (Exception e) {
            handleCatchException("企业信息表处理报错！", e);
        }
    }

    /**
     * 入库干部信息
     *
     * @param mapList
     */
    default void handlePersonExcel(List<Map<String, String>> mapList) {
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            Map<String, String> workDeptNodeIdMap = new LinkedHashMap<>();
            // 工作单位合并处理
            String workDeptLast = null;
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String workDept = MapUtils.getString(map, "工作单位");
                if (StringUtils.isNotBlank(workDept)) {
                    workDeptLast = workDept;
                    workDeptNodeIdMap.put(workDept, null);
                } else {
                    workDept = workDeptLast;
                    map.put("工作单位", workDept);
                }
            }
//            // 先入库工作单位
//            workDeptNodeIdMap.entrySet().forEach(entry -> {
//                String workDept = entry.getKey();
//                Map<String, Object> workDeptMap = new LinkedHashMap<>();
//                workDeptMap.put("name", workDept);
//                String workDeptId = relationGraphService.createNode(NodeLabelEnum.ORG.getLabel(), workDeptMap, null,
//                        "{name: $item.name}");
//                entry.setValue(workDeptId);
//            });

            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String name = MapUtils.getString(map, "姓名");
                if (StringUtils.isBlank(name)) {
                    return;
                }
                String workDept = MapUtils.getString(map, "工作单位");
                String userGender = MapUtils.getString(map, "性别");
                String idNumber = MapUtils.getString(map, "身份证号");
                String nation = MapUtils.getString(map, "民族");
                String nativePlace = MapUtils.getString(map, "籍贯");
                String birthPlace = MapUtils.getString(map, "出生地");
                String birthDate = MapUtils.getString(map, "出生年月");
                String politicalStatus = MapUtils.getString(map, "政治面貌");
                String joinPartyDate = MapUtils.getString(map, "入党时间");
                String workStartDate = MapUtils.getString(map, "参加工作时间");
                String currentPosition = MapUtils.getString(map, "现职务");


                Map<String, Object> personMap = new LinkedHashMap<>();
                personMap.put("name", name);
                personMap.put("工作单位", workDept);
                personMap.put("性别", userGender);
                personMap.put("身份证号", idNumber);
                personMap.put("民族", nation);
                personMap.put("籍贯", nativePlace);
                personMap.put("出生地", birthPlace);
                personMap.put("出生年月", birthDate);
                personMap.put("政治面貌", politicalStatus);
                personMap.put("入党时间", joinPartyDate);
                personMap.put("参加工作时间", workStartDate);
                personMap.put("现职务", currentPosition);
                String personId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), personMap, NodeLabelEnum.LEADER.getLabel(),
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});

//                relationGraphService.createNodeAndRelation(personId, workDeptNodeIdMap.get(workDept), RelationLabelEnum.WORK_DEPT.getLabel(), null);

            }

        } catch (Exception e) {
            handleCatchException("干部信息表处理报错！", e);
        }
    }

    /**
     * 入库干部亲属信息
     *
     * @param mapList
     */
    default void handleKinExcel(List<Map<String, String>> mapList) {
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            Map<String, String> workDeptNodeIdMap = new LinkedHashMap<>();
            Map<String, String> nameNodeIdMap = new LinkedHashMap<>();
            // 工作单位合并处理
            String workDeptLast = null;
            String nameLast = null;
            String idNoLast = null;
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String workDept = MapUtils.getString(map, "工作单位");
                if (StringUtils.isNotBlank(workDept)) {
                    workDeptLast = workDept;
                    workDeptNodeIdMap.put(workDept, null);
                } else {
                    workDept = workDeptLast;
                    map.put("工作单位", workDept);
                }
                String name = MapUtils.getString(map, "姓名");
                String idNo = MapUtils.getString(map, "身份证号");
                if (StringUtils.isNotBlank(name)) {
                    nameLast = name;
                    idNoLast = idNo;
                    String key = String.format("%s:%S:%s", name, idNo, workDept);
                    nameNodeIdMap.put(key, null);
                } else {
                    name = nameLast;
                    idNo = idNoLast;
                    map.put("姓名", name);
                    map.put("身份证号", idNo);
                }
            }
//            // 先入库工作单位
//            workDeptNodeIdMap.entrySet().forEach(entry -> {
//                String workDept = entry.getKey();
//                Map<String, Object> workDeptMap = new LinkedHashMap<>();
//                workDeptMap.put("name", workDept);
//                String workDeptId = relationGraphService.createNode(NodeLabelEnum.ORG.getLabel(), workDeptMap,null,
//                        "{name: $item.name}");
//                entry.setValue(workDeptId);
//            });
            nameNodeIdMap.entrySet().forEach(entry -> {
                String key = entry.getKey();
                String[] split = key.split(":");
                String name = split[0];
                String idNo = split[1];
                String workDept = split[2];
                Map<String, Object> personMap = new LinkedHashMap<>();
                personMap.put("name", name);
                personMap.put("身份证号", idNo);
                String personId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), personMap, NodeLabelEnum.LEADER.getLabel(),
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});
//                relationGraphService.createNodeAndRelation(personId, workDeptNodeIdMap.get(workDept), RelationLabelEnum.WORK_DEPT.getLabel(), null);
                entry.setValue(personId);
            });

            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String kinName = MapUtils.getString(map, "亲属姓名");
                if (StringUtils.isBlank(kinName)) {
                    return;
                }
                String workDept = MapUtils.getString(map, "工作单位");
                String name = MapUtils.getString(map, "姓名");
                String idNo = MapUtils.getString(map, "身份证号");
                String kinRelation = MapUtils.getString(map, "亲属关系");
                String kinIdNo = MapUtils.getString(map, "亲属身份证号");

                Map<String, Object> kinMap = new LinkedHashMap<>();
                kinMap.put("name", kinName);
                kinMap.put("身份证号", kinIdNo);
                String kinId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), kinMap, null,
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});

                String relationType = kinRelation;
                Map<String, Object> relationProps = MapUtil.of("关系确定性", RelationCertaintyEnum.ACCURATE.getLabel());
                relationGraphService.createRelationByPropId(nameNodeIdMap.get(String.format("%s:%s:%s", name, idNo, workDept)), kinId,
                        relationType, relationProps);

            }

        } catch (Exception e) {
            handleCatchException("干部亲属信息表处理报错！", e);
        }
    }

    /**
     * 入库 干部近亲属关系信息统计表
     *
     * @param mapList
     */
    default void handleKinV1Excel(List<Map<String, String>> mapList) {
        try {
            RelationGraphService relationGraphService = SpringUtils.getBean(RelationGraphService.class);
            Map<String, String> workDeptNodeIdMap = new LinkedHashMap<>();
            Map<String, String> nameNodeIdMap = new LinkedHashMap<>();
            // 工作单位合并处理
            String workDeptLast = null;
            String nameLast = null;
            String idNoLast = null;
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String workDept = MapUtils.getString(map, "所在单位");
                String currPosAndRank = MapUtils.getString(map, "现任职务职级");
                if (StringUtils.isNotBlank(workDept)) {
                    workDeptLast = workDept;
                    workDeptNodeIdMap.put(workDept, null);
                } else {
                    workDept = workDeptLast;
                    map.put("所在单位", workDept);
                }
                // TODO 表格中列名为 所在单位 -> 库中使用的列名为 工作单位
                map.put("工作单位", workDept);

                String name = MapUtils.getString(map, "姓名");
                String idNo = MapUtils.getString(map, "身份证号");
                if (StringUtils.isNotBlank(name)) {
                    nameLast = name;
                    idNoLast = idNo;
                    String key = String.format("%s:%S:%s:%s", name, idNo, workDept, currPosAndRank);
                    nameNodeIdMap.put(key, null);
                } else {
                    name = nameLast;
                    idNo = idNoLast;
                    map.put("姓名", name);
                    map.put("身份证号", idNo);
                }
            }
//            // 先入库工作单位
//            workDeptNodeIdMap.entrySet().forEach(entry -> {
//                String workDept = entry.getKey();
//                Map<String, Object> workDeptMap = new LinkedHashMap<>();
//                workDeptMap.put("name", workDept);
//                String workDeptId = relationGraphService.createNode(NodeLabelEnum.ORG.getLabel(), workDeptMap,null,
//                        "{name: $item.name}");
//                entry.setValue(workDeptId);
//            });
            nameNodeIdMap.entrySet().forEach(entry -> {
                String key = entry.getKey();
                String[] split = key.split(":");
                String name = split[0];
                String idNo = split[1];
                String workDept = split[2];
                String currPosAndRank = split[3];
                Map<String, Object> personMap = new LinkedHashMap<>();
                personMap.put("name", name);
                personMap.put("身份证号", idNo);
                personMap.put("工作单位", workDept);
                personMap.put("现职务", currPosAndRank);
                String personId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), personMap, NodeLabelEnum.LEADER.getLabel(),
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});
//                relationGraphService.createNodeAndRelation(personId, workDeptNodeIdMap.get(workDept), RelationLabelEnum.WORK_DEPT.getLabel(), null);
                entry.setValue(personId);
            });

            for (int i = 0; i < mapList.size(); i++) {
                Map<String, String> map = mapList.get(i);
                String kinName = MapUtils.getString(map, "亲属姓名");
                if (StringUtils.isBlank(kinName)) {
                    return;
                }
                String workDept = MapUtils.getString(map, "工作单位");
                String name = MapUtils.getString(map, "姓名");
                String idNo = MapUtils.getString(map, "身份证号");
                String currPosAndRank = MapUtils.getString(map, "现任职务职级");

                String kinRelation = MapUtils.getString(map, "亲属关系");
                String kinIdNo = MapUtils.getString(map, "亲属身份证号");
                String kinWorkDept = MapUtils.getString(map, "亲属所在单位");
                String kinCurrPosAndRank = MapUtils.getString(map, "亲属现任职务职级");

                Map<String, Object> kinMap = new LinkedHashMap<>();
                kinMap.put("name", kinName);
                kinMap.put("身份证号", kinIdNo);
                kinMap.put("工作单位", kinWorkDept);
                kinMap.put("现职务", kinCurrPosAndRank);

                String kinId = relationGraphService.createNodeMatchOrCreate(NodeLabelEnum.PERSON.getLabel(), kinMap, null,
                        new String[]{"{name: $item.name, `身份证号`: $item.`身份证号`}"});

                String relationType = kinRelation;
                Map<String, Object> relationProps = MapUtil.of("关系确定性", RelationCertaintyEnum.ACCURATE.getLabel());
                relationGraphService.createRelationByPropId(nameNodeIdMap.get(String.format("%s:%s:%s:%s", name, idNo, workDept, currPosAndRank)), kinId,
                        relationType, relationProps);

            }

        } catch (Exception e) {
            handleCatchException("干部近亲属关系信息统计表处理报错！", e);
        }
    }

}
