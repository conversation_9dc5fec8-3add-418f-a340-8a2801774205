package com.cb.ai.data.analysis.ai.common.registry;

import com.cb.ai.data.analysis.ai.component.choreography.extension.BaseExtension;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionManager;
import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.AssignableTypeFilter;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/6/6 15:07
 * @Copyright (c) 2025
 * @Description Extension扩展相关注册
 */
public class ExtensionManagerRegistryBase extends BaseDynamicBeanRegistry {

    @Override
    void dynamicRegistry(BeanDefinitionRegistry beanDefinitionRegistry, ClassPathScanningCandidateComponentProvider scanner) {
        scanner.addIncludeFilter(new AnnotationTypeFilter(ExtensionProvider.class));
        scanner.addIncludeFilter(new AssignableTypeFilter(BaseExtension.class));
        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents(packagePath("component.extensions"));
        if (!beanDefinitions.isEmpty()) {
            for (BeanDefinition beanDefinition : beanDefinitions) {
                // 注册为bean
                String beanClassName = beanDefinition.getBeanClassName();
                if (beanClassName == null) {
                    continue;
                }
                beanDefinitionRegistry.registerBeanDefinition(beanClassName, beanDefinition);
            }
        }
        // 注册ExtensionManager
        BeanDefinition managerBeanDef = BeanDefinitionBuilder
            .genericBeanDefinition(ExtensionManager.class)
            .setScope(BeanDefinition.SCOPE_SINGLETON)
            .setPrimary(true)
            .getBeanDefinition();
        beanDefinitionRegistry.registerBeanDefinition(ExtensionManager.class.getSimpleName(), managerBeanDef);
    }
}
