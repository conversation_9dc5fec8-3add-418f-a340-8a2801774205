package com.cb.ai.data.analysis.ai.domain.response;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/5 10:44
 * @Copyright (c) 2025
 * @Description 向量相似度查询结果实例
 */
@Getter
@Setter
@Accessors(chain = true)
public class EmbeddingResult {
    /* 向量id */
    private String id;
    /* 向量文本 */
    private String text;
    /* 向量原数据 */
    private Metadata metadata;
    /* 向量自定义排序下标 */
    private Integer index;

    public record Metadata (
        /* 知识库文件Id */
        String fileId,
        /* 溯源文件名 */
        String sourceFileName,
        /* 溯源文件url */
        String sourceFileUrl,
        /* 开始行 */
        String startRow,
        /* 相似度 */
        String distance,
        /* 源文件名 */
        String originFileName,
        /* 源文件url */
        String originFileUrl,
        /* 结束行 */
        String endRow,
        /* 知识库Id */
        String baseId,
        /* 所在页码 */
        String page,
        /* 文本内容 */
        String content
    ) {}
}
