package com.cb.ai.data.analysis.petition.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FileContentTypeEnum {

    PDF("application/pdf", "pdf", 1),
    XLSX("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx", 2),
    XLS("application/vnd.ms-excel", "xls", 3),
    DOC("application/msword", "doc", 4),
    DOCX("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx", 5);

    private final String contentType;
    private final String suffix;
    private final Integer code;

    // 根据contentType找枚举
    public static FileContentTypeEnum fromContentType(String contentType) {
        for (FileContentTypeEnum type : values()) {
            if (type.getContentType().equalsIgnoreCase(contentType)) {
                return type;
            }
        }
        return null;
    }

    public static FileContentTypeEnum fromCode(Integer code) {
        for (FileContentTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static Boolean isExcel(Integer code) {
        if (XLS.getCode().equals(code) || XLSX.getCode().equals(code)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public static FileContentTypeEnum fromSuffix(String contentType) {
        for (FileContentTypeEnum type : values()) {
            if (type.getContentType().equalsIgnoreCase(contentType)) {
                return type;
            }
        }
        return null;
    }
}
