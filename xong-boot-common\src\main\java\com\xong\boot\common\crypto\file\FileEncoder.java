package com.xong.boot.common.crypto.file;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件加解密接口
 * <AUTHOR>
 */
public interface FileEncoder {
    /**
     * 加密后关闭输入流
     * @param is  加密的字符串
     * @param out 输出流，可以是文件或网络位置
     */
    void encode(InputStream is, OutputStream out) throws IOException;

    /**
     * 加密
     * @param is      加密的字符串
     * @param out     输出流，可以是文件或网络位置
     * @param isClose 是否关闭输入流
     */
    void encode(InputStream is, OutputStream out, boolean isClose) throws IOException;

    /**
     * 解密后关闭输入流
     * @param is  加密的字符串
     * @param out 输出流，可以是文件或网络位置
     */
    void decode(InputStream is, OutputStream out) throws IOException;

    /**
     * 解密
     * @param is      加密的字符串
     * @param out     输出流，可以是文件或网络位置
     * @param isClose 是否关闭输入流
     */
    void decode(InputStream is, OutputStream out, boolean isClose) throws IOException;
}
