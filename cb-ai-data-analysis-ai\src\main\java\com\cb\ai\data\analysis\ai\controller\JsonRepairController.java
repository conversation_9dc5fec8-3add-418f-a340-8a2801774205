package com.cb.ai.data.analysis.ai.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.URLDecoder;
import com.cb.ai.data.analysis.ai.utils.CommonUtil;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/28 16:48
 * @Copyright (c) 2025
 * @Description Json修复控制器
 */
@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/json/repair")
public class JsonRepairController {
    /**
     * @description 方案审查等AI返回的特定格式的json修复
     * @param json 待修复的json字符串
     * @return 修复后的json字符串
     * @createtime 2025/7/28 下午4:54
     * <AUTHOR>
     * @version 1.0
     */
    @PostMapping
    public Result repair(@RequestBody String json) {
        Assert.notBlank(json, "待修复的json不能为空！");
        json = URLDecoder.decode(json, StandardCharsets.UTF_8);
        try {
            return Result.successData(CommonUtil.repairJson(json));
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

}
