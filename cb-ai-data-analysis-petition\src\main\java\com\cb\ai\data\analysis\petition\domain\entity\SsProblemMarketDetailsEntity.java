package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import org.springframework.data.annotation.Id;
import java.io.Serializable;

@Data
@TableName("ss_problem_market_details")
public class SsProblemMarketDetailsEntity extends BaseDomain implements Serializable {

    /***
     * 问题明细ID
     */
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /***
     * 问题分类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long problemClassifyId;

    /***
     * 问题名称
     */
    private String problemName;


    /***
     * 文件名称
     */
    @TableField(exist = false)
    private String  fileName;

    /***
     * 问题标题
     */
    @TableField(exist = false)
    private String  problemTitle;

    /**
     * 问题分类
     */
    @TableField(exist = false)
    private String  problemClassify;
}
