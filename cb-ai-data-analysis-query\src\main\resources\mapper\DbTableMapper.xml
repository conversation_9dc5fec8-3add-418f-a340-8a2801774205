<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.query.mapper.DbTableMapper">
    <resultMap id="DbTableVoResult" type="com.cb.ai.data.analysis.query.domain.vo.DbTableVo">
        <result property="tableName" column="table_name"/>
        <result property="tableComment" column="table_comment"/>
    </resultMap>
    <resultMap id="ColumnVoResult" type="com.cb.ai.data.analysis.query.domain.vo.ColumnVo">
        <result property="columnName" column="column_name"/>
        <result property="comments" column="comments"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="TableResultMap">
        select table_name, table_comment
        from information_schema.tables
    </sql>

    <sql id="ColumnResultMap">
        select column_name,
               column_comment as comments,
               data_type  as type
        from information_schema.columns
    </sql>

    <select id="selectMysqlDbTableVo" resultMap="DbTableVoResult">
        <include refid="TableResultMap"/>
        where table_schema = (select database()) and table_name = #{tableName}
    </select>

    <select id="selectMysqlTableColumnList" resultMap="ColumnVoResult">
        <include refid="ColumnResultMap"/>
        where  table_schema = (select database()) and table_name = #{tableName}
    </select>


    <select id="selectClickHouseDbTableVo" resultMap="DbTableVoResult">
        <include refid="TableResultMap"/>
        where table_schema = (select database()) and table_name = #{tableName}
    </select>

    <select id="selectClickHouseTableColumnList" resultMap="ColumnVoResult">
        <include refid="ColumnResultMap"/>
        where  table_schema = (select database()) and table_name = #{tableName}
    </select>

<!--    <select id="selectSlaveDbTableVo" resultMap="DbTableVoResult">-->
<!--        <include refid="TableResultMap"/>-->
<!--        where table_schema = (select database()) and table_name = #{tableName}-->
<!--    </select>-->

<!--    <select id="selectSlaveTableColumnList" resultMap="ColumnVoResult">-->
<!--        <include refid="ColumnResultMap"/>-->
<!--        where  table_schema = (select database()) and table_name = #{tableName}-->
<!--    </select>-->
</mapper>
