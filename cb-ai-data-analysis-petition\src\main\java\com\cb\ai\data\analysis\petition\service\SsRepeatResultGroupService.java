package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.xong.boot.common.service.BaseService;

public interface SsRepeatResultGroupService extends BaseService<SsRepeatResultGroup> {

    Page<SsRepeatResultGroup> pageByWrapper(Page<SsRepeatResultGroup> page, Wrapper<SsRepeatResultGroup> wrapper);

}
