<template>
  <a-modal
    :confirm-loading="loading"
    :open="visible"
    title="新增分析任务"
    width="50%"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form ref="formRef" :label-col="{ style: { width: '120px' } }" :model="formData">
      <a-form-item
        :rules="{ message: '必须填写任务名称', required: true }"
        label="任务名称"
        name="name"
      >
        <a-input
          v-model:value="formData.name"
          :maxlength="100"
          placeholder="请输入任务名称"
        />
      </a-form-item>
      
      <a-form-item
        :rules="{ message: '必须选择要分析的标签', required: true }"
        label="分析标签"
        name="tags"
      >
        <a-input
          v-model:value="formData.tags"
          :maxlength="200"
          placeholder="请输入要分析的标签，多个标签用英文逗号分隔"
        />
        <div class="form-help-text">
          多个标签请用英文逗号分隔，例如：发票,收据,合同
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts" name="VoucherTaskAdd">
import { type ComponentCustomProperties, getCurrentInstance, ref } from 'vue'
import { task } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible', 'success'])
const props = defineProps<{
  visible: boolean
}>()

const loading = ref(false)
const formRef = ref()
const formData = ref({
  name: '',
  tags: ''
})

function onCancel() {
  formRef.value?.resetFields()
  formData.value = {
    name: '',
    tags: ''
  }
  emits('update:visible', false)
}

function onOk() {
  _this.$form.validate(formRef.value, async (errors, values) => {
    if (errors) {
      return false
    }
    try {
      loading.value = true
      const { message } = await task.add(values)
      _this.$message.success(message || '新增任务成功，后台分析中...')
      emits('success', true)
      onCancel()
    } catch (error: any) {
      _this.$message.error(error.message || '新增任务失败')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style scoped lang="less">
.ant-form-item {
  margin-bottom: 16px;
}

.form-help-text {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style>
