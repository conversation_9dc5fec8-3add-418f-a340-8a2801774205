package com.cb.ai.data.analysis.ai.component.extensions;

import com.cb.ai.data.analysis.ai.component.choreography.extension.ExtensionProvider;
import com.cb.ai.data.analysis.ai.component.choreography.model.BusinessTypeEnum;
import com.cb.ai.data.analysis.ai.component.choreography.model.Route;
import com.cb.ai.data.analysis.ai.component.flows.chuangbo.PrivateFinanceAIChat;
import com.cb.ai.data.analysis.ai.domain.request.context.CommonAIRequestContext;
import com.cb.ai.data.analysis.ai.domain.response.PrivateAIBackData;
import com.cb.ai.data.analysis.ai.domain.response.ResultData;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 12:00
 * @Copyright (c) 2025
 * @Description 私有化AI大数据分析扩展
 */
@ExtensionProvider(desc = "私有化AI大数据分析扩展", businessScenes = {
    @Route(tag = "大数据分析", business = BusinessTypeEnum.PRIVATE_BUSINESS)
})
public class PrivateFinanceExtension implements PrivateAIExtension<CommonAIRequestContext> {

    @Override
    public Flux<ResultData<PrivateAIBackData>> invoke(CommonAIRequestContext requestContext) {
        return new PrivateFinanceAIChat().processData(requestContext);
    }
}
