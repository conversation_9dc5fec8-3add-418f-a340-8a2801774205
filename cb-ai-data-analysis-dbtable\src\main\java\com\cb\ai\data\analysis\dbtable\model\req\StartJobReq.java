package com.cb.ai.data.analysis.dbtable.model.req;

import com.alibaba.fastjson2.JSON;
import com.cb.ai.data.analysis.dbtable.model.ExcelConvert;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 启动工作请求
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StartJobReq extends ExcelHeadReq {
    /**
     * 数据表ID
     */
    private String tableId;
    /**
     * 数据开始位置
     */
    private Integer startRow;
    /**
     * 数据结束位置
     */
    private Integer endRow;
    /**
     * 文件
     */
    private MultipartFile file;
    /**
     * 转换规则
     */
    private String excelConverts;

    public List<ExcelConvert> getExcelConvertArray() {
        return JSON.parseArray(excelConverts, ExcelConvert.class);
    }
}
