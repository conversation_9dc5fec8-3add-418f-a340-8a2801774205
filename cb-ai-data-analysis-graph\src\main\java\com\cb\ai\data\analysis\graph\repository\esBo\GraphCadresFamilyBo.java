package com.cb.ai.data.analysis.graph.repository.esBo;

import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.time.LocalDateTime;

/**
 * 知识图谱-干部亲属表 ES 存储体类
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:18
 */
@Data
@Document(indexName = Constant.ES_GRAPH_DATA_INDEX + Constant.SLICING + "graph_cadres_family")
@Setting(shards = 1, replicas = 0)
public class GraphCadresFamilyBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    //ID
    @Id
    private String id;

    //工作单位
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String workUnit;

    //姓名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String name;

    //身份证号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String idCard;

    //亲属姓名
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String familyName;

    //亲属关系
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String familyRelation;

    //亲属身份证号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String familyIdCard;

    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String remark;
    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;

}

