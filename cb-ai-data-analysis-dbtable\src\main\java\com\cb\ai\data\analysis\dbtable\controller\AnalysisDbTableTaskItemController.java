package com.cb.ai.data.analysis.dbtable.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableTaskItemService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import org.springframework.web.bind.annotation.*;

/**
 * 数据导入工作日志Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table/task/item")
public class AnalysisDbTableTaskItemController extends BaseController<AnalysisDbTableTaskItemService, AnalysisDbTableTaskItem> {

    /**
     * 查询数据导入工作列表
     */
//    @PreAuthorize("@ss.hasPermi('datapool:joblog:list')")
    @GetMapping("/page")
    public Result list(AnalysisDbTableTaskItem dynamicTableTaskItem) {
        Page<AnalysisDbTableTaskItem> list = baseService.pageTableTaskItemList(dynamicTableTaskItem);
        return Result.successData(list);
    }

    /**
     * 删除数据导入工作
     */
//    @PreAuthorize("@ss.hasPermi('datapool:joblog:remove')")
    @DeleteMapping()
    public Result remove(@RequestParam(value = "ids") String[] ids) {
        baseService.deleteDynamicTableTaskItemByIds(ids);
        return Result.success();
    }
}
