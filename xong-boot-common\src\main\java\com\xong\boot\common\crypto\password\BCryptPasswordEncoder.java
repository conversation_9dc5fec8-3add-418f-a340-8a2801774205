package com.xong.boot.common.crypto.password;

/**
 * BCrypt加密密码
 * <AUTHOR>
 */
public class BCryptPasswordEncoder extends org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder implements PasswordEncoder {
    /**
     * 密码加密
     * @param rawPassword 明文密码
     * @param salt        盐值
     */
    @Override
    public String encode(CharSequence rawPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return encode(password);
    }

    /**
     * 密码验证
     * @param rawPassword     明文密码
     * @param encodedPassword 密文密码
     * @param salt            盐值
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword, CharSequence salt) {
        StringBuilder password = new StringBuilder();
        password.append(rawPassword);
        password.append(salt);
        return matches(password, encodedPassword);
    }
}
