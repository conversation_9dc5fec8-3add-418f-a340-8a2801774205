package com.cb.ai.data.analysis.ai.component.choreography.model;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/1 17:48
 * @Copyright (c) 2025
 * @Description 场景枚举
 */
public enum SceneEnum {
    /**
     * 默认场景
     */
    DEFAULT_SCENE,
    ///**
    // * 聊天场景
    // */
    //CHAT_SCENE,
    ///**
    // * 知识库问答场景
    // */
    //KNOWLEDGE_BASE_SCENE,
    ///**
    // * 生成报告场景
    // */
    //GENERATE_REPORTS_SCENE,
    ///**
    // * 文件分析场景
    // */
    //FILE_ANALYSIS_SCENE,
    ///**
    // * 问题问答场景
    // */
    //QUESTIONS_SCENE,
    ///**
    // * 谈话分析场景
    // */
    //CONVERSATION_ANALYSIS_SCENE,
    ///**
    // * 音频解析场景
    // */
    //AUDIO_ANALYSIS_SCENE,

    ;

}
