package com.cb.ai.data.analysis.petition.domain.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WorkFlowResponse {
    @JsonProperty("status_code")
    private int statusCode; // 状态码

    @JsonProperty("status_message")
    private String statusMessage; // 状态信息

    private DataContent data; // 主要数据内容

    @Data
    public static class DataContent {
        @JsonProperty("session_id")
        private String sessionId; // 会话ID

        private List<Event> events; // 事件列表
    }
    @Data
    public static class Event {
        private String event; // 事件类型，例如 guide_word（开场白）、guide_question（引导问题）、input（等待输入事件）等

        @JsonProperty("message_id")
        private String messageId; // 消息在数据库中的唯一ID

        @JsonProperty("status")
        private String status; // 事件状态

        @JsonProperty("node_id")
        private String nodeId; // 触发事件的工作流节点ID

        @JsonProperty("node_execution_id")
        private String nodeExecutionId; // 节点执行ID，用于唯一标识某次节点执行

        @JsonProperty("output_schema")
        private OutputSchema outputSchema; // 事件的输出数据

        @JsonProperty("input_schema")
        private InputSchema inputSchema; // 事件的输入数据
    }
    @Data
    public static class OutputSchema {
        private String message; // 直接展示给用户的文本输出内容

        @JsonProperty("reasoning_content")
        private String reasoningContent; // 推理模型的思考过程内容

        @JsonProperty("output_key")
        private String outputKey; // 输出内容对应的变量标识

        private List<FileData> files; // 输出的文件列表

        @JsonProperty("source_url")
        private String sourceUrl; // 知识库问答溯源页面地址

        private Map<String, Object> extra; // 额外数据，如 QA 知识库溯源内容
    }

    @Data
    public static class FileData {
        private String path; // 文件地址
        private String name; // 文件名称
    }

    @Data
    public static class InputSchema {
        @JsonProperty("input_type")
        private String inputType; // 输入类型，例如 form_input（表单输入）

        private List<InputValue> value; // 需要用户输入的字段信息
    }

    @Data
    public static class InputValue {
        private String key; // 字段的唯一key
        private String type; // 字段类型，例如 text（文本输入框）、select（下拉框）
        private String value; // 默认值
        private boolean multiple; // 是否可多选
        private String label; // 字段的前端展示名称
        private boolean required; // 是否必填
        private List<Option> options; // 可选项（如果字段是下拉框）
    }

    @Data
    public static class Option {
        private String id; // 选项的唯一ID
        private String text; // 选项文本
        private String type; // 选项类型（可能为空）
    }
}

