package com.cb.ai.data.analysis.ai.utils;


import cn.hutool.core.collection.ListUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.domain.common.FieldHandle;
import com.cb.ai.data.analysis.ai.domain.func.TriFunction;
import com.google.common.base.Throwables;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2021/10/11 14:00
 * @Copyright Copyright (c) 2021
 * @Description: 合并工具
 * 
 */
public class MergeUtil {
    /**
     * @description 拼接字符串
     */
    public static String mergeStr(Object sourceValue, Object targetValue) {
        sourceValue = sourceValue == null ? "" : sourceValue;
        targetValue = targetValue == null ? "" : targetValue;
        return JsonUtil.toStr(sourceValue) + JsonUtil.toStr(targetValue);
    }

    /**
     * @description 拼接字符串
     */
    public static String mergeStr(String sourceValue, String targetValue) {
        return sourceValue + targetValue;
    }

    /**
     * @description 含有'/'的路径合并
     */
    public static String mergePath(String sourceValue, String targetValue) {
        // 统一路径格式
        String source = normalizePath(sourceValue);
        String target = normalizePath(targetValue);
        return mergePath(source, target, '/');
    }

    /**
     * @description 路径合并
     * @param sourceValue 源路径
     * @param targetValue 目标路径
     * @return 合并后的完整路径
     * @createtime 2025/6/14 下午6:44
     * <AUTHOR>
     * @version 1.0
     */
    public static String mergePath(String source, String target, char pathSeparator) {

        // 规则1：如果 targetValue 包含 source，则直接返回原始 targetValue
        if (target.startsWith(source)) {
            return target;
        }
        // 规则2：如果 target 是 URL，则直接返回原始 target
        if (target.startsWith("http://") || target.startsWith("https://")) {
            return target;
        }
        // 找出公共路径段
        int commonLength = 0;
        int minLength = Math.min(source.length(), target.length());
        while (commonLength < minLength && source.charAt(commonLength) == target.charAt(commonLength)) {
            commonLength++;
        }
        // 回退到最后一个 pathSeparator 的位置
        int lastSlashIndex = -1;
        for (int i = 0; i < commonLength; i++) {
            if (source.charAt(i) == pathSeparator && source.charAt(i) == target.charAt(i)) {
                lastSlashIndex = i;
            } else {
                break;
            }
        }
        if (lastSlashIndex == -1) {
            // 没有共同路径段，整个 source + target
            String strPath = pathSeparator + "";
            if (source.endsWith(strPath) && target.startsWith(strPath)) {
                return source + target.substring(1);
            } else if (!source.endsWith(strPath) && !target.startsWith(strPath)) {
                return source + pathSeparator + target;
            } else {
                return source + target;
            }
        }
        // 截取 target 中 source 之后的部分进行拼接
        String suffix = target.substring(lastSlashIndex + 1);
        return source + pathSeparator + suffix;
    }

    /**
     * @description 实体合并
     */
    public static void mergeEntity(Object sourceEntity , Object targetEntity, String... ignoreFields) {
        mergeEntity(sourceEntity, targetEntity, null, ignoreFields);
    }

    /**
     * @methodName: merge
     * @description: 合并实体，获得一个新的实体；
     *               sourceEntity 的值向targetEntity合并，
     *               若值不同，以targetEntity的值为准，
     *               若 targetEntity 值为null，则以 sourceEntity 的值为准
     *               若不希望合并某个值，设置 ignoreFields 属性名
     * @param sourceEntity 源实体
     * @param targetEntity 需要合并的目标实体
     * @return sourceEntity
     * @createtime 2021/10/11 14:05
     * <AUTHOR>
     * @version 1.0
     */
    public static void mergeEntity(Object sourceEntity , Object targetEntity, TriFunction<Field, Object, Object, Object> mergeFunction, String... ignoreFields) {
        Class<?> sourceClass = sourceEntity.getClass();
        Class<?> targetClass = targetEntity.getClass();
        try {
            List<Class<?>> sourceClassList = ListUtil.toList(sourceClass);
            sourceClass = sourceClass.getSuperclass();
            while (sourceClass != null && sourceClass != Object.class) {
                sourceClassList.add(sourceClass);
                sourceClass = sourceClass.getSuperclass();
            }
            do {
                List<FieldHandle> targetFields = RefUtil.getStaticAndNormalFields(targetClass);
                main: for (FieldHandle targetField : targetFields) {
                    if (useLoop(ignoreFields, targetField.getFieldName())) {
                        continue;
                    }
                    for (Class<?> sourceC : sourceClassList) {
                        try {
                            FieldHandle sourceField = RefUtil.getField(sourceC, targetField.getFieldName(), targetField.getFieldType());
                            Object sourceVal = sourceField.get(sourceEntity);
                            Object targetVal = targetField.get(targetEntity);
                            if (mergeFunction != null) {
                                targetField.set(targetEntity, mergeFunction.apply(targetField.field(), sourceVal, targetVal));
                            } else if (sourceVal != null && targetVal == null) {
                                targetField.set(targetEntity, sourceVal);
                            }
                            continue main;
                        } catch (Exception e) {
                            CommonLog.error("字段{}合并异常，原因：{}", targetField.getFieldName(), Throwables.getRootCause(e).getMessage());
                        }
                    }
                }
                targetClass = targetClass.getSuperclass();
            } while (targetClass != null && !"java.lang.object".equalsIgnoreCase(targetClass.getName()));
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * @description 判断数值是否存在在数组中
     * @param arr
     * @param targetValue
     * @return boolean
     * @createtime 2021/10/11 14:05
     * <AUTHOR>
     * @version 1.0
     */
    public static boolean useLoop(String[] arr, String targetValue) {
        for(String s: arr){
            if (s.equals(targetValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @description 将路径统一为 Unix 风格（使用 / 分隔符）
     * @param path
     * @createtime 2025/6/14 下午6:39
     * <AUTHOR>
     * @version 1.0
     */
    private static String normalizePath(String path) {
        if (path == null) {
            return "";
        }
        return path.replace("\\", "/");
    }

}
