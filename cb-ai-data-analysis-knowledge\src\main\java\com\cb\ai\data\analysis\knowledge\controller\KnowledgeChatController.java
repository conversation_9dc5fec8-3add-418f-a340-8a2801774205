package com.cb.ai.data.analysis.knowledge.controller;


import com.cb.ai.data.analysis.knowledge.constant.Constants;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeChat;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeChatService;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import java.util.Arrays;

/***
 * <AUTHOR>
 * 聊天
 */
@Validated
@RestController
@RequestMapping(Constants.API_KNOWLEDGE_ROOT_PATH + "/chat")
public class KnowledgeChatController {


    @Autowired
    private KnowledgeChatService knowledgeChatService;

    /***
     * 知识库问答
     * @param knowledgeChat
     * @return
     */
    @PostMapping(value = "/knowledge", produces ="text/event-stream;charset=UTF-8")
    public Flux<String> knowledge(@RequestBody KnowledgeChat knowledgeChat) {
        try{
            String resp=knowledgeChatService.knowledgeChat(knowledgeChat);
            return Flux.fromStream(Arrays.stream(resp.split("\n")));
        }catch (XServiceException e){
            e.printStackTrace();
            return null;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
        /*Flux.create(emitter -> {
            InputStream inputStream=knowledgeChatService.knowledgeChat(knowledgeChat);
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null && !emitter.isCancelled()) {
                    emitter.next(line);
                }
                emitter.complete();
            } catch (IOException e) {
                emitter.error(e);
            }catch (Exception e) {
                emitter.error(e);
            }
        });*/
    }

    /***
     * 基础问答
     * @param knowledgeChat
     * @return
     */
    @PostMapping(value = "/memory", produces ="text/event-stream;charset=UTF-8")
    public Flux<String> memory(@RequestBody  KnowledgeChat knowledgeChat) {
        try{
            String resp=knowledgeChatService.memoryChat(knowledgeChat);
            return Flux.fromStream(Arrays.stream(resp.split("\n")));
        }catch (XServiceException e){
            e.printStackTrace();
            return null;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
