package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.SsReportEntity;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsReportPageQueryQueryVo;
import com.cb.ai.data.analysis.petition.service.SsReportService;
import com.xong.boot.common.api.Result;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/***
 * <AUTHOR>
 * 信访报告
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/report")
public class SsReportController {

    @Resource
    private SsReportService ssReportService;


    @GetMapping("/page")
    public Result page(SsReportPageQueryQueryVo queryPageVo) {
        try{
            Page<SsReportEntity> ssReportEntityPage=ssReportService.selectByPage(queryPageVo);
            return Result.successData(ssReportEntityPage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询信访报告列表失败！");
        }
    }

    @DeleteMapping("/{reportId}")
    public Result del(@PathVariable("reportId") String id) {
        try{
            if(StringUtils.isBlank(id)){
                return Result.fail("报告ID不能为空！");
            }
            ssReportService.removeById(id);
            return Result.success("信访报告删除成功");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("信访报告删除失败！");
        }
    }
    @GetMapping("/download/{reportId}")
    public void download(@PathVariable String reportId, HttpServletResponse response){
        try {
            ssReportService.download(reportId, response);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/listByIds")
    public Result listByIds(@RequestBody List<String> ids){
        try{
            if(!CollectionUtils.isEmpty(ids)&&ids.size()>0){
                return Result.successData(ssReportService.listByIds(ids));
            }else{
                return Result.successData(null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取问题信息失败！");
        }
    }
    @PostMapping("/generate")
    public Result generate(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate,
            @RequestParam("fileName") String fileName,
            @RequestParam("types") List<String> types,
            @RequestParam("previousReportData") String previousReportData,
            @RequestParam("city") String city,
            @RequestParam("district") String district,
            @RequestParam("relateRepetitiveSize") Integer relateRepetitiveSize
    ) {
        try {
            return ssReportService.submitGenerate(files, startDate, endDate, fileName, types, previousReportData, city
                    , district, relateRepetitiveSize);
        } catch (IOException e) {
            return Result.fail("生成报告异常:" + e.getMessage());
        }
    }
}
