package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cb.ai.data.analysis.ai.mapper.SqlParserMapper;
import com.cb.ai.data.analysis.ai.service.ISqlParserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class SqlParserServiceImpl implements ISqlParserService {

    @Resource
    private SqlParserMapper sqlParserMapper;

    @Override
    public Map<String, Object> selectData(String sql) {
        return sqlParserMapper.selectData(sql);
    }

    @Override
    public IPage<Map<String, Object>> selectDataPage(IPage<?> page, String sql) {
        return sqlParserMapper.selectDataPage(page, sql);
    }

    @Override
    public List<Map<String, Object>> selectTableInfoList() {
        return sqlParserMapper.selectTableInfoList();
    }


}