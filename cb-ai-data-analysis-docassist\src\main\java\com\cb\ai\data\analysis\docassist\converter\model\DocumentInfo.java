package com.cb.ai.data.analysis.docassist.converter.model;

import lombok.Data;

import java.util.List;

/**
 * 公文内容信息
 *
 * <AUTHOR>
 */
@Data
public class DocumentInfo {
    /**
     * 标题中是否包含印发
     */
    private boolean isPrintSend;
    /**
     * 公文类型
     */
    private String type;
//    /**
//     * 包含的段落位置
//     */
//    private List<Integer> inPos;
    /**
     * 红头
     */
    private DocRedhead redhead;
    /**
     * 标题
     */
    private List<DocTitle> titles;
//    /**
//     * 公文主标题
//     */
//    private DocMainTitle mainTitle;
//    /**
//     * 公文主标题下附注
//     */
//    private DocMainTitleAnnotation mainTitleAnnotation;
//    /**
//     * 公文主送机关
//     */
//    private DocMainDelivery mainDelivery;
//    /**
//     * 公文贯彻目标
//     */
//    private DocImplement implement;
//    /**
//     * 公文目的
//     */
//    private DocPurpose purpose;
//    /**
//     * 公文标题
//     */
//    private DocBodyTitle bodyTitle;
    /**
     * 公文发文机构署名
     */
    private DocOrgName orgName;
    /**
     * 公文成文时间
     */
    private DocCompleteDate completeDate;
//    /**
//     * 公文附注
//     */
//    private DocAnnotation annotation;
//    /**
//     * 公文附件信息
//     */
//    private DocAttachInfo attachInfo;
//    /**
//     * 公文会议人员信息
//     */
//    private DocMeetingPeople meetingPeople;

//    public void addInPos(Integer pos) {
//        if (this.inPos == null) {
//            this.inPos = new ArrayList<>();
//        }
//        this.inPos.add(pos);
//    }
//
//    public void addInPos(BaseDoc doc) {
//        if (doc == null || doc.getStart() == null) {
//            return;
//        }
//        if (this.inPos == null) {
//            this.inPos = new ArrayList<>();
//        }
//        for (int i = doc.getStart(); i <= doc.getEnd(); i++) {
//            this.inPos.add(i);
//        }
//    }
}
