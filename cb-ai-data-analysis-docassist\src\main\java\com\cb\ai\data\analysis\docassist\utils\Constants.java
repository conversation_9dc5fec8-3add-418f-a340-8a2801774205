package com.cb.ai.data.analysis.docassist.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * yml配置前缀
     */
    public static final String YML_PREFIX = "docassist";
    /**
     * 授权头
     */
    public static final String HEADER_X_AUTH_TOKEN = "X-Auth-Token";
    /**
     * token 令牌秘钥
     * 默认 md5("xong-boot")
     */
    public static final String TOKEN_SECRET_KEY = SecureUtil.md5("xong-doc-assist");
    /**
     * sm2加密私钥
     */
    public static final String PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgRAzY4aqIw48fGPOgYmI9+estpwt2XSQH5wfKijxox2KgCgYIKoEcz1UBgi2hRANCAASPtvLWsx0qw8awHPWMROBcCKLt63J9Ux7SsM3Og6wlI11qkLPIa/nPdt3Y58bHzsOwGFFwQ0PTNgqt8e11wMV3";
    /**
     * sm2加密公钥
     */
    public static final String PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEj7by1rMdKsPGsBz1jETgXAii7etyfVMe0rDNzoOsJSNdapCzyGv5z3bd2OfGx87DsBhRcEND0zYKrfHtdcDFdw==";
    /**
     * 资源路径
     */
    public static final String TEMP_RES_PATH = "/cb/docassist";

    /**
     * 鸿文校对版本——服务器版
     */
    public static final String HW_CHECK_VERSION_SERVER = "server";
    /**
     * 鸿文校对版本——桌面版
     */
    public static final String HW_CHECK_VERSION_DESKTOP = "desktop";

    /**
     * 客户端资源路径
     */
    public static final String APP_RES_PATH = "/CB/docassist";

    /**
     * SM4 key
     */
    public static final String SM4_KEY = "F4119619F4B76C53CDDE688071229D86";

    /**
     * 客户端设备唯一ID
     */
    public static final String APP_GUID = "GUID";

    /**
     * 获取临时资源路径
     *
     * @param docId 文件ID
     */
    public static Path getTempPath(String docId) {
        FileUtil.mkdir(Paths.get(FileUtil.getTmpDirPath(), TEMP_RES_PATH));
        return Paths.get(FileUtil.getTmpDirPath(), TEMP_RES_PATH, docId);
    }
}
