//package com.cb.ai.data.analysis.petition.service.impl;
//
//
//import cn.hutool.core.util.IdUtil;
//import com.alibaba.fastjson2.JSONObject;
//import com.cb.ai.data.analysis.petition.service.SsPetitionService;
//import com.xong.boot.common.api.Result;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.nio.file.Files;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//
//
//@Service
//public class SsPetitionServiceImpl implements SsPetitionService {
//
//
//    @Override
//    public Result aiAnalysis(String[] fileId) throws IOException {
//        StringBuilder errorMessage = new StringBuilder();
//        for (String fileId : fileIds) {
//            RawFileEntity rawFile = rawFileMapper.selectOneById(fileId);
//            if (rawFile == null) {
//                throw new RuntimeException("未找到对应的文件");
//            }
//            String url = rawFile.getFileDir() + rawFile.getUrl().split("upload")[1];
//            File file = new File(url);
//            if (file.exists()) {  // 检查文件是否存在
//                final String contentType = Files.probeContentType(file.toPath());
//                final String originalFilename = file.getName();
//
//                try (InputStream inputStream = new FileInputStream(file)) {
//                    // 解析 Excel
//                    Optional<FileContentTypeEnum> fileTypeOptional = Arrays.stream(FileContentTypeEnum.values()).filter(fileContentTypeEnum -> fileContentTypeEnum.getContentType().equals(contentType)).findAny();
//                    if (fileTypeOptional.isPresent()) {
//                        rawFileMapper.updateAnalysisStatus(fileId, FileAnalysisStatus.INPROCESS.getCode());
//                        if (fileTypeOptional.get().getContentType().equals(FileContentTypeEnum.XLS.getContentType()) || fileTypeOptional.get().getContentType().equals(FileContentTypeEnum.XLSX.getContentType())) {
//                            ExcelReaderHandler excelReaderHandler = (ExcelReaderHandler) readerFactory.getReaderByContentType(contentType);
//                            Map<String, List<JSONObject>> dataMap = excelReaderHandler.read(inputStream);
//
//                            if (CollectionUtils.isEmpty(dataMap)) {
//                                return AjaxResult.error("处理失败：excel内容为空或未解析到数据");
//                            }
//                            Long batchNo = IdUtil.getSnowflakeNextId();
//
//                            Integer totalDataSize = 0;
//                            for (Map.Entry<String, List<JSONObject>> entry : dataMap.entrySet()) {
//
//                                List<JSONObject> dataList = entry.getValue();
//                                Integer dataSize = handleExcel(dataList, originalFilename, contentType, batchNo);
//
//                                totalDataSize += dataSize;
//                            }
//                            //return AjaxResult.success("成功入库" + totalDataSize + "条文件", String.valueOf(batchNo));
//                        } else {
//                            Long batchNo = IdUtil.getSnowflakeNextId();
//                            handleText(originalFilename, contentType, inputStream, batchNo);
//                            //return AjaxResult.success("成功入库1条文件");
//                        }
//                        //后期考虑任务中处理
//                        rawFileMapper.updateAnalysisStatus(fileId, FileAnalysisStatus.COMPLETED.getCode());
//                    } else {
//                        String message = "文件【%s】解析excel失败,只支持 .xlsx / .xls / .doc / .docx / .pdf 格式文件 \n";
//                        errorMessage.append(String.format(message, originalFilename));
//                    }
//                } catch (Exception e) {
//                    String message = "文件【%s】上传失败,异常信息:%s \n";
//                    errorMessage.append(String.format(message, originalFilename, e.getMessage()));
//                    e.printStackTrace();
//                }
//            } else {
//                String message = "文件【%s】上传失败,未找到文件 \n";
//                errorMessage.append(String.format(message, rawFile.getOriginFileName()));
//            }
//        }
//        if (errorMessage.length() > 0) {
//            return AjaxResult.error(errorMessage.toString());
//        } else {
//            return AjaxResult.success("文件解析到信访库提交成功");
//        }
//    }
//}
