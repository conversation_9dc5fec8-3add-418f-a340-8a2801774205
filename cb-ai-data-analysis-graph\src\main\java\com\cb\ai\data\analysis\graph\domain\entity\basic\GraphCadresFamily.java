package com.cb.ai.data.analysis.graph.domain.entity.basic;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识图谱-干部亲属表(GraphCadresFamily)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-04 09:30:18
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class GraphCadresFamily extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //工作单位
    @TableField(condition = SqlCondition.LIKE)
    @ExcelIgnore
    private String workUnit;

    //姓名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "姓名")
    private String name;

    //身份证号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "身份证号")
    private String idCard;

    //亲属姓名
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "亲属姓名")
    private String familyName;

    //亲属关系
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "亲属关系")
    private String familyRelation;

    //亲属身份证号
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "亲属身份证号")
    private String familyIdCard;

    //会话临时解析数据
    @ExcelIgnore
    private String sessionId;

}

