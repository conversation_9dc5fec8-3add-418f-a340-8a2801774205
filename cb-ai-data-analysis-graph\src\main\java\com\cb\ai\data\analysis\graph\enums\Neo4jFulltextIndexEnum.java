package com.cb.ai.data.analysis.graph.enums;

import lombok.Getter;

/**
 * @Description: neo4j全文索引枚举
 * @Author: ARPHS
 * @Date: 2025-05-07 16:24
 * @Version: 1.0
 **/
@Getter
public enum Neo4jFulltextIndexEnum {
    ENTERPRISE_INDEX("企业索引", "企业", new String[]{"name", "曾用名", "公司类型", "所属省份", "所属城市", "所属区县", "登记状态"}),
    PERSON_INDEX("人员索引", "人员", new String[]{"name"}),
    LEADER_INDEX("干部索引", "干部", new String[]{"name", "现职务", "工作单位", "籍贯", "出生地"});

    private String indexName;
    private String label;
    private String[] props;

    Neo4jFulltextIndexEnum(String indexName, String label, String[] props) {
        this.indexName = indexName;
        this.label = label;
        this.props = props;
    }

}
