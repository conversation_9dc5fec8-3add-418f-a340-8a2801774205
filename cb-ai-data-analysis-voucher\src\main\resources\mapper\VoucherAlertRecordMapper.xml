<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.voucher.mapper.VoucherAlertRecordMapper">

    <resultMap type="com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord" id="VoucherAlertRecordMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="voucherId" column="voucher_id" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap type="com.cb.ai.data.analysis.voucher.domain.vo.VoucherAlertRecordVo$RespItem" id="PageMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="voucherId" column="voucher_id" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>

        <result property="taskName" column="taskName" jdbcType="VARCHAR"/>
        <result property="voucherName" column="voucherName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="SelectSQL" >
        select t.id,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.remark,
               t.task_id,
               t.voucher_id,
               t.content,
               t1.name as voucherName,
               t2.name as taskName
        from voucher_alert_record t
        left join voucher_info t1 on t.voucher_id = t1.id
        left join voucher_task t2 on t.task_id = t2.id
    </sql>

    <select id="pageByEntity" resultMap="PageMap" >
        <include refid="SelectSQL"></include>
        <where>
            <if test="et.taskId != null and et.taskId != ''">
                and t.task_id = #{et.taskId}
            </if>
            <if test="et.voucherId != null and et.voucherId != ''">
                and t.voucher_id = #{et.voucherId}
            </if>
            <if test="et.content != null and et.content != ''">
                and t.content like concat('%',#{et.content},'%')
            </if>
            <if test="et.voucherName != null and et.voucherName != ''">
                and t1.name like concat('%',#{et.voucherName},'%')
            </if>
            <if test="et.taskName != null and et.taskName != ''">
                and t2.name like concat('%',#{et.taskName},'%')
            </if>
        </where>
    </select>

    <select id="detail" resultMap="PageMap">
        <include refid="SelectSQL"></include>
        where t.id = #{id}
    </select>

    <select id="detailByVoucherId" resultMap="PageMap" >
        <include refid="SelectSQL"></include>
        where t.voucher_id = #{voucherId}
        and t.task_id is null
    </select>

</mapper>

