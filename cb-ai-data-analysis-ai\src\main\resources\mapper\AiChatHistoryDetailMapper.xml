<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.ai.mapper.AiChatHistoryDetailMapper">
    <resultMap type="com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail" id="AiChatHistoryDetailMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
        <result property="event" column="event" jdbcType="VARCHAR"/>
        <result property="reasoningContent" column="reasoning_content" jdbcType="NCLOB"/>
        <result property="content" column="content" jdbcType="NCLOB"/>
        <result property="dataList" column="data_list" jdbcType="NCLOB"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

