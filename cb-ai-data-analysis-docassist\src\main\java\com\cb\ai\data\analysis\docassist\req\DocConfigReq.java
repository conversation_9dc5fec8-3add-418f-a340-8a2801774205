package com.cb.ai.data.analysis.docassist.req;

import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import lombok.Data;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;

/**
 * 公文配置请求
 *
 * <AUTHOR>
 */
@Data
public class DocConfigReq {
    /**
     * 字体
     */
    private String fontName;
    /**
     * 其他字体
     */
    private String otherFontName;
    /**
     * 字体大小
     */
    private Float fontSize;
    /**
     * 行间距类型
     */
    private Integer lineSpacingRule;
    /**
     * 行间距值
     */
    private Float lineSpacing;
    /**
     * 标题字体
     */
    private String titleFontName;
    /**
     * 标题字体大小
     */
    private Float titleFontSize;
    /**
     * 标题行间距类型
     */
    private Integer titleLineSpacingRule;
    /**
     * 标题行间距值
     */
    private Float titleLineSpacing;
    /**
     * 一级标题字体
     */
    private String titleFontName1;
    /**
     * 二级标题字体
     */
    private String titleFontName2;
    /**
     * 三级标题字体
     */
    private String titleFontName3;
    /**
     * 四级标题字体
     */
    private String titleFontName4;
    /**
     * 附件头字体
     */
    private String attachHeadFontName;
    /**
     * 排版后是否替换源文件
     */
    private Boolean formatReplaceOriginal;
    /**
     * 排版表格宽度
     */
    private Boolean formatTableWidth;
    /**
     * 检查标点符号
     */
    private Boolean checkSymbol;
    /**
     * 删除超链接
     */
    private Boolean deleteHyperlink;

    /**
     * 孤寒控制
     */
    private boolean widowControl;

    public DocConfig getDocConfig() {
        DocConfig docConfig = new DocConfig();
        docConfig.setFontName(fontName);
        docConfig.setOtherFontName(otherFontName);
        docConfig.setFontSize(fontSize * 2);
        if (lineSpacingRule == 3) {
            docConfig.setLineSpacing(STLineSpacingRule.AT_LEAST);
        } else {
            docConfig.setLineSpacingRule(STLineSpacingRule.EXACT);
        }
        docConfig.setLineSpacing(lineSpacing * 20);
        docConfig.setTitleFontName(titleFontName);
        docConfig.setTitleFontSize(titleFontSize * 2);
        if (titleLineSpacingRule == 3) {
            docConfig.setTitleLineSpacingRule(STLineSpacingRule.AT_LEAST);
        } else {
            docConfig.setTitleLineSpacingRule(STLineSpacingRule.EXACT);
        }
        docConfig.setTitleLineSpacing(titleLineSpacing * 20);
        docConfig.setSerialNumber1(titleFontName1);
        docConfig.setSerialNumber2(titleFontName2);
        docConfig.setSerialNumber3(titleFontName3);
        docConfig.setSerialNumber4(titleFontName4);
        docConfig.setAttachHeadFontName(attachHeadFontName); // 设置附件头字体
        docConfig.setFormatReplaceOriginal(formatReplaceOriginal);
        docConfig.setFormatTableWidth(formatTableWidth);
        docConfig.setCheckSymbol(checkSymbol);
        docConfig.setDeleteHyperlink(deleteHyperlink);
        docConfig.setWidowControl(widowControl);
        return docConfig;
    }
}
