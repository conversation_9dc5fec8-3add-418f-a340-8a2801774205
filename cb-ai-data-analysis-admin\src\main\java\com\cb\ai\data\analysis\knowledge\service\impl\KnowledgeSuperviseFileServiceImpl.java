package com.cb.ai.data.analysis.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.cb.ai.data.analysis.knowledge.domain.KnowledgeSuperviseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFileReq;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeFileService;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeSuperviseFileService;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件选择实体
 */
@Service
public class KnowledgeSuperviseFileServiceImpl implements KnowledgeSuperviseFileService {

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;

    @Autowired
    private SuperviseResourceFolderService superviseResourceFolderService;


    @Autowired
    private KnowledgeFileService knowledgeFileService;


    /***
     * 根据文件ID，查询文件类别，含文件夹和文件
     * @param folderId
     * @return
     * @throws Exception
     */
    @Override
    public List<KnowledgeSuperviseFile> getKnowledgeSelectFile(String folderId) throws Exception {
        try{
            if(StringUtils.isBlank(folderId)){
                throw new XServiceException("文件ID不能为空");
            }
            List<KnowledgeSuperviseFile> resultFileList=new ArrayList<>();
            String username = SecurityUtils.getUsername();
            LambdaQueryWrapper<SuperviseResourceFolder> folderLambda = new LambdaQueryWrapper<>();
            folderLambda.eq(SuperviseResourceFolder::getCreateBy, username);
            folderLambda.eq(SuperviseResourceFolder::getParentId,folderId);
            folderLambda.orderByAsc(SuperviseResourceFolder::getSortOn);
            List<SuperviseResourceFolder> folderList = superviseResourceFolderService.list(folderLambda);
            for(SuperviseResourceFolder folder:folderList){
                KnowledgeSuperviseFile folderFile=new KnowledgeSuperviseFile();
                folderFile.setKey(folder.getId());
                folderFile.setTitle(folder.getFolderName());
                folderFile.setIsSelected(false);
                folderFile.setIsDir(true);
                folderFile.setFileSuffix("dir");
                resultFileList.add(folderFile);
            }
            // 不是跟目录才查询文件
            if(!StringUtils.equals(folderId,"0")){
                LambdaQueryWrapper<SuperviseResourceFile> fileLambda = new LambdaQueryWrapper<>();
                fileLambda.eq(SuperviseResourceFile::getFolderId, folderId);
                fileLambda.orderByAsc(SuperviseResourceFile::getSortOn);
                List<SuperviseResourceFile> fileList = superviseResourceFileService.list(fileLambda);
                for(SuperviseResourceFile file:fileList){
                    KnowledgeSuperviseFile fileFile=new KnowledgeSuperviseFile();
                    fileFile.setKey(file.getId());
                    fileFile.setTitle(file.getFilename());
                    fileFile.setIsSelected(false);
                    fileFile.setIsDir(false);
                    fileFile.setFileSuffix(file.getFileSuffix());
                    resultFileList.add(fileFile);
                }
            }
            return resultFileList;
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public void superviseSaveUpload(KnowledgeFileReq KnowledgeFile) throws Exception {
        try{
            if(StringUtils.isBlank(KnowledgeFile.getBaseId())){
                throw new XServiceException("知识库ID不能为空不能为空");
            }
            if(CollectionUtils.isEmpty(KnowledgeFile.getFileIds()) && CollectionUtils.isEmpty(KnowledgeFile.getFolderIds())){
                throw new XServiceException("所选文件ID不能为空！");
            }
            KnowledgeBaseFile knowledgeBaseFile=new KnowledgeBaseFile();
            knowledgeBaseFile.setBaseId(KnowledgeFile.getBaseId());

            List<KnowledgeFile> knowledgeFileList=new ArrayList<>();
            if(!CollectionUtils.isEmpty(KnowledgeFile.getFileIds())){
                LambdaQueryWrapper<SuperviseResourceFile> fileLambda = new LambdaQueryWrapper<>();
                fileLambda.in(SuperviseResourceFile::getId, KnowledgeFile.getFileIds());
                List<SuperviseResourceFile> fileList = superviseResourceFileService.list(fileLambda);
                for(SuperviseResourceFile file:fileList){
                    KnowledgeFile knowledgeFile=new KnowledgeFile();
                    knowledgeFile.setFileName(file.getFilename());
                    knowledgeFile.setFileUrl(file.getFilePath());
                    knowledgeFile.setFileId(file.getId());
                    knowledgeFile.setBaseId(KnowledgeFile.getBaseId());
                    knowledgeFileList.add(knowledgeFile);
                }
            }
            if(!CollectionUtils.isEmpty(KnowledgeFile.getFolderIds())){
                LambdaQueryWrapper<SuperviseResourceFile> fileLambda = new LambdaQueryWrapper<>();
                fileLambda.in(SuperviseResourceFile::getFolderId, KnowledgeFile.getFolderIds());
                List<SuperviseResourceFile> fileList = superviseResourceFileService.list(fileLambda);
                for(SuperviseResourceFile file:fileList){
                    KnowledgeFile knowledgeFile=new KnowledgeFile();
                    knowledgeFile.setFileName(file.getFilename());
                    knowledgeFile.setFileUrl(file.getFilePath());
                    knowledgeFile.setFileId(file.getId());
                    knowledgeFile.setBaseId(KnowledgeFile.getBaseId());
                    knowledgeFileList.add(knowledgeFile);
                }
            }
            knowledgeBaseFile.setFileList(knowledgeFileList);
            knowledgeFileService.saveupload(knowledgeBaseFile);
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }
}
