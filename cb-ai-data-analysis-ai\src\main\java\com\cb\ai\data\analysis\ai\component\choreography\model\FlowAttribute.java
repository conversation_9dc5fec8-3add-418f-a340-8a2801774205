package com.cb.ai.data.analysis.ai.component.choreography.model;

import cn.hutool.core.util.IdUtil;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/19 16:08
 * @Copyright (c) 2025
 * @Description 流程属性
 */
@Data
@Accessors(chain = true)
public class FlowAttribute {
    /* 追踪日志Id */
    private String traceId;

    /* 流程ID */
    private String flowId;

    /* 流程名称 */
    private String flowName;

    /* 流程描述 */
    private String flowDesc;

    /* 节点请求上下文 */
    private FlowContext flowContext;

    /* 节点是否在流程链中 */
    private boolean isInFlowChain;

    public FlowAttribute() {
        this.traceId = IdUtil.randomUUID();
        this.flowId = IdUtil.randomUUID();
        this.flowContext = new FlowContext(0);
    }

}
