package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

@Data
@TableName("ss_problem_market_classify")
public class SsProblemMarketClassifyEntity extends BaseDomain implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /***
     * 文件名称
     */
    private String  fileName;

    /***
     * 问题标题
     */
    private String  problemTitle;

    /**
     * 问题分类
     */
    private String  problemClassify;

    /***
     * 问题数量
     */
    private Integer  problemDataCount;

}
