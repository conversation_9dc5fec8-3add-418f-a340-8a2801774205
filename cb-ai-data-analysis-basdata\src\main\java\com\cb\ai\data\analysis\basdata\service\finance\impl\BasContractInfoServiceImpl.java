package com.cb.ai.data.analysis.basdata.service.finance.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasContractInfo;
import com.cb.ai.data.analysis.basdata.mapper.finance.BasContractInfoMapper;
import com.cb.ai.data.analysis.basdata.repository.finance.BasContractInfoRepository;
import com.cb.ai.data.analysis.basdata.repository.finance.esBo.BasContractInfoBo;
import com.cb.ai.data.analysis.basdata.service.finance.BasContractInfoService;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同信息表(BasContractInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14 21:55:22
 */
@Service("basContractInfoService")
public class BasContractInfoServiceImpl extends ServiceImpl<BasContractInfoMapper, BasContractInfo> implements BasContractInfoService {

    @Autowired
    private BasContractInfoRepository repository;

    @Override
    public boolean save(BasContractInfo info) {
        int insert = baseMapper.insert(info);
        if (insert > 0) {
            BasContractInfoBo bo = convert2Bo(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateById(BasContractInfo info) {
        int update = baseMapper.updateById(info);
        if (update > 0) {
            BasContractInfoBo bo = convert2Bo(info);
            repository.save(bo);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        int delete = baseMapper.deleteByIds(ids);
        if (delete > 0) {
            repository.deleteAllById(ids);
            return true;
        }
        return false;
    }

    @Override
    public boolean importExcel(List<BasContractInfo> list) {
        boolean b = this.saveBatch(list);
        if (b) {
            List<BasContractInfoBo> collect = list.stream()
                    .map(item -> convert2Bo(item))
                    .collect(Collectors.toList());
            repository.saveAll(collect);
        }
        return b;
    }

    private BasContractInfoBo convert2Bo(BasContractInfo entity) {
        BasContractInfoBo bo = new BasContractInfoBo();
        BeanUtils.copyProperties(entity, bo);
        bo.setDeptId(SecurityUtils.getDeptId());
        bo.setDistrictId(SecurityUtils.getDistrictId());
        return bo;
    }


}

