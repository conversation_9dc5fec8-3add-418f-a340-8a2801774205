package com.cb.ai.data.analysis.ai.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/15 20:48
 * @Copyright (c) 2025
 * @Description WebClient配置类
 */
public class WebClientConfig {

    public static WebClient getWebClient() {
        // 设置超时（设置10秒）
        HttpClient httpClient = HttpClient.create()
            .wiretap(true)
            .responseTimeout(Duration.ofMinutes(15)) // 响应超时
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 15 * 60 * 1000)
            // 连接超时设置
            .doOnConnected(conn ->
                // 读取超时设置
                conn.addHandlerLast(new ReadTimeoutHandler(900))
                // 写超时设置
                .addHandlerLast(new WriteTimeoutHandler(900))
            ); // 添加重试机制;

        // 配置自定义的WebClient.Builder
        return  WebClient.builder().clientConnector(new ReactorClientHttpConnector(httpClient))
            // 不设置缓冲区大小
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(-1))
            // 设置默认的认证，避免框架自动认证
            .defaultHeader(HttpHeaders.AUTHORIZATION, "1")
            .build();
    }
}
