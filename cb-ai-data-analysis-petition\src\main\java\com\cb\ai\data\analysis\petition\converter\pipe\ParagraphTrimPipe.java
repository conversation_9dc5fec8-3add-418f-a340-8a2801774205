package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 清除段落前后空白符
 * <AUTHOR>
 */
public class ParagraphTrimPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText();
        if (ReUtil.contains("^[  　]+", text)) {
            return true;
        }
        return ReUtil.contains("[  　]+$", text);
    }

    @Override
    public boolean isBreak() {
        return false;
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<XWPFRun> runs = paragraph.getRuns();
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String text = run.text();
            if (text.length() == 0) {
                continue;
            }
            if (i == 0) {
                run.setText(ReUtil.replaceAll(text, "^[  　]+", ""), 0);
                continue;
            }
            if (i == runs.size() - 1) {
                run.setText(ReUtil.replaceAll(text, "[  　]+$", ""), 0);
            }
        }
    }
}
