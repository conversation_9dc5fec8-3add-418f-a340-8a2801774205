package com.cb.ai.data.analysis.graph.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 关系图谱-上传文件记录(RgFileRecord)实体类
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class GraphFileRecord extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    //文件名
    private String fileName;

    //文件id
    private String fileId;

    //处理状态 0-待解析 1-解析中 2-已入库 3-解析失败
    private Integer status;

    //错误日志
    private String errLog;

    //总数
    private Integer totalCount;

    //已处理数
    private Integer processedCount;
}

