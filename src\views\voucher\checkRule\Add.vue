<template>
  <a-modal
    :confirm-loading="loading"
    :open="visible"
    title="新增凭证检测规则"
    width="60%"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form ref="formRef" :label-col="{ style: { width: '120px' } }" :model="formData">
      <a-form-item
        :rules="{ message: '必须填写规则名称', required: true }"
        label="规则名称"
        name="name"
      >
        <a-input
          v-model:value="formData.name"
          :maxlength="100"
          placeholder="请输入规则名称"
        />
      </a-form-item>
      
      <a-form-item
        label="规则说明"
        name="description"
      >
        <a-textarea
          v-model:value="formData.description"
          :maxlength="500"
          :rows="3"
          placeholder="请输入规则说明"
        />
      </a-form-item>
      
      <a-form-item
        :rules="[
          { message: '必须填写提示词', required: true },
          { message: '提示词长度不能少于10个字符', min: 10 }
        ]"
        label="提示词"
        name="promote"
      >
        <a-textarea
          v-model:value="formData.promote"
          :maxlength="2000"
          :rows="8"
          placeholder="请输入规则提示词，用于AI分析时的指导，至少10个字符"
          show-count
        />
      </a-form-item>
      
      <a-form-item
        label="状态"
        name="disable"
      >
        <a-radio-group v-model:value="formData.disable">
          <a-radio :value="0">启用</a-radio>
          <a-radio :value="1">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts" name="VoucherCheckRuleAdd">
import { type ComponentCustomProperties, getCurrentInstance, ref } from 'vue'
import { checkRule } from '@/api/voucher'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible', 'success'])
const props = defineProps<{
  visible: boolean
}>()

const loading = ref(false)
const formRef = ref()
const formData = ref({
  name: '',
  description: '',
  promote: '',
  disable: 0
})

function onCancel() {
  formRef.value?.resetFields()
  formData.value = {
    name: '',
    description: '',
    promote: '',
    disable: 0
  }
  emits('update:visible', false)
}

function onOk() {
  _this.$form.validate(formRef.value, async (errors, values) => {
    if (errors) {
      return false
    }
    try {
      loading.value = true
      const { message } = await checkRule.add(values)
      _this.$message.success(message || '新增成功')
      emits('success', true)
      onCancel()
    } catch (error: any) {
      _this.$message.error(error.message || '新增失败')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style scoped lang="less">
.ant-form-item {
  margin-bottom: 16px;
}
</style>
