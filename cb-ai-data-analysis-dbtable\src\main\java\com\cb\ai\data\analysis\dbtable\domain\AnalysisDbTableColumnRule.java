package com.cb.ai.data.analysis.dbtable.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xong.boot.common.domain.SimpleBaseDomain;
import com.xong.boot.common.valid.AddGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 动态数据表字段规则
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalysisDbTableColumnRule extends SimpleBaseDomain {
    /**
     * ID
     */
    @TableId
    private String id;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 字段名
     */
    @NotBlank(message = "字段名不存在", groups = AddGroup.class)
    private String columnName;
    /**
     * 校验文案
     */
    @NotBlank(message = "校验文案不存在", groups = AddGroup.class)
    private String ruleMessage;
    /**
     * 校验表达式
     */
    @NotBlank(message = "校验表达式不存在", groups = AddGroup.class)
    private String rulePattern;
    /**
     * 触发时机
     */
    private String ruleTrigger;
    /**
     * 排序
     */
    private Integer sortOn;
}
