package com.cb.ai.data.analysis.ai.domain.request.context;

import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.common.MinioFileData;
import com.cb.ai.data.analysis.ai.domain.common.MultiFileData;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:19
 * @Copyright (c) 2025
 * @Description 通用AI请求上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonAIRequestContext extends AIRequestContext {
    /** 分析标签 **/
    private String[] analyseTag;

    /** 知识库Id **/
    private String[] baseIds;

    /** 知识库文件Id **/
    private String[] fileIds;

    /** 知识库对应名称 **/
    private String[] baseNames;

    /** 上传的文件数据 **/
    private MultiFileData fileData;

    /** 入到minio后的文件数据 **/
    private List<MinioFileData> minioFileDataList;

    /** 扩展的数据（放置处理已定义的参数和个性化参数以外的额外参数） **/
    private JsonMap extendData;

    public CommonAIRequestContext() {
        this.extendData = new JsonMap();
    }

    /**
     * 处理扩展属性
     */
    @JsonAnySetter
    public void handleExtendProperty(String key, Object value) {
        extendData.putOnce(key, value);
    }
}
