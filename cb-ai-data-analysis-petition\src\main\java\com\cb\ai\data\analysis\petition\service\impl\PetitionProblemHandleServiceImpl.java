package com.cb.ai.data.analysis.petition.service.impl;


import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleResultEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionProblemHandleVo;
import com.cb.ai.data.analysis.petition.mapper.PetitionProblemHandleMapper;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleResultService;
import com.cb.ai.data.analysis.petition.service.PetitionProblemHandleService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.concurrent.ExecutorService;


/***
 * <AUTHOR>
 * 问题批处理
 */
@Service
public class PetitionProblemHandleServiceImpl extends BaseServiceImpl<PetitionProblemHandleMapper, PetitionProblemHandleEntity> implements PetitionProblemHandleService {

    @Resource
    private PetitionProblemHandleMapper petitionProblemHandleMapper;

    @Resource
    private PetitionProblemHandleResultService petitionProblemHandleResultService;

//    @Resource(name = "problemFileThreadPoolExecutor")
//    private ExecutorService problemFileThreadPoolExecutor;

    @Override
    public Page<PetitionProblemHandleEntity> selectByPage(PetitionProblemHandleVo petitionProblemHandle) {
        Page<PetitionProblemHandleEntity> page = new Page<>(petitionProblemHandle.getPageNo(), petitionProblemHandle.getPageSize());
        QueryWrapper<PetitionProblemHandleEntity> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(petitionProblemHandle.getSearchKey())){
            queryWrapper.like("file_name",petitionProblemHandle.getSearchKey());
        }
        if(!ObjectUtils.isEmpty(petitionProblemHandle.getHandleStatus())){
            queryWrapper.eq("handle_status",petitionProblemHandle.getHandleStatus());
        }
        queryWrapper.orderByDesc("create_time");
        return petitionProblemHandleMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delPetitionProblemHandle(String id) throws Exception {
        try{
            if(com.xong.boot.common.utils.StringUtils.isEmpty(id)){
                throw new XServiceException("请选择要删除的记录！");
            }
            if(this.baseMapper.deleteById(id)<=0){
                throw new XServiceException("删除问题记录失败！");
            }
            PetitionProblemHandleResultEntity resultEntity=new PetitionProblemHandleResultEntity();
            resultEntity.setProblemId(id);
            LambdaQueryWrapper<PetitionProblemHandleResultEntity> delWrapper = new LambdaQueryWrapper<>();
            delWrapper.eq(PetitionProblemHandleResultEntity::getProblemId, id);
            petitionProblemHandleResultService.remove(delWrapper);
            return 1;
        }catch (XServiceException e){
            e.printStackTrace();
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception(e.getMessage());
        }
    }

//    @Override
//    public int saveProblemHandleFile(List<PetitionProblemHandleEntity> problemHandleFileList) throws Exception {
//        try{
//            if(CollectionUtils.isEmpty(problemHandleFileList)){
//                throw new XServiceException("请求信息为空！");
//            }
//            for(PetitionProblemHandleEntity file:problemHandleFileList){
//                file.setId(IdUtil.getSnowflakeNextIdStr());
//                file.setHandleStatus(0);
//            }
//            baseMapper.insert(problemHandleFileList);
//            return 1;
//        }catch (XServiceException e){
//            e.printStackTrace();
//            throw new XServiceException(e.getMessage());
//        }catch (Exception e){
//            e.printStackTrace();
//            throw new Exception(e.getMessage());
//        }
//    }

}
