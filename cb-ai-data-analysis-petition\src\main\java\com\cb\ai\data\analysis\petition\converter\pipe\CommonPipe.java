package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 其他段落
 * <AUTHOR>
 */
public class CommonPipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        if (ReUtil.contains("[ ]{5,}", text) && pos < 10) {
            return false;
        }
        for (XWPFRun run : paragraph.getRuns()) {
            String color = run.getColor();
            if (StringUtils.isNotBlank(color) && color.equalsIgnoreCase("FF0000")) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        List<XWPFRun> runs = paragraph.getRuns();
        FormatTools.formatParagraphInd(paragraph, config);
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
