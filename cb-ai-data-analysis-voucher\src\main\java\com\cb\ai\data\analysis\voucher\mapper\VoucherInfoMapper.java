package com.cb.ai.data.analysis.voucher.mapper;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherInfoVo;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

public interface VoucherInfoMapper extends BaseMapper<VoucherInfo> {

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    Page<VoucherInfo> pageByEntity(Page<VoucherInfo> page, @Param(Constants.ENTITY) VoucherInfoVo.PageReq req);

    /**
     * 任务分析统计总数（按标签和日期统计）
     * @param tags
     * @param now
     * @return
     */
    long countByTagsAndDate(@Param("tags") String[] tags,
                            @Param("now") LocalDateTime now);

    /**
     * 分页查询凭证信息
     * @param page
     * @param tags
     * @param now 当前时间，只要创建时间小于当前的
     * @return
     */
    Page<VoucherInfo> pageByTagsAndDate(Page<VoucherInfo> page, @Param("tags") String[] tags, @Param("now") LocalDateTime now);


}
