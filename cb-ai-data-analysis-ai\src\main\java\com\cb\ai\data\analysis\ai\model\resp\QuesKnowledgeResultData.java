package com.cb.ai.data.analysis.ai.model.resp;

import com.cb.ai.data.analysis.ai.model.AiResultData;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库问答返回
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class QuesKnowledgeResultData extends AiResultData {
    /**
     * 序号
     */
    private Integer index;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 参考文献
     */
    private String references;
}
