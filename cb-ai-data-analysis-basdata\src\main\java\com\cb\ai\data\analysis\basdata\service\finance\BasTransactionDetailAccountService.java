package com.cb.ai.data.analysis.basdata.service.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.analysis.basdata.domain.entity.finance.BasTransactionDetailAccount;

import java.util.List;

/**
 * 交易明细账表(BasTransactionDetailAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14 16:32:32
 */
@DS("clickhouse")
public interface BasTransactionDetailAccountService extends IService<BasTransactionDetailAccount> {
    boolean save(BasTransactionDetailAccount info);

    boolean updateById(BasTransactionDetailAccount info);

    boolean deleteByIds(List<String> ids);

    public boolean importExcel(List<BasTransactionDetailAccount> list);
}

