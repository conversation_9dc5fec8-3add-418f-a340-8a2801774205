package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.cb.ai.data.analysis.docassist.converter.pipe.ext.IAttachExt;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 四级标题管道
 *
 * <AUTHOR>
 */
public class SerialNumber4Pipe extends IPipe implements IAttachExt {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        boolean currMatch = ReUtil.contains("^[(（][1-9]+[0-9]*[）)]", text);
        //往前10个段落内有附件的话是附件列表，不进行匹配
        return currMatch && !pre10ParagraphHasAttach(document, pos);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatParagraphInd(paragraph, config);
        List<XWPFRun> runs = paragraph.getRuns();
        // 修改序号后标点符号
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String runText = run.text();
            if (StringUtils.isBlank(runText)) {
                continue;
            }
            runText = runText.replace('(', '（');
            runText = runText.replace(')', '）');
            if (ReUtil.contains("[）)]$", runText)) {
                int n = i + 1;
                if (n >= runs.size()) {
                    break;
                }
                XWPFRun nRun = runs.get(i + 1);
                String nRunText = nRun.text();
                if (ReUtil.contains("^[,.．，。、]", nRunText)) {
                    String replaced = ReUtil.replaceAll(nRunText, "^([,.．，。、])(.*)", "$2");
                    nRun.setText(replaced, 0);
                }
                run.setText(runText, 0);
                break;
            }
            if (ReUtil.contains("[）)][,.．，。、]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "([）)])[,.．，。、]", "$1");
                run.setText(replaced, 0);
                break;
            }
        }
        // 修改序号字体样式
        String paragraphText = paragraph.getText().trim();
        boolean isFormatTop = false;
        if (paragraphText.indexOf("。") > 52 || paragraphText.indexOf("：") > 52) {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("）");
                if (index == -1) {
                    FormatTools.formatSerialNumber4(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber4(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber4(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        } else {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String runText = run.text();
                if (isFormatTop) {
                    FormatTools.format(run, config);
                    continue;
                }
                int index = runText.indexOf("：");
                if (index == -1) {
                    index = runText.indexOf("。");
                }
                if (index == -1) {
                    FormatTools.formatSerialNumber4(run, config);
                } else if (index >= runText.length() - 1) {
                    isFormatTop = true;
                    FormatTools.formatSerialNumber4(run, config); // 直接修改该run
                } else {
                    isFormatTop = true;
                    String start = runText.substring(0, index + 1);
                    run.setText(start, 0);
                    FormatTools.formatSerialNumber4(run, config);
                    String end = runText.substring(index + 1);
                    XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                    nowRun.setText(end, 0);
                    FormatTools.format(nowRun, config);
                }
            }
        }
    }
}
