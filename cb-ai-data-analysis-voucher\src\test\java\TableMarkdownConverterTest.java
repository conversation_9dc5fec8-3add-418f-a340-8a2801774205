import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简化版的TableMarkdownConverter测试类
 * 只包含核心转换逻辑，不依赖外部库
 */
public class TableMarkdownConverterTest {

    public static void main(String[] args) {
        // 测试用例1：简单表格
        String simpleTable = "<table><tr><th>姓名</th><th>年龄</th></tr><tr><td>张三</td><td>25</td></tr></table>";
        System.out.println("简单表格测试：");
        System.out.println(convertTableToMarkdown(simpleTable));
        System.out.println();
        
        // 测试用例2：包含合并单元格的表格
        String complexTable = "<table><tr><th colspan='2'>个人信息</th></tr><tr><td>姓名</td><td>张三</td></tr><tr><td>年龄</td><td>25</td></tr></table>";
        System.out.println("复杂表格测试：");
        System.out.println(convertTableToMarkdown(complexTable));
        System.out.println();
        
        // 测试用例3：多个表格
        String multipleTables = "这是第一个表格：<table><tr><th>A</th><th>B</th></tr><tr><td>1</td><td>2</td></tr></table>这是第二个表格：<table><tr><th>X</th><th>Y</th><th>Z</th></tr><tr><td>a</td><td>b</td><td>c</td></tr></table>";
        System.out.println("多表格测试：");
        System.out.println(convertTableToMarkdown(multipleTables));
        System.out.println();
        
        // 测试用例4：包含HTML实体的表格
        String entityTable = "<table><tr><th>符号</th><th>描述</th></tr><tr><td>&lt;test&gt;</td><td>&amp;nbsp;空格</td></tr></table>";
        System.out.println("HTML实体测试：");
        System.out.println(convertTableToMarkdown(entityTable));
    }

    public static String convertTableToMarkdown(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }

        Pattern pattern = Pattern.compile("<table[^>]*>(.*?)</table>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String tableContent = matcher.group(1);
            String markdownTable = convertSingleTable(tableContent);
            markdownTable = Matcher.quoteReplacement(markdownTable);
            matcher.appendReplacement(result, "\n\n" + markdownTable + "\n\n");
        }

        matcher.appendTail(result);
        return result.toString();
    }

    private static String convertSingleTable(String tableContent) {
        if (tableContent == null || tableContent.trim().isEmpty()) {
            return "";
        }
        
        List<String> rows = extractTableRows(tableContent);
        if (rows.isEmpty()) {
            return "";
        }
        
        int maxColumns = calculateMaxColumns(rows);
        if (maxColumns == 0) {
            return "";
        }
        
        StringBuilder markdownTable = new StringBuilder();
        boolean hasHeader = false;
        
        for (String rowContent : rows) {
            String rowMarkdown = convertTableRow(rowContent, maxColumns);
            
            if (isEmptyRow(rowMarkdown)) {
                continue;
            }
            
            markdownTable.append(rowMarkdown).append("\n");
            
            if (!hasHeader) {
                String separator = createHeaderSeparator(maxColumns);
                markdownTable.append(separator).append("\n");
                hasHeader = true;
            }
        }
        
        return markdownTable.toString().trim();
    }

    private static List<String> extractTableRows(String tableContent) {
        List<String> rows = new ArrayList<>();
        
        String processedContent = tableContent.replaceAll("</?t(head|body|foot)[^>]*>", "");
        
        Pattern rowPattern = Pattern.compile("<tr[^>]*>(.*?)</tr>", 
                Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher rowMatcher = rowPattern.matcher(processedContent);

        while (rowMatcher.find()) {
            String rowContent = rowMatcher.group(1);
            if (rowContent != null && !rowContent.trim().isEmpty()) {
                rows.add(rowContent);
            }
        }
        
        return rows;
    }

    private static int calculateMaxColumns(List<String> rows) {
        int maxColumns = 0;
        
        for (String rowContent : rows) {
            List<String> cells = extractCells(rowContent);
            maxColumns = Math.max(maxColumns, cells.size());
        }
        
        return maxColumns;
    }

    private static String convertTableRow(String rowContent, int maxColumns) {
        List<String> cells = extractCells(rowContent);
        
        StringBuilder rowMarkdown = new StringBuilder("|");
        
        for (String cellContent : cells) {
            String cleanContent = cleanCellContent(cellContent);
            rowMarkdown.append(" ").append(cleanContent).append(" |");
        }
        
        for (int i = cells.size(); i < maxColumns; i++) {
            rowMarkdown.append("  |");
        }

        return rowMarkdown.toString();
    }

    private static List<String> extractCells(String rowContent) {
        List<String> cells = new ArrayList<>();
        
        Pattern cellPattern = Pattern.compile("<(th|td)([^>]*)>(.*?)</\\1>", 
                Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher cellMatcher = cellPattern.matcher(rowContent);

        while (cellMatcher.find()) {
            String attributes = cellMatcher.group(2);
            String cellContent = cellMatcher.group(3);
            
            int colspan = extractColspan(attributes);
            
            cells.add(cellContent);
            
            for (int i = 1; i < colspan; i++) {
                cells.add("");
            }
        }
        
        return cells;
    }

    private static int extractColspan(String attributes) {
        if (attributes == null || attributes.trim().isEmpty()) {
            return 1;
        }
        
        Pattern colspanPattern = Pattern.compile("colspan\\s*=\\s*[\"']?(\\d+)[\"']?", 
                Pattern.CASE_INSENSITIVE);
        Matcher matcher = colspanPattern.matcher(attributes);
        
        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return 1;
            }
        }
        
        return 1;
    }

    private static String cleanCellContent(String cellContent) {
        if (cellContent == null || cellContent.trim().isEmpty()) {
            return "";
        }
        
        // 简单的HTML标签清理
        String cleanContent = cellContent.replaceAll("<[^>]+>", "");
        
        // 处理HTML实体
        cleanContent = cleanContent.replace("&nbsp;", " ")
                                 .replace("&lt;", "<")
                                 .replace("&gt;", ">")
                                 .replace("&amp;", "&")
                                 .replace("&quot;", "\"");
        
        // 规范化空白字符
        cleanContent = cleanContent.replaceAll("\\s+", " ").trim();
        
        // 转义Markdown特殊字符
        cleanContent = cleanContent.replace("|", "\\|")
                                 .replace("\n", " ")
                                 .replace("\r", " ");
        
        return cleanContent;
    }

    private static String createHeaderSeparator(int columnCount) {
        StringBuilder separator = new StringBuilder("|");
        for (int i = 0; i < columnCount; i++) {
            separator.append(" --- |");
        }
        return separator.toString();
    }

    private static boolean isEmptyRow(String rowMarkdown) {
        if (rowMarkdown == null || rowMarkdown.trim().isEmpty()) {
            return true;
        }
        
        String content = rowMarkdown.replace("|", "").replace(" ", "");
        return content.trim().isEmpty();
    }
}
