package com.cb.ai.data.analysis.knowledge.domain.req;

import com.xong.boot.common.domain.BasePage;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 知识库请求实体
 */
@Data
public class KnowledgeBase extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    private String id;
    /**
     * 知识库名称
     */
    private String name;

    /***
     * 权限标识 1公开,2部门,3私有
     */
    private Integer purviewMark;

    /***
     * 部门ID
     */
    private List<String> deptIds;
    /***
     * 知识库创建时间
     */
    private Date createTime;
    /**
     * 知识库父级ID
     */
    private String parentId;

    /***
     * 用户ID
     */
    private String createUserId;

    /***
     * 标签
     */
    private String tag;

    /***
     * 备注
     */
    private String remark;

    /***
     * 搜索关键字
     */
    private String searchKey;

    /***
     * 判断是否需要展示文件解析状态
     */
    private Boolean isShowFileStatus;
}
