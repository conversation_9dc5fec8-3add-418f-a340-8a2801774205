package com.cb.ai.data.analysis.graph.domain.vo;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public interface RelationGraphVo {

    @Data
    class BatchAddRelationshipReq {
        private String startNodeLabel;//起始节点标签
        private String endNodeLabel;//截止节点标签
        @NotBlank(message = "匹配条件不能为空")
        // n-起始节点 m-截止节点 item-nodeMatchList中的元素
        private String matchCondition;//匹配条件 示例: n.name = item.startValue AND m.name = item.endValue
        private String relationType;//关系类型
        private List<Map<String, Object>> nodeMatchList;//匹配条件的数据
        private boolean accurate;// 确切的-accurate 疑似的-possible
    }

    @Data
    class BatchDeleteRelationshipReq {
        private String startNodeLabel;//起始节点标签
        private String endNodeLabel;//截止节点标签
        @NotBlank(message = "匹配条件不能为空")
        // n-起始节点 m-截止节点 item-nodeMatchList中的元素
        private String matchCondition;//匹配条件 示例: n.name = item.startValue AND m.name = item.endValue
        private String relationType;//关系类型
        private List<Map<String, Object>> nodeMatchList;//匹配条件的数据
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class QueryResp {
        private List<Map<String, Object>> nodes;
        private List<Map<String, Object>> links;
        private List<Map<String, Object>> extras;// 普通属性（如统计数量、属性名等）
        private String text;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class ScoreNodeMatchItem {
        private Long id;// 节点的id
        private String nodeId;//节点的属性id
        private String nodeName;//节点的属性name
        private Double score;//匹配的分数
    }

    @Getter
    class AddNodeReq {
        @NotBlank(message = "标签不能为空")
        private List<String> labels;
        private Map<String, Object> node;

        private AddNodeReq(List<String> labels, Map<String, Object> node) {
            this.labels = labels;
            this.node = node;
        }

        /**
         * 预处理新增节点的信息
         *
         * @param req
         * @return
         */
        public static AddNodeReq of(Map<String, Object> req) {
            String name = MapUtil.getStr(req, "name");
            if (StringUtils.isBlank(name)) {
                throw new IllegalArgumentException("节点名称不能为空");
            }
            Object labelsObj = req.get("labels");
            if (null == labelsObj || ObjectUtil.isEmpty(labelsObj)) {
                throw new IllegalArgumentException("节点标签不能为空");
            }
            List<String> labels = null;
            if (labelsObj instanceof List) {
                labels = (List<String>) labelsObj;
            } else if (labelsObj instanceof String) {
                String lablesStr = (String) labelsObj;
                labels = Arrays.stream(StringUtils.split(lablesStr, ","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
            }
            req.remove("labels");
            return new AddNodeReq(labels, req);
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class AddNodeAndRelationReq {
        @NotEmpty(message = "起始节点不能为空")
        private Map<String, Object> source;
        @NotEmpty(message = "截止节点不能为空")
        private Map<String, Object> target;
        @NotBlank(message = "关系类型不能为空")
        private String type;
        private Map<String, Object> data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class AddRelationByPropIdReq {
        @NotBlank(message = "起始节点属性id不能为空")
        private String sourceId;
        @NotBlank(message = "截止节点属性id不能为空")
        private String targetId;
        @NotBlank(message = "关系类型不能为空")
        private String type;
        private Map<String, Object> data;
    }

    /**
     * 修改关系类型请求参数
     */
    @Data
    class ChangeRelationReq {
        @NotBlank(message = "起始节点属性id不能为空")
        private String sourceId;
        @NotBlank(message = "截止节点属性id不能为空")
        private String targetId;
        @NotBlank(message = "关系类型不能为空")
        private String type;
        private Map<String, Object> data;
        /**
         * 原来的关系类型
         * 如果为空代表只是修改关系的data
         * 不为空则代表着变更关系类型，将oldType修改为type 比如：将 A-同学-C 修改为 A-配偶-C
         */
        private String oldType;
    }

    /**
     * 删除关系的请求参数
     * 删除的逻辑跟参数有关系（所有条件删除关系的层级都只为1层）：
     * 1.sourceId非空，则删除起始节点为sourceId的关系，若type有值则只删除类型为type的关系，若无值则删除关系从sourceId发起的所有关系
     * 2.targetId非空，则删除终点节点为targetId的关系，若type有值则只删除类型为type的关系，若无值则删除关系指向targetId的所有关系
     * 3.sourceId和targetId都不为空，则删除起始节点为sourceId，终点节点为targetId的关系，若type有值则只删除类型为type的关系，若无值则删除关系从sourceId发起，指向targetId的所有关系
     * 4.sourceId和targetId都为空，type不为空，则删除所有关系类型为type的关系
     * 5.sourceId和targetId都为空，type为空，则删除所有关系, 这个默认是不允许的！！！！！！
     */
    @Data
    class DelRelationReq {
        private String sourceId;// 起点id
        private String targetId;// 终点id
        private String type;// 关系类型

        public void validate() {
            if (StringUtils.isBlank(sourceId) && StringUtils.isBlank(targetId) && StringUtils.isBlank(type)) {
                throw new IllegalArgumentException("删除关系请求参数错误：起始节点、目标节点、关系类型不能同时为空");
            }
            if (StringUtils.isBlank(sourceId)) {
                sourceId = null;
            }
            if (StringUtils.isBlank(targetId)) {
                targetId = null;
            }
            if (StringUtils.isBlank(type)) {
                type = null;
            }
        }
    }

    @Data
    class MergeNodeReq {
        @NotBlank(message = "合并时要保持的节点不能为空")
        private String keepId;//保持的节点id
        @NotBlank(message = "合并时要合并的节点不能为空")
        private String mergeId;//被合并的节点id
    }


}
