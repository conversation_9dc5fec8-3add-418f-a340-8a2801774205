<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.graph.mapper.basic.GraphCadresInfoMapper">
    <resultMap type="com.cb.ai.data.analysis.graph.domain.entity.basic.GraphCadresInfo" id="GraphCadresInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="workUnit" column="work_unit" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="nation" column="nation" jdbcType="VARCHAR"/>
        <result property="nativePlace" column="native_place" jdbcType="VARCHAR"/>
        <result property="birthPlace" column="birth_place" jdbcType="VARCHAR"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="politicalIdentity" column="political_identity" jdbcType="VARCHAR"/>
        <result property="partyJoinTime" column="party_join_time" jdbcType="TIMESTAMP"/>
        <result property="startWorkTime" column="start_work_time" jdbcType="TIMESTAMP"/>
        <result property="workPost" column="work_post" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

