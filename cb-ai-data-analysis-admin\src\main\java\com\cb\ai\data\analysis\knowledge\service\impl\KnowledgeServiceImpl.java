package com.cb.ai.data.analysis.knowledge.service.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeBaseFile;
import com.cb.ai.data.analysis.knowledge.domain.req.KnowledgeFile;
import com.cb.ai.data.analysis.knowledge.domain.req.NlsqlData;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeFileService;
import com.cb.ai.data.analysis.knowledge.service.KnowledgeService;
import com.xong.boot.common.exception.XServiceException;
import com.xong.boot.framework.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/***
 * <AUTHOR>
 * 知识库文件管理 因为要跟文件管理联动，故写在admin模块
 */
@Service
public class KnowledgeServiceImpl implements KnowledgeService {

    @Autowired
    private KnowledgeFileService knowledgeFileService;

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;

    @Autowired
    private SuperviseResourceFolderService superviseResourceFolderService;


    @Override
    public String saveupload(KnowledgeBaseFile knowledgeBaseFile) throws Exception{
        try{
            if(ObjectUtils.isEmpty(knowledgeBaseFile)){
                throw new XServiceException("请求信息为空！");
            }
            if(ObjectUtils.isEmpty(knowledgeBaseFile.getBaseId())){
                throw new XServiceException("知识库ID不能为空！");
            }
            if(CollectionUtils.isEmpty(knowledgeBaseFile.getFileList())){
                throw new XServiceException("文件列表为空！");
            }
            /*for(KnowledgeFile file:knowledgeBaseFile.getFileList()){
                file.setFileUrl(superviseResourceFileService.getDownloadUrl(file.getFileId()));
            }*/
            return knowledgeFileService.saveupload(knowledgeBaseFile);
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }



    /*@Override
    public SuperviseResourceFile upload(MultipartFile file, String baseId) throws Exception {
       try {

           SuperviseResourceFolder superviseResourceFolder=this.getResourceFolderRootInfo();
           SuperviseResourceFolder byId = superviseResourceFolderService.getById(superviseResourceFolder.getId());
           if (byId == null) {
               byId = new SuperviseResourceFolder();
               byId.setId("0");
               byId.setFullPath("0");
           }
           LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
           lambda.eq(SuperviseResourceFile::getDigestMd5,null);
           SuperviseResourceFile one = superviseResourceFileService.getOne(lambda);
           if (!ObjectUtils.isEmpty(one)) {
               return one;
           }
           SuperviseResourceFile superviseResourceFile = superviseResourceFileService.uploadFile(file, byId, null,null);
           boolean save = superviseResourceFileService.save(superviseResourceFile);
           if (save) {
               throw new XServiceException("文件上传信息保存失败");
           }

           List<KnowledgeFile> fileList=new ArrayList<>();
           KnowledgeBaseFile knowledgeBaseFile=new KnowledgeBaseFile();
           knowledgeBaseFile.setBaseId(baseId);
           KnowledgeFile knowledgeFile=new KnowledgeFile();
           knowledgeFile.setFileUrl(superviseResourceFile.getFilePath());
           knowledgeFile.setFileName(superviseResourceFile.getFilename());
           fileList.add(knowledgeFile);

           knowledgeFileService.saveupload(knowledgeBaseFile);

           return superviseResourceFile;
       }catch (XServiceException e){
           throw new XServiceException(e.getMessage());
       }catch (Exception e){
           throw new Exception(e.getMessage());
       }
    }*/

    @Override
    public SuperviseResourceFolder getResourceFolderRootInfo() throws Exception{
        try {
            LambdaQueryWrapper<SuperviseResourceFolder> superviseResourceFolderLambdaQueryWrapper = new QueryWrapper<SuperviseResourceFolder>().lambda()
                    .eq(SuperviseResourceFolder::getCreateBy, SecurityUtils.getUsername())
                    .eq(SuperviseResourceFolder::getParentId,"0")
                    .eq(SuperviseResourceFolder::getFolderName,"我的文件夹")
                    .orderByAsc(SuperviseResourceFolder::getSortOn);
            SuperviseResourceFolder superviseResourceFolder = superviseResourceFolderService.getOne(superviseResourceFolderLambdaQueryWrapper);
           if(ObjectUtils.isEmpty(superviseResourceFolder)){
               throw new XServiceException("获取用户文件信息为空！");
           }
           return superviseResourceFolder;
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public String saveNlsqlToFile(NlsqlData nlsqlData) {
        String fileContent = nlsqlData.getJsonStr();
        try {
            SuperviseResourceFolder superviseResourceFolder = getResourceFolderRootInfo();
            byte[] bytes = fileContent.getBytes(CharsetUtil.UTF_8);
            String md5 = DigestUtil.md5Hex(bytes);
            InputStream fileStream = new ByteArrayInputStream(bytes);
            SuperviseResourceFile superviseResourceFile = superviseResourceFileService.uploadFileStreamByFolderId(fileStream, md5,
                    nlsqlData.getFileName() + ".nl2sql", superviseResourceFolder.getId(), (long) bytes.length, "application/json", null, SecurityUtils.getUsername());
            KnowledgeBaseFile knowledgeBaseFile = new KnowledgeBaseFile();
            knowledgeBaseFile.setBaseId("2");
            knowledgeBaseFile.setTopicType(0);
            List<KnowledgeFile> fileList = new ArrayList<>();
            fileList.add(new KnowledgeFile(superviseResourceFile.getFilename(), superviseResourceFile.getFilePath(), "2", superviseResourceFile.getId()));
            knowledgeBaseFile.setFileList(fileList);
            return saveupload(knowledgeBaseFile);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void delKnowledgeFile(String knowledgeFileId) throws Exception {
        try{
            String fileId=knowledgeFileService.getFileId(knowledgeFileId);
            knowledgeFileService.delKnowledgeFile(knowledgeFileId);
            // 同步删除文件管理的ID，但是这里没删除es
            if(StringUtils.isNotBlank(fileId)){
                superviseResourceFileService.removeById(fileId);
            }
        }catch (XServiceException e){
            throw new XServiceException(e.getMessage());
        }catch (Exception e){
            throw new Exception(e);
        }
    }
}
