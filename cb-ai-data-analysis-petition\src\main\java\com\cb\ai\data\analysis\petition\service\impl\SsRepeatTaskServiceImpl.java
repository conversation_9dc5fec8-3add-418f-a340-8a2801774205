package com.cb.ai.data.analysis.petition.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultItem;
import com.cb.ai.data.analysis.petition.domain.SsRepeatTask;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.handler.WriterVectorBatchResultHandler;
import com.cb.ai.data.analysis.petition.mapper.SsRepeatTaskMapper;
import com.cb.ai.data.analysis.petition.service.SsPetitionAnalyzedService;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultGroupService;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultItemService;
import com.cb.ai.data.analysis.petition.service.SsRepeatTaskService;
import com.cb.ai.data.analysis.petition.utils.EmbeddingUtils;
import com.xong.boot.common.properties.FileProperties;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SsRepeatTaskServiceImpl extends BaseServiceImpl<SsRepeatTaskMapper, SsRepeatTask> implements SsRepeatTaskService {

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;
    @Autowired
    private SsPetitionAnalyzedService petitionAnalyzedService;
    @Autowired
    private SsRepeatResultGroupService resultGroupService;
    @Autowired
    private SsRepeatResultItemService resultItemService;

    @Resource
    private TaskExecutor repeatTaskExecutor;

    @Resource
    private EmbeddingUtils embeddingUtils;

    @Autowired
    private FileProperties fileProperties;

    private String getTaskDataDir(String taskId) {
        String profile=fileProperties.getRootDir();
        return profile + "/ss-repeat-task/" + taskId + "/";
    }

    @Override
    public void createTask(SsRepeatTaskVo.CreateReq req) {
        SsRepeatTask task = new SsRepeatTask();
        String id = IdUtil.getSnowflakeNextIdStr();
        task.setId(id);
        task.setTitle(req.getTitle());
        task.setBeginDate(req.getBeginDate());
        task.setEndDate(req.getEndDate());
        task.setStatus(1);//分析中
        int insert = baseMapper.insert(task);
        // 异步执行分析人物
        startAsyncTask(id, req.getThreshold(), false);
    }

    /**
     * 执行异步任务
     *
     * @param taskId
     * @param threshold 相似度阈值
     * @param force     是否强制执行，不管什么状态都强制执行
     */
    @Override
    public void startAsyncTask(String taskId, double threshold, boolean force) {
        log.info("开始执行重复信访件分析任务：{}", taskId);
//        SsRepeatTask nowTask = baseMapper.selectById(taskId);
        // 使用Future异步分析生成文件并保存位置，指定线程池
        CompletableFuture<SsRepeatTaskVo.RepeatTaskFutureResult> future = CompletableFuture.supplyAsync(() -> {
            SsRepeatTask nowTask = baseMapper.selectById(taskId);
            if (null != nowTask) {
                String nowTaskId = nowTask.getId();
                String userName = nowTask.getCreateBy();
                String beginStr = nowTask.getBeginDate();
                String endStr = nowTask.getEndDate();
                Date begin = StringUtils.isBlank(beginStr) ? null : DateUtil.parseDate(beginStr);
                Date end = StringUtils.isBlank(endStr) ? null : DateUtil.endOfDay(DateUtil.parseDate(endStr));
                //状态为未开始才执行
                if (force || nowTask.getStatus() < 2) {
                    try {
                        //获取信访件分组
                        List<SsRepeatTaskVo.RepeatTaskAnalyzeGroup> analyzeGroup = petitionAnalyzedService.getAnalyzeGroup(begin, end);
                        analyzeGroup.forEach(group -> {
                            group.setTaskId(nowTaskId);
                            group.setBeginDate(begin);
                            group.setEndDate(end);
                        });
                        // 本地数据存储路径
                        String taskDataDir = getTaskDataDir(nowTaskId);
                        // 检查目录是否存在，不存在则创建
                        FileUtil.mkdir(taskDataDir);
                        //总的所有相似分组id
                        Map<Long, Set<Long>> fullMultiIdMap = new LinkedHashMap<>();
                        AtomicLong total = new AtomicLong();
                        analyzeGroup.stream().forEach(group -> {
                            //写摘要的向量到文件
                            processGroupWithBatchHandler(group, nowTaskId, taskDataDir);
                            //读取向量文件分析重复件
                            EmbeddingUtils.VectorFileIndexer vectorIndexer = new EmbeddingUtils.VectorFileIndexer(taskDataDir + group.genVectorFileName());
                            total.addAndGet(vectorIndexer.getTotalLines());
                            //所有判定为存在相似的id
                            Set<Long> hasMultiIds = new HashSet<>();
                            // id对应的重复项的id,所有相似分组的map
                            Map<Long, Set<Long>> groupMultiIdMap = new LinkedHashMap<>();
                            for (int i = 1; i < vectorIndexer.getTotalLines(); i++) {
                                SsRepeatTaskVo.VectorItem currItem = vectorIndexer.readLine(i);
                                //如果存在相似，必然已有组，跳过
                                if (null == currItem || hasMultiIds.contains(currItem.getId())) {
                                    continue;
                                }
                                //当前项的相似项id集合
                                Set<Long> currMultiIds = new LinkedHashSet<>();
                                currMultiIds.add(currItem.getId());
                                for (int j = i + 1; j <= vectorIndexer.getTotalLines(); j++) {
                                    SsRepeatTaskVo.VectorItem item = vectorIndexer.readLine(j);
                                    //如果存在相似，必然已有组，跳过
                                    if (null == item || hasMultiIds.contains(item.getId())) {
                                        continue;
                                    }
                                    double sim = EmbeddingUtils.cosineSimilarity(currItem.getVector(), item.getVector());
                                    if (sim > threshold) {
                                        currMultiIds.add(item.getId());
                                    }
                                }
                                if (currMultiIds.size() > 1) {
                                    hasMultiIds.addAll(currMultiIds);
                                    groupMultiIdMap.put(currItem.getId(), currMultiIds);
                                }
                            }
                            fullMultiIdMap.putAll(groupMultiIdMap);
                        });
                        // 清理向量存储文件
                        FileUtils.deleteDirectory(new File(taskDataDir));
                        Set<Long> groupIds = fullMultiIdMap.keySet();
                        //有重复记录才入库
                        if (ObjectUtil.isNotEmpty(groupIds)) {
                            Map<Long, List<SsPetitionAnalyzedEntity>> groupIdMap = petitionAnalyzedService.listByIds(groupIds)
                                    .stream().collect(Collectors.groupingBy(SsPetitionAnalyzedEntity::getId));
                            fullMultiIdMap.forEach((id, multiIds) -> {
                                List<SsPetitionAnalyzedEntity> entities = groupIdMap.get(id);
                                if (ObjectUtil.isNotEmpty(entities)) {
                                    SsPetitionAnalyzedEntity entity = entities.get(0);
                                    SsRepeatResultGroup group = new SsRepeatResultGroup();
                                    String groupId = IdUtil.getSnowflakeNextIdStr();
                                    group.setId(groupId);
                                    group.setTaskId(nowTaskId);
                                    group.setPetitionPurposeCategory(entity.getPetitionPurposeCategory());
                                    group.setPetitionPurpose(entity.getPetitionPurpose());
                                    group.setPetitionDomainCategory(entity.getPetitionDomainCategory());
                                    group.setPetitionDomain(entity.getPetitionDomain());
                                    group.setPetitionProvince(entity.getPetitionProvince());
                                    group.setPetitionCity(entity.getPetitionCity());
                                    group.setBrief(entity.getBrief());
                                    group.setCreateBy(userName);
                                    group.setCreateTime(LocalDateTime.now());
                                    group.setUpdateBy(userName);
                                    group.setUpdateTime(LocalDateTime.now());
                                    resultGroupService.save(group);
                                    List<SsRepeatResultItem> items = multiIds.stream().map(multiId -> {
                                        SsRepeatResultItem item = new SsRepeatResultItem();
                                        item.setId(IdUtil.getSnowflakeNextIdStr());
                                        item.setGroupId(groupId);
                                        item.setTaskId(nowTaskId);
                                        item.setAnalyzedId(multiId);
                                        item.setCreateBy(userName);
                                        item.setCreateTime(LocalDateTime.now());
                                        item.setUpdateBy(userName);
                                        item.setUpdateTime(LocalDateTime.now());
                                        return item;
                                    }).collect(Collectors.toList());
                                    resultItemService.saveBatch(items);
                                }

                            });
                        }

                        SsRepeatTaskVo.RepeatTaskFutureResult result = new SsRepeatTaskVo.RepeatTaskFutureResult();
                        result.setTaskId(nowTaskId);
                        long similar = fullMultiIdMap.values().stream().flatMap(Set::stream).count();
                        result.setSimilar(similar);
                        result.setTotal(total.get());
                        result.setStatus(2);
                        result.setUserName(nowTask.getCreateBy());
                        return result;
                    } catch (Exception e) {
                        log.error("重复信访件分析执行异常：{}", e);
                        SsRepeatTaskVo.RepeatTaskFutureResult result = new SsRepeatTaskVo.RepeatTaskFutureResult();
                        result.setTaskId(nowTaskId);
                        result.setStatus(3);
                        result.setUserName(nowTask.getCreateBy());
                        result.setErrMsg(getTruncatedStackTrace(e, 500));
                        return result;
                    }

                }
            }
            return null;
        }, repeatTaskExecutor);
        future.thenAccept(result -> {
            if (null != result) {
                LambdaUpdateWrapper<SsRepeatTask> updateWrapper = Wrappers.<SsRepeatTask>lambdaUpdate()
                        .eq(SsRepeatTask::getId, result.getTaskId())
                        .set(SsRepeatTask::getStatus, result.getStatus())
                        .set(SsRepeatTask::getUpdateBy, result.getUserName());
                SsRepeatTask update = new SsRepeatTask();
                update.setId(result.getTaskId());
                update.setStatus(result.getStatus());
                update.setUpdateBy(result.getUserName());
                //成功
                if (Integer.valueOf(2).equals(result.getStatus())) {
                    updateWrapper.set(SsRepeatTask::getSimilar, result.getSimilar())
                            .set(SsRepeatTask::getTotal, result.getTotal())
                            .set(SsRepeatTask::getErrMsg, null);
                } else {
                    updateWrapper.set(SsRepeatTask::getErrMsg, result.getErrMsg());
                }
                baseMapper.update(updateWrapper);
            }
        });
    }

    public static String getTruncatedStackTrace(Throwable throwable, int maxLength) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        String stackTrace = sw.toString();

        // 截取前 maxLength 个字符
        if (stackTrace.length() > maxLength) {
            return stackTrace.substring(0, maxLength);
        } else {
            return stackTrace;
        }
    }

    @Override
    public Page<SsRepeatTask> pageByWrapper(Page<SsRepeatTask> page, Wrapper<SsRepeatTask> wrapper) {
        return baseMapper.pageByWrapper(page, wrapper);
    }

    @Override
    public Page<SsRepeatTask> pageBySelf(Page<SsRepeatTask> page, SsRepeatTask query) {
        QueryWrapper<SsRepeatTask> wrapper = new QueryWrapper<SsRepeatTask>().eq("t.create_by", query.getCreateBy());
        wrapper.eq("t.del_flag", false);
        if (StringUtils.isNotBlank(query.getTitle())) {
            wrapper.like("t.title", query.getTitle());
        }
        if (null != query.getStatus()) {
            wrapper.eq("t.status", query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getBeginDate())) {
            wrapper.ge("t.create_time", DateUtil.parseDate(query.getBeginDate()));
        }
        if (StringUtils.isNotBlank(query.getEndDate())) {
            wrapper.le("t.create_time", DateUtil.endOfDay(DateUtil.parseDate(query.getEndDate())));
        }
        wrapper.orderByDesc("t.create_time");
        return baseMapper.pageByWrapper(page, wrapper);
    }

    @Override
    public int delTask(String taskId) {
        SsRepeatTask task = baseMapper.selectById(taskId);
        if (null != task && Integer.valueOf(1).equals(task.getStatus())) {
            throw new RuntimeException("任务正在执行中，不允许删除！");
        }
        int i = baseMapper.deleteById(taskId);
        //删除结果分组
        resultGroupService.remove(Wrappers.<SsRepeatResultGroup>lambdaQuery().eq(SsRepeatResultGroup::getTaskId, taskId));
        //删除结果关联记录
        resultItemService.remove(Wrappers.<SsRepeatResultItem>lambdaQuery().eq(SsRepeatResultItem::getTaskId, taskId));
        return i;
    }

    @Override
    public Page<SsRepeatResultGroup> getRepeatTaskResult(String taskId, Page<SsRepeatResultGroup> page, SsRepeatResultGroup entity) {
        SsRepeatTask task = baseMapper.selectById(taskId);
        if (null == task) {
            throw new RuntimeException("任务不存在");
        }
        LambdaQueryWrapper<SsRepeatResultGroup> queryWrapper = Wrappers.<SsRepeatResultGroup>lambdaQuery()
                .eq(SsRepeatResultGroup::getTaskId, taskId)
                .like(StringUtils.isNotBlank(entity.getPetitionPurposeCategory()), SsRepeatResultGroup::getPetitionPurposeCategory, entity.getPetitionPurposeCategory())
                .like(StringUtils.isNotBlank(entity.getPetitionPurpose()), SsRepeatResultGroup::getPetitionPurpose, entity.getPetitionPurpose())
                .like(StringUtils.isNotBlank(entity.getPetitionDomainCategory()), SsRepeatResultGroup::getPetitionDomainCategory, entity.getPetitionDomainCategory())
                .like(StringUtils.isNotBlank(entity.getPetitionDomain()), SsRepeatResultGroup::getPetitionDomain, entity.getPetitionDomain())
                .like(StringUtils.isNotBlank(entity.getPetitionProvince()), SsRepeatResultGroup::getPetitionProvince, entity.getPetitionProvince())
                .like(StringUtils.isNotBlank(entity.getPetitionCity()), SsRepeatResultGroup::getPetitionCity, entity.getPetitionCity())
                .like(StringUtils.isNotBlank(entity.getBrief()), SsRepeatResultGroup::getBrief, entity.getBrief());

        Page<SsRepeatResultGroup> groupPage = resultGroupService.pageByWrapper(QueryHelper.getPage(), queryWrapper);
        List<SsRepeatResultGroup> records = groupPage.getRecords();
        Set<String> groupIds = records.stream().map(item -> item.getId()).collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(groupIds)) {
            Map<String, List<SsPetitionAnalyzedEntity>> groupIdMap = resultItemService.getGroupIdAnalyzedMap(groupIds);
            records.forEach(grouop -> {
                List<SsPetitionAnalyzedEntity> items = groupIdMap.get(grouop.getId());
                if (ObjectUtil.isEmpty(items)) {
                    grouop.setItems(Collections.emptyList());
                } else {
                    grouop.setItems(items);
                }
            });
        }
        return groupPage;
    }

    /**
     * 使用批处理机制处理分组数据
     *
     * @param group       分析分组
     * @param taskId      任务ID
     * @param taskDataDir 任务数据目录
     */
    private void processGroupWithBatchHandler(SsRepeatTaskVo.RepeatTaskAnalyzeGroup group, String taskId, String taskDataDir) {
        File groupDataFile = new File(taskDataDir, group.genVectorFileName());
        try {
            if (groupDataFile.exists()) {
                // 清空文件内容
                try (FileWriter writer = new FileWriter(groupDataFile)) {
                    writer.write("");
                }
            } else {
                // 创建新文件
                groupDataFile.createNewFile();
            }
        } catch (IOException e) {
            throw new RuntimeException("初始化分组数据文件失败", e);
        }
        log.info("开始处理分组数据: {}", group.genMD5());

        // 创建批处理Handler实例
        WriterVectorBatchResultHandler batchHandler = new WriterVectorBatchResultHandler(
                group, taskId, taskDataDir, repeatTaskExecutor);

        try {
            // 执行流式查询
            petitionAnalyzedService.streamPetitionByAnalyzeGroup(group, batchHandler);

            // 完成处理并等待所有批次完成
            boolean success = batchHandler.finish(300); // 等待5分钟

            // 获取统计信息
            var stats = batchHandler.getStats();

            if (success && !batchHandler.hasException()) {
                log.info("分组 {} 处理成功 - {}", group.genMD5(), stats);
            } else {
                log.error("分组 {} 处理失败 - {}", group.genMD5(), stats);
                if (batchHandler.hasException()) {
                    throw new RuntimeException("批处理失败", batchHandler.getLastException());
                }
            }

        } catch (Exception e) {
            log.error("分组 {} 批处理执行失败", group.genMD5(), e);
            throw new RuntimeException("批处理执行失败", e);
        }
    }


}
