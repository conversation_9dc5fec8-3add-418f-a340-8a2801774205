package com.cb.ai.data.json.tool.service.impl;

import com.cb.ai.data.json.tool.constants.Constant;
import com.cb.ai.data.json.tool.domain.JsonAnalysisTask;
import com.cb.ai.data.json.tool.domain.ReportContextData;
import com.cb.ai.data.json.tool.enums.AnalysisStatus;
import com.cb.ai.data.json.tool.reg.JsonRegExp;
import com.cb.ai.data.json.tool.service.JsonAnalysisTaskService;
import com.cb.ai.data.json.tool.service.JsonToolService;
import com.cb.ai.data.json.tool.utils.ExportUtils;
import com.cb.ai.data.json.tool.utils.FileUploadUtils;
import com.cb.ai.data.json.tool.utils.JsonReadUtils;
import com.cb.ai.data.json.tool.utils.ZipUtils;
import com.xong.boot.common.properties.FileProperties;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * 2025/7/31
 */
@Slf4j
@Service
public class JsonToolServiceImpl implements JsonToolService {
    private final FileProperties fileProperties;
    private final JsonAnalysisTaskService taskService;

    public JsonToolServiceImpl(FileProperties fileProperties, JsonAnalysisTaskService taskService) {
        this.fileProperties = fileProperties;
        this.taskService = taskService;
    }

    /**
     * 获取JSON工具目录路径
     *
     * @return JSON工具目录的绝对路径
     */
    private String getJsonToolDir() {
        return fileProperties.getRootDir() + File.separator + "jsonTool";
    }


    @Override
    public void analysisZipJsonFile(MultipartFile file) {
        try {
            // 保存并解压上传的ZIP文件到指定目录
            File desc = FileUploadUtils.uploadZip(getJsonToolDir(), file);
            File extractFile = ZipUtils.unZip(desc, getJsonToolDir());
            // 保存解析任务
            JsonAnalysisTask task = taskService.saveTask(getJsonToolDir(), file, desc, extractFile);
            this.analysisJsonFile(extractFile, task);
        } catch (IOException e) {
            log.error("read zip file error", e);
        }
    }

    @Override
    public void reAnalyzeJsonFile(String taskId) throws Exception {
        JsonAnalysisTask task = taskService.getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        // 先删除已经解压的文件
        File originalExtractFile = new File(task.getBasePath() + File.separator + task.getExtractFileName());
        FileUtils.deleteDirectory(originalExtractFile);
        // 重新解压文件
        File zipFile = new File(task.getBasePath() + File.separator + task.getSourceFileName());
        File extractFile = ZipUtils.unZip(zipFile, getJsonToolDir());
        // 更新任务状态为“处理中”
        task.setStatus(AnalysisStatus.PROCESSING.getStatus());
        task.setStartTime(LocalDateTime.now());
        taskService.updateById(task);
        this.analysisJsonFile(extractFile, task);
    }


    @Override
    public void downloadCompressFile(HttpServletResponse response, String taskId) {
        JsonAnalysisTask task = taskService.getById(taskId);
        File file = new File(task.getBasePath() + File.separator + task.getExtractFileName());
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }
        try {
            // 设置响应内容类型为二进制流
            response.setContentType("application/octet-stream");
            // 设置文件下载的响应头，文件名进行URL编码
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(file.getName() + Constant.DELIMITER + Constant.ZIP_EXTENSION, StandardCharsets.UTF_8));
            // 设置响应字符编码
            response.setCharacterEncoding("utf-8");
            // 获取目录下所有 CSV 文件
            Collection<File> csvFiles = FileUtils.listFiles(file, new String[]{Constant.CSV_EXTENSION}, true);
            if (csvFiles.isEmpty()) {
                throw new RuntimeException("没有找到任何 CSV 文件");
            }

            // 使用内存中的 ByteArrayOutputStream 进行压缩，避免写入磁盘
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                 ZipOutputStream zos = new ZipOutputStream(baos, StandardCharsets.UTF_8)) {
                for (File csvFile : csvFiles) {
                    // 添加文件到 ZIP 流
                    try (FileInputStream fis = new FileInputStream(csvFile)) {
                        zos.putNextEntry(new ZipEntry(csvFile.getName()));
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                        zos.closeEntry();
                    }
                }
                zos.finish();
                // 将压缩后的内容写入响应输出流
                response.getOutputStream().write(baos.toByteArray());
            }
        } catch (IOException e) {
            log.error("压缩文件失败", e);
            throw new RuntimeException("文件压缩失败", e);
        }
    }

    /**
     * 解析JSON文件并写入Excel
     *
     * @param extractFile
     * @param task
     */
    private void analysisJsonFile(File extractFile, JsonAnalysisTask task) {
        // 递归获取解压后的所有JSON文件
        Collection<File> files = FileUtils.listFiles(extractFile, new String[]{Constant.JSON_EXTENSION}, true);
        // 按父目录对文件进行分组，便于后续按目录处理
        Map<String, List<File>> fileMap = files.stream().collect(Collectors.groupingBy(File::getParent));
        // 使用异步线程处理Excel导出，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始处理JSON文件并写入Excel");
                // 构建报表上下文数据映射并写入Excel文件
                buildContextDataAndWrite2Excel(fileMap);
                // 解析完成，更新任务状态为“已完成”
                task.setEndTime(LocalDateTime.now());
                task.setStatus(AnalysisStatus.COMPLETE.getStatus());
            } catch (Exception e) {
                // 如果发生异常，更新任务状态为“失败”
                task.setStatus(AnalysisStatus.FAIL.getStatus());
                task.setEndTime(LocalDateTime.now());
                task.setErrorMsg(e.getMessage());
                log.error("处理JSON文件并写入Excel时发生错误", e);
            } finally {
                // 更新任务信息到数据库
                taskService.updateById(task);
                log.info("解析任务处理完成");
            }
        }, CompletableFuture.delayedExecutor(0, TimeUnit.MILLISECONDS, Executors.newFixedThreadPool(10)));
    }

    /**
     * 构建报表上下文数据映射
     * 该方法遍历文件映射，为每个目录下的配置文件和数据文件构建ReportContextData对象
     *
     * @param fileMap 按目录分组的文件映射
     * @return 包含每个目录对应报表数据的映射
     * @throws FileNotFoundException 当文件未找到时抛出异常
     */
    private void buildContextDataAndWrite2Excel(Map<String, List<File>> fileMap) throws FileNotFoundException {
        // 遍历每个目录及其对应的文件列表
        for (Map.Entry<String, List<File>> entry : fileMap.entrySet()) {
            String dir = entry.getKey(); // 当前目录路径
            List<File> value = entry.getValue(); // 当前目录下的所有文件

            // 查找并获取配置文件（符合CONFIG_FILE_REG正则表达式的文件）
            File config = value.stream()
                    .filter(file -> JsonReadUtils.regexMatchFileName(file.getName(), JsonRegExp.CONFIG_FILE_REG))
                    .findFirst()
                    .orElse(null);

            // 筛选出所有数据文件（符合DATA_FILE_REG正则表达式的文件）
            List<File> dataList = value.stream()
                    .filter(file -> JsonReadUtils.regexMatchFileName(file.getName(), JsonRegExp.DATA_FILE_REG))
                    .collect(Collectors.toList());

            // 如果存在配置文件，则处理该目录下的报表数据
            if (config != null) {
                // 读取配置文件内容
                String configContent = JsonReadUtils.processJsonFile(new FileInputStream(config));
                // 解析配置文件内容，生成ReportContextData对象
                ReportContextData contextData = JsonReadUtils.readConfig(configContent);
                // 提取目录名称作为文件名前缀
                String lastDirectoryName = dir.substring(dir.lastIndexOf(File.separator) + 1);
                contextData.setFileName(lastDirectoryName + "_data");

                // 如果存在数据文件，则读取并处理数据
                if (!dataList.isEmpty()) {
                    // 初始化数据映射列表
                    List<LinkedHashMap<Integer, Object>> dataMapList = new ArrayList<>();
                    // 遍历所有数据文件
                    for (int i = 0; i < dataList.size(); i++) {
                        File file = dataList.get(i);
                        // 读取数据文件内容
                        String dataContent = JsonReadUtils.processJsonFile(new FileInputStream(file));
                        // 解析数据文件内容，并将其添加到数据映射列表中
                        dataMapList.addAll(JsonReadUtils.readJSONObjectList(dataContent, contextData));
                    }
                    // 将处理后的数据设置到ReportContextData对象中
                    contextData.setData(dataMapList);
                }
                // 如果数据映射列表不为空，则进行Excel导出
                if (contextData.getData() != null && !contextData.getData().isEmpty()) {
                    try {
                        // 调用工具类将数据写入Excel文件
                        ExportUtils.write2Excel(contextData, dir);
                        // 记录导出成功的日志信息
                        log.info("导出Excel文件成功，目录：{}", dir);
                    } catch (Exception e) {
                        // 记录导出失败的日志信息及异常堆栈
                        log.error("导出Excel文件失败，目录：{}", dir, e);
                    }
                }
            }
        }
    }
}
