package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 长线
 *
 * <AUTHOR>
 */
public class LongLinePipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.contains("^[-—]{2}", text);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatParagraphInd(paragraph, config);
        List<XWPFRun> runs = paragraph.getRuns();
        boolean isFinish = false;
        boolean haveOn = false;
        for (int i = 0; i < runs.size() && i < 4; i++) {
            XWPFRun run = runs.get(i);
            String runText = run.text().trim();
            if (StringUtils.isBlank(runText)) {
                continue;
            }
            if (!isFinish && ReUtil.contains("^[-—]", runText)) {
                if (runText.length() == 1) {
                    if (haveOn) {
                        isFinish = true;
                    }
                    run.setText("—", 0);
                    FormatTools.format(run, config).setFonts("Times New Roman", "Times New Roman");
                    haveOn = true;
                } else {
                    if (ReUtil.contains("^[-—][^-—]", runText)) {
                        String replaced = ReUtil.replaceAll(runText, "^([-—]).*", "—1$");
                        run.setText(replaced, 0);
                        String start = runText.substring(0, 1);
                        run.setText(start, 0);
                        FormatTools.format(run, config).setFonts("Times New Roman", "Times New Roman");
                        String end = runText.substring(1);
                        XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                        nowRun.setText(end, 0);
                        FormatTools.format(nowRun, config);
                    } else {
                        String replaced = ReUtil.replaceAll(runText, "^([-—]{2}).*", "——1$");
                        run.setText(replaced, 0);
                        String start = runText.substring(0, 2);
                        run.setText(start, 0);
                        FormatTools.format(run, config).setFonts("Times New Roman", "Times New Roman");
                        String end = runText.substring(2);
                        XWPFRun nowRun = paragraph.insertNewRun(i + 1);
                        nowRun.setText(end, 0);
                        FormatTools.format(nowRun, config);
                    }
                    isFinish = true;
                }
            } else {
                FormatTools.format(run, config);
            }
        }
    }
}
