package com.cb.ai.data.analysis.file.util;

import cn.hutool.core.util.IdUtil;
import com.cb.ai.data.analysis.file.config.CustomMinioClient;
import com.cb.ai.data.analysis.file.config.MinioConfigInfo;
import com.cb.ai.data.analysis.file.enums.HttpCodeEnum;
import com.cb.ai.data.analysis.file.exception.ConditionException;
import com.cb.ai.data.analysis.file.model.FileUploadInfo;
import com.cb.ai.data.analysis.file.model.UploadUrlsVO;
import com.google.common.collect.HashMultimap;

import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.DeletedObject;
import io.minio.messages.Part;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MinioUtil {

    private CustomMinioClient customMinioClient;

    @Resource
    private MinioConfigInfo minioConfigInfo;

    // spring自动注入会失败
    @PostConstruct
    public void init() {
        MinioAsyncClient minioClient = MinioAsyncClient.builder()
                .endpoint(minioConfigInfo.getEndpoint())
                .credentials(minioConfigInfo.getAccessKey(), minioConfigInfo.getSecretKey())
                .build();
        customMinioClient = new CustomMinioClient(minioClient);
    }

    /**
     * 上传文件
     * @param filename
     * @param stream
     * @param size
     * @param contentType
     * @return
     * @throws Exception
     */
    public String upload(String filename, InputStream stream, long size, String contentType) throws Exception {
        // 确保 bucket 存在
        CompletableFuture<Boolean> exists = customMinioClient.bucketExists(BucketExistsArgs.builder().bucket(minioConfigInfo.getBucket()).build());
        Boolean b = exists.get();
        if (!b) {
            customMinioClient.makeBucket(MakeBucketArgs.builder().bucket(minioConfigInfo.getBucket()).build());
        }

        CompletableFuture<ObjectWriteResponse> objectWriteResponseCompletableFuture = customMinioClient.putObject(PutObjectArgs.builder()
                .bucket(minioConfigInfo.getBucket())
                .object(filename)
                .stream(stream, size, -1)
                .contentType(contentType)
                .build());
        ObjectWriteResponse objectWriteResponse = objectWriteResponseCompletableFuture.get();
        // 返回下载地址（默认生成 7 天有效的下载链接）
        return customMinioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(minioConfigInfo.getBucket())
                        .object(filename)
                        .method(Method.GET)
                        .build()
        );
    }

    /**
     * 检查文件是否存在
     * @param objectName
     * @return
     */
    public Boolean checkObject(String objectName){
        try {
            customMinioClient.statObject(StatObjectArgs.builder()
                    .bucket(minioConfigInfo.getBucket())
                    .object(objectName)
                    .build()).get();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    /**
     * 返回文件下载链接
     * @param filename
     * @return
     * @throws ServerException
     * @throws InsufficientDataException
     * @throws ErrorResponseException
     * @throws IOException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws InvalidResponseException
     * @throws XmlParserException
     * @throws InternalException
     */
    public String GetPresignedObjectUrl(String filename) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return customMinioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(minioConfigInfo.getBucket())
                        .object(filename)
                        .method(Method.GET)
                        .build());
    }
    /**
     * 获取 Minio 中已经上传的分片文件
     * @param object 文件名称
     * @param uploadId 上传的文件id（由 minio 生成）
     * @return List<Integer>
     */
    @SneakyThrows
    public List<Integer> getListParts(String object, String uploadId) {
        List<Part> parts = getParts(object, uploadId);
        return parts.stream()
                .map(Part::partNumber)
                .collect(Collectors.toList());
    }

    /**
     * 单文件签名上传
     * @param object 文件名称（uuid 格式）
     * @return UploadUrlsVO
     */
    public UploadUrlsVO getUploadObjectUrl(String contentType, String object) {
        try {
            log.info("<{}> 开始单文件上传<minio>", object);
            UploadUrlsVO urlsVO = new UploadUrlsVO();
            List<String> urlList = new ArrayList<>();
            // 主要是针对图片，若需要通过浏览器直接查看，而不是下载，需要指定对应的 content-type
            HashMultimap<String, String> headers = HashMultimap.create();
            if (contentType == null || contentType.equals("")) {
                contentType = "application/octet-stream";
            }
            headers.put("Content-Type", contentType);

            String uploadId = IdUtil.simpleUUID();
            Map<String, String> reqParams = new HashMap<>();
            reqParams.put("uploadId", uploadId);

            String url = customMinioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)
                    .bucket(minioConfigInfo.getBucket())
                    .object(object)
                    .extraHeaders(headers)
                    .extraQueryParams(reqParams)
                    .expiry(minioConfigInfo.getExpiry(), TimeUnit.DAYS)
                    .build());
            urlList.add(url);
            urlsVO.setUploadId(uploadId).setUrls(urlList);
            return urlsVO;
        } catch (Exception e) {
            log.error("单文件上传失败: {}", e.getMessage());
            throw new ConditionException(HttpCodeEnum.UPLOAD_FILE_FAILED);
        }
    }

    /**
     * 初始化分片上传
     * @param fileUploadInfo 前端传入的文件信息
     * @param object object
     * @return UploadUrlsVO
     */
    public UploadUrlsVO initMultiPartUpload(FileUploadInfo fileUploadInfo, String object) {
        Integer chunkCount = fileUploadInfo.getChunkCount();
        String contentType = fileUploadInfo.getContentType();
        String uploadId = fileUploadInfo.getUploadId();

        log.info("文件<{}> - 分片<{}> 初始化分片上传数据 请求头 {}", object, chunkCount, contentType);
        UploadUrlsVO urlsVO = new UploadUrlsVO();
        try {
            HashMultimap<String, String> headers = HashMultimap.create();
            if (contentType == null || contentType.equals("")) {
                contentType = "application/octet-stream";
            }
            headers.put("Content-Type", contentType);

            // 如果初始化时有 uploadId，说明是断点续传，不能重新生成 uploadId
            if (fileUploadInfo.getUploadId() == null || fileUploadInfo.getUploadId().equals("")) {
                uploadId = customMinioClient.initMultiPartUpload(minioConfigInfo.getBucket(), null, object, headers, null);
            }
            urlsVO.setUploadId(uploadId);

            List<String> partList = new ArrayList<>();
            Map<String, String> reqParams = new HashMap<>();
            reqParams.put("uploadId", uploadId);
            for (int i = 1; i <= chunkCount; i++) {
                reqParams.put("partNumber", String.valueOf(i));
                String uploadUrl = customMinioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .method(Method.PUT)
                        .bucket(minioConfigInfo.getBucket())
                        .object(object)
                        .expiry(1, TimeUnit.DAYS)
                        .extraQueryParams(reqParams)
                        .build());
                partList.add(uploadUrl);
            }

            log.info("文件初始化分片成功");
            urlsVO.setUrls(partList);
            return urlsVO;
        } catch (Exception e) {
            log.error("初始化分片上传失败: {}", e.getMessage());
            // 返回 文件上传失败
            throw new ConditionException(HttpCodeEnum.UPLOAD_FILE_FAILED);
        }
    }

    /**
     * 合并文件
     * @param object object
     * @param uploadId uploadUd
     */
    @SneakyThrows
    public boolean mergeMultipartUpload(String object, String uploadId) {
        log.info("通过 <{}-{}-{}> 合并<分片上传>数据", object, uploadId, minioConfigInfo.getBucket());
        // 获取所有分片
        List<Part> partsList = getParts(object, uploadId);
        Part[] parts = new Part[partsList.size()];
        int partNumber = 1;
        for (Part part : partsList) {
            parts[partNumber - 1] = new Part(partNumber, part.etag());
            partNumber++;
        }
        // 合并分片
        customMinioClient.mergeMultipartUpload(minioConfigInfo.getBucket(), null, object, uploadId, parts, null, null);
        return true;
    }

    /**
     * 获取文件流
     * @param objectName
     * @return
     */
   public InputStream getObjectStream(String objectName){
        try{
            CompletableFuture<GetObjectResponse> object = customMinioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfigInfo.getBucket())
                            .object(objectName)
                            .build());
            GetObjectResponse getObjectResponse = object.get();
            byte[] bytes = getObjectResponse.readAllBytes();
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
   }
    /**
     * 获取文件内容和元信息，该文件不存在会抛异常
     * @param object object
     * @return StatObjectResponse
     */
    @SneakyThrows
    public StatObjectResponse statObject(String object) {
        return customMinioClient.statObject(StatObjectArgs.builder()
                .bucket(minioConfigInfo.getBucket())
                .object(object)
                .build())
                .get();
    }

    @SneakyThrows
    public GetObjectResponse getObject(String object, Long offset, Long contentLength) {
        return customMinioClient.getObject(GetObjectArgs.builder()
                .bucket(minioConfigInfo.getBucket())
                .object(object)
                .offset(offset)
                .length(contentLength)
                .build())
                .get();
    }

    /**
     * 复制文件
     * @param sourceObject 源文件
     * @param targetObject 目标文件路径
     * @return
     */
    public Boolean copyObject(String sourceObject, String targetObject) {
        try{
            // 复制对象
            customMinioClient.copyObject(
                    CopyObjectArgs.builder()
                            .bucket(minioConfigInfo.getBucket())
                            .object(targetObject)
                            .source(CopySource.builder()
                                    .bucket(minioConfigInfo.getBucket())
                                    .object(sourceObject)
                                    .build())
                            .build());
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }

    @NotNull
    private  List<Part> getParts(String object, String uploadId) throws Exception {
        int partNumberMarker = 0;
        boolean isTruncated = true;
        List<Part> parts = new ArrayList<>();
        while(isTruncated){
            ListPartsResponse partResult = customMinioClient.listMultipart(minioConfigInfo.getBucket(), null, object, 1000, partNumberMarker, uploadId, null, null);
            parts.addAll(partResult.result().partList());
            // 检查是否还有更多分片
            isTruncated = partResult.result().isTruncated();
            if (isTruncated) {
                // 更新partNumberMarker以获取下一页的分片数据
                partNumberMarker = partResult.result().nextPartNumberMarker();
            }
        }
        return parts;
    }


    /**
     * 删除文件
     */
    public Boolean deleteObject(String object)   {
        try{
            RemoveObjectArgs build = RemoveObjectArgs.builder().bucket(minioConfigInfo.getBucket()).object(object).build();
            customMinioClient.removeObject(build);
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }
}
