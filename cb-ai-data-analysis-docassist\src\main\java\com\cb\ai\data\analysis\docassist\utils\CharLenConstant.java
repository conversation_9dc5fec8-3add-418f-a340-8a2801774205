package com.cb.ai.data.analysis.docassist.utils;

/**
 * 字符占位长度标记常量
 */
public class CharLenConstant {

    /**
     * 空格占位长度
     */
    public static final Long SPACE = 50L;
    /**
     * 数字占位长度
     */
    public static final Long NUMBER_CHAR = 71L;

    /**
     * 一个汉字占为长度
     */
    public static final Long CHINESE_CHAR = 100L;

    /**
     * 判断是否为汉字
     *
     * @param c 字符
     */
    private static boolean isChineseCharacter(char c) {
        // 汉字的Unicode范围为0x4E00-0x9FFF
        return (c >= 0x4E00 && c <= 0x9FFF);
    }

    /**
     * 获取字符串长度
     *
     * @param character 字符串
     */
    public static Long calcCharactersLen(String character) {
        String trimChar = character.trim();
        long len = 0L;
        for (int i = 0; i < trimChar.length(); i++) {
            char c = trimChar.charAt(i);
            if (isChineseCharacter(c)) {
                len += CharLenConstant.CHINESE_CHAR;
            } else {
                len += CharLenConstant.NUMBER_CHAR;
            }
        }
        return len;
    }
}
