package com.cb.ai.data.analysis.voucher.controller;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherCheckRule;
import com.cb.ai.data.analysis.voucher.service.VoucherCheckRuleService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 凭证检测规则
 */
@Validated
@RestController
@RequestMapping(Constants.API_VOUCHER_ROOT_PATH + "/checkRule")
public class VoucherCheckRuleController extends BaseController<VoucherCheckRuleService, VoucherCheckRule> {

    @GetMapping("/page")
    public Result page(VoucherCheckRule entity){
        Wrapper<VoucherCheckRule> wrapper = buildPageWrapper(entity);
        Page<VoucherCheckRule> page = baseService.page(QueryHelper.getPage(), wrapper);
        return Result.successData(page);
    }

    private Wrapper<VoucherCheckRule> buildPageWrapper(VoucherCheckRule entity){
        LambdaQueryWrapper<VoucherCheckRule> wrapper = Wrappers.<VoucherCheckRule>lambdaQuery()
                .eq(StringUtils.isNotBlank(entity.getId()), VoucherCheckRule::getId, entity.getId())
                .eq(null != entity.getDisable(), VoucherCheckRule::getDisable, entity.getDisable())
                .like(StringUtils.isNotBlank(entity.getName()), VoucherCheckRule::getName, entity.getName())
                .like(StringUtils.isNotBlank(entity.getDescription()), VoucherCheckRule::getDescription, entity.getDescription())
//                .eq(StringUtils.isNotBlank(entity.getCreateBy()), VoucherCheckRule::getCreateBy, entity.getCreateBy())
                ;
        return wrapper;
    }

    @PostMapping()
    @XLog(title = "新增凭证检查规则", execType = ExecType.INSERT)
    public Result add(@Validated @RequestBody VoucherCheckRule entity) {
        entity.setId(IdUtil.getSnowflakeNextIdStr());
        entity.setDisable(0);
        baseService.save(entity);
        return Result.success("新增成功");
    }

    @PutMapping()
    @XLog(title = "修改凭证检查规则", execType = ExecType.INSERT)
    public Result update(@Validated(value = UpdateGroup.class) @RequestBody VoucherCheckRule entity) {
        baseService.updateById(entity);
        return Result.success("修改成功");
    }

    @DeleteMapping
    @XLog(title = "删除凭证检查规则", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "凭证检查规则ID不存在") String[] ids) {
        baseService.removeBatchByIds(Arrays.asList(ids));
        return Result.success("删除成功");
    }

    /**
     * 禁用凭证检查规则
     * @param id
     * @return
     */
    @PutMapping("/disableRule/{id}")
    public Result disableRule(@PathVariable("id") String id){
        baseService.disableRule(id);
        return Result.success("禁用成功");
    }
}
