package com.cb.ai.data.analysis.basdata.repository.finance.esBo;


import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易明细账表
 *
 * <AUTHOR>
 * @since 2025-07-14 16:32:33
 */
@Data
@Document(indexName = Constant.ES_FINANCE_DATA_INDEX + Constant.SLICING + "bas_transaction_detail_account")
@Setting(shards = 1, replicas = 0)
public class BasTransactionDetailAccountBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    //科目代码
    @Field(type = FieldType.Text)
    private String accountsCode;

    //科目名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String accountsName;

    //日期
    @Field(type = FieldType.Long)
    private LocalDateTime transactionTime;

    //业务日期
    @Field(type = FieldType.Long)
    private LocalDateTime businessTime;

    //凭证字号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String voucherNumber;

    //摘要
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String abstractInfo;

    //业务编号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String businessNumber;

    //结算方式
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String paymentsWay;

    //结算号
    @Field(type = FieldType.Text)
    private String paymentsNumber;

    //对方科目
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String oppositeAccounts;

    //关联凭证字号
    @Field(type = FieldType.Text)
    private String joinVoucherNumber;

    //借方金额
    @Field(type = FieldType.Double)
    private BigDecimal debit;

    //贷方金额
    @Field(type = FieldType.Double)
    private BigDecimal creditMount;

    //方向
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String direction;

    //余额
    @Field(type = FieldType.Double)
    private BigDecimal balance;

    //参考信息
    @Field(type = FieldType.Text)
    private String referenceInfo;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;

}

