package com.cb.ai.data.analysis.query.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.AcknowledgedResponse;
import co.elastic.clients.elasticsearch._types.ElasticsearchException;
import co.elastic.clients.elasticsearch.cat.indices.IndicesRecord;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.ReindexResponse;
import co.elastic.clients.elasticsearch.indices.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.DynamicFieldMapping;
import com.cb.ai.data.analysis.query.domain.vo.IndexInfo;
import com.cb.ai.data.analysis.query.service.IndexService;
import com.cb.ai.data.analysis.query.utils.ElasticIndexUtils;
import com.xong.boot.common.utils.ServletUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ES 索引操作服务
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    private final ElasticsearchClient client;

    public IndexServiceImpl(ElasticsearchClient client) {
        this.client = client;
    }

    @Override
    public Page<IndexInfo> indexManageList(IndexInfo info) {
        try {
            String indexName = info.getIndex();
            // 分页参数处理
            Integer pageNum = ServletUtils.getParameterToInt(QueryHelper.PARAM_NAME_PAGE_CURRENT);
            Integer pageSize = ServletUtils.getParameterToInt(QueryHelper.PARAM_NAME_PAGE_SIZE);
            // 获取所有索引数据
            List<IndicesRecord> allIndices = client.cat().indices().valueBody();

            // 过滤数据：如果 indexName 不为空，则进行模糊匹配
            List<IndexInfo> indexInfoList = allIndices.stream()
                    .filter(record -> {
                        if (StringUtils.isBlank(indexName)) {
                            return record.index().startsWith(Constant.ES_BASE_PREFIX);
                        } else {
                            return record.index().contains(indexName);
                        }
                    })
                    .map(record -> new IndexInfo(
                            record.uuid(),
                            record.index(),
                            record.health(),
                            record.status(),
                            Integer.parseInt(record.pri()),
                            Integer.parseInt(record.rep()),
                            Long.parseLong(record.docsCount()),
                            Long.parseLong(record.docsDeleted()),
                            record.storeSize(),
                            record.priStoreSize()
                    ))
                    .skip((long) (pageNum - 1) * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());

            // 构建分页对象
            Page<IndexInfo> page = new Page<>();
            page.setCurrent(pageNum);
            page.setSize(pageSize);
            page.setTotal(allIndices.size());
            page.setRecords(indexInfoList);

            return page;
        } catch (IOException | ElasticsearchException e) {
            log.error("获取索引列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取索引列表失败", e);
        }
    }


    /**
     * 手动创建索引（带自定义映射）
     *
     * @param indexName 索引名称
     * @param mappings  字段映射
     * @return
     */
    @Override
    public boolean createIndex(String indexName, List<DynamicFieldMapping> mappings) {
        try {
            if (existsIndex(indexName)) {
                throw new RuntimeException("索引已存在");
            }
            if (mappings == null || mappings.isEmpty()) {
                throw new IllegalArgumentException("字段映射不能为空，无法创建索引。");
            }
            CreateIndexResponse response = client.indices().create(b -> b
                    .index(indexName)
                    .mappings(m -> {
                        ElasticIndexUtils.buildFieldMapping(m, mappings);
                        return m;
                    })
            );
            return response.acknowledged();
        } catch (IOException | ElasticsearchException e) {
            log.error("创建索引失败: {}", e.getMessage(), e);
            return false;
        }
    }


    @Override
    public boolean addFieldMapping(String indexName, List<DynamicFieldMapping> mappings) {
        try {
            // 1. 检查索引是否存在
            if (!existsIndex(indexName)) {
                throw new RuntimeException("索引不存在");
            }

            // 2. 检查字段映射是否为空
            if (mappings == null || mappings.isEmpty()) {
                throw new IllegalArgumentException("字段映射不能为空，无法创建索引。");
            }

            // 3.构建 PutMappingRequest 请求
            PutMappingRequest request = PutMappingRequest.of(b -> {
                        b.index(indexName);
                        ElasticIndexUtils.addFieldMapping(b, mappings);
                        return b;
                    }
            );

            // 4. 执行更新映射
            AcknowledgedResponse response = client.indices().putMapping(request);

            // 5. 返回更新结果
            if (response.acknowledged()) {
                log.info("索引映射更新成功: {}", indexName);
                return true;
            } else {
                log.error("索引映射更新未被确认: {}", indexName);
                return false;
            }
        } catch (IOException | ElasticsearchException e) {
            log.error("索引映射更新失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 迁移数据到新索引
     *
     * @param sourceIndex 源索引名称
     * @param destIndex   目标索引名称
     * @return
     */
    public Long migrationData(String sourceIndex, String destIndex) {
        try {
            if (!existsIndex(sourceIndex)) {
                log.warn("源索引不存在，无法迁移数据: {}", sourceIndex);
                throw new RuntimeException("源索引不存在");
            }
            if (!existsIndex(destIndex)) {
                log.warn("目标索引不存在: {}", destIndex);
                throw new RuntimeException("目标索引不存在");
            }
            log.info("开始迁移数据: {} -> {}", sourceIndex, destIndex);
            ReindexResponse response = client.reindex(r -> r
                    .source(s -> s.index(sourceIndex))
                    .dest(d -> d.index(destIndex))
            );
            log.info("数据迁移完成: {} -> {}, 创建了 {} 条数据", sourceIndex, destIndex, response.created());
            return response.created();
        } catch (IOException | ElasticsearchException e) {
            log.error("数据迁移失败: {}", e.getMessage(), e);
            return 0L;
        }
    }


    /**
     * 删除索引
     *
     * @param indexName 索引名称
     * @return
     */
    @Override
    public boolean deleteIndex(String indexName) {
        try {
            // 删除前校验索引是否存在
            if (!existsIndex(indexName)) {
                log.warn("索引不存在，无法删除: {}", indexName);
                throw new RuntimeException("索引中存在数据，无法删除");
            }

            // 检查索引下是否存在数据
            CountResponse countResponse = client.count(b -> b.index(indexName));
            if (countResponse.count() > 0) {
                log.warn("索引 {} 中存在数据，无法删除", indexName);
                throw new RuntimeException("索引中存在数据，无法删除");
            }

            DeleteIndexResponse response = client.indices().delete(b -> b.index(indexName));
            return response.acknowledged();
        } catch (IOException | ElasticsearchException e) {
            log.error("删除索引时发生错误: {}, 错误信息: {}", indexName, e.getMessage());
            return false;
        }
    }

    /**
     * 查看索引是否存在
     *
     * @param indexName 索引名称
     * @return
     */
    @Override
    public boolean existsIndex(String indexName) {
        try {
            return client.indices().exists(b -> b.index(indexName)).value();
        } catch (Exception e) {
            log.error("检查索引是否存在失败: {}", e.getMessage());
            throw new RuntimeException("检查索引是否存在失败", e);
        }
    }
}