<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.dbtable.mapper.AnalysisDbTableTaskItemMapper">
    <resultMap type="com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem" id="DynamicTableTaskItemResult">
        <result property="id" column="id"/>
        <result property="jobId" column="job_id"/>
        <result property="dataId" column="data_id"/>
        <result property="sourceData" column="source_data"/>
        <result property="targetData" column="target_data"/>
        <result property="confirmBy" column="confirm_by"/>
        <result property="confirmTime" column="confirm_time"/>
        <result property="errorMessage" column="error_message"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDynamicTableTaskItemVo">
        SELECT id,
               job_id,
               data_id,
               source_data,
               target_data,
               confirm_by,
               confirm_time,
               error_message,
               status,
               create_by,
               create_time,
               update_by,
               update_time
        FROM analysis_db_table_task_item
    </sql>

    <select id="pageTableTaskItemList" parameterType="com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTableTaskItem" resultMap="DynamicTableTaskItemResult">
        <include refid="selectDynamicTableTaskItemVo"/>
        <where>
            <if test="et.jobId != null and et.jobId != ''">AND job_id = #{et.jobId}</if>
            <if test="et.dataId != null and et.dataId != ''">AND data_id LIKE CONCAT('%', #{et.dataId}, '%')</if>
            <if test="et.confirmBy != null and et.confirmBy != ''">AND confirm_by = #{et.confirmBy}</if>
            <if test="et.confirmTime != null">AND confirm_time = #{et.confirmTime}</if>
            <if test="et.status != null">AND status = #{et.status}</if>
        </where>
        ORDER BY status DESC, id DESC
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO analysis_db_table_task_item (id, job_id, data_id, source_data, target_data, confirm_by, confirm_time, error_message, status, create_by, create_time, update_by, update_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.jobId}, #{item.dataId}, #{item.sourceData}, #{item.targetData}, #{item.confirmBy}, #{item.confirmTime}, #{item.errorMessage}, #{item.status}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
