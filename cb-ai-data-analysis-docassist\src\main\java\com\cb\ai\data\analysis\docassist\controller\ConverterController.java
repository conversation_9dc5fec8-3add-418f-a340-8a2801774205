package com.cb.ai.data.analysis.docassist.controller;

import com.cb.ai.data.analysis.docassist.request.WordFormatRequestVo;
import com.cb.ai.data.analysis.docassist.service.ConverterService;
import com.xong.boot.common.api.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("/converter")
public class ConverterController {

    @Resource
    private ConverterService converterService;

    @PostMapping("/common")
    public Result common(@RequestBody WordFormatRequestVo wordFormatRequestVo) {
        String newFileAbsolutePath;
        try {
            newFileAbsolutePath = converterService.formatWord(wordFormatRequestVo);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Result.success("生成成功", newFileAbsolutePath);
    }
}
