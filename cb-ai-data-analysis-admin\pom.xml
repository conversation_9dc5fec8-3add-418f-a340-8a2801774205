<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cb.ai</groupId>
        <artifactId>cb-ai-data-analysis</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>cb-ai-data-analysis-admin</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xong.boot</groupId>
            <artifactId>xong-boot-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-query</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-knowledge</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-dbtable</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-graph</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-docassist</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-petition</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-basdata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-voucher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cb.ai</groupId>
            <artifactId>cb-ai-data-analysis-json-tool</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.cb.ai.data.analysis.Application</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
