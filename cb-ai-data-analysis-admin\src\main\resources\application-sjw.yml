# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  tomcat:
    connection-timeout: 30000ms

spring:
  codec:
    max-in-memory-size: 10MB
  datasource:
    dynamic:
      datasource:
        master:
          url: **********************************************************************************************************************************************************************
          username: root
          password: 1234
          driver-class-name: com.mysql.cj.jdbc.Driver
        clickhouse:
          url: *************************************************************************
          username: default
          password: 123456
          driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
          druid:
            filters: stat #去除wall不然无法创建表
  data:
    redis:
      host: ***********
      port: 6379
      password:
    neo4j:
      database: neo4j

  # 文件上传大小设置
  servlet:
    multipart:
      max-file-size: 2050MB
      max-request-size: 2100MB

  # elasticsearch 配置
  elasticsearch:
    uris:
      - http://************:9200  # 测试环境 可新增多个节点
    username:
    password:
    connectionTimeout: 30
    readTimeout: 30

  # neo4j 配置
  neo4j:
    uri: neo4j://***********:7687
    authentication:
      username: neo4j
      password: 1q2w3e4r

cb:
  dbtable:
    db-name: db_data_analysis #动态表数据库名
  ai:
    # Authorization: 1
    # url:
    # knowledgeBase: http://***********:8686/cb-ai/knowledge/base
    # knowledgeVectorSearch: http://***********:8686/cb-ai/rag/embedding/search
    # chat: http://***********:8686/cb-ai/chat
    # knowledgeFile: http://***********:8686/cb-ai/knowledge/base/file
    # textEmbeddingVector: http://***********:8686/cb-ai/rag/embedding/textVector
    embeddingChat:
      topK: 100  # 私有化向量查询问答topK设置
    # 下面的配置是cb-ai-data-analysis-ai模块的配置
    private-ai-base:
      base-url: http://************:8686/cb-ai
      llm:
        model: qwen3
        role: user
        temperature: 0.7
      header-map:
        Authorization: 1
      chat:
        stream-url: /chat/basic
        block-url: /chat/sync
      knowledge: /chat/knowledge
      ocr: /rag/ocr
      file-store: /knowledge/base/file/saveupload
      finance: http://***********:8055/api/v1/chat/stream
      file-analysis: /tool/file/analysis/upload
      audio-analysis:
        base-url: http://************:5306/api/v1
        invoke-url: /upload
        health-url: /health
        task-status-url: /status/{task_id}
        task-result-url: /task/{task_id}
        task-list-url: /tasks
        delete-task-url: /delete/{task_id}
      deep-think-url: http://************:8121/api/chat/stream
      embedding-url: /rag/embedding/search # 向量查询地址
    base-url: http://************:8686
    py-base-url: http://************:8121

logging:
  level:
    #    org: debug
    #    org.springframework: debug
    #    com.baomidou.dynamic: debug
    com.xong.boot: debug
    #    org.activiti.engine.impl.persistence.entity: debug
    org.springframework.data.neo4j: debug

minio:
  endpoint: http://***********:9600
  bucket: cb-ai-knowledge
  accessKey: minioadmin
  secretKey: minioadmin123
  expiry: 1 # redis 文件信息失效时间 天
  breakpointTime: 1 # 分片地址失效时间 天

upload:
  xfFolderId: 1946100942269550592  # 信访库文件上传目录
  problemFolderId: 1946101241042407424 #问答批处理文件上传目录
