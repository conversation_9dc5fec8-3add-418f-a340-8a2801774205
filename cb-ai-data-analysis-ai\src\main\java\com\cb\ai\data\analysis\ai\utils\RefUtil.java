package com.cb.ai.data.analysis.ai.utils;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.TypeUtil;
import com.cb.ai.data.analysis.ai.common.log.CommonLog;
import com.cb.ai.data.analysis.ai.domain.common.FieldHandle;
import com.google.common.base.Throwables;
import org.springframework.core.ParameterizedTypeReference;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.lang.invoke.VarHandle;
import java.lang.reflect.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/2 10:05
 * @Copyright (c) 2025
 * @Description 反射工具类
 */
public class RefUtil extends ClassUtil {

    /**
     * @description 获取类方法
     * @param clazz 类
     * @param methodName 方法名
     * @param returnType 方法返回类型
     * @param parameterTypes 方法参数类型列表
     * @return MethodHandle 方法句柄
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static MethodHandle getMethod(Class<?> clazz, String methodName, ParameterizedTypeReference<?> returnType, Class<?>... parameterTypes) {
        try {
            MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
            return lookup.findVirtual(clazz, methodName, MethodType.methodType(getRawType(returnType), parameterTypes));
        } catch (Exception e) {
            CommonLog.error("未找到方法:{}, 原因：{}", methodName, Throwables.getRootCause(e).getMessage());
            Throwables.throwIfUnchecked(e);
            return null;
        }
    }

    /**
     * @description 执行类方法
     * @param classObj 对象
     * @param methodName 方法名
     * @param returnType 方法返回类型
     * @param parameters 方法参数列表
     * @return T 方法返回值
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T invokeMethod(Object classObj, String methodName, ParameterizedTypeReference<T> returnType, Object... parameters) throws Throwable {
        MethodHandle method = getMethod(classObj.getClass(), methodName, returnType, convertParamToClass(parameters));
        return invoke(method, classObj, parameters);
    }

    /**
     * @description 执行类方法
     * @param classObj 对象
     * @param method 方法
     * @param parameters 方法参数列表
     * @return T 方法返回值
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T invokeMethod(Object classObj, Method method, Object... parameters) throws Throwable {
        MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(classObj.getClass(), MethodHandles.lookup());
        return invoke(lookup.unreflect(method), classObj, parameters);
    }

    /**
     * @description 获取类的静态方法
     * @param clazz 类
     * @param methodName 静态方法名
     * @param returnType 方法返回类型
     * @param parameterTypes 方法参数类型列表
     * @return MethodHandle 方法句柄
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static MethodHandle getStaticMethod(Class<?> clazz, String methodName, ParameterizedTypeReference<?> returnType, Class<?>... parameterTypes) {
        try {
            MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
            return lookup.findStatic(clazz, methodName, MethodType.methodType(getRawType(returnType), parameterTypes));
        } catch (Exception e) {
            CommonLog.error("未找到方法:{}, 原因：{}", methodName, Throwables.getRootCause(e).getMessage());
            Throwables.throwIfUnchecked(e);
            return null;
        }
    }

    /**
     * @description 执行类静态方法
     * @param classObj 对象
     * @param methodName 静态方法名
     * @param returnType 方法返回类型
     * @param parameters 方法参数列表
     * @return T 方法返回值
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T invokeStaticMethod(Object classObj, String methodName, ParameterizedTypeReference<T> returnType, Object... parameters) throws Throwable {
        MethodHandle method = getStaticMethod(classObj.getClass(), methodName, returnType, convertParamToClass(parameters));
        return invoke(method, classObj, parameters);
    }

    /**
     * @description 执行类静态方法
     * @param classObj 对象
     * @param method 静态方法
     * @param parameters 方法参数列表
     * @return T 方法返回值
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T invokeStaticMethod(Object classObj, Method method, Object... parameters) throws Throwable {
        return invokeMethod(classObj, method, parameters);
    }

    /**
     * @description 获取类的构造器
     * @param clazz 类
     * @param parameterTypes 构造参数类型列表
     * @return MethodHandle 方法句柄
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static MethodHandle getConstructor(Class<?> clazz, Class<?>... parameterTypes) {
        try {
            MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
            return lookup.findConstructor(clazz, MethodType.methodType(void.class, parameterTypes));
        } catch (Exception e) {
            CommonLog.error("未找到{}({})构造函数, 原因：{}", clazz.getSimpleName(), StrUtil.join(",", parameterTypes), Throwables.getRootCause(e).getMessage());
            Throwables.throwIfUnchecked(e);
            return null;
        }
    }

    /**
     * @description 根据构造函数创建对象
     * @param clazz 类
     * @param parameters 方法参数列表
     * @return T 对象
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T invokeConstructor(Class<T> clazz, Object... parameters) throws Throwable {
        MethodHandle method = getConstructor(clazz, convertParamToClass(parameters));
        return invoke(method, null, parameters);
    }

    /**
     * @description 设置类的字段值
     * @param varHandle 字段句柄
     * @param classObj 对象
     * @param value 值
     * @return List<VarHandle> 字段集合（包含常量，静态等）
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static void invokeField(FieldHandle varHandle, Object classObj, Object value) throws Throwable {
        varHandle.set(classObj, value);
    }

    /**
     * @description 获取类的指定字段
     * @param clazz 类
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @return VarHandle 字段句柄
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static FieldHandle getField(Class<?> clazz, String fieldName, Class<?> fieldType) {
        try {
            MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
            Field field = getDeclaredField(clazz, fieldName);
            VarHandle varHandle;
            if (Modifier.isStrict(field.getModifiers())) {
                varHandle = lookup.findStaticVarHandle(clazz, fieldName, fieldType);
            } else {
                varHandle = lookup.findVarHandle(clazz, fieldName, fieldType);
            }
            return new FieldHandle(varHandle, field);
        } catch (Exception e) {
            CommonLog.error("未找到{}的名为{}的字段, 原因：{}", clazz.getSimpleName(), fieldName, Throwables.getRootCause(e).getMessage());
            Throwables.throwIfUnchecked(e);
            return null;
        }
    }

    /**
     * @description 获取类的所有字段
     * @param clazz 类
     * @return List<VarHandle> 字段集合
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static List<FieldHandle> getAllFields(Class<?> clazz) {
        return getFields(clazz);
    }

    /**
     * @description 获取类的静态字段和常规字段
     * @param clazz 类
     * @return List<VarHandle> 字段集合
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static List<FieldHandle> getStaticAndNormalFields(Class<?> clazz) {
        return getFields(clazz, Modifier::isFinal);
    }

    /**
     * @description 获取类的非常量和静态字段
     * @param clazz 类
     * @return List<VarHandle> 字段集合
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static List<FieldHandle> getNormalFields(Class<?> clazz) {
        return getFields(clazz, Modifier::isFinal, Modifier::isStatic);
    }

    /**
     * @description 获取类的私有字段
     * @param clazz 类
     * @return List<VarHandle> 字段集合（包含常量，静态等）
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    public static List<FieldHandle> getPrivateFields(Class<?> clazz) {
        return getFields(clazz, Modifier::isFinal, Modifier::isStatic, Modifier::isPublic, Modifier::isProtected);
    }

    /**
     * @description 获取类及其所有父类的所有字段
     * @param clazz 类
     * @return List<VarHandle> 字段集合（包含常量，静态等）
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    @SafeVarargs
    private static List<FieldHandle> getFields(Class<?> clazz, Predicate<Integer>... modifiersPredicates) {
        List<FieldHandle> varHandles = new ArrayList<>();
        if (clazz == null) {
            return varHandles;
        }
        try {
            MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
            while (clazz != null && clazz != Object.class) {
                for (Field field : clazz.getDeclaredFields()) {
                    if (!shouldProcessField(field, modifiersPredicates)) {
                        continue;
                    }
                    try {
                        varHandles.add(new FieldHandle(lookup.unreflectVarHandle(field), field));
                    }  catch (IllegalAccessException ignored) {
                        CommonLog.error("无法访问字段: {}", field.getName());
                    }
                }
                clazz = clazz.getSuperclass();
            }
        } catch (Exception e) {
            CommonLog.error("未找到{}类的相关字段, 原因：{}", clazz.getSimpleName(), Throwables.getRootCause(e).getMessage());
            Throwables.throwIfUnchecked(e);
        }
        return varHandles;
    }

    private static boolean shouldProcessField(Field field, Predicate<Integer>... predicates) {
        if (predicates == null || predicates.length == 0) {
            return true;
        }
        int modifiers = field.getModifiers();
        for (var predicate : predicates) {
            if (predicate.test(modifiers)) {
                return false;
            }
        }
        return true;
    }

    /*
     * 获取类型的原始类型
     */
    private static Class<?> getRawType(ParameterizedTypeReference<?> parameterizedTypeReference) {
        Type type = parameterizedTypeReference.getType();
        if (type instanceof Class) {
            return (Class<?>) type;
        } else {
            return (Class<?>) ((ParameterizedType) type).getRawType();
        }
    }

    /**
     * @description 转换参数类型列表
     * @param parameters 方法参数列表
     * @return Class<?>[] 方法参数类型列表
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    private static Class<?>[] convertParamToClass(Object... parameters) {
        Class<?>[] parameterTypes = new Class<?>[parameters.length];
        for (int i = 0; i < parameters.length; i++) {
            parameterTypes[i] = parameters[i].getClass();
        }
        return parameterTypes;
    }

    /**
     * @description 执行句柄
     * @param classObj 对象
     * @param methodName 方法名
     * @param returnType 方法返回类型
     * @param parameters 方法参数列表
     * @return T 方法返回值
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    @SuppressWarnings("unchecked")
    private static <T> T invoke(MethodHandle methodHandle, Object classObj, Object... parameters) throws Throwable {
        if (methodHandle != null) {
            if (classObj == null) {
                return (T) methodHandle.invokeWithArguments(parameters);
            }
            return (T) methodHandle.bindTo(classObj).invokeWithArguments(parameters);
        }
        return null;
    }

    /**
     * @description 获取指定类的泛型
     * @param clazz 对象类型
     * @param index 泛型下标
     * @param predicate 类型判断
     * @return Class<?> 泛型类型
     * @createtime 2025/7/2 下午2:50
     * <AUTHOR>
     * @version 1.0
     */
    @SafeVarargs
    public static <R> Class<R> getGenericType(Class<?> clazz, int index, Predicate<Type>... predicates) {
        // 参数校验
        if (clazz == null) {
            throw new IllegalArgumentException("Class cannot be null");
        }
        if (index < 0) {
            throw new IllegalArgumentException("Index cannot be negative");
        }
        Class<?> currentType = clazz;
        // 如果有类型判断条件，则向上查找匹配的父类
        if (predicates != null && predicates.length > 0 && predicates[0] != null) {
            while (currentType != Object.class && currentType.getSuperclass() != Object.class) {
                if (predicates[0].test(currentType.getSuperclass())) {
                    break;
                }
                currentType = currentType.getSuperclass();
            }
        }
        // 获取泛型参数类型
        Class<?> typeArgument = (Class<?>) TypeUtil.getTypeArgument(currentType, index);
        if (typeArgument == null) {
            throw new IllegalStateException("Could not determine generic type at index " + index);
        }
        @SuppressWarnings("unchecked")
        Class<R> result = (Class<R>) typeArgument;
        return result;
    }

}
