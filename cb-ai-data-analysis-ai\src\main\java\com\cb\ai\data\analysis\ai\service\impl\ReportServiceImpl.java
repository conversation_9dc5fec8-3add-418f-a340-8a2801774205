package com.cb.ai.data.analysis.ai.service.impl;

import cn.hutool.core.util.StrUtil;
import com.aspose.words.*;
import com.cb.ai.data.analysis.ai.common.client.DocAssistClient;
import com.cb.ai.data.analysis.ai.domain.request.HtmlToWordRequest;
import com.cb.ai.data.analysis.ai.domain.request.WordFormatRequest;
import com.cb.ai.data.analysis.ai.service.IReportService;
import com.xong.boot.common.utils.DateUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

@Slf4j
@Service
public class ReportServiceImpl implements IReportService {

    @Resource
    private DocAssistClient docAssistClient;

    private static Map<String, FootnoteData> extractFootnotes(String html) {
        Map<String, FootnoteData> footnotesMap = new LinkedHashMap<>();

        org.jsoup.nodes.Document doc = Jsoup.parse(html);
        Elements supTags = doc.select("sup[data-footnote]");

        for (Element sup : supTags) {
            String refText = sup.text().trim();
            String footnoteText = sup.attr("data-footnote").trim();

            // 只保留第一个遇到的相同 refText 的脚注内容（避免冲突）
            if (!footnotesMap.containsKey(refText)) {
                footnotesMap.put(refText, new FootnoteData(refText, footnoteText));
            }
        }

        return footnotesMap;
    }

    private static String removeDataAttributes(String html) {
        return html.replaceAll("<sup\\s+data-footnote=\"[^\"]*\"([^>]*)>", "<sup$1>");
    }

    private static void processSharedFootnotes(Document doc, Map<String, FootnoteData> footnotesMap)
            throws Exception {

        DocumentBuilder builder = new DocumentBuilder(doc);
        List<String> footnoteCreated = new ArrayList<>();

        // 收集待删除的 run
        List<Run> runsToRemove = new ArrayList<>();

        // 遍历所有段落，而不是所有 Run
        NodeCollection paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
        for (Object paraObj : paragraphs) {
            Paragraph para = (Paragraph) paraObj;

            // 只获取当前段落下的 Run（非递归）
            NodeCollection runs = para.getChildNodes(NodeType.RUN, false);
            for (Object runObj : runs) {
                Run run = (Run) runObj;

                // 先判断是否上标，再判断文本
                if (!run.getFont().getSuperscript()) continue;

                String text = run.getText().trim();

                // 确保文本是纯数字角标（可选）
                if (!text.matches("\\d+")) continue;

                // 只处理你知道的脚注
                if (!footnotesMap.containsKey(text)) continue;

                FootnoteData data = footnotesMap.get(text);
                builder.moveTo(run);

                if (!footnoteCreated.contains(text)) {
                    // 首次：插入脚注
                    Footnote footnote = builder.insertFootnote(FootnoteType.FOOTNOTE, "");
                    builder.moveTo(footnote.getFirstParagraph());
                    builder.insertHyperlink(data.footnoteText, "相关引用文件/" + data.footnoteText, false);

                    setBookmarkOnFootnote(footnote, data.bookmarkId);
                    footnoteCreated.add(text);
                } else {
                    // 重复：插入超链接
                    String bookmarkId = footnotesMap.get(text).bookmarkId;
                    if (bookmarkId != null) {
                        builder.insertHyperlink(text, bookmarkId, true);

                        Run newRun = (Run) builder.getCurrentNode();
                        if (newRun == null) {
                            newRun = findNewHyperlinkRun(para, text);
                        }
                        if (newRun != null) {
                            newRun.getFont().setSuperscript(true);
                            newRun.getFont().setColor(java.awt.Color.BLACK);
                            newRun.getFont().setUnderline(Underline.NONE);
                        }
                    }
                }

                runsToRemove.add(run);
            }
        }

        // 统一删除
        for (Run run : runsToRemove) {
            try {
                if (run.getParentNode() != null) {
                    run.remove();
                }
            } catch (Exception e) {
                // 忽略已被删除的
            }
        }
    }

    /**
     * 安全查找新插入的超链接Run
     */
    private static Run findNewHyperlinkRun(CompositeNode parentNode, String refText) {
        NodeCollection runs = parentNode.getChildNodes(NodeType.RUN, false);
        for (int i = runs.getCount() - 1; i >= 0; i--) {
            Run run = (Run) runs.get(i);
            if (run.getText().trim().equals(refText) && run.getFont().getSuperscript()) {
                return run;
            }
        }
        return null;
    }

    /**
     * 在脚注内容上设置书签
     */
    private static void setBookmarkOnFootnote(Footnote footnote, String bookmarkId) throws Exception {
        Paragraph firstPara = (Paragraph) footnote.getChild(NodeType.PARAGRAPH, 0, true);
        if (firstPara != null) {
            DocumentBuilder builder = new DocumentBuilder((Document) footnote.getDocument());
            builder.moveTo(firstPara);
            builder.startBookmark(bookmarkId);
            builder.write("\u200B"); // 零宽空格
            builder.endBookmark(bookmarkId);
        }
    }

    @Override
    public String htmlToDocx(HttpServletRequest request, HtmlToWordRequest htmlToWordRequestVo) {
        String html = htmlToWordRequestVo.html();

        Document doc = null;
        String filePath;

        String fileName = htmlToWordRequestVo.fileName();
        String formatFileName;

        if (StrUtil.isBlank(fileName)) {
            fileName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "_" + "报告.docx";
        }

        log.info(Arrays.toString(fileName.split("\\.")));

        formatFileName = fileName.split("\\.")[0] + "_" + "format" + ".docx";

        String finalFilePath;
        try {
            String uploadPath = String.join(File.separator, System.getProperty("user.dir"), "report_gen", DateUtils.date().toString("yyyy/MM/dd")) + File.separator;

//            ByteArrayInputStream inputStream = new ByteArrayInputStream(html.getBytes(StandardCharsets.UTF_8));
//
//            doc = new Document(inputStream);

            doc = new Document();

            // 设置脚注序号样式
            Style footnoteRefStyle = doc.getStyles().getByStyleIdentifier(StyleIdentifier.FOOTNOTE_REFERENCE);
            Font font = footnoteRefStyle.getFont();
            font.setSuperscript(false); // 关键：关闭上标
            font.setSize(8);           // 可选：设置字体大小
            font.setName("Arial");      // 可选：设置字体

            // 设置脚注内容样式
            Style footnoteTextStyle = doc.getStyles().getByStyleIdentifier(StyleIdentifier.FOOTNOTE_TEXT);
            footnoteTextStyle.getFont().setSize(8);
            footnoteTextStyle.getFont().setColor(java.awt.Color.BLACK);
            footnoteTextStyle.getFont().setName("宋体");
            footnoteTextStyle.getFont().setSuperscript(false);

            // 获取所有脚注信息
            Map<String, FootnoteData> footnotes = extractFootnotes(html);
            String cleanHtml = removeDataAttributes(html);
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.insertHtml(cleanHtml);
            processSharedFootnotes(doc, footnotes);

            doc.save(uploadPath + fileName, SaveFormat.DOCX);

            if (htmlToWordRequestVo.whetherFormat()) {
                WordFormatRequest wordFormatRequestVo = new WordFormatRequest();
                wordFormatRequestVo.setOldFileAbsolutePath(uploadPath + fileName);
                wordFormatRequestVo.setNewFileDir(uploadPath);
                wordFormatRequestVo.setNewFileName(formatFileName);
                wordFormatRequestVo.setWhetherMarkdown(htmlToWordRequestVo.whetherMarkdown());

                finalFilePath = docAssistClient.formatDocx(request, wordFormatRequestVo);
            } else {
                finalFilePath = uploadPath + fileName;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return finalFilePath;
    }

    static class FootnoteData {
        String refText;
        String footnoteText;
        String bookmarkId;

        FootnoteData(String refText, String footnoteText) {
            this.refText = refText;
            this.footnoteText = footnoteText;
            this.bookmarkId = "footnote_" + Objects.hash(footnoteText);
        }
    }


}
