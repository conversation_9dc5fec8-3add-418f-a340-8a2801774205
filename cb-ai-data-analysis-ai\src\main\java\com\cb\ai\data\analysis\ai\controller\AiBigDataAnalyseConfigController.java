package com.cb.ai.data.analysis.ai.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.ai.domain.entity.AiBigDataAnalyseConfig;
import com.cb.ai.data.analysis.ai.service.AiBigDataAnalyseConfigService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.easyexcel.EasyExcelService;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 大数据分析配置(AiBigDataAnalyseConfig)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-27 15:25:48
 */
@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/bigdata/analyse/config")
public class AiBigDataAnalyseConfigController extends BaseController<AiBigDataAnalyseConfigService, AiBigDataAnalyseConfig> {

    @Resource
    private EasyExcelService easyExcelService;

    /**
     * 分页获取大数据分析配置
     *
     * @param aiBigDataAnalyseConfig 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(AiBigDataAnalyseConfig aiBigDataAnalyseConfig) {
        LambdaQueryWrapper<AiBigDataAnalyseConfig> queryWrapper = XQueryWrapper.newInstance(aiBigDataAnalyseConfig)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(AiBigDataAnalyseConfig::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 获取大数据分析配置
     *
     * @param aiBigDataAnalyseConfig 查询实体
     * @return 相关数据
     */
    @GetMapping("/list")
    public Result list(AiBigDataAnalyseConfig aiBigDataAnalyseConfig) {
        LambdaQueryWrapper<AiBigDataAnalyseConfig> queryWrapper = XQueryWrapper.newInstance(aiBigDataAnalyseConfig)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(AiBigDataAnalyseConfig::getCreateTime);
        return Result.successData(baseService.list(queryWrapper));
    }

    /**
     * 获取大数据分析配置
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "大数据分析配置ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增大数据分析配置
     *
     * @param aiBigDataAnalyseConfig 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增大数据分析配置", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody AiBigDataAnalyseConfig aiBigDataAnalyseConfig) {
        if (baseService.save(aiBigDataAnalyseConfig)) {
            return Result.success("大数据分析配置新增成功！");
        }
        return Result.fail("大数据分析配置新增失败！");
    }

    /**
     * 修改大数据分析配置
     *
     * @param aiBigDataAnalyseConfig 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改大数据分析配置", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody AiBigDataAnalyseConfig aiBigDataAnalyseConfig) {
        if (baseService.updateById(aiBigDataAnalyseConfig)) {
            return Result.success("大数据分析配置修改成功！");
        }
        return Result.fail("大数据分析配置修改失败！");
    }

    /**
     * 删除大数据分析配置
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除大数据分析配置", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "大数据分析配置ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.removeByIds(list)) {
            return Result.success("大数据分析配置删除成功！");
        }
        return Result.fail("大数据分析配置删除失败！");
    }

    @PostMapping("/import")
    @XLog(title = "导入大数据分析配置", execType = ExecType.IMPORT)
    public Result importData(MultipartFile file) {
        AtomicInteger count = new AtomicInteger();
        List<String> errMsgList = new ArrayList<>();
        try {
            easyExcelService.importExcel(
                    file.getInputStream(),
                    AiBigDataAnalyseConfig.class,
                    entityList -> {
                        count.addAndGet(entityList.size());
                        return baseService.saveBatch(entityList);
                    },
                    errorMessage -> errMsgList.add(errorMessage)
            );
        } catch (Exception e) {
            // 记录异常信息
            return Result.fail("导入大数据分析配置失败: " + e.getMessage());
        }
        StringBuilder msg = new StringBuilder();
        if (!errMsgList.isEmpty()) {
            msg.append("</br>导入失败！部分记录中的内容不正确：</br>" + String.join("</br>", errMsgList));
        }
        if (count.get() > 0) {
            msg.insert(0, "成功导入<span style=\"color:red;\">" + count + "</span>条数据!");
        }
        return Result.success(msg.toString());
    }

    @GetMapping("/importTemplate")
    @XLog(title = "下载大数据分析配置导入模板", execType = ExecType.DOWNLOAD)
    public void importTemplate(HttpServletResponse response) {
        easyExcelService.downloadExcelTemplate(AiBigDataAnalyseConfig.class,
                "大数据分析配置导入模板", "大数据分析配置", response);
    }


    @GetMapping("/export")
    @XLog(title = "导出大数据分析配置", execType = ExecType.EXPORT)
    public void exportData(AiBigDataAnalyseConfig family, HttpServletResponse response) {
        LambdaQueryWrapper<AiBigDataAnalyseConfig> queryWrapper = XQueryWrapper.newInstance(family)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(AiBigDataAnalyseConfig::getCreateTime);
        List<AiBigDataAnalyseConfig> list = baseService.list(queryWrapper);
        easyExcelService.exportExcel(AiBigDataAnalyseConfig.class, "大数据分析配置", "大数据分析配置", list, response);
    }
}

