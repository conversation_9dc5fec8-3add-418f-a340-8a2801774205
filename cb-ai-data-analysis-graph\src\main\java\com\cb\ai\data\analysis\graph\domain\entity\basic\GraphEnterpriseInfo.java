package com.cb.ai.data.analysis.graph.domain.entity.basic;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识图谱-企业信息(GraphEnterpriseInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-04 11:06:14
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class GraphEnterpriseInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    //ID
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private String id;

    //企业名称
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "企业名称")
    private String enterpriseName;

    //法定代表人
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "法定代表人")
    private String legalRepresentative;

    //注册资本
    @ExcelProperty(value = "注册资本")
    private String registerCapital;

    //登记状态
    @ExcelProperty(value = "登记状态")
    private String registerStatus;

    //统一社会信用代码
    @TableField(condition = SqlCondition.LIKE)
    @ExcelProperty(value = "统一社会信用代码")
    private String unifySocialCreditCode;

    //成立日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "成立日期")
    private LocalDateTime foundDate;

    //工商注册号
    @ExcelProperty(value = "工商注册号")
    private String industryCommerceRegisterNo;

    //纳税人识别号
    @ExcelProperty(value = "纳税人识别号")
    private String taxpayerIdentifyNo;

    //企业类型
    @ExcelProperty(value = "企业类型")
    private String enterpriseType;

    //核准日期
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ExcelProperty(value = "核准日期")
    private LocalDateTime approveDate;

    //曾用名
    @ExcelProperty(value = "曾用名")
    private String formerName;

    //实缴资本
    @ExcelProperty(value = "实缴资本")
    private String contributedCapital;

    //所属省份
    @ExcelProperty(value = "所属省份")
    private String province;

    //所属城市
    @ExcelProperty(value = "所属城市")
    private String city;

    //所属区县
    @ExcelProperty(value = "所属区县")
    private String county;

    //股东人员
    @ExcelProperty(value = "股东人员")
    private String shareholder;

    //会话临时解析数据
    @ExcelIgnore
    private String sessionId;

}

