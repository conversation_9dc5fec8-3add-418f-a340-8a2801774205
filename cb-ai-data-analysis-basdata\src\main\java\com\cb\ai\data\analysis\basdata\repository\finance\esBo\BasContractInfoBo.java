package com.cb.ai.data.analysis.basdata.repository.finance.esBo;


import com.baomidou.mybatisplus.annotation.*;
import com.cb.ai.data.analysis.query.constant.Constant;
import com.cb.ai.data.analysis.query.domain.bo.EsPermBo;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同信息表
 *
 * <AUTHOR>
 * @since 2025-07-14 21:55:36
 */
@Data
@Document(indexName = Constant.ES_FINANCE_DATA_INDEX + Constant.SLICING + "bas_contract_info")
@Setting(shards = 1, replicas = 0)
public class BasContractInfoBo extends EsPermBo {

    private static final long serialVersionUID = 1L;

    //主键
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    //合同编号
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String contractNumber;

    //合同名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String contractName;

    //合同类型
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String contractType;

    //签订日期
    @Field(type = FieldType.Long)
    private LocalDateTime signingDate;

    //合同开始日期
    @Field(type = FieldType.Long)
    private LocalDateTime startTime;

    //合同结束日期
    @Field(type = FieldType.Long)
    private LocalDateTime endTime;

    //合同状态
    @Field(type = FieldType.Keyword)
    private String status;

    //甲方名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String partA;

    //乙方名称
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String partB;

    //联系人
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String telName;

    //电话
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String telPhone;

    //合同标的
    @Field(type = FieldType.Text, analyzer = "ik_smart")
    private String contractObject;

    //合同金额
    @Field(type = FieldType.Double)
    private BigDecimal contractAmount;

    //付款条款
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String payItem;

    /**
     * 创建者
     */
    @Field(type = FieldType.Keyword)
    private String createBy;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Keyword)
    private String updateBy;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Long)
    private LocalDateTime updateTime;

}

