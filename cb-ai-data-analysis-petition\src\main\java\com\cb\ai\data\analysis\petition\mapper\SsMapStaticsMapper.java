package com.cb.ai.data.analysis.petition.mapper;


import com.cb.ai.data.analysis.petition.annotation.DataSource;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.request.PetitionPurposeDateQueryConditionVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.RegisterDateQueryCondition;
import com.cb.ai.data.analysis.petition.domain.vo.request.SelfDeptSendOrgNameQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.request.SsStaticsQueryVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.MapStaticsVo;
import com.cb.ai.data.analysis.petition.enums.DataSourceType;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
@DataSource(DataSourceType.SLAVE)
public interface SsMapStaticsMapper {
    List<MapStaticsVo> countByRegion(@Param("param") SsStaticsQueryVo queryVo);

    List<MapStaticsVo> countByDomain(@Param("param") SsStaticsQueryVo queryVo);

    List<String> domainSelectOptions(Integer type);

    List<String> regionSelectOptions(Integer type);

    List<MapStaticsVo> countBySelfDeptSendOrgName(@Param("selfDeptSendOrgNameQueryVo") SelfDeptSendOrgNameQueryVo selfDeptSendOrgNameQueryVo);

    List<MapStaticsVo> countByRegisterDate(@Param("registerDateQueryCondition") RegisterDateQueryCondition registerDateQueryCondition);

    List<MapStaticsVo> countByAnalyzedStatus();

    List<MapStaticsVo> countByPetitionPurpose(@Param("petitionPurposeDateQueryConditionVo") PetitionPurposeDateQueryConditionVo petitionPurposeDateQueryConditionVo);



 }
