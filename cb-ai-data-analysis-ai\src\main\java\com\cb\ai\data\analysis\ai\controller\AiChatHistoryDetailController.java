package com.cb.ai.data.analysis.ai.controller;


import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail;
import com.cb.ai.data.analysis.ai.service.IAiChatHistoryDetailService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:39
 * @Copyright (c) 2025
 * @Description AI历史会话详情控制器
 */
@Validated
@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/chat/history/detail")
public class AiChatHistoryDetailController extends BaseController<IAiChatHistoryDetailService, AiChatHistoryDetail> {

    /**
     * 分页获取会话历史记录详情
     * @param aiChatHistoryDetail 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(AiChatHistoryDetail aiChatHistoryDetail) {
        return Result.successData(baseService.pageByTime(aiChatHistoryDetail));
    }

    /**
     * 获取会话历史记录详情
     * @param aiChatHistoryDetail 查询实体
     * @return 相关数据
     */
    @GetMapping
    public Result list(@RequestBody AiChatHistoryDetail aiChatHistoryDetail) {
        return Result.successData(baseService.getHistoryDetailById(aiChatHistoryDetail.getSessionId(), aiChatHistoryDetail.getUserId()));
    }

    /**
     * 保存会话历史记录详情
     * @param aiChatHistoryDetail
     * @return 相关数据
     */
    @PostMapping
    public Result save(@RequestBody AiChatHistoryDetail aiChatHistoryDetail) {
        return Result.successData(baseService.saveHistoryDetail(aiChatHistoryDetail));
    }

    /**
     * 删除AI 会话历史记录详情
     *
     */
    @DeleteMapping("/byId")
    @XLog(title = "删除AI 会话历史记录", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "AI 会话历史记录详情ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.removeByIds(list)) {
            return Result.success("AI 会话历史记录详情删除成功");
        } else {
            return Result.fail("AI 会话历史记录详情删除失败");
        }
    }

    /**
     * 删除AI 会话历史记录详情
     *
     */
    @DeleteMapping
    @XLog(title = "删除AI 会话历史记录", execType = ExecType.DELETE)
    public Result delete(AiChatHistoryDetail aiChatHistoryDetail) {
        if (baseService.removeById(aiChatHistoryDetail.getSessionId(), aiChatHistoryDetail.getUserId())) {
            return Result.success("AI 会话历史记录详情删除成功");
        } else {
            return Result.fail("AI 会话历史记录详情删除失败");
        }
    }
}

