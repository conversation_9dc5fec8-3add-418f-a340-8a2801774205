package com.cb.ai.data.analysis.voucher.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherTask;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherInfoVo;
import com.cb.ai.data.analysis.voucher.mapper.VoucherInfoMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherAlertRecordService;
import com.cb.ai.data.analysis.voucher.service.VoucherCheckRuleService;
import com.cb.ai.data.analysis.voucher.service.VoucherInfoService;
import com.cb.ai.data.analysis.voucher.utils.ocr.AiUtil;
import com.cb.ai.data.analysis.voucher.utils.ocr.TableMarkdownConverter;
import com.cb.ai.data.analysis.voucher.utils.pdf.PdfToImageUtil;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class VoucherInfoServiceImpl extends BaseServiceImpl<VoucherInfoMapper, VoucherInfo> implements VoucherInfoService {

    @Autowired
    private SuperviseResourceFileService superviseResourceFileService;

    @Autowired
    private VoucherAlertRecordService voucherAlertRecordService;
    @Autowired
    private VoucherCheckRuleService voucherCheckRuleService;

    @Autowired
    private AiUtil aiUtil;

    @Override
    public Page<VoucherInfo> pageByEntity(Page<VoucherInfo> page, VoucherInfoVo.PageReq req) {
        return baseMapper.pageByEntity(page, req);
    }

    @Override
    public void asyncOcrByIds(Set<String> ids, boolean force) {
        CompletableFuture future = CompletableFuture.runAsync( ()->{
            Iterator<String> it = ids.iterator();
            while (it.hasNext()) {
                String id = it.next();
                VoucherInfo voucherInfo = baseMapper.selectById(id);
                if (null == voucherInfo) {
                    continue;
                }
                String fileId = voucherInfo.getFileId();
                Integer status = voucherInfo.getStatus();
                //强制 或 待解析
                if (force || Integer.valueOf(0).equals(status)) {
                    // 修改状态为开始
                    LambdaUpdateWrapper<VoucherInfo> updateWrapper = Wrappers.lambdaUpdate(VoucherInfo.class)
                            .eq(VoucherInfo::getId, id)
                            .set(VoucherInfo::getStatus, 1)
                            .set(VoucherInfo::getOcrText, null)
                            .set(VoucherInfo::getOcrErr, null)
                            .set(VoucherInfo::getOcrStartTime, LocalDateTime.now())
                            .set(VoucherInfo::getOcrEndTime, null);
                    baseMapper.update(updateWrapper);
                } else {
                    log.info("凭证ocr跳过。id:{} status:{}", id, status);
                    continue;
                }
                //最终更新的wrapper
                LambdaUpdateWrapper<VoucherInfo> finalUpdateWrapper = Wrappers.lambdaUpdate(VoucherInfo.class)
                        .eq(VoucherInfo::getId, id);
//                try (InputStream is = superviseResourceFileService.getFileStream(fileId)){
                String filePath = "C:\\Users\\<USER>\\Desktop\\pdf2img\\202402-23付海北市商务局赴昆考察接待餐费.pdf";
                try (InputStream is = new FileInputStream(filePath)){
                    StringBuilder sb = new StringBuilder();
                    // 调用ocr
                    PdfToImageUtil.PdfConverter converter = PdfToImageUtil.from(is).dpi(200.00f);
                    int pageCount = converter.getPageCount();
                    for (int i = 0; i < pageCount; i++) {
                        List<String> dataUri = converter.page(i).toBase64();
                        if(ObjectUtil.isNotEmpty(dataUri)){
                            String base64 = dataUri.get(0);
                            String text = aiUtil.ocrBase64(base64, true);
                            // ocr文本处理
                            text = ocrTextProcess(text);
                            sb.append(System.lineSeparator()).append(text);
                        }

                    }
                    String ocrText = sb.toString();
                    finalUpdateWrapper.set(VoucherInfo::getOcrText, ocrText);
                    finalUpdateWrapper.set(VoucherInfo::getStatus, 2);
                } catch (Exception e) {
                    e.printStackTrace();
                    finalUpdateWrapper.set(VoucherInfo::getStatus, 3);
                    finalUpdateWrapper.set(VoucherInfo::getOcrErr, e.getMessage());
                } finally {
                    finalUpdateWrapper.set(VoucherInfo::getOcrEndTime, LocalDateTime.now());
                    baseMapper.update(finalUpdateWrapper);
                }
            }
        });
    }

    /**
     * ocr识别的文本处理
     * 将<table></table>标签转为markdown表格
     * @param text
     * @return
     */
    private String ocrTextProcess(String text){
        // 转换<table>标签为markdown表格
        String rst = TableMarkdownConverter.convertTableToMarkdown(text);
        return rst.replaceAll("\r\n\r\n","\r\n").replaceAll("\n\n","\n");
    }

    @Override
    public void asyncAnalysisById(String id) {
        String username = SecurityUtils.getUsername();
        VoucherInfo voucherInfo = baseMapper.selectById(id);
        if (null == voucherInfo) {
            throw new RuntimeException("凭证不存在！");
        }
        Integer status = voucherInfo.getStatus();
        if(2 != status){
            throw new RuntimeException("凭证ocr尚未成功，不能进行分析！");
        }
        String orcText = voucherInfo.getOcrText();
        if (StringUtils.isBlank(orcText)) {
            throw new RuntimeException("凭证ocr结果为空，不能进行分析！");
        }
        CompletableFuture future = CompletableFuture.runAsync( ()->{
            try{
                // 获取分析规则
                String checkRules = voucherCheckRuleService.buildCheckRuleText();
                log.info("凭证检查规则共{}字", checkRules.length());
                // 调用ai分析
                String result = aiUtil.chatSyncAnalysis(orcText, checkRules);
                log.info("凭证分析结果：{}", result);
                LambdaQueryWrapper<VoucherAlertRecord> queryWrapper = Wrappers.lambdaQuery(VoucherAlertRecord.class)
                        .eq(VoucherAlertRecord::getVoucherId, id)
                        .isNull(VoucherAlertRecord::getTaskId);
                VoucherAlertRecord exist = voucherAlertRecordService.getOne(queryWrapper);
                LocalDateTime now = LocalDateTime.now();
                if(null != exist){
                    exist.setContent(result);
                    exist.setCreateBy(username);
                    exist.setCreateTime(now);
                    exist.setUpdateBy(username);
                    exist.setUpdateTime(now);
                    voucherAlertRecordService.updateById(exist);
                } else {
                    VoucherAlertRecord record = new VoucherAlertRecord();
                    record.setVoucherId(IdUtil.getSnowflakeNextIdStr());
                    record.setVoucherId(id);
                    record.setContent(result);
                    record.setCreateBy(username);
                    record.setCreateTime(now);
                    record.setUpdateBy(username);
                    record.setUpdateTime(now);
                    voucherAlertRecordService.save(record);
                }
            } catch (Exception e){
                e.printStackTrace();
            }
        });
    }

    @Override
    public boolean analysisByTaskAndId(String rules, VoucherTask task, String id) {
        String username = task.getCreateBy();
        String taskId = task.getId();
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("任务id不能为空");
        }
        VoucherInfo voucherInfo = baseMapper.selectById(id);
        if (null == voucherInfo) {
            throw new RuntimeException("凭证不存在！");
        }
        Integer status = voucherInfo.getStatus();
        if(2 != status){
            throw new RuntimeException("凭证ocr尚未成功，不能进行分析！");
        }
        String orcText = voucherInfo.getOcrText();
        if (StringUtils.isBlank(orcText)) {
            throw new RuntimeException("凭证ocr结果为空，不能进行分析！");
        }
        try{
            // 获取分析规则
            String checkRules = rules;
            if(StringUtils.isBlank(checkRules)){
                checkRules = voucherCheckRuleService.buildCheckRuleText();
            }
            log.info("凭证检查规则共{}字", checkRules.length());
            // 调用ai分析
            String result = aiUtil.chatSyncAnalysis(orcText, checkRules);
            if(null != result){
                result = result.trim();
            }
            log.info("凭证分析结果：{}", result);
            LambdaQueryWrapper<VoucherAlertRecord> queryWrapper = Wrappers.lambdaQuery(VoucherAlertRecord.class)
                    .eq(VoucherAlertRecord::getVoucherId, id)
                    .eq(VoucherAlertRecord::getTaskId,taskId);
            VoucherAlertRecord exist = voucherAlertRecordService.getOne(queryWrapper);
            LocalDateTime now = LocalDateTime.now();
            if(null != exist){
                exist.setContent(result);
                exist.setCreateBy(username);
                exist.setCreateTime(now);
                exist.setUpdateBy(username);
                exist.setUpdateTime(now);
                voucherAlertRecordService.updateById(exist);
            } else {
                VoucherAlertRecord record = new VoucherAlertRecord();
                record.setVoucherId(IdUtil.getSnowflakeNextIdStr());
                record.setTaskId(taskId);
                record.setVoucherId(id);
                record.setContent(result);
                record.setCreateBy(username);
                record.setCreateTime(now);
                record.setUpdateBy(username);
                record.setUpdateTime(now);
                voucherAlertRecordService.save(record);
            }
            return "[]".equals(result);
        } catch (Exception e){
            throw new RuntimeException("凭证分析失败！", e);
        }
    }

    @Override
    public long countByTagsAndDate(String[] tags, LocalDateTime now) {
        return baseMapper.countByTagsAndDate(tags, now);
    }

    @Override
    public Page<VoucherInfo> pageByTagsAndDate(Page<VoucherInfo> page, String[] tags, LocalDateTime now) {
        return baseMapper.pageByTagsAndDate(page, tags, now);
    }

}
