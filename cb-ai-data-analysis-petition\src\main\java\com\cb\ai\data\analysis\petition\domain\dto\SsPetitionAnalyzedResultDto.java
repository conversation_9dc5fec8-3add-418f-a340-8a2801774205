package com.cb.ai.data.analysis.petition.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ss_analyzed_result
 * <AUTHOR>
@Data
public class SsPetitionAnalyzedResultDto implements Serializable {
    /**
     * 元数据id
     */
    @JsonProperty("origin_id")
    private String originId;

    /**
     * 提交信访者姓名
     */
    @JsonProperty("petition_person")
    private String petitionPerson;

    /**
     * 所属省份
     */
    @JsonProperty("petition_province")
    private String petitionProvince;

    /**
     * 所属城市
     */
    @JsonProperty("petition_city")
    private String petitionCity;

    /**
     * 所属区县
     */
    @JsonProperty("petition_district")
    private String petitionDistrict;

    /**
     * 所属街道
     */
    @JsonProperty("petition_street")
    private String petitionStreet;

    /**
     * 举报人居住省份
     */
    @JsonProperty("user_residence_province")
    private String userResidenceProvince;

    /**
     * 举报人居住市
     */
    @JsonProperty("user_residence_city")
    private String userResidenceCity;

    /**
     * 举报人居住区县
     */
    @JsonProperty("user_residence_district")
    private String userResidenceDistrict;

    /**
     * 举报人居住街道
     */
    @JsonProperty("user_residence_street")
    private String userResidenceStreet;

    /**
     * 所属领域
     */
    @JsonProperty("petition_domain")
    private String petitionDomain;
    /**
     * 所属领域（一级分类）
     */
    @JsonProperty("petition_domain_category")
    private String petitionDomainCategory;

    /**
     * 信访目的
     */
    @JsonProperty("petition_purpose")
    private String petitionPurpose;
    /**
     * 信访目的（一级分类）
     */
    @JsonProperty("petition_purpose_category")
    private String petitionPurposeCategory;

    /**
     * 处理建议
     */
    @JsonProperty("petition_handle_suggestion")
    private String petitionHandleSuggestion;

    /**
     * 摘要
     */
    @JsonProperty("brief")
    private String brief;

    /**
     * 如果信访件中包含对某某公务员/领导，在贪污腐败或渎职等问题上的检举/控告/投诉 则填写被检举/举报公务员的姓名 没有则空 多个则以,分隔返回
     */
    @JsonProperty("petition_accused_person")
    private String petitionAccusedPerson;

    /**
     * 人物关系
     */
    @JsonProperty("petition_relation")
    private String petitionRelation;

    /**
     * 信访接收机构或信访去向机构
     */
    @JsonProperty("petition_belong_org")
    private String petitionBelongOrg;

    @JsonProperty("register_date")
    private String registerDate;

    @JsonProperty("petition_date")
    private String petitionDate;

    private static final long serialVersionUID = 1L;


}
