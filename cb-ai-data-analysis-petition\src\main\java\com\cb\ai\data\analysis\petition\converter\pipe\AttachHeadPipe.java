package com.cb.ai.data.analysis.petition.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.DocConfig;
import com.cb.ai.data.analysis.petition.converter.FormatTools;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.BreakType;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 附件头
 * <AUTHOR>
 */
public class AttachHeadPipe extends IPipe {
    @Override
    public boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return ReUtil.isMatch("^附[  　]*件[  　]*[\\d]?", text);
    }

    @Override
    public void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化附件头
        FormatTools.formatAttachHead(paragraph, config);
        List<XWPFRun> runList = paragraph.getRuns();
        for (XWPFRun run : runList) {
            run.addBreak(BreakType.TEXT_WRAPPING); // 格式化标题之前新增换行
            FormatTools.formatSerialAttachHead(run, config);
        }
    }
}
