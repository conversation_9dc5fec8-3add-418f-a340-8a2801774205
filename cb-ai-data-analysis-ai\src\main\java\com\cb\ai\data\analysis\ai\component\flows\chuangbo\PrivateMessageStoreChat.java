package com.cb.ai.data.analysis.ai.component.flows.chuangbo;


import cn.hutool.extra.spring.SpringUtil;
import com.cb.ai.data.analysis.ai.common.event.model.HistoryChat;
import com.cb.ai.data.analysis.ai.common.properties.PrivateAIBaseProperties;
import com.cb.ai.data.analysis.ai.component.choreography.engine.BaseNode;
import com.cb.ai.data.analysis.ai.component.choreography.engine.ISyncNode;
import com.cb.ai.data.analysis.ai.component.http.HttpClient;
import com.cb.ai.data.analysis.ai.component.http.OkHttpsClient;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 09:42
 * @Copyright (c) 2025
 * @Description 存储消息接口（已废弃）
 */
@Deprecated
public class PrivateMessageStoreChat extends BaseNode implements ISyncNode<HistoryChat, Boolean> {

    @Override
    public String getNodeName() {
        return "私有化-消息存储-接口";
    }

    @Override
    public Boolean syncProcess(HistoryChat historyChat) {
        PrivateAIBaseProperties properties = SpringUtil.getBean(PrivateAIBaseProperties.class);
        HttpClient httpClient = new OkHttpsClient();
        return httpClient.url(MergeUtil.mergePath(properties.getBaseUrl(), "chat"))
            .method(HttpMethod.PUT)
            .headers(headers -> headers.setAll(properties.getHeaderMap()))
            .body(historyChat)
            .onSuccess("存储成功"::equals)
            .executeBlock();
    }

}
