package com.cb.ai.data.analysis.knowledge.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CommonVectorMetadata implements Serializable {

    private static final long serialVersionUID = 1L;

    // id
    private String id;

    // 原文件名称
    private String originFileName;

    // 描述
    private String description;

    // 查询类型
    private String query_type;

    // 知识库id
    private String baseId;

    // 标题
    private String title;

    // 说明
    private String explanation;

    // 标签
    private List<String> tags;

    private String difficulty;

    private String sql_query;

    private String business_domain;

    // 用户问题
    private String user_question;

    // 文件Id
    private String fileId;

    // 文件名
    private String fileName;

}
