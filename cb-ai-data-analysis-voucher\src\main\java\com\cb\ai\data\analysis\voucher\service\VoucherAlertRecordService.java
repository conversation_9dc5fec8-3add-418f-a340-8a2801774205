package com.cb.ai.data.analysis.voucher.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord;
import com.cb.ai.data.analysis.voucher.domain.vo.VoucherAlertRecordVo;
import com.xong.boot.common.service.BaseService;

public interface VoucherAlertRecordService extends BaseService<VoucherAlertRecord> {

    /**
     * 分页查询
     * @param page
     * @param req
     * @return
     */
    public Page<VoucherAlertRecordVo.RespItem> pageByEntity(Page<VoucherAlertRecordVo.RespItem> page, VoucherAlertRecordVo.PageReq req);

    /**
     * 查询详情
     * @param id
     * @return
     */
    public VoucherAlertRecordVo.RespItem detail(String id);

    /**
     * 根据凭证ID查询(非任务分析)
     * @param voucherId
     * @return
     */
    public VoucherAlertRecordVo.RespItem detailByVoucherId(String voucherId);

    /**
     * 根据任务ID删除相关的告警记录
     * @param taskId
     */
    public void deleteByTaskId(String taskId);

}
