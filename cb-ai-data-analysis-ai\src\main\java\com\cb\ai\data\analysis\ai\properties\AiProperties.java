package com.cb.ai.data.analysis.ai.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties("cb.ai")
public class AiProperties {
    /**
     * AI平台地址
     */
    private String baseUrl = "http://***********:8686";
    /**
     * AI平台KEY
     */
    private String appKey = "1";
    /**
     * python平台地址
     */
    private String pyBaseUrl = "http://***********:8121";
    /**
     * python平台KEY
     */
    private String pyAppKey = "";
}
