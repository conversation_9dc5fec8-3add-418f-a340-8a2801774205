package com.cb.ai.data.analysis.dbtable.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.cb.ai.data.analysis.dbtable.enums.CHColumnType;
import com.cb.ai.data.analysis.dbtable.mapper.ClickHouseTableMapper;
import com.cb.ai.data.analysis.dbtable.model.clickhouse.DBColumn;
import com.cb.ai.data.analysis.dbtable.service.ClickHouseTableService;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * clickhouse ServiceImpl
 * <AUTHOR>
 */
@Service
public class ClickHouseTableServiceImpl implements ClickHouseTableService {
    /**
     * 数据库名称
     */
    @Value("${cb.dbtable.db-name}")
    private String dbName = "clickhouse";
    private final ClickHouseTableMapper clickHouseTableMapper;

    public ClickHouseTableServiceImpl(ClickHouseTableMapper clickHouseTableMapper) {
        this.clickHouseTableMapper = clickHouseTableMapper;
    }

    @Override
    public boolean existSqlTable(String tableName) {
        return clickHouseTableMapper.existSqlTable(dbName, tableName);
    }

    @Override
    public boolean existTableData(String tableName) {
        return clickHouseTableMapper.existTableData(tableName);
    }

    /**
     * 创建表
     * @param tableName    表名
     * @param tableComment 表注释
     * @param columns      字段集
     */
    @Override
    public void createTable(String tableName, String tableComment, List<DBColumn> columns) {
        if (SqlInjectionUtils.check(tableName)) {
            throw new XServerException(String.format("存在SQL注入：%s", tableName));
        }
        if (SqlInjectionUtils.check(tableComment)) {
            throw new XServerException(String.format("存在SQL注入：%s", tableComment));
        }
        List<String> primaryColumns = new ArrayList<>();
        List<String> sqlColumns = new ArrayList<>();
        for (DBColumn column : columns) {
            StringBuilder sqlColumn = new StringBuilder();
            sqlColumn.append("`");
            sqlColumn.append(column.getName());
            sqlColumn.append("` ");
            // 除了字符串其他都允许空，排除创建时间与更新时间，创建时间会用来分区所以不能为空
            if (column.getType() != CHColumnType.String && !column.getName().equals("create_time") && !column.getName().equals("update_time")) {
                sqlColumn.append("Nullable(");
            }
            sqlColumn.append(column.getType());
            // 填充长度
            if (column.getPrecise() != null && column.getPrecise() > 0) {
                sqlColumn.append("(");
                sqlColumn.append(column.getPrecise());
                if (column.getScale() != null && column.getScale() > 0) {
                    sqlColumn.append(",");
                    sqlColumn.append(column.getScale());
                }
                sqlColumn.append(")");
            }
            if (column.getType() != CHColumnType.String && !column.getName().equals("create_time") && !column.getName().equals("update_time")) {
                sqlColumn.append(")");
            }
            // 填充默认值
            if (StringUtils.isNotBlank(column.getDefVal())) {
                sqlColumn.append(" DEFAULT ");
                if (Arrays.asList(CHColumnType.String, CHColumnType.Date, CHColumnType.DateTime, CHColumnType.DateTime32, CHColumnType.DateTime64).contains(column.getType())) {
                    sqlColumn.append("'");
                    sqlColumn.append(column.getDefVal());
                    sqlColumn.append("'");
                } else {
                    sqlColumn.append(column.getDefVal());
                }
            }
            // 填充注释
            if (StringUtils.isNotBlank(column.getComment())) {
                sqlColumn.append(" COMMENT '");
                sqlColumn.append(column.getComment());
                sqlColumn.append("'");
            }
            if (SqlInjectionUtils.check(sqlColumn.toString())) {
                throw new XServerException(String.format("存在SQL注入：%s", sqlColumn));
            }
            sqlColumns.add(sqlColumn.toString());
            if (column.getIsPrimary()) {
                primaryColumns.add(column.getName());
            }
        }
        clickHouseTableMapper.createTable(tableName, tableComment, primaryColumns, sqlColumns);
    }

    /**
     * 删除表
     * @param tableName 表名
     */
    @Override
    public void removeTable(String tableName) {
        clickHouseTableMapper.deleteTable(tableName);
    }

    /**
     * 修改表名
     * @param tableName    表名
     * @param nowTableName 修改后表名
     */
    @Override
    public boolean renameTableName(String tableName, String nowTableName) {
        return clickHouseTableMapper.renameTableName(tableName, nowTableName);
    }

    /**
     * 拷贝表数据
     * @param columnNames     表字段名集
     * @param sourceTableName 源数据表名
     * @param targetTableName 目标数据表名
     */
    @Override
    public boolean copyTableData(List<String> columnNames, String sourceTableName, String targetTableName) {
        return SqlHelper.retBool(clickHouseTableMapper.copyTableData(columnNames, sourceTableName, targetTableName));
    }

    @Override
    public List<String> listTableColumnName(String tableName) {
        return clickHouseTableMapper.selectTableColumnName(dbName, tableName);
    }

    @Override
    public Page<Map<String, Object>> pageTableRow(Page<?> page, String tableName) {
        return clickHouseTableMapper.selectTableRows(page, tableName);
    }

    @Override
    public int deleteTableData(String tableName, List<String> ids) {
        if(ObjectUtil.isEmpty(ids)){
            return 0;
        }
        return clickHouseTableMapper.deleteTableData(tableName, ids);
    }
}
