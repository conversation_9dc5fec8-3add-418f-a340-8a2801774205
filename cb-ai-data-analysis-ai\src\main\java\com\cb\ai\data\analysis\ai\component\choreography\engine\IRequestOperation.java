package com.cb.ai.data.analysis.ai.component.choreography.engine;

import com.xong.boot.common.exception.CustomException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.codec.ServerSentEvent;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/6/16 14:58
 * @Copyright (c) 2025
 * @Description web请求操作接口
 */
public interface IRequestOperation<Rv> {
    /**
     * @description 设置请求方法
     * @return HttpMethod
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    default HttpMethod setRequestMethod() { return HttpMethod.POST; }

    /**
     * @description 设置请求的url
     * @return 完整的url
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    String setRequestUrl();

    /**
     * @description 设置请求头
     * @param headers 请求头
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    default void setRequestHeader(HttpHeaders headers) {}

    /**
     * @description 设置请求体
     * @return 请求体
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    <E> E setRequestBody();

    /**
     * @description 自定义类型转换后最终的数据处理
     * @param ssEvent 原始数据
     * @return 最终的数据
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    Rv resultConvert(ServerSentEvent<String> ssEvent);

    /**
     * @description 错误事件处理
     * @param throwable 转换后的原始数据
     * @return 最终的异常处理
     * @createtime 2025/6/9 上午8:42
     * <AUTHOR>
     * @version 1.0
     */
    default CustomException errorConvert(Throwable throwable) { return new CustomException("", throwable); }

}
