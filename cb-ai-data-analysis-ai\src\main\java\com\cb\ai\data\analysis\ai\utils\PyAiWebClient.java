package com.cb.ai.data.analysis.ai.utils;

import cn.hutool.http.Method;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.ai.exception.AiApiException;
import com.cb.ai.data.analysis.ai.model.AiConfig;
import com.cb.ai.data.analysis.ai.properties.AiProperties;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.StringUtils;
import org.springframework.stereotype.Component;

/**
 * python平台AI接口
 * <AUTHOR>
 */
@Component
public class PyAiWebClient {
    private final AiProperties aiProperties;

    public PyAiWebClient(AiProperties aiProperties) {
        this.aiProperties = aiProperties;
//        this.webClient = WebClient.builder()
//                .baseUrl(config.getPyBaseUrl())
//                .defaultHeader(HttpHeaders.AUTHORIZATION, config.getPySecretKey())
//                .build();
    }

    /**
     * 获取PY AI平台用户配置
     * @param userId 用户ID
     */
    public AiConfig getConfig(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new AiApiException("thread_id参数不存在");
        }
        String body = HttpUtils.createRequest(Method.GET, aiProperties.getPyBaseUrl() + "/api/user/config?thread_id=" + userId)
                .execute()
                .body();
        JSONObject jsonObject = JSON.parseObject(body);
        JSONObject config = jsonObject.getJSONObject("config");
        JSONObject KNOWLEDGE_BASE = config.getJSONObject("KNOWLEDGE_BASE");
        JSONObject WORKFLOW = config.getJSONObject("WORKFLOW");
        JSONObject MCP_SETTINGS = config.getJSONObject("MCP_SETTINGS");
        AiConfig aiConfig = new AiConfig();
        aiConfig.setPySimilarityThreshold(KNOWLEDGE_BASE.getFloat("similarity_threshold"));
        aiConfig.setPyTopK(KNOWLEDGE_BASE.getInteger("top_k"));
        aiConfig.setPyAutoAcceptedPlan(WORKFLOW.getBoolean("auto_accepted_plan"));
        aiConfig.setPyMaxPlanIterations(WORKFLOW.getInteger("max_plan_iterations"));
        aiConfig.setPyMaxStepNum(WORKFLOW.getInteger("max_step_num"));
        aiConfig.setPyMcpSettings(MCP_SETTINGS);
        return aiConfig;
    }

    /**
     * 更新PY AI平台用户配置
     * @param userId 用户ID
     * @param data   更新配置
     */
    public boolean updateConfig(String userId, AiConfig data) {
        if (StringUtils.isBlank(userId)) {
            throw new AiApiException("thread_id参数不存在");
        }
        JSONObject requestBody = new JSONObject();
        requestBody.put("thread_id", userId);
        JSONObject config = new JSONObject();
        JSONObject KNOWLEDGE_BASE = new JSONObject();
        KNOWLEDGE_BASE.put("similarity_threshold", data.getPySimilarityThreshold());
        KNOWLEDGE_BASE.put("top_k", data.getPyTopK());
        config.put("KNOWLEDGE_BASE", KNOWLEDGE_BASE);
        JSONObject WORKFLOW = new JSONObject();
        WORKFLOW.put("auto_accepted_plan", data.getPyAutoAcceptedPlan());
        WORKFLOW.put("max_plan_iterations", data.getPyMaxPlanIterations());
        WORKFLOW.put("max_step_num", data.getPyMaxStepNum());
        config.put("WORKFLOW", WORKFLOW);
        config.put("MCP_SETTINGS", data.getPyMcpSettings());
        requestBody.put("config", config);
        String body = HttpUtils.createRequest(Method.POST, aiProperties.getPyBaseUrl() + "/api/user/config")
                .contentType("application/json")
                .body(requestBody.toJSONString())
                .execute()
                .body();
        JSONObject responseBody = JSON.parseObject(body);
        return responseBody.getString("message").contains("successfully");
    }

    /**
     * 删除PY AI平台用户配置
     * @param userId 用户ID
     */
    public boolean deleteConfig(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new AiApiException("thread_id参数不存在");
        }
        String body = HttpUtils.createRequest(Method.DELETE, aiProperties.getPyBaseUrl() + "/api/user/config?thread_id=" + userId)
                .execute()
                .body();
        JSONObject responseBody = JSON.parseObject(body);
        return responseBody.getString("message").contains("successfully");
    }
}
