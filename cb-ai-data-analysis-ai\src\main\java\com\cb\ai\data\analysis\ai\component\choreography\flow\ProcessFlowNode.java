package com.cb.ai.data.analysis.ai.component.choreography.flow;

import com.cb.ai.data.analysis.ai.component.choreography.model.FlowContext;
import com.cb.ai.data.analysis.ai.component.choreography.model.NodeContext;
import lombok.Getter;
import reactor.core.publisher.Flux;

import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/8/1 10:33
 * @Copyright (c) 2025
 * @Description 操作流程节点处理类
 */
@Getter
public class ProcessFlowNode implements IFlowNode {
    /**
     * 自实现的Flux流
     */
    private Flux<?> node;
    /**
     * 节点和上下文列表
     */
    private BiFunction<FlowContext, NodeContext, Flux<?>> nodeFun;
    /**
     * 节点名称
     */
    private String nodeName;

    public ProcessFlowNode(Flux<?> node, String nodeName) {
        this.node = node;
        this.nodeName = nodeName;
    }

    public ProcessFlowNode(BiFunction<FlowContext, NodeContext, Flux<?>> nodeFun, String nodeName) {
        this.nodeFun = nodeFun;
        this.nodeName = nodeName;
    }
}
