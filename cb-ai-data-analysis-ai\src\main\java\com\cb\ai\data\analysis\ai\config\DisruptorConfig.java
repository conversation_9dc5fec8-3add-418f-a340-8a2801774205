package com.cb.ai.data.analysis.ai.config;

import com.cb.ai.data.analysis.ai.common.event.HistoryChatEventProducer;
import com.cb.ai.data.analysis.ai.common.event.HistoryEventHandler;
import com.cb.ai.data.analysis.ai.common.event.model.HistoryChatEvent;
import com.lmax.disruptor.YieldingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 11:44
 * @Copyright (c) 2025
 * @Description TODO
 */
@Configuration
public class DisruptorConfig {
    private static final int BUFFER_SIZE = 1024 * 256; // 必须是2的幂次方

    @Bean
    @DependsOn("historyEventHandler")
    public Disruptor<HistoryChatEvent> historyEventDisruptor(HistoryEventHandler handler) {
        Disruptor<HistoryChatEvent> disruptor = new Disruptor<>(
                HistoryChatEvent::new,
                BUFFER_SIZE,
                DaemonThreadFactory.INSTANCE,
                ProducerType.MULTI,
                new YieldingWaitStrategy()
        );
        disruptor.handleEventsWith(handler);
        disruptor.start();
        return disruptor;
    }

    @Bean
    @DependsOn("historyEventDisruptor")
    public HistoryChatEventProducer historyChatEventProducer(Disruptor<HistoryChatEvent> disruptor) {
        return new HistoryChatEventProducer(disruptor.getRingBuffer());
    }

}
