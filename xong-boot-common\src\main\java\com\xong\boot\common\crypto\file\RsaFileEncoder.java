package com.xong.boot.common.crypto.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * RSA文件加解密器
 * <AUTHOR>
 */
public class RsaFileEncoder implements FileEncoder {
    /**
     * 加密算法
     */
    private final RSA rsa;

    public RsaFileEncoder(String privateKey, String publicKey) {
        rsa = SecureUtil.rsa(privateKey, publicKey);
    }

    @Override
    public void decode(InputStream is, OutputStream out) throws IOException {
        decode(is, out, true);
    }

    @Override
    public void decode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            byte[] bytes = rsa.decrypt(is, KeyType.PublicKey);
            IoUtil.write(out, isClose, bytes);
        } finally {
            if (isClose && is != null) {
                is.close();
            }
        }
    }

    @Override
    public void encode(InputStream is, OutputStream out) throws IOException {
        encode(is, out, true);
    }

    @Override
    public void encode(InputStream is, OutputStream out, boolean isClose) throws IOException {
        try {
            byte[] bytes = rsa.encrypt(is, KeyType.PrivateKey);
            IoUtil.write(out, isClose, bytes);
        } finally {
            if (isClose && is != null) {
                is.close();
            }
        }
    }
}
