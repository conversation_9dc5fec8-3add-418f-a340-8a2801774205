package com.cb.ai.data.analysis.ai.domain.common;

import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/4 17:52
 * @Copyright (c) 2025
 * @Description 多文件数据
 */
public class MultiFileData extends LinkedMultiValueMap<String, MultipartFile> {
    private MultiFileData() {}
    
    public static MultiFileData of(String key, MultipartFile value) {
        MultiFileData inputData = new MultiFileData();
        inputData.add(key, value);
        return inputData;
    }

    public static MultiFileData of(String key, List<MultipartFile> values) {
        MultiFileData inputData = new MultiFileData();
        inputData.addAll(key, values);
        return inputData;
    }

    public static MultiFileData of(Map<String, MultipartFile> values) {
        MultiFileData inputData = new MultiFileData();
        values.forEach(inputData::add);
        return inputData;
    }

    public static MultiFileData of(MultiValueMap<String, MultipartFile> values) {
        MultiFileData inputData = new MultiFileData();
        inputData.addAll(values);
        return inputData;
    }

    public MultiFileData adds(String key, MultipartFile value) {
        this.add(key, value);
        return this;
    }

    public MultiFileData addsIfAbsent(String key, MultipartFile value) {
        this.addIfAbsent(key, value);
        return this;
    }

    public MultiFileData sets(String key, MultipartFile value) {
        this.set(key, value);
        return this;
    }

    /**
     * 转换为Map<String, Object>
     * 每个键对应的文件列表会被转换为文件数组
     */
    public Map<String, Object> toObjMap() {
        Map<String, Object> result = new LinkedHashMap<>();
        this.forEach((key, files) -> {
            if (files.size() == 1) {
                result.put(key, files.get(0));
            } else {
                result.put(key, files.toArray(new MultipartFile[0]));
            }
        });
        return result;
    }
}
