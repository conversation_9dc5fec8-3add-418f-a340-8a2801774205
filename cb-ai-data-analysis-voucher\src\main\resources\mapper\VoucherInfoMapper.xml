<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.voucher.mapper.VoucherInfoMapper">

    <resultMap type="com.cb.ai.data.analysis.voucher.domain.entity.VoucherInfo" id="VoucherInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
        <result property="ocrText" column="ocr_text" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="ocrErr" column="ocr_err" jdbcType="VARCHAR"/>
        <result property="ocrStartTime" column="ocr_start_time" jdbcType="TIMESTAMP"/>
        <result property="ocrEndTime" column="ocr_end_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="pageByEntity" resultMap="VoucherInfoMap">
        select distinct tt.*
        from voucher_tag t
        left join voucher_info tt on t.voucher_id = tt.id
        <where>
            <if test="et.id != null and et.id != ''">
                and tt.id = #{et.id}
            </if>
            <if test="et.status != null">
                and tt.status = #{et.status}
            </if>
            <if test="et.ocrText != null and et.ocrText != ''">
                and tt.name like concat('%',#{et.ocrText},'%')
            </if>
            <if test="et.createBy != null and et.createBy != ''">
                and tt.create_by = #{et.createBy}
            </if>
            <if test="et.beginDate != null">
                and tt.create_time &gt;= #{et.beginDate}
            </if>
            <if test="et.endDate != null">
                and tt.create_time &lt;= #{et.endDate}
            </if>
            <if test="et.tags != null and et.tags.length > 0">
                and t.tag in
                <foreach item="tag" collection="et.tags" separator="," open="(" close=")" index="">
                    #{tag}
                </foreach>
            </if>
        </where>
        order by tt.create_time desc
    </select>

    <select id="countByTagsAndDate" resultType="long">
        select count(1)
        from (
            select distinct tt.id
            from voucher_tag t
            left join voucher_info tt on t.voucher_id = tt.id
            where tt.create_time &lt;= #{now}
            <if test="tags != null and tags.length > 0">
                and t.tag in
                <foreach item="tag" collection="tags" separator="," open="(" close=")" index="">
                    #{tag}
                </foreach>
            </if>
        ) aa

    </select>

    <select id="pageByTagsAndDate" resultMap="VoucherInfoMap">
        select distinct tt.*
        from voucher_tag t
        left join voucher_info tt on t.voucher_id = tt.id
        where tt.create_time &lt;= #{now}
        <if test="tags != null and tags.length > 0">
            and t.tag in
            <foreach item="tag" collection="tags" separator="," open="(" close=")" index="">
                #{tag}
            </foreach>
        </if>
    </select>

</mapper>

