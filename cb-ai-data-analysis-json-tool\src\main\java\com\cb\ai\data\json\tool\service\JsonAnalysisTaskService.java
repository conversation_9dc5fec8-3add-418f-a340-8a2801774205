package com.cb.ai.data.json.tool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cb.ai.data.json.tool.domain.JsonAnalysisTask;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

public interface JsonAnalysisTaskService extends IService<JsonAnalysisTask> {

    int saveTask(JsonAnalysisTask task);

    JsonAnalysisTask saveTask(String jsonToolDir, MultipartFile originalFile, File uploadFile, File extractFile);

    int deleteTask(String taskId);

}
