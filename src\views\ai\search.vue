<script lang="ts" name="AiSearch" setup>
import searchInput from './components/search.vue'
import FileSearchResult from '@/views/query/components/File.vue'
import { useAiStore } from '@/stores/index'
import DynamicDataResult from '@/views/query/components/DynamicData.vue'

const aiStore = useAiStore()
</script>

<template>
  <div class="aiSearch scrollBeauty">
    <searchInput title="全文检索" />
    <div class="searchContent scrollBeauty">
      <a-list :data-source="aiStore.dataList" :loading="aiStore.loading" item-layout="horizontal">
        <template #renderItem="{ item }">
          <FileSearchResult v-if="item.dataType == 'file'" :file="item"></FileSearchResult>
          <!--  TODO 需完善动态数据的回显      -->
          <DynamicDataResult v-else :data="item"></DynamicDataResult>
        </template>
        <template #empty>
          <a-empty description="暂无搜索结果" />
        </template>
      </a-list>
    </div>
    <a-pagination
      v-model:current="aiStore.queryParams.pageCurrent"
      v-model:pageSize="aiStore.queryParams.pageSize"
      :page-size-options="['10', '20', '30', '40', '50']"
      :show-total="(total: number) => `共 ${total} 条记录`"
      :total="aiStore.dataTotal"
      class="searchPagination"
      hideOnSinglePage
      show-quick-jumper
      show-size-changer
      @change="aiStore.handlePageChange"
      @showSizeChange="aiStore.handlePageSizeChange"
    />
  </div>
</template>

<style lang="less" scoped>
@import '@/assets/styles/utils';

.aiSearch {
  overflow: auto;
  padding-right: 5px;
  width: .px2vw(960) [ @vw];
  padding-top: var(--headerSize);
  min-width: 960px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .searchContent {
    padding: 10px;
    overflow: auto;
    margin-top: 1vh;
    margin-bottom: 1vh;
    flex-grow: 1;
    width: 100%;
    height: 0;
    flex-shrink: 1;
    background-color: var(--layout-light-bgcolor);
    border-radius: 5px;
  }

  .searchPagination {
    flex-shrink: 0;
    margin-bottom: 1vh;
  }
}
</style>
