package com.cb.ai.data.analysis.petition.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionSourceFileEntity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xong.boot.common.json.deserializer.LocalDateTimeDeserializer;
import com.xong.boot.common.json.serializer.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SourceFileVo  extends PetitionSourceFileEntity {

    private String fileName;

    private String fileType;

    private String filePath;

    @TableField(updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime uploadTime;
}
