package com.cb.ai.data.analysis.file.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xong.boot.common.domain.BaseDomain;
import com.xong.boot.common.valid.UpdateGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SuperviseResourceFile  extends BaseDomain {
    /**
     * ID
     */
    @TableId
    @NotBlank(message = "ID不存在", groups = UpdateGroup.class)
    private String id;
    /**
     * 文件夹ID
     */
    private String folderId;

    /**
     * 文件名称
     */
    private String filename;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件标签
     */
    private String fileTags;

    /**
     * 加密算法
     */
    private String algorithm;

    /**
     * MD5摘要
     */
    private String digestMd5;

    /**
     * 排序
     */
    private Integer sortOn;

    /**
     * 状态（0正常 1禁用）
     */
    private Integer status;

    /**
     * 删除标志（0正常 1删除）
     */
    @JsonIgnore
    @TableLogic
    private Boolean delFlag;

    /**
     * 原路径
     */
    private String originalPath;
    /**
     * 原路径
     */
    private String fileContent;
}
