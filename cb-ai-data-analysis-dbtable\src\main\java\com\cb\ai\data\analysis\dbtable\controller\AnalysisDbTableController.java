package com.cb.ai.data.analysis.dbtable.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.dbtable.constant.DbtableConstants;
import com.cb.ai.data.analysis.dbtable.domain.AnalysisDbTable;
import com.cb.ai.data.analysis.dbtable.model.req.TableDataReq;
import com.cb.ai.data.analysis.dbtable.service.AnalysisDbTableService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 动态数据表 Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping(DbtableConstants.API_DBTABLE_ROOT_PATH + "/table")
public class AnalysisDbTableController extends BaseController<AnalysisDbTableService, AnalysisDbTable> {
    /**
     * 新增数据表
     * @param data 数据表对象
     */
    @PostMapping
    @XLog(title = "新增数据表", execType = ExecType.INSERT)
//    @PreAuthorize("hasAuthority('dbtable:table:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody TableDataReq data) {
        baseService.saveTable(data.getTable(), data.getColumns(), data.getIndexs(), data.getRules());
        return Result.success("数据表新增成功");
    }

    /**
     * 删除数据表
     * @param ids 数据表ID
     */
    @DeleteMapping
    @XLog(title = "删除数据表", execType = ExecType.DELETE)
//    @PreAuthorize("hasAuthority('dbtable:table:delete')")
    public Result delete(@NotEmpty(message = "数据表ID不存在") String[] ids) {
        baseService.removeByIds(Arrays.asList(ids));
        return Result.success("数据表删除成功");
    }

    /**
     * 编辑数据表
     * @param data 数据表对象
     */
    @PutMapping
    @XLog(title = "修改数据表", execType = ExecType.UPDATE)
//    @PreAuthorize("hasAuthority('dbtable:table:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody TableDataReq data) {
        baseService.updateTable(data.getTable(), data.getColumns(), data.getIndexs(), data.getRules());
        return Result.success("数据表修改成功");
    }

    /**
     * 数据表详情
     * @param id 数据表ID
     */
    @GetMapping
//    @PreAuthorize("@ss.hasPermission('dbtable:table:*')")
    public Result detail(@NotBlank(message = "数据表ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 分页获取数据表列表
     * @param params 查询条件
     */
    @GetMapping("/page")
//    @PreAuthorize("hasAuthority('dbtable:table:list')")
    public Result page(AnalysisDbTable params) {
        LambdaQueryWrapper<AnalysisDbTable> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByAsc(AnalysisDbTable::getSortOn);
        queryWrapper.orderByDesc(AnalysisDbTable::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 表同步数据库成功
     * @param data
     * @return
     */
    @PostMapping("/syncdb")
    @XLog(title = "表同步数据库", execType = ExecType.INSERT)
//    @PreAuthorize("hasAuthority('dbtable:table:list')")
    public Result syncdb(@RequestBody JSONObject data) {
        int type = data.getIntValue("type", 1);
        String tableId = data.getString("id");
        baseService.syncdbTable(type, tableId);
        return Result.success("表同步数据库成功");
    }
}
