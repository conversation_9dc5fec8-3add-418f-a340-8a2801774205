package com.cb.ai.data.analysis.knowledge.domain.req;

import lombok.Data;

import java.util.Map;

/**
 * AI配置
 */
@Data
public class ChatAiConfig {
    /**
     * 模型名称，如 gpt-4, qwen-max, chatglm-6b
     */
    private String modelName;
    /**
     * 温度（控制随机性，范围 0~2）。低温（接近 0） ：模型更保守、更确定，倾向于选择概率最高的词。 高温（接近 1 或更高） ：模型更随机、更有创意，可能会选择不太常见的词
     */
    private Float temperature;
    /**
     * 采样策略（控制多样性，范围 0~1）。如果设置 top_p=0.9，模型会从概率加起来占前 90% 的词中进行随机采样
     */
    private Float topP;
    /**
     * 最大返回 token 数量（默认2048）。控制模型生成文本的最大长度
     */
    private Double maxTokens;
    /**
     * 频率惩罚项（关注频率，范围 -2.0 ~ 2.0）。用于惩罚已经频繁出现过的 token，从而鼓励模型生成新的、多样化的词汇。值为 正数 → 惩罚重复词，减少重复；值为 负数 → 鼓励重复词，增加重复；值为 0 → 不做任何惩罚或鼓励
     */
    private Float frequencyPenalty;
    /**
     * 出现惩罚项（关注是否出现过，范围 -2.0 ~ 2.0）。用于惩罚已经频繁出现过的 token，越大越鼓励新内容。值为 正数 → 惩罚重复词，减少重复；值为 负数 → 鼓励重复词，增加重复；值为 0 → 不做任何惩罚或鼓励
     */
    private Float presencePenalty;
    // ------------ python平台 ------------
    /**
     * 向量检索相似度阈值，范围0-1，越高表示需要召回内容需要越相似
     */
    private Float pySimilarityThreshold;
    /**
     * 向量检索召回知识块数量，推荐范围3-15
     */
    private Integer pyTopK;
    /**
     * MCP Server设置，是一个复杂的嵌套字典，包含服务器配置和工具，默认不填
     */
    private Map<String, Object> pyMcpSettings;
    /**
     * 是否启用自主探索模式，true表示跳过用户交互逻辑
     */
    private Boolean pyAutoAcceptedPlan;
    /**
     * 深度研究的最大迭代轮数
     */
    private Integer pyMaxPlanIterations;
    /**
     * 每轮研究中，planner指定的研究方向数量
     */
    private Integer pyMaxStepNum;
    // ------------ 网页生成 ------------
    /**
     * 网页模型
     */
    private String mhCurrentModel;
    /**
     * 网页模型
     */
    private Boolean mhEnableSearch;
    /**
     * 转换格式
     */
    private String mhLanguage;

    public static ChatAiConfig newInstance() {
        ChatAiConfig config = new ChatAiConfig();
//        try {
//            AiConfig pyConfig = SpringUtils.getBean(PyAiWebClient.class).getConfig(SecurityUtils.getUserId());
//            BeanUtils.merger(config, pyConfig);
//        } catch (Exception e) {
//            throw new AiApiException(e);
//        }
        return config;
    }
}
