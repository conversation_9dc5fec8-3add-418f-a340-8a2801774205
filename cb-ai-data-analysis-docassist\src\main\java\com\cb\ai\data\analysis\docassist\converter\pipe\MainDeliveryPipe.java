package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 主送机关
 *
 * <AUTHOR>
 */
public class MainDeliveryPipe extends IPipe {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        String text = paragraph.getText().trim();
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return pos < 20 && (ReUtil.contains("[省市区县镇村组府局厅部委校位体构院业门司长理总站位导][:：]$", text) || ReUtil.contains("^主送", text));
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        FormatTools.formatParagraph(paragraph, config);
        List<XWPFRun> runs = paragraph.getRuns();
        for (XWPFRun run : runs) {
//            if (documentInfo.isPrintSend()) {
//                FormatTools.formatSerialNumber2(run, config);
//            } else {
//                FormatTools.format(run, config);
//            }
            FormatTools.format(run, config);
        }
    }
}
