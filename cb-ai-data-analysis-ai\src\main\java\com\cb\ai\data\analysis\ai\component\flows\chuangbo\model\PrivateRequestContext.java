package com.cb.ai.data.analysis.ai.component.flows.chuangbo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @CreateTime 2025/7/31 10:01
 * @Copyright (c) 2025
 * @Description 私有化接口请求上下文（增加此类目的是防止在请求前后的操作中修改前端请求上下文以及保证数据一致性）
 */
@Getter
@Setter
@Accessors(chain = true)
public class PrivateRequestContext {
        /** 当前SessionId **/
        private String sessionId;
        /** 当前用户Id **/
        private String userId;

        /** 用户输入提示词 **/
        private String promote;
        /** 系统提示词 **/
        private String systemPromote;

        /** 知识库是否溯源 **/
        boolean source;
        /** 知识库Id **/
        private String[] baseIds;
        /** 知识库文件Id列表 **/
        private String[] fileIds;

        /** 材料分析 - 文件名（minio文件名） **/
        private String fileName;
        /** 材料分析 - 文件Url（minio文件相对路径） **/
        private String fileUrl;

        /** 模型名称 **/
        private String model;
        /** 采样温度 **/
        private Float temperature;
        /** 采样策略（控制多样性，范围 0~1） **/
        @JsonProperty("top_p")
        private Float topP;
        /** 向量相似度检索的结果数量 **/
        private Integer topK;
        /** 生成的最大token数 **/
        @JsonProperty("max_tokens")
        private Integer maxTokens;
        /** 向量相似度检索（范围 0~1.0） **/
        private Float similarityHolds;
        /** 出现惩罚项（关注是否出现过，范围 -2.0 ~ 2.0） **/
        @JsonProperty("presence_penalty")
        private Float presencePenalty;
        /** 控制输出文本的重复率，范围 0~1 **/
        @JsonProperty("frequency_penalty")
        private Float frequencyPenalty;
        /** 如果为 true，则跳过“断崖法”兜底，将返回符合标准语义（0.80以上的数据）； 否则走断崖法 + TopK 兜底 **/
        private boolean usePresetScoreThreshold;


}
