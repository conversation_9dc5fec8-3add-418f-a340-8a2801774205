package com.cb.ai.data.analysis.petition.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.entity.PetitionProblemHandleEntity;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionProblemHandleVo;
import com.xong.boot.common.service.BaseService;

import java.util.List;

/***
 * <AUTHOR>
 * 问题批处理
 */
public interface PetitionProblemHandleService extends BaseService<PetitionProblemHandleEntity> {


    /**
     * 分页查询解析信息
     * @param petitionProblemHandle
     * @return
     */
    Page<PetitionProblemHandleEntity> selectByPage(PetitionProblemHandleVo petitionProblemHandle);

    /***
     * 删除问题记录，同步删除回答内容
     * @param id
     * @return
     * @throws Exception
     */
    int delPetitionProblemHandle(String id)  throws Exception;




//    int saveProblemHandleFile(List<PetitionProblemHandleEntity> problemHandleFileList) throws Exception;

}
