package com.cb.ai.data.analysis.petition.mapper;


import com.cb.ai.data.analysis.petition.annotation.DataSource;
import com.cb.ai.data.analysis.petition.domain.entity.SsPetitionAnalyzedEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.domain.vo.response.OrgExportDTO;
import com.cb.ai.data.analysis.petition.domain.vo.response.PurposeStatisticExportDTO;
import com.cb.ai.data.analysis.petition.enums.DataSourceType;
import com.xong.boot.common.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.Date;
import java.util.List;

@Mapper
@DataSource(DataSourceType.SLAVE)
public interface SsPetitionAnalyzedMapper extends BaseMapper<SsPetitionAnalyzedEntity> {


    List<SsPetitionAnalyzedEntity> selectByDateRange(@Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("types") List<String> types,
                                                 @Param("city") String city,
                                                 @Param("district") String district,
                                                 @Param("size") Integer size);



    List<PurposeStatisticExportDTO> getPurposeStatistics();

    List<PurposeStatisticExportDTO> getDomainStatistics();


    List<OrgExportDTO> getOrgStatistics();

    /**
     * 获取某个时间段内的信访件的分类分组（数据量太大，需要用 先分组，把每个分组的记录控制降低一些）
     * 使用分组的字段为: 信访目的(一级分类)	信访目的（二级分类）	所属领域（一级分类）	所属领域（二级分类）	所属省份	所属城市
     * @param beginDate
     * @param endDate
     * @return
     */
    List<SsRepeatTaskVo.RepeatTaskAnalyzeGroup> getAnalyzeGroup(@Param("beginDate") Date beginDate,
                                                                @Param("endDate") Date endDate);

    /**
     * 根据分组信息，流式获取信访件数据
     * @param g
     * @param resultHandler
     */
    void streamPetitionByAnalyzeGroup(@Param("g") SsRepeatTaskVo.RepeatTaskAnalyzeGroup g,
                                      @Param("resultHandler") ResultHandler<SsPetitionAnalyzedEntity> resultHandler);

}
