<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.PetitionSourceFileMapper">

    <resultMap type="com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo" id="SourceFileMap">
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="filename"    />
        <result property="fileType"    column="file_suffix"    />
        <result property="filePath"    column="file_path"    />
        <result property="uploadTime"  column="upload_time"    />
        <result property="id"          column="petition_file_id"    />
        <result property="analysisStatus"    column="analysis_status"    />
        <result property="dataCount"    column="data_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"       column="remark"    />

    </resultMap>
    <select id="querySourceFileList" resultMap="SourceFileMap" parameterType="com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile">
        SELECT
            t1.id as file_id,
            t1.filename,
            t1.file_suffix,
            t1.file_path,
            t1.create_time as upload_time,
            t2.id as petition_file_id,
            t2.analysis_status,
            t2.create_by,
            t2.create_time,
            t2.update_by,
            t2.update_time,
            t2.remark,
            (select count(id) from ss_petition_origin t where t.upload_batch_no=t2.file_id and t2.analysis_status=1) as data_count
        FROM supervise_resource_file t1
        left join ss_petition_source_file t2 on t1.id=t2.file_id
        ${ew.customSqlSegment}
    </select>
</mapper>
