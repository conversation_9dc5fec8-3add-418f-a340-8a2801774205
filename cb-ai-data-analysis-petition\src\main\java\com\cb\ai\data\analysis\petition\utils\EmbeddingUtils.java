package com.cb.ai.data.analysis.petition.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@Component
public class EmbeddingUtils {

    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/rag/embedding/textVector'}")
    private String API_URL;// = "http://10.10.10.72:8686/cb-ai/rag/embedding/textVector";
    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    public String API_KEY;// = "1";

    /**
     * 获取文本向量
     *
     * @param texts
     * @return
     * @throws IOException
     */
    public List<float[]> embedding2Vector(String... texts) {
        if (ObjectUtil.isEmpty(texts)) {
            throw new IllegalArgumentException("No text provided");
        }
        JSONArray param = new JSONArray();
        for (int i = 0; i < texts.length; i++) {
            param.add(texts[i]);
        }
        HttpRequest req = HttpRequest.post(API_URL)
                .body(param.toJSONString())
                .contentType(ContentType.JSON.getValue())
                .timeout(5 * 60 * 1000); // 设置超时时间为 5分钟
        if (API_KEY != null && !API_KEY.isEmpty()) {
            req.header("Authorization", API_KEY);
        }
        HttpResponse resp = req.execute();
        if (resp.getStatus() != 200) throw new RuntimeException("文本向量化失败。Unexpected code " + resp.getStatus());
        String respStr = resp.body();
        JSONObject json = JSONObject.parseObject(respStr);
        List<float[]> result = new ArrayList<>();
        try {
            JSONArray jsonArray = json.getJSONArray("data");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONArray vectorArray = jsonArray.getJSONArray(i);
                float[] vector = new float[vectorArray.size()];
                for (int j = 0; j < vectorArray.size(); j++) {
                    vector[j] = vectorArray.getFloatValue(j);
                }
                result.add(vector);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 余弦相似度（float[]版本，适用于阈值分组法）
     */
    /**
     * 计算两个向量的余弦相似度（高性能优化版本）
     * 自动选择最优算法：低维度使用循环展开，高维度使用并行计算
     * @param v1 向量1
     * @param v2 向量2
     * @return 余弦相似度值 [0, 1]
     */
    public static double cosineSimilarity(float[] v1, float[] v2) {
        if (v1 == null || v2 == null) {
            throw new IllegalArgumentException("向量不能为null");
        }
        if (v1.length != v2.length) {
            throw new IllegalArgumentException("向量维度不匹配: " + v1.length + " vs " + v2.length);
        }
        if (v1.length == 0) {
            return 0.0;
        }

        int length = v1.length;

        // 根据向量维度选择最优算法
        if (length >= 2000) {
            // 高维向量使用并行计算
            return cosineSimilarityParallel(v1, v2, length);
        } else {
            // 低维向量使用循环展开优化
            return cosineSimilarityOptimized(v1, v2, length);
        }
    }

    /**
     * 并行计算余弦相似度（适用于高维向量）
     */
    private static double cosineSimilarityParallel(float[] v1, float[] v2, int length) {
        double dot = IntStream.range(0, length)
                .parallel()
                .mapToDouble(i -> (double) v1[i] * v2[i])
                .sum();

        double norm1 = IntStream.range(0, length)
                .parallel()
                .mapToDouble(i -> (double) v1[i] * v1[i])
                .sum();

        double norm2 = IntStream.range(0, length)
                .parallel()
                .mapToDouble(i -> (double) v2[i] * v2[i])
                .sum();

        double denominator = Math.sqrt(norm1) * Math.sqrt(norm2);
        return denominator == 0 ? 0.0 : dot / denominator;
    }

    /**
     * 循环展开优化的余弦相似度计算（适用于中低维向量）
     */
    private static double cosineSimilarityOptimized(float[] v1, float[] v2, int length) {
        double dot = 0.0, norm1 = 0.0, norm2 = 0.0;

        // 循环展开优化，每次处理8个元素
        int i = 0;
        for (; i <= length - 8; i += 8) {
            // 手动展开8个元素的计算
            double d0 = (double) v1[i] * v2[i];
            double d1 = (double) v1[i+1] * v2[i+1];
            double d2 = (double) v1[i+2] * v2[i+2];
            double d3 = (double) v1[i+3] * v2[i+3];
            double d4 = (double) v1[i+4] * v2[i+4];
            double d5 = (double) v1[i+5] * v2[i+5];
            double d6 = (double) v1[i+6] * v2[i+6];
            double d7 = (double) v1[i+7] * v2[i+7];

            dot += d0 + d1 + d2 + d3 + d4 + d5 + d6 + d7;

            double n10 = (double) v1[i] * v1[i];
            double n11 = (double) v1[i+1] * v1[i+1];
            double n12 = (double) v1[i+2] * v1[i+2];
            double n13 = (double) v1[i+3] * v1[i+3];
            double n14 = (double) v1[i+4] * v1[i+4];
            double n15 = (double) v1[i+5] * v1[i+5];
            double n16 = (double) v1[i+6] * v1[i+6];
            double n17 = (double) v1[i+7] * v1[i+7];

            norm1 += n10 + n11 + n12 + n13 + n14 + n15 + n16 + n17;

            double n20 = (double) v2[i] * v2[i];
            double n21 = (double) v2[i+1] * v2[i+1];
            double n22 = (double) v2[i+2] * v2[i+2];
            double n23 = (double) v2[i+3] * v2[i+3];
            double n24 = (double) v2[i+4] * v2[i+4];
            double n25 = (double) v2[i+5] * v2[i+5];
            double n26 = (double) v2[i+6] * v2[i+6];
            double n27 = (double) v2[i+7] * v2[i+7];

            norm2 += n20 + n21 + n22 + n23 + n24 + n25 + n26 + n27;
        }

        // 处理剩余元素
        for (; i < length; i++) {
            double d = (double) v1[i] * v2[i];
            double n1 = (double) v1[i] * v1[i];
            double n2 = (double) v2[i] * v2[i];

            dot += d;
            norm1 += n1;
            norm2 += n2;
        }

        double denominator = Math.sqrt(norm1) * Math.sqrt(norm2);
        return denominator == 0.0 ? 0.0 : dot / denominator;
    }

    // ========== 向量文件读取和写入方法 ==========

    /**
     * 向量文件索引器，用于高效的随机访问
     */
    public static class VectorFileIndexer {
        private final File file;
        private final List<Long> lineOffsets; // 每行在文件中的字节偏移量
        private final int totalLines;

        public VectorFileIndexer(File file) throws IOException {
            this.file = file;
            this.lineOffsets = new ArrayList<>();
            this.totalLines = buildIndex();
        }

        public VectorFileIndexer(String filePath){
            try {
                this.file = new File(filePath);
                this.lineOffsets = new ArrayList<>();
                this.totalLines = buildIndex();
            } catch (IOException e) {
                log.error("创建向量文件索引器失败: {}", filePath, e);
                throw new RuntimeException("创建向量文件索引器失败", e);
            }
        }


        /**
         * 构建行索引
         */
        private int buildIndex() throws IOException {
            lineOffsets.add(0L); // 第一行从0开始

            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                long offset = 0;
                int lineCount = 0;

                while (raf.readLine() != null) {
                    lineCount++;
                    offset = raf.getFilePointer();
                    if (offset < raf.length()) {
                        lineOffsets.add(offset);
                    }
                }

                log.info("构建向量文件索引完成: {}, 总行数: {}", file.getAbsolutePath(), lineCount);
                return lineCount;
            }
        }

        /**
         * 读取指定行号的向量数据（高效随机访问）
         * @param lineNumber 行号（从1开始）
         * @return 向量数据项，如果行不存在返回null
         */
        public SsRepeatTaskVo.VectorItem readLine(int lineNumber) {
            if (lineNumber <= 0 || lineNumber > totalLines) {
                return null;
            }

            long offset = lineOffsets.get(lineNumber - 1);

            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                raf.seek(offset);
                String line = raf.readLine();

                if (line != null && !line.trim().isEmpty()) {
                    return parseVectorLine(line.trim());
                }
            } catch (IOException e){
                log.error("读取向量文件行失败: {}, 行号: {}", file.getAbsolutePath(), lineNumber, e);
                return null;
            }

            return null;
        }

        /**
         * 批量读取多个行号的向量数据（高效随机访问）
         * @param lineNumbers 行号列表（从1开始）
         * @return 向量数据列表
         */
        public List<SsRepeatTaskVo.VectorItem> readLines(List<Integer> lineNumbers) throws IOException {
            List<SsRepeatTaskVo.VectorItem> results = new ArrayList<>();

            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                for (Integer lineNumber : lineNumbers) {
                    if (lineNumber > 0 && lineNumber <= totalLines) {
                        long offset = lineOffsets.get(lineNumber - 1);
                        raf.seek(offset);
                        String line = raf.readLine();

                        if (line != null && !line.trim().isEmpty()) {
                            try {
                                SsRepeatTaskVo.VectorItem item = parseVectorLine(line.trim());
                                results.add(item);
                            } catch (Exception e) {
                                log.warn("解析向量文件第{}行失败: {}", lineNumber, e.getMessage());
                            }
                        }
                    }
                }
            }

            return results;
        }

        /**
         * 读取从指定行号开始的所有向量数据
         * @param startLineNumber 起始行号（从1开始，包含）
         * @return 向量数据列表
         */
        public List<SsRepeatTaskVo.VectorItem> readLinesFrom(int startLineNumber) throws IOException {
            if (startLineNumber <= 0 || startLineNumber > totalLines) {
                return new ArrayList<>();
            }

            List<SsRepeatTaskVo.VectorItem> results = new ArrayList<>();
            long offset = lineOffsets.get(startLineNumber - 1);

            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                raf.seek(offset);
                String line;
                int currentLine = startLineNumber;

                while ((line = raf.readLine()) != null && currentLine <= totalLines) {
                    line = line.trim();
                    if (!line.isEmpty()) {
                        try {
                            SsRepeatTaskVo.VectorItem item = parseVectorLine(line);
                            results.add(item);
                        } catch (Exception e) {
                            log.warn("解析向量文件第{}行失败: {}", currentLine, e.getMessage());
                        }
                    }
                    currentLine++;
                }
            }

            return results;
        }

        /**
         * 读取指定行号范围的向量数据
         * @param startLineNumber 起始行号（从1开始，包含）
         * @param endLineNumber 结束行号（从1开始，包含）
         * @return 向量数据列表
         */
        public List<SsRepeatTaskVo.VectorItem> readLinesRange(int startLineNumber, int endLineNumber) throws IOException {
            if (startLineNumber <= 0 || endLineNumber <= 0 || startLineNumber > endLineNumber || startLineNumber > totalLines) {
                return new ArrayList<>();
            }

            endLineNumber = Math.min(endLineNumber, totalLines);
            List<SsRepeatTaskVo.VectorItem> results = new ArrayList<>();
            long offset = lineOffsets.get(startLineNumber - 1);

            try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
                raf.seek(offset);
                String line;
                int currentLine = startLineNumber;

                while ((line = raf.readLine()) != null && currentLine <= endLineNumber) {
                    line = line.trim();
                    if (!line.isEmpty()) {
                        try {
                            SsRepeatTaskVo.VectorItem item = parseVectorLine(line);
                            results.add(item);
                        } catch (Exception e) {
                            log.warn("解析向量文件第{}行失败: {}", currentLine, e.getMessage());
                        }
                    }
                    currentLine++;
                }
            }

            return results;
        }

        /**
         * 获取总行数
         */
        public int getTotalLines() {
            return totalLines;
        }

        /**
         * 获取文件信息
         */
        public String getFileInfo() {
            return String.format("VectorFileIndexer{file=%s, totalLines=%d}", file.getAbsolutePath(), totalLines);
        }
    }

    /**
     * 将向量数据批量写入文件（追加模式）
     * @param file 目标文件
     * @param vectorItems 向量数据列表
     * @throws IOException 写入异常
     */
    public static void writeVectorFile(File file, List<SsRepeatTaskVo.VectorItem> vectorItems) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file, true))) {
            for (SsRepeatTaskVo.VectorItem item : vectorItems) {
                writeVectorLine(writer, item.getId(), item.getVector());
            }
            writer.flush();
        }
    }

    /**
     * 将ID和向量数组批量写入文件（追加模式）
     * @param file 目标文件
     * @param ids ID数组
     * @param vectors 向量数组
     * @throws IOException 写入异常
     */
    public static void writeVectorFile(File file, Long[] ids, float[][] vectors) throws IOException {
        if (ids.length != vectors.length) {
            throw new IllegalArgumentException("ID数组和向量数组长度不匹配");
        }

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file, true))) {
            for (int i = 0; i < ids.length; i++) {
                writeVectorLine(writer, ids[i], vectors[i]);
            }
            writer.flush();
        }
    }

    /**
     * 将ID和向量数组批量写入文件（追加模式）- 使用List
     * @param file 目标文件
     * @param ids ID列表
     * @param vectors 向量列表
     * @throws IOException 写入异常
     */
    public static void writeVectorFile(File file, List<Long> ids, List<float[]> vectors) throws IOException {
        if (ids.size() != vectors.size()) {
            throw new IllegalArgumentException("ID列表和向量列表长度不匹配");
        }

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file, true))) {
            for (int i = 0; i < ids.size(); i++) {
                writeVectorLine(writer, ids.get(i), vectors.get(i));
            }
            writer.flush();
        }
    }

    /**
     * 写入单行向量数据
     * @param writer 写入器
     * @param id ID
     * @param vector 向量数组
     * @throws IOException 写入异常
     */
    private static void writeVectorLine(BufferedWriter writer, Long id, float[] vector) throws IOException {
        // 将id和向量数组写到一行：格式为 "id,v1,v2,v3,..."
        StringBuilder line = new StringBuilder();
        line.append(id);

        for (float value : vector) {
            line.append(",").append(value);
        }

        // 写入一行并换行
        writer.write(line.toString());
        writer.newLine();
    }


    /**
     * 解析单行向量数据
     * @param line 格式为 "id,v1,v2,v3,..."
     * @return 向量数据项
     */
    private static SsRepeatTaskVo.VectorItem parseVectorLine(String line) {
        String[] parts = line.split(",");

        if (parts.length < 2) {
            throw new IllegalArgumentException("向量数据格式错误，至少需要id和一个向量值");
        }

        // 解析ID
        Long id = Long.parseLong(parts[0]);

        // 解析向量数组
        float[] vector = new float[parts.length - 1];
        for (int i = 1; i < parts.length; i++) {
            vector[i - 1] = Float.parseFloat(parts[i]);
        }

        return new SsRepeatTaskVo.VectorItem(id, vector);
    }

}