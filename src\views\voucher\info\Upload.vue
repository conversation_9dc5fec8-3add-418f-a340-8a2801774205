<template>
  <a-modal
    :confirm-loading="loading"
    :open="visible"
    title="上传凭证文件"
    width="60%"
    @cancel="onCancel"
    @ok="onOk"
  >
    <a-form ref="formRef" :label-col="{ style: { width: '120px' } }" :model="formData">
      <a-form-item
        :rules="{ message: '必须选择文件', required: true }"
        label="选择文件"
        name="files"
      >
        <a-upload
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
          :multiple="true"
          accept=".pdf,.jpg,.jpeg,.png,.bmp,.tiff"
          list-type="text"
        >
          <a-button>
            <template #icon>
              <x-icon type="UploadOutlined" />
            </template>
            选择文件
          </a-button>
        </a-upload>
        <div class="form-help-text">
          支持格式：PDF、JPG、JPEG、PNG、BMP、TIFF，可多选
        </div>
      </a-form-item>
      
      <a-form-item
        label="标签"
        name="tags"
      >
        <a-input
          v-model:value="formData.tags"
          :maxlength="200"
          placeholder="请输入标签，多个标签用英文逗号分隔"
        />
        <div class="form-help-text">
          为上传的凭证添加标签，便于后续分析和管理，多个标签用英文逗号分隔
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts" name="VoucherInfoUpload">
import { type ComponentCustomProperties, getCurrentInstance, ref } from 'vue'
import { info } from '@/api/voucher'
import type { UploadFile } from 'ant-design-vue'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible', 'success'])
const props = defineProps<{
  visible: boolean
}>()

const loading = ref(false)
const formRef = ref()
const fileList = ref<UploadFile[]>([])
const formData = ref({
  tags: ''
})

function onCancel() {
  formRef.value?.resetFields()
  fileList.value = []
  formData.value = {
    tags: ''
  }
  emits('update:visible', false)
}

function beforeUpload(file: UploadFile) {
  // 阻止自动上传，手动控制上传
  return false
}

function onOk() {
  if (fileList.value.length === 0) {
    _this.$message.error('请选择要上传的文件')
    return
  }

  _this.$form.validate(formRef.value, async (errors, values) => {
    if (errors) {
      return false
    }
    try {
      loading.value = true
      
      // 创建FormData
      const formData = new FormData()
      
      // 添加文件
      fileList.value.forEach((file) => {
        if (file.originFileObj) {
          formData.append('file', file.originFileObj)
        }
      })
      
      // 添加标签
      if (values.tags) {
        formData.append('tags', values.tags)
      }
      
      const { message } = await info.upload(formData)
      _this.$message.success(message || '上传成功，后台解析中...')
      emits('success', true)
      onCancel()
    } catch (error: any) {
      _this.$message.error(error.message || '上传失败')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style scoped lang="less">
.ant-form-item {
  margin-bottom: 16px;
}

.form-help-text {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

:deep(.ant-upload-list) {
  max-height: 200px;
  overflow-y: auto;
}
</style>
