package com.cb.ai.data.analysis.petition.domain.entity;


import com.cb.ai.data.analysis.petition.domain.vo.TreeEntityVo;
import org.springframework.data.annotation.Id;

/**
 * 区划信息对象 sys_area
 * 
 * <AUTHOR>
 * @date 2024-12-03
 */
public class SysArea extends TreeEntityVo
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Id
    private Long areaId;

    /** 区域编码 */
    private String areaCode;

    /**
     * 层级：0国 1省 2市 3区县
     */
    private Integer level;

    /**
     * 地图编码
     */
    private String adCode;

    /** 区域名称 */
    private String areaName;

    /** 详细地址 */
    private String address;

    /** 启用标记 */
    private String enabled;


    private Long parentId;

    /**
     * 排序字段
     */
    private Integer sortOrder;


    /** 备注 */
    private String remark;

    /** 删除标志（1代表存在 2代表删除） */
    private String delFlag;

    public void setAreaId(Long areaId) 
    {
        this.areaId = areaId;
    }

    public Long getAreaId() 
    {
        return areaId;
    }
    public void setAreaCode(String areaCode) 
    {
        this.areaCode = areaCode;
    }

    public String getAreaCode() 
    {
        return areaCode;
    }
    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }

    public String getAreaName()
    {
        return areaName;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setEnabled(String enabled) 
    {
        this.enabled = enabled;
    }

    public String getEnabled() 
    {
        return enabled;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }


    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    public Long getParentId() {
        return this.parentId;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
    public Integer getLevel() {
        return this.level;
    }
}
