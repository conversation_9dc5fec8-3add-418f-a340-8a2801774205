package com.cb.ai.data.analysis.file.model;

import com.cb.ai.data.analysis.file.constant.HttpCodeUploadEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 文件上传信息，查询 redis 后的返回信息
 */
@Data
@Accessors(chain = true)
public class FileUploadInfo {
    @NotBlank(message = "digestMd5 不能为空")
    private String digestMd5;

    private String id;

    @NotBlank(message = "uploadId 不能为空")
    private String uploadId;

    @NotBlank(message = "文件名不能为空")
    private String filename;

    @NotBlank(message = "文件夹不能为空")
    private String folderId;

    private Integer uploadStatus;

    // 仅秒传会有值
    private String url;
    // 后端使用
    private String filePath;
    private String type;

    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @NotNull(message = "分片数量不能为空")
    private Integer chunkCount;

    @NotNull(message = "分片大小不能为空")
    private Long chunkSize;

    private String contentType;
    /**
     * 是否已存在，true已存在，false不存在
     */
    private Boolean alreadyExists=false;

    // listParts 从 1 开始，前端需要上传的分片索引+1
    private List<Integer> listParts;

}

