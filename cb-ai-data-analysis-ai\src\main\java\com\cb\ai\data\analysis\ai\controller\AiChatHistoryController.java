package com.cb.ai.data.analysis.ai.controller;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistory;
import com.cb.ai.data.analysis.ai.service.AiChatHistoryService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI 会话历史记录(AiChatHistory)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-07 14:23:50
 */
@Validated
@RestController
@RequestMapping(Constants.API_AI_ROOT_PATH + "/chat/history")
public class AiChatHistoryController extends BaseController<AiChatHistoryService, AiChatHistory> {

    /**
     * 分页获取AI 会话历史记录
     *
     * @param aiChatHistory 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(AiChatHistory aiChatHistory) {
        LambdaQueryWrapper<AiChatHistory> queryWrapper = XQueryWrapper.newInstance(aiChatHistory)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(AiChatHistory::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    @GetMapping("/userChatHistory")
    public Result userChatHistory(AiChatHistory aiChatHistory) {
        if (ObjectUtil.isNull(aiChatHistory)) {
            aiChatHistory = new AiChatHistory();
        }
        aiChatHistory.setUserId(SecurityUtils.getUserId());
        LambdaQueryWrapper<AiChatHistory> queryWrapper = XQueryWrapper.newInstance(aiChatHistory)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(AiChatHistory::getCreateTime);
        return Result.successData(baseService.list(queryWrapper));
    }

    /**
     * 获取AI 会话历史记录
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "AI 会话历史记录ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增AI 会话历史记录
     *
     * @param aiChatHistory 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增AI 会话历史记录", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody AiChatHistory aiChatHistory) {
        String title = aiChatHistory.getTitle();
        title = title.trim();
        if (title.length() > 255) {
            Pattern pattern = Pattern.compile("[.,?]");
            Matcher matcher = pattern.matcher(title);
            if (matcher.find() && matcher.start() <= 255) {
                title = title.substring(0, matcher.start());
            } else {
                title = title.substring(0, 250);
            }
        }
        aiChatHistory.setTitle(title);
        aiChatHistory.setUserId(SecurityUtils.getUserId());
        if (baseService.save(aiChatHistory)) {
            return Result.success("新增AI 会话历史记录成功", aiChatHistory);
        } else {
            return Result.fail("新增AI 会话历史记录失败");
        }
    }

    /**
     * 修改AI 会话历史记录
     *
     * @param aiChatHistory 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改AI 会话历史记录", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody AiChatHistory aiChatHistory) {
        if (baseService.updateById(aiChatHistory)) {
            return Result.success("AI 会话历史记录修改成功", aiChatHistory);
        } else {
            return Result.fail("AI 会话历史记录修改失败");
        }
    }

    /**
     * 删除AI 会话历史记录
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除AI 会话历史记录", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "AI 会话历史记录ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.removeByIds(list)) {
            return Result.success("AI 会话历史记录删除成功");
        } else {
            return Result.fail("AI 会话历史记录删除失败");
        }
    }
}

