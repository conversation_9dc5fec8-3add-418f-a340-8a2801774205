package com.cb.ai.data.analysis.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.file.config.MinioConfigInfo;
import com.cb.ai.data.analysis.file.constant.Constants;
import com.cb.ai.data.analysis.file.constant.HttpCodeUploadEnum;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFolder;
import com.cb.ai.data.analysis.file.mapper.SuperviseResourceFileMapper;
import com.cb.ai.data.analysis.file.model.FileUploadInfo;
import com.cb.ai.data.analysis.file.model.UploadUrlsVO;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFileService;
import com.cb.ai.data.analysis.file.service.SuperviseResourceFolderService;
import com.cb.ai.data.analysis.file.util.BeanCopyUtils;
import com.cb.ai.data.analysis.file.util.MinioUtil;
import com.xong.boot.common.crypto.file.DelegatingFileEncoder;
import com.xong.boot.common.exception.XFileException;
import com.xong.boot.common.properties.FileProperties;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.*;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.utils.SecurityUtils;
import io.minio.GetObjectResponse;
import io.minio.StatObjectResponse;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SuperviseResourceFileServiceImpl extends BaseServiceImpl<SuperviseResourceFileMapper, SuperviseResourceFile> implements SuperviseResourceFileService {
    private final FileProperties fileProperties;
    private final DelegatingFileEncoder delegatingFileEncoder;
    private final SuperviseResourceFolderService superviseResourceFolderService;
    private final String redisPrefix="minioUpload:";
    @Resource
    private MinioUtil minioUtil;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private MinioConfigInfo minioConfigInfo;
    private static final Integer BUFFER_SIZE = 1024 * 64; // 64KB

    public SuperviseResourceFileServiceImpl(FileProperties fileProperties, DelegatingFileEncoder delegatingFileEncoder, SuperviseResourceFolderService superviseResourceFolderService) {
        this.fileProperties = fileProperties;
        this.delegatingFileEncoder = delegatingFileEncoder;
        this.superviseResourceFolderService = superviseResourceFolderService;
    }

    /**
     * @param inputStream 输入流
     * @param md5         计算的md5的值，md5为空我就自己算
     * @param fileName    件原始名称，带后缀的
     * @param folderName  文件夹名称，会创建在根目录下
     * @param fileSize    文件大小
     * @param contentType 文件类型
     * @param fileTags    文件标签，可以不传
     * @param userName    用户名
     * @return
     * @throws Exception
     */
    @Override
    public SuperviseResourceFile uploadFileStreamByFolderName(InputStream inputStream, String md5, String fileName, String folderName, Long fileSize,
                                                              String contentType, String fileTags,
                                                              String userName
    ) throws Exception {
        LambdaQueryWrapper<SuperviseResourceFolder> lambda = new QueryWrapper<SuperviseResourceFolder>().lambda();
        lambda.eq(SuperviseResourceFolder::getFolderName, folderName);
        List<SuperviseResourceFolder> list = superviseResourceFolderService.list(lambda);
        SuperviseResourceFolder superviseResourceFolder = null;
        if (list.isEmpty()) {
            //创建一个处于根目录的文件夹
            superviseResourceFolder = new SuperviseResourceFolder();
            superviseResourceFolder.setFolderName(folderName);
            superviseResourceFolder.setId(IdUtil.getSnowflakeNextIdStr());
            superviseResourceFolder.setParentId("0");
            superviseResourceFolder.setFullPath("0," + superviseResourceFolder.getId());
            superviseResourceFolder.setCreateBy(userName);
            superviseResourceFolder.setStatus(0);
            superviseResourceFolder.setSortOn(1);
            superviseResourceFolderService.save(superviseResourceFolder);
        } else {
            superviseResourceFolder = list.get(0);
        }
        return uploadFileStreamByFolderId(inputStream, md5, fileName, superviseResourceFolder.getId(), fileSize, contentType, fileTags, userName);
    }

    /**
     * @param inputStream 输入流
     * @param md5         计算的md5的值，md5为空我就自己算
     * @param fileName    件原始名称，带后缀的
     * @param folderId    文件夹id
     * @param fileSize    文件大小
     * @param contentType 文件类型
     * @param fileTags    文件标签，可以不传
     * @param userName    用户名
     * @return
     * @throws Exception
     */
    @Override
    public SuperviseResourceFile uploadFileStreamByFolderId(InputStream inputStream, String md5, String fileName, String folderId, Long fileSize,
                                                            String contentType, String fileTags,
                                                            String userName
    ) throws Exception {
        //md5为空我就自己算
        if (StringUtils.isBlank(md5)) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(baos.toByteArray());
            inputStream = new ByteArrayInputStream(baos.toByteArray());
            md5 = SecureUtils.md5(byteArrayInputStream);
        }
//        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
        //同一文件夹下只能有一个文件
//        lambda.eq(SuperviseResourceFile::getDigestMd5,md5);
//        lambda.eq(SuperviseResourceFile::getFolderId,folderId);
//        lambda.eq(SuperviseResourceFile::getFilename,fileName);
//        lambda.eq(SuperviseResourceFile::getCreateBy,userName);
//        List<SuperviseResourceFile> list = list(lambda);

        String suffix = FileNameUtils.extName(fileName);
        if (!ArrayUtil.containsIgnoreCase(Constants.ALLOW_SUFFIX, "ALL") && !FileNameUtils.isType(Constants.ALLOW_SUFFIX, suffix)) {
            throw new XFileException(String.format("只能上传%s格式文件", Arrays.toString(Constants.ALLOW_SUFFIX)));
        }
        if (fileSize > Constants.SINGLE_MAX_SIZE.toBytes()) {
            throw new XFileException(String.format("文件大小请控制在%dM以内", Constants.SINGLE_MAX_SIZE.toMegabytes()));
        }
        //如果文件不存在就处理
        SuperviseResourceFile fileByFileNameAndFolderId = getFileByFileNameAndFolderId(fileName, folderId, userName);
        if (fileByFileNameAndFolderId == null) {
            SuperviseResourceFile superviseResourceFile = new SuperviseResourceFile();
            superviseResourceFile.setId(IdUtil.getSnowflakeNextIdStr());
            superviseResourceFile.setFolderId(folderId);
            superviseResourceFile.setContentType(contentType);
            superviseResourceFile.setFilename(fileName);
            superviseResourceFile.setFileSuffix(suffix);
            superviseResourceFile.setFileSize(fileSize);
            superviseResourceFile.setFileTags(fileTags); // 文件标签
            superviseResourceFile.setAlgorithm(String.valueOf(fileProperties.getAlgorithm()));
            superviseResourceFile.setDigestMd5(md5);
            String filePath = getFilePath(fileName, userName, md5, suffix);
            superviseResourceFile.setFilePath(filePath);
            String upload = minioUtil.upload(filePath, inputStream, fileSize, contentType);
            save(superviseResourceFile);
            return superviseResourceFile;
        }

        //如果文件存在，并且md5的值是不一样的，那就覆盖
        if (!md5.equals(fileByFileNameAndFolderId.getDigestMd5())) {
            /**
             * 原本是想按当天的日期重新存储一个新的文件路径，但是这里条件需要，就直接用之前的路径直接覆盖原来的文件也就不用删除了，minio会自己覆盖
             */
            //删除原有的
//            Boolean b = minioUtil.deleteObject(fileByFileNameAndFolderId.getFilePath());
//            fileName = FileUtil.mainName(fileName);
//            // 对文件重命名，并以年月日文件夹格式存储
//            String nestFile = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd");
//            String object = nestFile + "/" + fileName + "_" + md5 + "." + suffix;
//            String filePath="/"+userName+"/"+object;
//            fileByFileNameAndFolderId.setFilePath(filePath);
            //上传现在的
            fileByFileNameAndFolderId.setDigestMd5(md5);
            String upload = minioUtil.upload(fileByFileNameAndFolderId.getFilePath(), inputStream, fileSize, contentType);
            updateById(fileByFileNameAndFolderId);
        }
        return fileByFileNameAndFolderId;
    }

    private String getFilePath(String fileName, String userName, String md5, String suffix) {
        fileName = FileUtil.mainName(fileName);

        // 对文件重命名，并以年月日文件夹格式存储
        String nestFile = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd");
        long timestamp = System.currentTimeMillis();
        String object = nestFile + "/" + fileName + "_" + md5 +"_"+timestamp+ "." + suffix;

        String filePath = "/" + userName + "/" + object;
        return filePath;
    }

    /**
     * 根据文件id获取下载链接
     *
     * @param fileId
     * @return
     */
    @Override
    public String getDownloadUrl(String fileId) {
        SuperviseResourceFile byId = getById(fileId);
        if (byId != null) {
            try {
                return minioUtil.GetPresignedObjectUrl(byId.getFilePath());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /**
     * 获取文件输入流
     *
     * @param fileId
     * @return
     */
    @Override
    public InputStream getFileStream(String fileId) {
        SuperviseResourceFile byId = getById(fileId);
        if (byId != null) {
            try {
                return minioUtil.getObjectStream(byId.getFilePath());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /**
     * 上传文件
     *
     * @param file
     * @param superviseResourceFolder
     * @param md5
     * @param fileTags
     * @return
     * @throws Exception
     */
    @Override
    public SuperviseResourceFile uploadFile(MultipartFile file, SuperviseResourceFolder superviseResourceFolder, String md5, String fileTags) throws Exception {
//        if (file == null || file.isEmpty()) { // 判断是否有文件
//            throw new XFileException("文件不存在");
//        }
//        String suffix = FileNameUtils.extName(file.getOriginalFilename());
//        if (!ArrayUtil.containsIgnoreCase(Constants.ALLOW_SUFFIX, "ALL") && !FileNameUtils.isType(Constants.ALLOW_SUFFIX, suffix)) {
//            throw new XFileException(String.format("只能上传%s格式文件", Arrays.toString(Constants.ALLOW_SUFFIX)));
//        }
//        long fileSize = file.getSize();
//        if (fileSize > Constants.SINGLE_MAX_SIZE.toBytes()) {
//            throw new XFileException(String.format("文件大小请控制在%dM以内", Constants.SINGLE_MAX_SIZE.toMegabytes()));
//        }
//        SuperviseResourceFile superviseResourceFile = new SuperviseResourceFile();
//        superviseResourceFile.setId(IdUtil.getSnowflakeNextIdStr());
//        superviseResourceFile.setFolderId(superviseResourceFolder.getId());
//        superviseResourceFile.setContentType(file.getContentType());
//        superviseResourceFile.setFilename(file.getOriginalFilename());
//        superviseResourceFile.setContentType(file.getContentType());
//        superviseResourceFile.setFileSuffix(suffix);
//        superviseResourceFile.setFileSize(fileSize);
//        superviseResourceFile.setFileTags(fileTags); // 文件标签
//        superviseResourceFile.setAlgorithm(String.valueOf(fileProperties.getAlgorithm()));
//        superviseResourceFile.setDigestMd5(md5);
//
//        //改用minio文件上传
//        //构建文件名称
//        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
//        String username = userDetails.getUsername();
////        //获取文件夹
//        String originFileName = file.getOriginalFilename();
//        String fileName = FileUtil.mainName(originFileName);
//        // 对文件重命名，并以年月日文件夹格式存储
//        String nestFile = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd");
//        String object = nestFile + "/" + fileName + "_" + md5 + "." + suffix;
////        fileUploadInfo.setFilePath(object).setType(suffix);
//        String filePath="/"+username+"/"+object;
//        superviseResourceFile.setFilePath(filePath);
//        String upload = minioUtil.upload(filePath, file.getInputStream(), fileSize, file.getContentType());
        UserDetailsImpl userDetails = SecurityUtils.getUserDetails();
        String username = userDetails.getUsername();
        SuperviseResourceFile superviseResourceFile = uploadFileStreamByFolderId(file.getInputStream(), md5, file.getOriginalFilename(),
                superviseResourceFolder.getId(),
                file.getSize(), file.getContentType(), fileTags, username);
        return superviseResourceFile;
    }

    /**
     * 获取自己文件夹下面的文件名文件
     *
     * @param fileName
     * @param folderId
     * @param userName
     * @return
     */
    @Override
    public SuperviseResourceFile getFileByFileNameAndFolderId(String fileName, String folderId, String userName) {
        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
        lambda.eq(SuperviseResourceFile::getFilename, fileName);
        lambda.eq(SuperviseResourceFile::getFolderId, folderId);
        lambda.eq(SuperviseResourceFile::getCreateBy, userName);
        List<SuperviseResourceFile> list = list(lambda);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    ;


    @Override
    public void downloadDirect(
            String fileId,
            HttpServletResponse response) throws IOException {
        SuperviseResourceFile fileInfo = getFileDetail(fileId);
        if (fileInfo == null) {
            response.sendError(HttpStatus.NOT_FOUND.value(), "文件不存在");
            return;
        }
        response.setContentType(fileInfo.getContentType());
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"" + encodeFilename(fileInfo.getFilename()) + "\"");
        Path encryptedPath = fileProperties.getAbsolutePath(fileInfo.getFilePath());
        if (!Files.exists(encryptedPath)) {
            response.sendError(HttpStatus.NOT_FOUND.value(), "文件数据不存在");
            return;
        }
        try (InputStream encryptedIn = Files.newInputStream(encryptedPath);
             OutputStream responseOut = response.getOutputStream()) {
            delegatingFileEncoder.decode(encryptedIn, responseOut);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("文件下载失败", e);
            if (!response.isCommitted()) {
                response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件下载失败");
            }
        }
    }

    // 处理中文文件名编码问题
    private String encodeFilename(String filename) {
        try {
            return URLEncoder.encode(filename, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            return filename;
        }
    }


    @Override
    public Page<SuperviseResourceFile> pageDeletedFiles(Page<SuperviseResourceFile> page, SuperviseResourceFile superviseResourceFile) {
        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
        lambda.eq(SuperviseResourceFile::getDelFlag, true);
        lambda.eq(SuperviseResourceFile::getCreateBy, superviseResourceFile.getCreateBy());
        if (StringUtils.isNotBlank(superviseResourceFile.getFilename())) {
            lambda.like(SuperviseResourceFile::getFilename, superviseResourceFile.getFilename());
        }
        // 使用自定义方法查询已删除文件
        return baseMapper.getFileList(page, lambda);
    }

    /**
     * @param id
     * @return
     */
    @Override
    public SuperviseResourceFile getFileDetail(String id) {
        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
        lambda.eq(SuperviseResourceFile::getDelFlag, true);
        lambda.eq(SuperviseResourceFile::getId, id);
        List<SuperviseResourceFile> fileListByAlreadyDeleted = baseMapper.getFileList(lambda);
        if (fileListByAlreadyDeleted.size() > 0) {
            return fileListByAlreadyDeleted.get(0);
        }
        // 使用自定义方法查询已删除文件
        return null;
    }

    /**
     * 这里的设计逻辑是，如果这个文件夹被删除了，那么归属于文件夹下面的文件以及文件夹，一定会被删除，所以这里能还原文件，那么文件夹一定未被删除
     *
     * @param ids
     */
    @Override
    public Integer restoreFile(List<String> ids, String folderId) {
        return baseMapper.restoreFiles(ids, folderId);
    }

    /**
     * 根据文件id更新文件的原路径
     *
     * @param idList
     * @return
     */
    @Override
    public Boolean updateFilesOriginalPath(List<String> idList) {

        LambdaQueryWrapper<SuperviseResourceFile> lambda = new QueryWrapper<SuperviseResourceFile>().lambda();
        lambda.in(SuperviseResourceFile::getId, idList);
        List<SuperviseResourceFile> fileList = list(lambda);
        List<String> folderIdList = fileList.stream().map(SuperviseResourceFile::getFolderId).distinct().toList();
        /**
         * 获取删除文件的所有文件夹
         */
        LambdaQueryWrapper<SuperviseResourceFolder> superviseResourceFolderQueryWrapper = new QueryWrapper<SuperviseResourceFolder>().lambda();
        superviseResourceFolderQueryWrapper.in(SuperviseResourceFolder::getId, folderIdList);
        List<SuperviseResourceFolder> folderList = superviseResourceFolderService.list(superviseResourceFolderQueryWrapper);
        Map<String, SuperviseResourceFolder> folderIdMap = folderList.stream().collect(Collectors.toMap(SuperviseResourceFolder::getId, e -> e, (k1, k2) -> k1));
        /**
         * 获取全路径的文件夹
         */
        List<String> list = folderList.stream().map(SuperviseResourceFolder::getFullPath).toList();
        Map<String, SuperviseResourceFolder> allFolderIdMap = superviseResourceFolderService.folderIdMap(list);
        /**
         * 前置数据准备完成，准备删除文件
         */
        for (SuperviseResourceFile superviseResourceFile : fileList) {
            //更新他的前置原文件路径
            SuperviseResourceFolder superviseResourceFolder = folderIdMap.get(superviseResourceFile.getFolderId());
            ArrayList<String> strings = new ArrayList<>();

            if (superviseResourceFolder == null) {
                strings.add("根目录");
            } else {
                String fullPath = superviseResourceFolder.getFullPath();
                String[] split = fullPath.split(",");
                for (String s : split) {
                    SuperviseResourceFolder superviseResourceFolder1 = allFolderIdMap.get(s);
                    if (superviseResourceFolder1 == null) {
                        strings.add(superviseResourceFolder1.getFolderName());
                    }

                }
            }
            strings.add(superviseResourceFile.getFilename());
            superviseResourceFile.setOriginalPath(String.join("/", strings));
        }
        return updateBatchById(fileList);
    }

    @Override
    public List<SuperviseResourceFile> getFileList(SuperviseResourceFile superviseResourceFile) {
        LambdaQueryWrapper<SuperviseResourceFile> eq = new QueryWrapper<SuperviseResourceFile>().lambda().eq(SuperviseResourceFile::getDelFlag, true);
        return baseMapper.getFileList(eq);
    }

    @Override
    public List<SuperviseResourceFile> getFileList(LambdaQueryWrapper<SuperviseResourceFile> queryWrapper) {
        return baseMapper.getFileList(queryWrapper);
    }

    @Override
    public List<SuperviseResourceFile> getFileListByIds(List<String> ids) {
        LambdaQueryWrapper<SuperviseResourceFile> in = new QueryWrapper<SuperviseResourceFile>().lambda().in(SuperviseResourceFile::getId, ids);
        return baseMapper.getFileList(in);
    }


    @Override
    public boolean permanentlyDelete(List<String> ids) {
        if (null == ids || ids.isEmpty()) {
            return true;
        }
        List<SuperviseResourceFile> fileListByIds = getFileListByIds(ids);
        for (SuperviseResourceFile fileListById : fileListByIds) {
            Boolean b = deletePhysicalFile(fileListById);
//            if (b) {
                LambdaQueryWrapper<SuperviseResourceFile> in = new QueryWrapper<SuperviseResourceFile>().lambda().eq(SuperviseResourceFile::getId, fileListById.getId());
                baseMapper.deleteByCustom(in);
//            }
        }
        return true;
    }

    private Boolean deletePhysicalFile(SuperviseResourceFile file) {
        Boolean b = minioUtil.deleteObject(file.getFilePath());
        return b;
//        try {
//            // 构建文件绝对路径
//            String relativePath = file.getFilePath();
//            if (StringUtils.isBlank(relativePath)) {
//                throw new XFileException("文件路径为空");
//            }
//            Path absolutePath = fileProperties.getAbsolutePath(relativePath);
//            // 确保路径在允许的目录内（安全防护）
//            if (Files.exists(absolutePath)) {
//                Files.delete(absolutePath);
//                log.info("已删除物理文件: {}", absolutePath);
//            } else {
//                log.warn("文件不存在: {}", absolutePath);
//            }
//        } catch (IOException e) {
////            log.error("删除物理文件失败: {}", file.getFilename(), e);
//            throw new XFileException("删除物理文件失败: " + e.getMessage());
//        }
    }

    /**
     * 检查md5是否存在缓存
     *
     * @param md5
     * @return
     */
    @Override
    public FileUploadInfo checkFileByMd5(String md5, String fileName, String userName, String folderId) {
        log.info("查询md5: <{}> 在redis是否存在", md5);
        String fileNameMd5 =redisPrefix+ DigestUtil.md5Hex(fileName+folderId)+":"+md5;
//        MD5 md6 = Md5U.create(fileName);
        String s = redisUtils.get(fileNameMd5);
        if (s != null) {
            //状态上传中
            JSONObject jsonObject = JSONObject.parseObject(s);
            FileUploadInfo fileUploadInfo = jsonObject.toJavaObject(FileUploadInfo.class);
            if (fileUploadInfo.getChunkCount()==1){
                //分片1，检查的时候就让他重新上传，否则会出错，因为分片1的时候没有走分片上传
                 fileUploadInfo=new FileUploadInfo();
                fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.NOT_UPLOAD.getCode());
                return fileUploadInfo;
            }
            List<Integer> listParts = minioUtil.getListParts(fileUploadInfo.getFilePath(), fileUploadInfo.getUploadId());
            fileUploadInfo.setListParts(listParts);
            fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.UPLOADING.getCode());
            return fileUploadInfo;
        }
        //ps：记错了，这一步我在初始化上传分片的时候做了
        //增加一步拦截，文件夹下有同名文件，但是内容不同
//        List<SuperviseResourceFile> listByName = list(new LambdaQueryWrapper<SuperviseResourceFile>()
//                .eq(SuperviseResourceFile::getFilename, fileName)
//         .eq(SuperviseResourceFile::getFolderId, folderId));
//        if (listByName != null || listByName.size() > 0) {
//            SuperviseResourceFile superviseResourceFile = listByName.get(0);
//            if (md5.equals(superviseResourceFile.getDigestMd5())) {
//                //文件相同，返回成功
//                FileUploadInfo dbFileInfo = BeanCopyUtils.copyBean(superviseResourceFile, FileUploadInfo.class);
//                dbFileInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
//                return dbFileInfo;
//            }else{
//                //文件不同，覆盖他
//                FileUploadInfo dbFileInfo = BeanCopyUtils.copyBean(superviseResourceFile, FileUploadInfo.class);
//                dbFileInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
//                return dbFileInfo;
//            }
//        }
        log.info("redis中不存在md5: <{}> 查询mysql是否存在", md5);
        //这里其实全局找别人有没有传过相同的文件，有的话直接复制就行了
        List<SuperviseResourceFile> list = list(new LambdaQueryWrapper<SuperviseResourceFile>()
                .eq(SuperviseResourceFile::getDigestMd5, md5));
//                .eq(SuperviseResourceFile::getFolderId, folderId));
        if (!list.isEmpty()) {
            //检查文件是否存在，
//            SuperviseResourceFile file = list.get(0);
            SuperviseResourceFile file = null;
            List<SuperviseResourceFile> superviseResourceFiles = new ArrayList<>();
            for (SuperviseResourceFile superviseResourceFile : list) {
                Boolean b = minioUtil.checkObject(superviseResourceFile.getFilePath());
                if (!b){
                    log.info("mysql中存在md5: <{}> 的文件 该文件已上传至minio 秒传直接过", md5);
                    permanentlyDelete(Collections.singletonList(superviseResourceFile.getId()));
                }else{
                    superviseResourceFiles.add(superviseResourceFile);
                    file=superviseResourceFile;
                }
            }
            if (file == null) {
                FileUploadInfo fileUploadInfo = new FileUploadInfo();
                fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.NOT_UPLOAD.getCode());
                return fileUploadInfo;
            }
            log.info("mysql中存在md5: <{}> 的文件 该文件已上传至minio 秒传直接过", md5);
            //这时候要判断这个文件夹下面，有没有这个文件，如果没有，应该要从自己传的其他文件夹或者其他人传的文件夹minio中复制过来
//            LambdaQueryWrapper<SuperviseResourceFile> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(SuperviseResourceFile::getFolderId, folderId);
//            queryWrapper.eq(SuperviseResourceFile::getDigestMd5, md5);
//            List<SuperviseResourceFile> listByFolder = list(queryWrapper);
            List<SuperviseResourceFile> listByFolder = superviseResourceFiles.stream().filter(e -> e.getFolderId().equals(folderId)).collect(Collectors.toList());
            if (listByFolder.isEmpty()) {
                //文件夹下没有，直接复制
                String suffix = FileNameUtils.extFileSuffix(fileName);
                String filePath = getFilePath(fileName, userName, md5, suffix);
                Boolean b = minioUtil.copyObject(file.getFilePath(), filePath);
                if (!b){
                    FileUploadInfo fileUploadInfo = new FileUploadInfo();
                    fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.NOT_UPLOAD.getCode());
                    return fileUploadInfo;
                }
                //复制完成后再添加一条记录
                SuperviseResourceFile saveFile = BeanCopyUtils.copyBean(file, SuperviseResourceFile.class);
                saveFile.setId(IdUtil.getSnowflakeNextIdStr());
                saveFile.setFilename(fileName);
                saveFile.setFilePath(filePath);
                saveFile.setFolderId(folderId);
                saveFile.setCreateBy(null);
                saveFile.setCreateTime(null);
                saveFile.setUpdateBy(null);
                saveFile.setUpdateTime(null);
                save(saveFile);
                FileUploadInfo fileUploadInfo = BeanCopyUtils.copyBean(saveFile, FileUploadInfo.class);
                fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
                return fileUploadInfo;
            }else{
                //文件夹下有，那就看看是不是同名文件，文件同名，则覆盖
                List<SuperviseResourceFile> collect = listByFolder.stream().filter(e -> fileName.equals(e.getFilename())).collect(Collectors.toList());
                if (collect.isEmpty()) {
                    //相同文件夹下面有相同内容的文件，但是文件名不相同
                    String suffix = FileNameUtils.extFileSuffix(fileName);
                    String filePath = getFilePath(fileName, userName, md5, suffix);
                    Boolean b = minioUtil.copyObject(file.getFilePath(), filePath);
                    if (!b){
                        FileUploadInfo fileUploadInfo = new FileUploadInfo();
                        fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.NOT_UPLOAD.getCode());
                        return fileUploadInfo;
                    }
                    //复制完成后再添加一条记录
                    SuperviseResourceFile saveFile = BeanCopyUtils.copyBean(file, SuperviseResourceFile.class);
                    saveFile.setId(IdUtil.getSnowflakeNextIdStr());
                    saveFile.setFilename(fileName);
                    saveFile.setFilePath(filePath);
                    saveFile.setFolderId(folderId);
                    saveFile.setCreateBy(null);
                    saveFile.setCreateTime(null);
                    saveFile.setUpdateBy(null);
                    saveFile.setUpdateTime(null);
                    save(saveFile);
                    FileUploadInfo fileUploadInfo = BeanCopyUtils.copyBean(saveFile, FileUploadInfo.class);
                    fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
                    return fileUploadInfo;
                }else{
                    //文件夹下有相同的文件，并且名字也相同
                    SuperviseResourceFile superviseResourceFile = collect.get(0);
                    FileUploadInfo dbFileInfo = BeanCopyUtils.copyBean(superviseResourceFile, FileUploadInfo.class);
                    dbFileInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
                    return dbFileInfo;
                }
            }

//            FileUploadInfo dbFileInfo = BeanCopyUtils.copyBean(file, FileUploadInfo.class);
//            dbFileInfo.setUploadStatus(HttpCodeUploadEnum.SUCCESS.getCode());
//            dbFileInfo.setAlreadyExists(true);
//            return dbFileInfo;
        }

        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        fileUploadInfo.setUploadStatus(HttpCodeUploadEnum.NOT_UPLOAD.getCode());
        return fileUploadInfo;
    }

    @Override
    public UploadUrlsVO initMultipartUpload(FileUploadInfo fileUploadInfo) {
        String fileNameMd5 =redisPrefix+ DigestUtil.md5Hex(fileUploadInfo.getFilename()+fileUploadInfo.getFolderId())+":"+fileUploadInfo.getDigestMd5();
        String s = redisUtils.get(fileNameMd5);

        // 若 redis 中有该 md5 的记录，以 redis 中为主
        String object;
        if (s != null) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            FileUploadInfo redisFileUploadInfo = jsonObject.toJavaObject(FileUploadInfo.class);
            fileUploadInfo = redisFileUploadInfo;
            object = redisFileUploadInfo.getFilePath();
        } else {
            String originFileName = fileUploadInfo.getFilename();
            String suffix = FileUtil.extName(originFileName);
            String fileName = FileUtil.mainName(originFileName);
            //先去找文件夹下面有没有同名文件，有的话要按之前的上传
            LambdaQueryWrapper<SuperviseResourceFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SuperviseResourceFile::getFolderId, fileUploadInfo.getFolderId());
            queryWrapper.eq(SuperviseResourceFile::getFilename, originFileName);
            List<SuperviseResourceFile> list = list(queryWrapper);
            if (list.isEmpty()) {
                // 对文件重命名，并以年月日文件夹格式存储
                String username = SecurityUtils.getUsername();
                object = getFilePath(fileName, username, fileUploadInfo.getDigestMd5(), suffix);

                fileUploadInfo.setFilePath(object).setType(suffix);
            }else {
                SuperviseResourceFile superviseResourceFile = list.get(0);
                object= superviseResourceFile.getFilePath();
                fileUploadInfo.setId(superviseResourceFile.getId());
                fileUploadInfo.setFilePath(object).setType(suffix);
            }
        }

        UploadUrlsVO urlsVO;
        // 单文件上传
        if (fileUploadInfo.getChunkCount() == 1) {
            log.info("当前分片数量 <{}> 单文件上传", fileUploadInfo.getChunkCount());
            urlsVO = minioUtil.getUploadObjectUrl(fileUploadInfo.getContentType(), object);
        } else {
            // 分片上传
            log.info("当前分片数量 <{}> 分片上传", fileUploadInfo.getChunkCount());
            urlsVO = minioUtil.initMultiPartUpload(fileUploadInfo, object);
        }
        fileUploadInfo.setUploadId(urlsVO.getUploadId());

        // 存入 redis （单片存 redis 唯一用处就是可以让单片也入库，因为单片只有一个请求，基本不会出现问题）

        redisUtils.setObj(fileNameMd5, fileUploadInfo, Duration.ofDays(minioConfigInfo.getBreakpointTime()));
        return urlsVO;
    }


    @Override
    public SuperviseResourceFile mergeMultipartUpload(String md5,String folderId,String fileName,String userName) {
        String fileNameMd5 =redisPrefix+ DigestUtil.md5Hex(fileName+folderId)+":"+md5;
        String s = redisUtils.get(fileNameMd5);
        String suffix = FileNameUtils.getSuffix(fileName);
        String filePath = getFilePath(fileName, userName, md5,suffix);
        if (StringUtils.isBlank(s)) {
            //这里代表着两个人同时上传同一个文件但是后面那个人来合并文件的时候已经被上一个人合并掉了，所以这里要处理一下
            LambdaQueryWrapper<SuperviseResourceFile> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(SuperviseResourceFile::getFolderId, folderId);
            queryWrapper.eq(SuperviseResourceFile::getDigestMd5, md5);
            List<SuperviseResourceFile> list = list(queryWrapper);
            if (list.isEmpty()) {
                return null;
            }
            List<SuperviseResourceFile> collect = list.stream().filter(e -> {
                return e.getFolderId().equals(folderId);
            }).collect(Collectors.toList());
            if (collect.isEmpty()) {
                //说明是别人上传的，自己的这个文件夹下面并没有，所以要处理复制过来
                SuperviseResourceFile superviseResourceFile = list.get(0);
                Boolean b = minioUtil.copyObject(superviseResourceFile.getFilePath(), filePath);

                SuperviseResourceFile saveFile = BeanCopyUtils.copyBean(superviseResourceFile, SuperviseResourceFile.class);
                saveFile.setId(IdUtil.getSnowflakeNextIdStr());
                saveFile.setFilename(fileName);
                saveFile.setFilePath(filePath);
                saveFile.setCreateBy(userName);
                saveFile.setFolderId(folderId);
                saveFile.setCreateTime(null);
                saveFile.setUpdateTime(null);
                saveFile.setUpdateBy(null);
                save(saveFile);
                return saveFile;
            }
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        FileUploadInfo redisFileUploadInfo = jsonObject.toJavaObject(FileUploadInfo.class);

//        String url = StrUtil.format("{}/{}/{}", minioConfigInfo.getEndpoint(), minioConfigInfo.getBucket(), redisFileUploadInfo.getFilePath());
//        String filename = redisFileUploadInfo.getFilename();
        SuperviseResourceFile files = BeanCopyUtils.copyBean(redisFileUploadInfo, SuperviseResourceFile.class);
//        files.setUrl(url)
//                .setBucket(minioConfigInfo.getBucket())
//                .setCreateTime(LocalDateTime.now());

        Integer chunkCount = redisFileUploadInfo.getChunkCount();
        // 分片为 1 ，不需要合并，否则合并后看返回的是 true 还是 false
        boolean isSuccess = chunkCount == 1 || minioUtil.mergeMultipartUpload(redisFileUploadInfo.getFilePath(), redisFileUploadInfo.getUploadId());
        if (isSuccess) {
            saveOrUpdate(files);
            redisUtils.delete(fileNameMd5);
            // 假如说，两个人同时上传了一个相同的文件，但是文件id不同，则这里要同时处理
            if (!files.getFolderId().equals(folderId)){
                minioUtil.copyObject(redisFileUploadInfo.getFilePath(),filePath);
                SuperviseResourceFile superviseResourceFile = BeanCopyUtils.copyBean(redisFileUploadInfo, SuperviseResourceFile.class);
                superviseResourceFile.setId(IdUtil.getSnowflakeNextIdStr());
                superviseResourceFile.setFilename(fileName);
                superviseResourceFile.setFilePath(filePath);
                superviseResourceFile.setFolderId(folderId);
                save(superviseResourceFile);
                return  superviseResourceFile;
            }
            return files;
        }
        return files;
    }

    @Override
    public byte[] downloadMultipartFile(Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // redis 缓存当前文件信息，避免分片下载时频繁查库
        SuperviseResourceFile file = null;
        String s = redisUtils.get(String.valueOf(id));
        JSONObject jsonObject = JSONObject.parseObject(s);
        SuperviseResourceFile redisFile = jsonObject.toJavaObject(SuperviseResourceFile.class);
//        SuperviseResourceFile redisFile = (SuperviseResourceFile)redisUtil.get(String.valueOf(id));
        if (redisFile == null) {
            SuperviseResourceFile dbFile = getById(id);
            if (dbFile == null) {
                return null;
            } else {
                file = dbFile;
                redisUtils.setObj(String.valueOf(id), file, Duration.ofDays(1));
            }
        } else {
            file = redisFile;
        }

        String range = request.getHeader("Range");
        String fileName = file.getFilename();
        log.info("下载文件的 object <{}>", file.getFilePath());
        // 获取 bucket 桶中的文件元信息，获取不到会抛出异常
        StatObjectResponse objectResponse = minioUtil.statObject(file.getFilePath());
        long startByte = 0; // 开始下载位置
        long fileSize = objectResponse.size();
        long endByte = fileSize - 1; // 结束下载位置
        log.info("文件总长度：{}，当前 range：{}", fileSize, range);

        BufferedOutputStream os = null; // buffer 写入流
        GetObjectResponse stream = null; // minio 文件流

        // 存在 range，需要根据前端下载长度进行下载，即分段下载
        // 例如：range=bytes=0-52428800
        if (range != null && range.contains("bytes=") && range.contains("-")) {
            range = range.substring(range.lastIndexOf("=") + 1).trim(); // 0-52428800
            String[] ranges = range.split("-");
            // 判断range的类型
            if (ranges.length == 1) {
                // 类型一：bytes=-2343 后端转换为 0-2343
                if (range.startsWith("-")) endByte = Long.parseLong(ranges[0]);
                // 类型二：bytes=2343- 后端转换为 2343-最后
                if (range.endsWith("-")) startByte = Long.parseLong(ranges[0]);
            } else if (ranges.length == 2) { // 类型三：bytes=22-2343
                startByte = Long.parseLong(ranges[0]);
                endByte = Long.parseLong(ranges[1]);
            }
        }

        // 要下载的长度
        // 确保返回的 contentLength 不会超过文件的实际剩余大小
        long contentLength = Math.min(endByte - startByte + 1, fileSize - startByte);
        // 文件类型
        String contentType = request.getServletContext().getMimeType(fileName);

        // 解决下载文件时文件名乱码问题
        byte[] fileNameBytes = fileName.getBytes(StandardCharsets.UTF_8);
        fileName = new String(fileNameBytes, 0, fileNameBytes.length, StandardCharsets.ISO_8859_1);

        // 响应头设置---------------------------------------------------------------------------------------------
        // 断点续传，获取部分字节内容：
        response.setHeader("Accept-Ranges", "bytes");
        // http状态码要为206：表示获取部分内容,SC_PARTIAL_CONTENT,若部分浏览器不支持，改成 SC_OK
        response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
        response.setContentType(contentType);
        response.setHeader("Last-Modified", objectResponse.lastModified().toString());
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setHeader("Content-Length", String.valueOf(contentLength));
        // Content-Range，格式为：[要下载的开始位置]-[结束位置]/[文件总大小]
        response.setHeader("Content-Range", "bytes " + startByte + "-" + endByte + "/" + objectResponse.size());
        response.setHeader("ETag", "\"".concat(objectResponse.etag()).concat("\""));
        response.setContentType("application/octet-stream;charset=UTF-8");

        try {
            // 获取文件流
            stream = minioUtil.getObject(objectResponse.object(), startByte, contentLength);
            os = new BufferedOutputStream(response.getOutputStream());
            // 将读取的文件写入到 OutputStream
            byte[] bytes = new byte[BUFFER_SIZE];
            long bytesWritten = 0;
            int bytesRead = -1;
            while ((bytesRead = stream.read(bytes)) != -1) {
                if (bytesWritten + bytesRead >= contentLength) {
                    os.write(bytes, 0, (int) (contentLength - bytesWritten));
                    break;
                } else {
                    os.write(bytes, 0, bytesRead);
                    bytesWritten += bytesRead;
                }
            }
            os.flush();
            response.flushBuffer();
            // 返回对应http状态
            return bytes;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) os.close();
            if (stream != null) stream.close();
        }
        return null;
    }

    @Override
    public String getDownloadUrlByFilePath(String filePath) {
        try {
            if (StringUtils.isEmpty(filePath)) {
                return null;
            }
            return minioUtil.GetPresignedObjectUrl(filePath);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public InputStream getInputStream(String filePath) {
        return minioUtil.getObjectStream(filePath);
    }
}
