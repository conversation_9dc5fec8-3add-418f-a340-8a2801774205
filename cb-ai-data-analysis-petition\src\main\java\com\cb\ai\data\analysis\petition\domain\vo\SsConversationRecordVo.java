package com.cb.ai.data.analysis.petition.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.Date;

@Data
public class SsConversationRecordVo  extends PageQueryVo implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /***
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /***
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /***
     * ocr解析内容
     */
    private String conversationContent;
    /***
     * 文件名称
     */
    private String fileName;
}
