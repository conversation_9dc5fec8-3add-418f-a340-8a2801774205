package com.cb.ai.data.analysis.voucher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cb.ai.data.analysis.voucher.domain.entity.VoucherAlertRecord;
import com.cb.ai.data.analysis.voucher.mapper.VoucherAlertRecordMapper;
import com.cb.ai.data.analysis.voucher.service.VoucherAlertRecordService;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class VoucherAlertRecordServiceImpl extends BaseServiceImpl<VoucherAlertRecordMapper, VoucherAlertRecord> implements VoucherAlertRecordService {

    @Override
    public void deleteByTaskId(String taskId) {
        if(StringUtils.isBlank(taskId)){
            throw new RuntimeException("任务id不能为空");
        }
        LambdaQueryWrapper<VoucherAlertRecord> queryWrapper = Wrappers.<VoucherAlertRecord>lambdaQuery()
                .eq(VoucherAlertRecord::getTaskId, taskId);
        baseMapper.delete(queryWrapper);
    }
}
