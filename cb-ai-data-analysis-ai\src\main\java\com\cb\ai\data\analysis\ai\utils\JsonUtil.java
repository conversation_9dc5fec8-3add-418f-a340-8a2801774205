package com.cb.ai.data.analysis.ai.utils;

import cn.hutool.core.util.StrUtil;
import com.cb.ai.data.analysis.ai.config.JsonConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Createtime 2025/5/27 13:54
 * @Copyright: Copyright (c) 2025
 * @Description: jackson JSON工具
 */
public class JsonUtil {
    private static final ObjectMapper OBJECT_MAPPER;

    private static final ObjectMapper OBJECT_MAPPER_ROOT;

    static {
        // 为了区别Spring全局序列化，同时保留全局设置
        OBJECT_MAPPER = JsonConfig.customizer(null).createXmlMapper(false).build();
        // 序列化时，null 字段不序列化
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 反序列化忽略位置属性
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // 对于空的对象转json的时候不抛出错误
        OBJECT_MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        // 允许属性名称没有引号
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        // 允许单引号
        OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        // 不序列号空的key-value
        OBJECT_MAPPER.configOverride(Map.class).setInclude(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.NON_NULL));
        // 美化输出
        // OBJECT_MAPPER.enable(SerializationFeature.INDENT_OUTPUT);
        // 含Root的Mapper
        OBJECT_MAPPER_ROOT = OBJECT_MAPPER.copy();
        OBJECT_MAPPER_ROOT.enable(SerializationFeature.WRAP_ROOT_VALUE);
        OBJECT_MAPPER_ROOT.enable(DeserializationFeature.UNWRAP_ROOT_VALUE);
    }

    public static ObjectMapper getInstance() {
        return OBJECT_MAPPER;
    }

    /**
     * @description: 获取对象的json字符串（ bean、array、List、Map --> json）
     * @params: obj（Object）
     * @return String
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static String toStr(Object obj) {
        try {
            if (obj == null) {
                return "";
            }
            if (obj instanceof String) {
                return (String) obj;
            }
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toStr(Object)方法异常，转换前的数据为：" + obj + "，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * @description: 解析对象（json --> bean ）
     * @params: [os, clazz]
     * @return T
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T toBean(Object os, Class<T> clazz) {
        try {
            T t;
            if (os instanceof String) {
                t = OBJECT_MAPPER.readValue(os.toString(), clazz);
            } else {
                if (ObjectUtils.isEmpty(os)) {
                    t = clazz.getDeclaredConstructor().newInstance();
                } else {
                    t = OBJECT_MAPPER.convertValue(os, clazz);
                }
            }
            return t;
        } catch (Exception e) {
            throw new RuntimeException("JsonUtil - toBean(Object, Class)方法异常，转换前的数据为：" + os + ", 转换对象为：" + clazz.getSimpleName() + "，异常信息为：" + e.getMessage(), e);
        }
    }


    /**
     * @description: 解析复杂对象（json --> bean，array，list，map等 ）
     * @params: [jsonStr, TypeReference]
     * @return T
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T toBean(Object os, TypeReference<T> reference) {
        try {
            T t = null;
            if (os instanceof String) {
                t = OBJECT_MAPPER.readValue(os.toString(), reference);
            } else {
                if (os == null) {
                    ParameterizedType type = (ParameterizedType) reference.getType();
                    if (List.class.isAssignableFrom(((Class<T>) type.getRawType()))) {
                        t = (T) new ArrayList(8);
                    } else if (Map.class.isAssignableFrom((Class<T>) type.getRawType())) {
                        t = (T) new HashMap(8);
                    } else {
                        Class<T> rawType = (Class<T>) type.getRawType();
                        t = rawType.getDeclaredConstructor().newInstance();
                    }
                } else {
                    t = OBJECT_MAPPER.convertValue(os, reference);
                }
            }
            return t;
        } catch (Exception e) {
            throw new RuntimeException("JsonUtil - toBean(Object, TypeReference)方法异常，转换前的数据为：" + os + ", 转换对象为：" + reference + "，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * @description: 解析对象（json --> bean ）
     * @params: [os, clazz]
     * @return T
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T toBean(Object os, ParameterizedTypeReference<T> typeReference) {
        return toBean(os, new TypeReference<T>() {
            @Override
            public Type getType() {
                return typeReference.getType();
            }
        });
    }

    /**
     * @description: 解析为List对象
     * @params: [sourceList, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> List<T> toList(List<?> sourceList, Class<T> elementClass) {
        return toList(toStr(sourceList), elementClass);
    }

    /**
     * @description: 解析json字符串为List对象
     * @params: [jsonStr, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> List<T> toList(String jsonStr, Class<T> elementClass) {
        return ((List<T>) toCollection(jsonStr, ArrayList.class, elementClass));
    }

    /**
     * @description: 解析为Set对象
     * @params: [jsonStr, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Set<T> toSet(Set<?> sourceSet, Class<T> elementClass) {
        return toSet(toStr(sourceSet), elementClass);
    }

    /**
     * @description: 解析json字符串为Set对象
     * @params: [jsonStr, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Set<T> toSet(String jsonStr, Class<T> elementClass) {
        return ((Set<T>) toCollection(jsonStr, HashSet.class, elementClass));
    }

    /**
     * @description: 解析json字符串为集合对象
     * @params: [jsonStr, collectionClass, elementClasses]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Collection<T> toCollection(String jsonStr, Class<? extends Collection> collectionClass, Class<T> elementClass) {
        try {
            if (StrUtil.isBlank(jsonStr)) {
                return Collections.emptyList();
            }
            JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(collectionClass, elementClass);
            return OBJECT_MAPPER.readValue(jsonStr, javaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toCollection(String, Class, Class)方法异常，转换前的数据为：" + jsonStr + "，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * @description: 转换POJO对象为Map对象
     * @params: [obj]
     * @return Map<String, Object>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Map<String, Object> toMap(T obj) {
        return toMap(toStr(obj));
    }

    /**
     * @description: 解析json字符串为 键值对 对象
     * @params: [jsonStr]
     * @return Map<String, Object>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static Map<String, Object> toMap(String jsonStr) {
        return toMap(jsonStr, Object.class);
    }

    /**
     * @description: 解析json字符串为 键值对 对象
     * @params: [jsonStr, valueClass]
     * @return Map<String, V>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <V> Map<String, V> toMap(String jsonStr, Class<V> valueClass) {
        return toMap(jsonStr, String.class, valueClass);
    }

    /**
     * @description: 解析json字符串为 键值对 对象
     * @params: [jsonStr, keyClass, valueClass]
     * @return Map<K, V>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T extends Map<K,V>, K,V> T toMap(String jsonStr, Class<K> keyClass, Class<V> valueClass) {
        return (T) toMap(jsonStr, LinkedHashMap.class, keyClass, valueClass);
    }

    /**
     * @description: 解析json字符串为 键值对 对象
     * @params: [jsonStr, mapClass, keyClass, valueClass]
     * @return Map<K, V>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T extends Map<K,V>, K,V> T toMap(String jsonStr, Class<T> mapClass, Class<K> keyClass, Class<V> valueClass) {
        try {
            if (StrUtil.isBlank(jsonStr)) {
                return (T) Collections.emptyMap();
            }
            JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(mapClass, keyClass, valueClass);
            return OBJECT_MAPPER.readValue(jsonStr, javaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toMap(String, Class, Class, Class)方法异常，转换前的数据为：" + jsonStr + "，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * @description: 获取对象的json字符串(含RootName)（ bean、array、List、Map --> json）
     * @params: obj（Object）
     * @return String
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static String toRootStr(Object obj) {
        try {
            return OBJECT_MAPPER_ROOT.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toRootStr(Object)方法异常", e);
        }
    }

    /**
     * @description: 解析json字符串为对象(含RootName)（json --> bean ）
     * @params: [jsonStr, clazz]
     * @return T
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T toRootBean(String jsonStr, Class<T> clazz) {
        try {
            return OBJECT_MAPPER_ROOT.readValue(jsonStr, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toRootBean(String, Class)方法异常", e);
        }
    }

    /**
     * @description: 解析json字符串为复杂对象(含RootName)（json --> bean，array，list，map等 ）
     * @params: [jsonStr, reference]
     * @return T
     * @createtime 2025/5/27 14:45
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> T toRootBean(String jsonStr, TypeReference<T> reference) {
        try {
            return OBJECT_MAPPER_ROOT.readValue(jsonStr, reference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toRootBean(String, TypeReference)方法异常", e);
        }
    }

    /**
     * @description: 解析json字符串为List对象(含RootName)
     * @params: [jsonStr, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> List<T> toRootList(String jsonStr, Class<T> elementClass) {
        return ((List<T>) toRootCollection(jsonStr, ArrayList.class, elementClass));
    }

    /**
     * @description: 解析json字符串为Set对象(含RootName)
     * @params: [jsonStr, collectionClass, elementClass]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Set<T> toRootSet(String jsonStr, Class<T> elementClass) {
        return ((Set<T>) toRootCollection(jsonStr, HashSet.class, elementClass));
    }

    /**
     * @description: 解析json字符串为集合对象(含RootName)
     * @params: [jsonStr, collectionClass, elementClasses]
     * @return Collection<T>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Collection<T> toRootCollection(String jsonStr, Class<? extends Collection> collectionClass, Class<T>... elementClasses) {
        try {
            return OBJECT_MAPPER_ROOT.readValue(jsonStr, OBJECT_MAPPER_ROOT.getTypeFactory().constructParametricType(collectionClass, elementClasses));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toRootCollection(String, Class, Class)方法异常", e);
        }
    }

    /**
     * @description: 转换POJO对象为Map对象(含RootName)
     * @params: [obj]
     * @return Map<String, Object>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <T> Map<String, Object> toRootMap(T obj) {
        return toRootMap(toRootStr(obj));
    }

    /**
     * @description: 解析json字符串为 键值对 对象(含RootName)
     * @params: [jsonStr]
     * @return Map<String, Object>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static Map<String, Object> toRootMap(String jsonStr) {
        return toRootMap(jsonStr, Object.class);
    }

    /**
     * @description: 解析json字符串为 键值对 对象(含RootName)
     * @params: [jsonStr, keyClass]
     * @return Map<K, Object>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <V> Map<String, V> toRootMap(String jsonStr, Class<V> valueClass) {
        return toRootMap(jsonStr, String.class, valueClass);
    }

    /**
     * @description: 解析json字符串为 键值对 对象(含RootName)
     * @params: [jsonStr, keyClass, valueClass]
     * @return Map<K, V>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <K,V> Map<K,V> toRootMap(String jsonStr, Class<K> keyClass, Class<V> valueClass) {
        return toRootMap(jsonStr, LinkedHashMap.class, keyClass, valueClass);
    }

    /**
     * @description: 解析json字符串为 键值对 对象(含RootName)
     * @params: [jsonStr, mapClass, keyClass, valueClass]
     * @return Map<K, V>
     * @createtime 2025/5/27 13:54
     * <AUTHOR>
     * @version 1.0
     */
    public static <K,V> Map<K,V> toRootMap(String jsonStr, Class<? extends Map> mapClass, Class<K> keyClass, Class<V> valueClass) {
        try {
            JavaType javaType = OBJECT_MAPPER_ROOT.getTypeFactory().constructParametricType(mapClass, keyClass, valueClass);
            return OBJECT_MAPPER_ROOT.readValue(jsonStr, javaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JsonUtil - toRootMap(String, Class, Class, Class)方法异常", e);
        }
    }

}
