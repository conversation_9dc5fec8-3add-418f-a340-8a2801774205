package com.cb.ai.data.analysis.voucher.utils.ocr;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.xong.boot.common.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class AiUtil {

    //    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/chat'}")
    private String chatUrl = "http://10.10.10.72:8686/cb-ai/chat";

    //    @Value("#{'${cb.ai.private-ai-base.base-url}' + '/rag/ocr'}")
    private String ocrUrl = "http://10.10.10.72:8686/cb-ai/rag/ocr";

    //    @Value("${cb.ai.private-ai-base.header-map.Authorization}")
    private String Authorization = "1";

    //    @Value("${cb.ai.private-ai-base.llm.role}")
    private String llmRole = "user";

    //    @Value("${cb.ai.private-ai-base.llm.temperature}")
    private Float llmTemperature = 0.0f;

    /**
     * 系统提示词正则表达式
     */
    private static final Pattern SYSTEM_PROMOTE_PATTERN = Pattern.compile("@systemPromote\\s*([\\s\\S]*?)(?=@end|$)");
    /**
     * 用户提示词正则表达式
     */
    private static final Pattern USER_PROMOTE_PATTERN = Pattern.compile("@userPromote\\s*([\\s\\S]*?)(?=@end|$)");
    /**
     * 自定义的提示词内部变量替换正则表达式
     */
    private static final Pattern REPLACE_CODE_PATTERN = Pattern.compile("@#([^#]+)#@");


    /**
     * ocr识别图片
     *
     * @param base64
     * @param original 是否原模原样返回，默认false（默认调用模型对识别内容进行一道语义处理）
     * @return
     */
    public String ocrBase64(String base64, boolean original) {
        Map<String, Object> ocrData = new HashMap<>();
        ocrData.put("base64", base64);
        ocrData.put("translate", !original);
        Map<String, String> headers = new HashMap<>();
        headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        headers.put(Header.AUTHORIZATION.getValue(), Authorization);
        HttpRequest httpRequest = HttpUtils.createRequest(Method.POST, ocrUrl)
                .headerMap(headers, true)
                .body(JSON.toJSONString(ocrData))
                .timeout(30 * 60 * 1000);// 30分钟超时
        HttpResponse httpResponse = httpRequest.execute();
        if (ObjectUtils.isEmpty(httpResponse)) {
            throw new RuntimeException("ocr请求响应为空！");
        }
        if (httpResponse.getStatus() != HttpStatus.HTTP_OK) {
            throw new RuntimeException(httpResponse.body());
        }
        return httpResponse.body();
    }

    /**
     * ai同步分析
     *
     * @param input     输入内容
     * @param checkRule 检测规则
     * @return
     */
    public String chatSyncAnalysis(String input, String checkRule) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("checkRule", checkRule);
            params.put("input", input);
            String resPath = "promote/voucher_analysis.md";
            Map<String, Object> chatReq = new HashMap<>();
            chatReq.put("role", llmRole);
            chatReq.put("systemPromote", buildSysPrompt(resPath, params));
            chatReq.put("promote", buildUserPrompt(resPath, params));
            chatReq.put("temperature", llmTemperature);
            Map<String, String> headers = new HashMap<>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
            headers.put(Header.AUTHORIZATION.getValue(), Authorization);
            HttpRequest httpRequest = HttpUtils.createRequest(Method.POST, chatUrl + "/sync")
                    .headerMap(headers, true)
                    .body(JSON.toJSONString(chatReq))
                    .timeout(30 * 60 * 1000);// 30分钟超时
            HttpResponse httpResponse = httpRequest.execute();
            if (ObjectUtils.isEmpty(httpResponse)) {
                throw new RuntimeException("ai对话(同步)请求响应为空！");
            }
            if (httpResponse.getStatus() != HttpStatus.HTTP_OK) {
                throw new RuntimeException(httpResponse.body());
            }
            String resp = httpResponse.body();
            log.info("ai对话(同步)响应：{}",  resp);
            // 提权content
            return parseContent(resp);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new RuntimeException("调用ai对话(同步)接口失败！", e);
            }
        }

    }

    /**
     * 从相应文本中提取content内容
     * @param resp
     * @return
     */
    private String parseContent(String resp) {
        JSONObject jsonObject = JSON.parseObject(resp);
        String content = (String) JSONPath.eval(jsonObject, "$.choices[0].message.content");//jsonObject.getString("choices[0].message.content");
        String regex = "(`{3})\\s*(?:json)?\\r?\\n([\\s\\S]*?)\\1";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        return matcher.replaceAll("$2");
    }

    /**
     * 构建系统提示词
     *
     * @param filePath
     * @param params
     * @return
     */
    private String buildSysPrompt(String filePath, Map<String, Object> params) {
        String promptTpl = getPromptTpl(filePath, SYSTEM_PROMOTE_PATTERN);
        return renderPrompt(promptTpl, params);
    }

    /**
     * 构建用户提示词
     *
     * @param filePath
     * @param params
     * @return
     */
    private String buildUserPrompt(String filePath, Map<String, Object> params) {
        String promptTpl = getPromptTpl(filePath, USER_PROMOTE_PATTERN);
        return renderPrompt(promptTpl, params);
    }

    /**
     * 获取提示词模版
     *
     * @param filePath
     * @param pattern  正则匹配，用于匹配何种提示词 SYSTEM_PROMOTE_PATTERN/USER_PROMOTE_PATTERN
     * @return
     */
    private String getPromptTpl(String filePath, Pattern pattern) {
        String content = "";
        try {
            Path path = Paths.get(filePath);
            InputStream is = null;
            if (!path.isAbsolute()) {
                ResourceLoader resourceLoader = new DefaultResourceLoader(AiUtil.class.getClassLoader());
                Resource resource = resourceLoader.getResource("classpath:" + filePath);
                if (!resource.exists()) {
                    throw new IllegalArgumentException("Resource not found: " + filePath);
                }
                is = resource.getInputStream();
            } else {
                is = new FileInputStream(path.toFile());
            }
            content = IoUtil.read(is, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new IllegalArgumentException("Error reading resource: " + filePath, e);
        }
        Matcher userMatcher = pattern.matcher(content);
        if (userMatcher.find()) {
            content = userMatcher.group(1).trim();
        } else {
            content = "";
        }
        return content;
    }

    /**
     * 渲染提示词，将变量替换掉
     *
     * @param markdownContent
     * @param params
     * @return
     */
    private String renderPrompt(String markdownContent, Map<String, Object> params) {
        // 匹配 @#var_xxx 格式的标记
        Matcher matcher = REPLACE_CODE_PATTERN.matcher(markdownContent);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            // 获取变量名
            String key = matcher.group(1);
            // 从map中获取值，如果没有则设为空字符串
            String value = Convert.toStr(params.get(key), "");
            // 替换匹配到的标记
            matcher.appendReplacement(result,  Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);

        return result.toString();
    }

}
