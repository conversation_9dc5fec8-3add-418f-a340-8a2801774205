<script lang="ts" setup>
import { computed, createVNode, inject, reactive, ref, useTemplateRef } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { extractTreeWithPaths } from '@/utils'
import { fetchStream } from '@/utils/http'
import { useUserStore } from '@/stores'
import errorLog from '@/utils/errorLog'

interface Item {
  title: string
  content: string | Item[]
}

const props = defineProps({
  level: {
    type: Number,
    default: 0
  },
  parentId: {
    type: String,
    default: ''
  },
  optionLevel: {
    type: Number,
    default: 2
  }
})
const visible = ref(false)
const ruleForm = reactive({
  user_input: '',
  currentData: undefined,
  currentDataIndex: undefined
})
const userStore = useUserStore()
const getSelected = inject('getSelected')
let abortController = undefined
const getAllList = inject('getAllList')
const changeItem = inject('changeItem')
const getPromote = inject('getPromote')
const getBaseIds = inject('getBaseIds')
const getReCreateContent = inject('getReCreateContent')
const setThinkNode = inject('setThinkNode')
const setController = inject('setController')
const loading = defineModel('loading', {
  type: Boolean,
  default: false
})
const list = defineModel<Record<string, any>[]>('list', {
  type: Array,
  default: () => []
})
const ruleFormRef = useTemplateRef('ruleFormRef')
const baseFontSize = ref(30)
const style = computed(() => ({
  fontSize: `${baseFontSize.value - props.level * 5}px`
}))

function handleMenuClick({ key }, item, index) {
  if (key === 'del') {
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否删除',
      onOk() {
        return new Promise((resolve) => {
          list.value = list.value.filter((v, i) => i !== index)
          changeItem(false, getNodeId(index))
          resolve()
        })
      }
    })
  } else if (key === 'remake') {
    remake(item, index)
  } else if (key === 'merger') {
    visible.value = true
    ruleForm.currentData = item
    ruleForm.currentDataIndex = index
    ruleForm.user_input = getPromote()
  }
}

function isSelect(id) {
  return getSelected().includes(id)
}

function getNodeId(index) {
  const id = `${props.parentId}-${index}`
  return id.replace(/^-/, '')
}

function change(v, id) {
  changeItem(v.target.checked, id)
}

function zhuiwen() {
  ruleFormRef.value?.validate().then(() => {
    const id = getNodeId(ruleForm.currentDataIndex)
    const data = getAllList()
    const pathData = extractTreeWithPaths(data, [id]).at()
    if (Array.isArray(pathData.content)) {
      const los = pathData.title
      const title = pathData.content[0].title
      reCreateContent({
        baseIds: getBaseIds(),
        los,
        title,
        content: ruleForm.currentData,
        promote: ruleForm.user_input,
        index: ruleForm.currentDataIndex
      })
      visible.value = false
    } else {
      errorLog.push({
        msg: '非数组结构',
        stack: 'zhuiwen114',
        title: '数据格式有误，请重新生成',
        data: pathData
      })
      message.error('数据格式有误，请重新生成')
    }
  })
}

function remake(item, index) {
  const id = getNodeId(index)
  const data = getAllList()
  const pathData = extractTreeWithPaths(data, [id]).at()
  if (Array.isArray(pathData.content)) {
    const los = pathData.title
    const title = pathData.content[0].title
    reCreateContent({
      baseIds: getBaseIds(),
      los,
      title,
      content: item,
      promote: getPromote(),
      index
    })
  } else {
    errorLog.push({
      msg: '非数组结构',
      stack: 'remake136',
      title: '数据格式有误，请重新生成',
      data: pathData
    })
    message.error('数据格式有误，请重新生成')
  }
}

async function reCreateContent({ los, title, content, promote, index, baseIds }) {
  const thinkNode = reactive([])
  setThinkNode(thinkNode)
  const params = {
    promote: promote,
    analyseTag: getReCreateContent(),
    los,
    title,
    content,
    baseIds
  }
  abortController = new AbortController()
  loading.value = true
  setController(abortController)
  fetchStream({
    url: `${import.meta.env.VITE_HTTP_BASE_URL}/api/ai/common/execute`,
    method: 'post',
    signal: abortController.signal,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${userStore.token}`
    },
    body: JSON.stringify(params),
    success: (res: string) => {
      try {
        const data = JSON.parse(res)
        const result = data.result
        if (data.status === 'start') {
          thinkNode.push({
            status: 'streaming',
            nodeId: data.nodeId,
            files: [],
            reasoning_content: result?.reasoning_content || '',
            message: result?.content || ''
          })
        } else if (data.status === 'streaming') {
          const hasNode = thinkNode.find((v) => v.nodeId === data.nodeId)
          if (hasNode) {
            hasNode.reasoning_content += result?.reasoning_content || ''
            hasNode.message += result?.content || ''
            if (result?.data?.fileId) {
              hasNode.files.push(result.data)
            }
          }
        } else if (data.status === 'end') {
          const hasNode = thinkNode.find((v) => v.nodeId === data.nodeId)
          if (hasNode) {
            hasNode.status = 'end'
            if (hasNode.message?.trim()) {
              list.value = list.value.map((v, i) => {
                if (i === index) {
                  return {
                    title: hasNode.message?.replace(/(\[\^(\d+)]|```)/g, '')?.trim()
                  }
                } else {
                  return v
                }
              })
            } else {
              message.error('内容错误请重试')
            }
          }
        } else if (data.status === 'error') {
          throw new Error(data.error)
        }
      } catch (err) {
        errorLog.push({
          msg: err.message,
          stack: err.stack,
          title: 'reCreateContent207',
          data: res
        })
        message.error(err.message)
      }
    },
    error: (err) => {
      message.error(err)
    },
    complete: () => {
      loading.value = false
      thinkNode.forEach((v) => {
        v.status = 'end'
      })
    }
  })
}
</script>

<template>
  <div v-for="(item, index) in list" :key="index" :class="['item', level === 0 && 'nopdL']">
    <div class="title">
      <a-checkbox
        v-if="props.level === props.optionLevel"
        :checked="isSelect(getNodeId(index))"
        :disabled="loading"
        @change="change($event, getNodeId(index))"
      ></a-checkbox>
      <a-textarea
        v-model:value="item.title"
        :autoSize="true"
        :disabled="loading"
        :style="style"
      ></a-textarea>
      <a-dropdown v-if="props.level === props.optionLevel" :disabled="loading">
        <a class="ant-dropdown-link" @click.prevent>操作</a>
        <template #overlay>
          <a-menu @click="handleMenuClick($event, item, index)">
            <a-menu-item key="del">删除</a-menu-item>
            <a-menu-item key="remake">更换</a-menu-item>
            <a-menu-item key="merger">追问</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div v-if="Array.isArray(item.content) && item.content.length > 0" class="children">
      <reportView
        v-model:list="item.content"
        v-model:loading="loading"
        :level="level + 1"
        :parentId="getNodeId(index)"
      />
    </div>
  </div>
  <a-modal v-if="props.level === props.optionLevel" v-model:open="visible" title="追问">
    <a-form
      ref="ruleFormRef"
      :disabled="loading"
      :model="ruleForm"
      class="ruleForm"
      name="ruleForm"
    >
      <a-form-item
        :rules="[{ required: true, message: '请输入提示词' }]"
        label="提示词"
        name="user_input"
      >
        <a-textarea v-model:value="ruleForm.user_input" :rows="5" placeholder="请输入提示词" />
      </a-form-item>
    </a-form>
    <template v-slot:footer>
      <a-button
        :disabled="loading"
        :loading="loading"
        danger
        type="primary"
        @click="visible = false"
      >
        关闭
      </a-button>
      <a-button
        :disabled="loading"
        :loading="loading"
        class="btnSpacing"
        type="primary"
        @click="zhuiwen"
      >
        追问
      </a-button>
    </template>
  </a-modal>
</template>

<style lang="less" scoped>
.item {
  width: 100%;
  box-sizing: border-box;
  padding-left: 2em;
  margin-bottom: 1em;

  .ant-dropdown-link {
    flex-shrink: 0;
  }

  .title {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 1em;
  }

  .children {
    width: 100%;
  }

  .content {
    width: 100%;
    display: flex;
    align-items: flex-start;
    padding-left: 2em;

    .text {
      margin-left: 1em;
      font-size: 20px;
      flex-grow: 1;
    }

    .operator {
      margin-left: 10px;
      display: flex;
      width: fit-content;
      flex-shrink: 0;
    }
  }

  .btnSpacing {
    margin-left: 5px;
  }
}

.nopdL {
  padding-left: 0;
}
</style>
