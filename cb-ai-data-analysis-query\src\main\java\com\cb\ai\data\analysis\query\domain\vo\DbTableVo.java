package com.cb.ai.data.analysis.query.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DbTableVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String tableName;
    private String tableComment;
    private List<ColumnVo> columns;
}
