package com.cb.ai.data.analysis.petition.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.xong.boot.common.domain.BaseDomain;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.Date;

/**
 * ss_conversation
 * <AUTHOR>
@Data
@TableName("ss_conversation_record")
public class SsConversationRecordEntity  extends BaseDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原始文本
     */
    private String conversationContent;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationAnalyzed;

    /**
     * 谈话摘要
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationBrief;

    /**
     * 谈话时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationDate;

    /**
     * 谈话对象
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationObj;

    /**
     * 谈话人
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationHolder;

    /**
     * 被谈话干部汇报和表态内容
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkStatement;
    /**
     * 谈话地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String conversationAddress;
    /**
     * 记录人
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String recordUser;
    /**
     * 谈话对象性别
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkGender;
    /**
     * 谈话对象出生日期
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkBirthday;
    /**
     * 谈话对象民族
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkNationality;
    /**
     * 谈话对象政治面貌
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkPolitical;
    /**
     * 谈话对象文化程度
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkEducation;
    /**
     * 谈话对象职务
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkPost;
    /**
     * 谈话对象工作单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkOrg;
    /**
     * 谈话对象地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String talkAddress;

    @TableField(exist = false)
    private MultipartFile file;
}
