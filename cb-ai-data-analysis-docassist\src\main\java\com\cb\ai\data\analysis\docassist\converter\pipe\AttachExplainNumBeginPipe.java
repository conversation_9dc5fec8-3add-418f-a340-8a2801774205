package com.cb.ai.data.analysis.docassist.converter.pipe;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.docassist.converter.DocConfig;
import com.cb.ai.data.analysis.docassist.converter.FormatTools;
import com.cb.ai.data.analysis.docassist.converter.model.DocumentInfo;
import com.cb.ai.data.analysis.docassist.converter.pipe.ext.IAttachExt;
import com.xong.boot.common.utils.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.util.List;

/**
 * 公文目录（以数字开头的附件段落）
 *
 * <AUTHOR>
 */
public class AttachExplainNumBeginPipe extends IPipe implements IAttachExt {
    @Override
    boolean check(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        //去除特殊空白字符
        String text = paragraph.getText().trim().replaceAll("\\p{Zs}", "");
        if (StringUtils.isBlank(text)) {
            return false;
        }
        boolean currMatch = ReUtil.isMatch("^[\\d]{1,2}\\..+", text);
        //往前10个段落内有附件的话是附件列表，进行匹配
        return currMatch && pre10ParagraphHasAttach(document, pos);
    }

    @Override
    void format(XWPFDocument document, XWPFParagraph paragraph, DocConfig config, Integer pos, DocumentInfo documentInfo) {
        // 格式化附件目录
        List<XWPFRun> runs = paragraph.getRuns();
        // 修改序号后标点
        for (XWPFRun run : runs) {
            //去除特殊空白字符
            String runText = run.text().replaceAll("\\p{Zs}", "");
            if (StringUtils.isBlank(runText)) {
                continue;
            }
            if (ReUtil.contains("[0-9][.。．]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "([0-9])([.。．])", "$1.");
                run.setText(replaced, 0);
                break;
            }
            if (ReUtil.contains("^[.。．]", runText)) {
                String replaced = ReUtil.replaceAll(runText, "^([.。．])", ".");
                run.setText(replaced, 0);
                break;
            }
        }
        // 格式化段落
        FormatTools.formatAttachExplain(paragraph, config, false);
        for (XWPFRun run : runs) {
            FormatTools.format(run, config);
        }
    }
}
