package com.xong.boot.common.annotation;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 数据权限过滤注解
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface DataScope {
    /**
     * 处理的方法名称 com.xong.boot.**.selectById
     */
    @AliasFor("includes")
    String value() default "";

    /**
     * 处理的方法名称
     */
    @AliasFor("value")
    String includes() default "";
}
