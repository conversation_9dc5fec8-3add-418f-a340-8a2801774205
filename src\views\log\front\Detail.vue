<template>
  <a-modal :open="visible" :footer="false" @cancel="onCancel" width="60%" title="日志详情">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="用户账号">
        {{ logInfo.principal }}
      </a-descriptions-item>
      <a-descriptions-item label="登录平台">
        {{ Platform.L(logInfo.platform) }}
      </a-descriptions-item>
      <a-descriptions-item label="登录类型">
        {{ LoginMode.L(logInfo.mode) }}
      </a-descriptions-item>
      <a-descriptions-item label="登录时间">
        {{ $date.formatDateTime(logInfo.loginTime) }}
      </a-descriptions-item>
      <a-descriptions-item label="登录IP">
        {{ logInfo.ipaddr }}
      </a-descriptions-item>
      <a-descriptions-item label="操作系统">
        {{ userAgent.os?.name }}
      </a-descriptions-item>
      <a-descriptions-item label="浏览器">
        {{ userAgent.browser?.name }}
      </a-descriptions-item>
      <a-descriptions-item label="状态编码">
        {{ logInfo.code }}
      </a-descriptions-item>
      <a-descriptions-item label="状态信息" :span="2">
        {{ logInfo.message }}
      </a-descriptions-item>
      <a-descriptions-item label="浏览器信息" :span="2">
        {{ userAgent }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script setup lang="ts">
import { type ComponentCustomProperties, computed, getCurrentInstance, ref, watch } from 'vue'
import { login } from '@/api/log'
import { Platform, LoginMode } from '@/enums'

const _this = getCurrentInstance()?.proxy as ComponentCustomProperties
const emits = defineEmits(['update:visible'])
const props = withDefaults(
  defineProps<{
    visible: boolean
    id?: string
  }>(),
  { visible: false }
)
const logInfo = ref<Record<string, any>>({})

const userAgent = computed(() => {
  const { userAgent } = logInfo.value || {}
  if (!userAgent) {
    return {}
  }
  return userAgent
})

function onCancel() {
  emits('update:visible', false)
}
async function loadData() {
  if (!props.id || props.id === '') {
    _this.$message.error('ID不存在')
    onCancel()
    return
  }
  try {
    const { data = {} } = await login.detail(props.id)
    logInfo.value = data || {}
  } catch (error: any) {
    _this.$message.error(error.message)
  }
}

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      loadData()
    }
  }
)
</script>
