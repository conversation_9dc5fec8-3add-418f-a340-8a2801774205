package com.cb.ai.data.analysis.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cb.ai.data.analysis.ai.common.properties.PrivateAIBaseProperties;
import com.cb.ai.data.analysis.ai.domain.common.JsonMap;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistory;
import com.cb.ai.data.analysis.ai.domain.entity.AiChatHistoryDetail;
import com.cb.ai.data.analysis.ai.mapper.AiChatHistoryMapper;
import com.cb.ai.data.analysis.ai.service.AiChatHistoryService;
import com.cb.ai.data.analysis.ai.service.IAiChatHistoryDetailService;
import com.cb.ai.data.analysis.ai.utils.MergeUtil;
import com.xong.boot.common.exception.CustomException;
import io.github.admin4j.http.util.HttpUtil;
import jakarta.annotation.Resource;
import okhttp3.Response;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * AI 会话历史记录(AiChatHistory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07 14:23:50
 */
@Service
public class AiChatHistoryServiceImpl extends ServiceImpl<AiChatHistoryMapper, AiChatHistory> implements AiChatHistoryService {
    @Resource
    private PrivateAIBaseProperties aiBaseProperties;

    @Resource
    private IAiChatHistoryDetailService aiChatHistoryDetailService;

    @Override
    public boolean initAiChatHistory(String sessionId) {
        AiChatHistory aiChatHistory = new AiChatHistory();
        aiChatHistory.setSessionId(sessionId);
        return baseMapper.insertOrUpdate(aiChatHistory);
    }

    @Override
    public AiChatHistory getChatHistoryBySessionId(String sessionId) {
        QueryWrapper<AiChatHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("session_id", sessionId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public AiChatHistory getChatHistoryByUserId(String userId) {
        QueryWrapper<AiChatHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean removeBySessionId(String sessionId) {
        return baseMapper.delete(new QueryWrapper<AiChatHistory>().eq("session_id", sessionId)) > 0;
    }

    @Override
    public boolean removeByUserId(String userId) {
        return baseMapper.delete(new QueryWrapper<AiChatHistory>().eq("user_id", userId)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<String> ids) {
        if (baseMapper.deleteByIds(ids) > 0) {
            if (aiChatHistoryDetailService.remove(new QueryWrapper<AiChatHistoryDetail>().in("session_id", ids))) {
                try (Response response = HttpUtil.delete(
                        MergeUtil.mergePath(aiBaseProperties.getBaseUrl(), "/chat/" + ids.get(0)),
                        null,
                        new JsonMap(aiBaseProperties.getHeaderMap()).putOnce(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                )) {
                    if (response.isSuccessful()) {
                        return true;
                    } else {
                        throw new RuntimeException(response.message());
                    }
                }
            } else {
                throw new CustomException("删除AI会话历史记录详情失败");
            }
        }
        return false;
    }

}

