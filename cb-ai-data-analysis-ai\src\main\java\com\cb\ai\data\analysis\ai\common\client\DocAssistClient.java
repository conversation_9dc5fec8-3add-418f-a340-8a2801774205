package com.cb.ai.data.analysis.ai.common.client;

import com.cb.ai.data.analysis.ai.domain.request.WordFormatRequest;
import com.xong.boot.common.api.Result;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;



@Component
public class DocAssistClient {

    private final RestTemplate restTemplate = new RestTemplateBuilder().build();

    @Value("${server.port}")
    private String port;

    public String formatDocx(HttpServletRequest request, WordFormatRequest wordFormatRequestVo) {
        // 构建请求 URL
        String url = "http://localhost:" + port + "/converter/common";

        // 获取 Authorization 头部
        String authorizationHeader = request.getHeader("Authorization");

        // 构建 headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (authorizationHeader != null) {
            headers.set("Authorization", authorizationHeader);
        }

        // 构建请求实体
        HttpEntity<WordFormatRequest> httpEntity = new HttpEntity<>(wordFormatRequestVo, headers);

        // 发送 POST 请求
        ResponseEntity<Result> response = restTemplate.exchange(
            url,
            HttpMethod.POST,
            httpEntity,
            Result.class
        );

        Result result = response.getBody();

        if (result == null || result.getData() == null) {
            throw new RuntimeException("调用 cb-docassist 格式化接口失败或返回无数据");
        }

        return result.getData().toString();
    }



}
